package com.jlr.ecp.notification.controller.admin;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.enums.ErrorCodeConstants;
import com.jlr.ecp.notification.excel.service.NotificationExcelService;
import com.jlr.ecp.notification.excel.vo.UploadExcelRespVO;
import com.jlr.ecp.notification.util.FileCheckUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Objects;


@Tag(name = "手动任务，excel操作管理")
@RestController
@RequestMapping("/manual/task/excel")
@Slf4j
public class NotificationExcelController {
    @Resource
    private NotificationExcelService excelService;

    /**
     * 2MB
     */
    private static final long MAX_FILE_SIZE = 2L * 1024 * 1024;

    @GetMapping("/download/template")
    @Operation(summary = "下载手动短信发送模板")
    @PreAuthorize("@ss.hasPermission('notification:smstask-manual:edit')")
    public CommonResult<String> downloadTemplateUrl(@RequestParam Integer excelTemplateType) {
        log.info("downloadTemplateUrl, excelTemplateType:{}", excelTemplateType);
        // 获取模板文件的URL
        String templateUrl = excelService.getTemplateUrl(excelTemplateType);
        return CommonResult.success(templateUrl);
    }


    @PostMapping("/upload")
    @Operation(summary = "上传excel文件")
    @PreAuthorize("@ss.hasPermission('notification:smstask-manual:edit')")
    public CommonResult<UploadExcelRespVO> batchUpload(@RequestParam MultipartFile file, @RequestParam Integer excelType) {
        log.info("batchUpload excel, file:{}, excelTemplateType:{}", file, excelType);
        long startTime = System.currentTimeMillis();
        if (Objects.isNull(file)) {
            return CommonResult.error(ErrorCodeConstants.UPLOAD_EXCEL_ERROR);
        }
        log.info("文件大小:{}", file.getSize());
        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            return CommonResult.error(ErrorCodeConstants.FILE_SIZE_EXCEED_LIMIT);
        }
        //校验文件是否为excel
        if (!FileCheckUtil.isExcelFile(file)) {
            return CommonResult.error(ErrorCodeConstants.UPLOAD_FILE_FORMAT_ERROR);
        }
        CommonResult<UploadExcelRespVO> resp = excelService.uploadExcel(file, excelType);
        long endTime = System.currentTimeMillis();
        log.info("上传excel文件解析完成，花费时间:{}毫秒，resp:{}", (endTime-startTime), resp);
        return resp;
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除excel文件")
    @PreAuthorize("@ss.hasPermission('notification:smstask-manual:edit')")
    public CommonResult<Boolean> deleteUploadExcel( @RequestParam String filePath) {
        log.info("删除excel文件, filePath:{}", filePath);
        return excelService.deleteExcel(filePath);
    }

}
