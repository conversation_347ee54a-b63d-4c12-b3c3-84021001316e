package com.jlr.ecp.notification.util;

import com.jlr.ecp.notification.util.sms.TimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class InstanceCodeGenerator {
    @Resource
    private RedissonClient redissonClient;

    private static final String SEQUENCE = "001";

    public String generateTaskBatchId(String taskType) {
        RLock lock = null;
        try {
            String timePart = TimeFormatUtil.timeToStringByFormat(LocalDateTime.now(), TimeFormatUtil.formatter_5);
            String key = taskType + timePart + ":counter";
            // 获取锁实例
            lock = redissonClient.getLock(taskType + timePart + ":lock");
            boolean acquired = false;
            for (int cnt = 1; cnt <= 6; cnt++) {
                acquired = lock.tryLock(10, 5, TimeUnit.SECONDS);
                if (acquired) {
                    break;
                }
            }
            // 多次获取redis锁失败，给默认值
            if (!acquired) {
                log.warn("多次无法获取锁, 无法生成InstanceCode, taskType:{}", taskType);
                return taskType + timePart + SEQUENCE;
            }

            // 获取或创建计数器，并设置过期时间
            RAtomicLong counter = redissonClient.getAtomicLong(key);

            // 设置键的过期时间为10分钟（即使键已经存在）
            // 如果键不存在，则初始化它；否则，只更新过期时间
            if (counter.compareAndSet(0, 0)) {
                counter.expire(Duration.ofMinutes(10));
            } else {
                counter.expire(Duration.ofMinutes(10));
            }

            long sequence = counter.incrementAndGet();
            String sequenceStr = String.format("%03d", sequence);
            return taskType + timePart + sequenceStr;
        } catch (Exception e) {
            log.info("生成InstanceCode异常: {}", e.getMessage());
            return taskType + TimeFormatUtil.timeToStringByFormat(LocalDateTime.now(), TimeFormatUtil.formatter_5)
                    + SEQUENCE;
        } finally {
            // 检查以确认锁是由当前线程持有的
            if (Objects.nonNull(lock) && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
