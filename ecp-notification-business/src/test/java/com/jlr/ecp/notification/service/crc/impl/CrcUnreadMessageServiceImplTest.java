package com.jlr.ecp.notification.service.crc.impl;

import cn.hutool.core.lang.Snowflake;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.dal.dataobject.crc.CrcMessagesDO;
import com.jlr.ecp.notification.dal.repository.CrcUnreadMessageRepository;
import com.jlr.ecp.notification.dto.crc.ReadCrcMessageDTO;
import com.jlr.ecp.notification.dto.crc.SaveCrcUnreadMsgDTO;
import com.jlr.ecp.notification.vo.crc.SaveCrcUnreadMsgVO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CrcUnreadMessageServiceImplTest {

    @Mock
    private Snowflake mockSnowflake;
    @Mock
    private CrcUnreadMessageRepository mockCrcMessageRepository;

    @InjectMocks
    private CrcUnreadMessageServiceImpl crcUnreadMessageServiceImplUnderTest;

    private static final String TEST_JLR_ID = "testJlrid";
    private static final String TEST_BUSINESS_CODE = "testBusinessCode";
    private static final String TEST_MESSAGE_ID = "testMessageId";
    private static final String TEST_MESSAGE_TYPE = "1";
    private static final String TEST_MESSAGE_CONTENT = "testMessageContent";
    private static final LocalDateTime TEST_MESSAGE_TIME = LocalDateTime.of(2020, 1, 1, 0, 0, 0);

    @Before
    public void setUp() {
        when(mockSnowflake.nextId()).thenReturn(1L);
    }

    /**
     * 测试添加CRC未读消息成功场景
     */
    @Test
    public void testAddCrcUnreadMessage_Success() {
        // Setup
        final SaveCrcUnreadMsgDTO saveCrcUnreadMsgDTO = buildSaveCrcUnreadMsgDTO();
        final CommonResult<SaveCrcUnreadMsgVO> expectedResult = CommonResult.success(
                SaveCrcUnreadMsgVO.builder()
                        .jlrid(TEST_JLR_ID)
                        .businessCode(TEST_BUSINESS_CODE)
                        .messageId(TEST_MESSAGE_ID)
                        .status("success")
                        .build());
        
        when(mockCrcMessageRepository.saveCrcMessagesDO(any(CrcMessagesDO.class))).thenReturn(true);

        // Run the test
        final CommonResult<SaveCrcUnreadMsgVO> result = crcUnreadMessageServiceImplUnderTest.addCrcUnreadMessage(
                saveCrcUnreadMsgDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    /**
     * 测试添加CRC未读消息失败场景
     */
    @Test
    public void testAddCrcUnreadMessage_Failure() {
        // Setup
        final SaveCrcUnreadMsgDTO saveCrcUnreadMsgDTO = buildSaveCrcUnreadMsgDTO();
        final CommonResult<SaveCrcUnreadMsgVO> expectedResult = CommonResult.success(
                SaveCrcUnreadMsgVO.builder()
                        .jlrid(TEST_JLR_ID)
                        .businessCode(TEST_BUSINESS_CODE)
                        .messageId(TEST_MESSAGE_ID)
                        .status("failed")
                        .build());
        
        when(mockCrcMessageRepository.saveCrcMessagesDO(any(CrcMessagesDO.class))).thenReturn(false);

        // Run the test
        final CommonResult<SaveCrcUnreadMsgVO> result = crcUnreadMessageServiceImplUnderTest.addCrcUnreadMessage(
                saveCrcUnreadMsgDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    /**
     * 测试查询未读消息数量为0的场景
     */
    @Test
    public void testFindCrcUnreadMessage_ZeroCount() {
        // Setup
        final CommonResult<Integer> expectedResult = CommonResult.success(0);
        when(mockCrcMessageRepository.findCrcMsgByJlrIdAndStatus(TEST_JLR_ID, 0, TEST_BUSINESS_CODE)).thenReturn(0L);

        // Run the test
        final CommonResult<Integer> result = crcUnreadMessageServiceImplUnderTest.findCrcUnreadMessage(
                TEST_JLR_ID, 0, TEST_BUSINESS_CODE);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    /**
     * 测试查询未读消息数量大于0的场景
     */
    @Test
    public void testFindCrcUnreadMessage_PositiveCount() {
        // Setup
        final CommonResult<Integer> expectedResult = CommonResult.success(5);
        when(mockCrcMessageRepository.findCrcMsgByJlrIdAndStatus(TEST_JLR_ID, 0, TEST_BUSINESS_CODE)).thenReturn(5L);

        // Run the test
        final CommonResult<Integer> result = crcUnreadMessageServiceImplUnderTest.findCrcUnreadMessage(
                TEST_JLR_ID, 0, TEST_BUSINESS_CODE);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    /**
     * 测试标记所有消息为已读成功场景
     */
    @Test
    public void testReadCrcAllMessage_Success() {
        // Setup
        final ReadCrcMessageDTO readCrcMsgDTO = ReadCrcMessageDTO.builder()
                .jlrid(TEST_JLR_ID)
                .businessCode(TEST_BUSINESS_CODE)
                .build();
        final CommonResult<String> expectedResult = CommonResult.success("success");
        when(mockCrcMessageRepository.updateCrcAllMsgStatusByJlrId(TEST_JLR_ID, TEST_BUSINESS_CODE)).thenReturn(true);

        // Run the test
        final CommonResult<String> result = crcUnreadMessageServiceImplUnderTest.readCrcAllMessage(readCrcMsgDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    /**
     * 测试标记所有消息为已读失败场景
     */
    @Test
    public void testReadCrcAllMessage_Failure() {
        // Setup
        final ReadCrcMessageDTO readCrcMsgDTO = ReadCrcMessageDTO.builder()
                .jlrid(TEST_JLR_ID)
                .businessCode(TEST_BUSINESS_CODE)
                .build();
        final CommonResult<String> expectedResult = CommonResult.success("failed");
        when(mockCrcMessageRepository.updateCrcAllMsgStatusByJlrId(TEST_JLR_ID, TEST_BUSINESS_CODE)).thenReturn(false);

        // Run the test
        final CommonResult<String> result = crcUnreadMessageServiceImplUnderTest.readCrcAllMessage(readCrcMsgDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    /**
     * 构建测试用的SaveCrcUnreadMsgDTO对象
     */
    private SaveCrcUnreadMsgDTO buildSaveCrcUnreadMsgDTO() {
        return SaveCrcUnreadMsgDTO.builder()
                .jlrid(TEST_JLR_ID)
                .businessCode(TEST_BUSINESS_CODE)
                .messageId(TEST_MESSAGE_ID)
                .messageType(TEST_MESSAGE_TYPE)
                .messageContent(TEST_MESSAGE_CONTENT)
                .messageTime(TEST_MESSAGE_TIME)
                .build();
    }
}
