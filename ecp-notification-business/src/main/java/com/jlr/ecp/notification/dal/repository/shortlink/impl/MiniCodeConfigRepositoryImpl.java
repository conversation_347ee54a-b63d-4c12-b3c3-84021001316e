package com.jlr.ecp.notification.dal.repository.shortlink.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.notification.dal.dataobject.shortlink.MiniCodeConfigDO;
import com.jlr.ecp.notification.dal.dataobject.shortlink.ShortLinkCreateRecordDO;
import com.jlr.ecp.notification.dal.mysql.shortlink.MiniCodeConfigDOMapper;
import com.jlr.ecp.notification.dal.repository.shortlink.MiniCodeConfigRepository;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkCreateListDTO;
import com.jlr.ecp.notification.enums.SortEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class MiniCodeConfigRepositoryImpl extends ServiceImpl<MiniCodeConfigDOMapper, MiniCodeConfigDO> implements
        MiniCodeConfigRepository {
    @Resource
    private MiniCodeConfigDOMapper miniCodeConfigDOMapper;

    /**
     * 分页查询小程序配置列表，支持按创建时间排序。
     *
     * @param listDTO 分页查询条件参数，包含分页信息和排序方式
     * @return 分页结果对象，包含符合查询条件的小程序配置数据
     */
    @Override
    public Page<MiniCodeConfigDO> pageList(ShortLinkCreateListDTO listDTO) {
        Page<MiniCodeConfigDO> page = new Page<>(listDTO.getPageNo(), listDTO.getPageSize());
        LambdaQueryWrapper<MiniCodeConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MiniCodeConfigDO::getIsDeleted, false);
        if (SortEnum.ASC.getSort().equals(listDTO.getOperatorTimeSort())) {
            queryWrapper.orderByAsc(MiniCodeConfigDO::getCreatedTime);
        } else {
            queryWrapper.orderByDesc(MiniCodeConfigDO::getCreatedTime);
        }
        return miniCodeConfigDOMapper.selectPage(page, queryWrapper);
    }

    /**
     * 根据配置ID查询小程序配置信息
     *
     * @param configId 配置ID（字符串格式的数字）
     * @return 返回匹配的MiniCodeConfigDO对象，若未找到或参数无效则返回null
     */
    @Override
    public MiniCodeConfigDO selectByConfigId(String configId) {
        if (StringUtils.isBlank(configId)) {
            return null;
        }
        try {
            Long id = Long.valueOf(configId);
            LambdaQueryWrapper<MiniCodeConfigDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MiniCodeConfigDO::getId, id)
                    .eq(MiniCodeConfigDO::getIsDeleted, false);
            return getOne(queryWrapper);
        } catch (Exception e) {
            log.info("根据配置ID查询小程序配置信息异常:{}", e.getMessage());
        }
        return null;
    }
}
