package com.jlr.ecp.notification.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.jlr.ecp.notification.excel.pojo.PhoneNumber;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PhoneReadListener implements ReadListener<PhoneNumber> {
    private static final int BATCH_COUNT = 500;

    private List<PhoneNumber> dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private List<List<String>> phoneList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    Set<String> phoneSet = new HashSet<>(BATCH_COUNT);

    private static final String PHONE_START_1 = "+86";

    private static final String PHONE_START_2 = "86";
    private int cnt = 1;

    /**
     * 调用给定的VinAndPhoneNumber对象和AnalysisContext对象执行某些操作。
     * 该方法会根据传入的车辆识别号码（VIN）和电话号码进行特定的分析处理。
     *
     * @param phoneNumber 包含车辆识别号码和电话号码的对象，用于分析处理。
     * @param analysisContext 分析上下文，提供额外的上下文信息以支持分析过程。
     */
    @Override
    public void invoke(PhoneNumber phoneNumber, AnalysisContext analysisContext) {
        try {
            dataList.add(phoneNumber);
            if (dataList.size() >= BATCH_COUNT) {
                log.info("PhoneNUmber第：{}批次，读取数量：{}", cnt++, dataList.size());
                phoneList.add(getPhones(dataList));
                //重置空间
                dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            }
        } catch (Exception e) {
            log.error("解析电话号码异常：", e);
            //重置空间
            dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    /**
     * 在所有分析完成后执行的操作。
     * 这个方法是分析上下文生命周期的一部分，可以在整个分析过程结束后执行一些额外的逻辑。
     *
     * @param analysisContext 分析上下文，提供了关于当前分析会话的上下文信息，例如分析的文件列表等。
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("批量读取最后剩余的手机号码");
        try {
            //调用批量处理逻辑
            phoneList.add(getPhones(dataList));
            log.info("PhoneNUmber第：{}批次，读取数量：{}", cnt++, dataList.size());
        } catch (Exception e) {
            log.error("批量读取最后剩余的手机号码异常：", e);
        }  finally {
            dataList = null;
            phoneSet = null;
        }
        log.info("批量读取最后剩余的手机号码，所有数据解析完成！");
    }

    /**
     * 获取电话号码列表中的所有电话号码字符串。
     *
     * @param phoneNumbers 电话号码对象列表，每个对象包含一个电话号码。
     * @return 返回一个包含所有电话号码的字符串列表。
     */
    private List<String> getPhones(List<PhoneNumber> phoneNumbers) {
        List<String> phones = new ArrayList<>();
        for (PhoneNumber phoneNumber : phoneNumbers) {
            String phone = phoneNumber.getPhoneNumber();
            if (phone.startsWith(PHONE_START_1)) {
                phone = phone.substring(3);
            } else if (phone.startsWith(PHONE_START_2)) {
                phone = phone.substring(2);
            }
            if (!phoneSet.contains(phone)) {
                phones.add(phone);
                phoneSet.add(phone);
            }
        }
        return phones;
    }
}
