package com.jlr.ecp.notification.dal.dataobject.shortlink;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_notification_config")
public class NotificationConfigDO extends BaseDO {
    @TableId
    private Long id;

    /**
     *  remoteService短链商品SPU
     * */
    @TableField(value = "remote_service_spu")
    private String remoteServiceSpu;

    /**
     *  remoteService短链商品SPU
     * */
    @TableField(value = "remote_service_spu_name")
    private String remoteServiceSpuName;

    /**
     *  PIVIService短链商品SPU
     * */
    @TableField(value = "pivi_service_spu")
    private String piviServiceSpu;

    /**
     *  PIVIService短链商品SPU
     * */
    @TableField(value = "pivi_service_spu_name")
    private String piviServiceSpuName;

    /**
     * 品牌code
     * */
    @TableField("brand_code")
    private String brandCode;

    /**
     * 租户号
     * */
    @TableField(value = "tenant_id")
    private Integer tenantId;
}
