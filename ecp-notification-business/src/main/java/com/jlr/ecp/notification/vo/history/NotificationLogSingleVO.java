package com.jlr.ecp.notification.vo.history;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@Schema(description = "通知日志-个案查询返回值")
public class NotificationLogSingleVO {
    @Schema(description = "任务批次编号")
    private String taskInstanceCode;

    @Schema(description = "任务名称")
    private String taskName;

    @Schema(description = "发送类型")
    private String sendType;

    @Schema(description = "发送内容")
    private String sendContent;

    @Schema(description = "发送时间")
    private String sendTime;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "vin")
    private String carVin;

    @Schema(description = "品牌")
    private String brand;

    @Schema(description = "配置名称")
    private String configName;

    @Schema(description = "发送状态")
    private String sendStatus;

    @Schema(description = "失败阶段")
    private String failStage;

    @Schema(description = "失败原因")
    private String failReasons;

    @Schema(description = "是否打开短链，0否 1是")
    private Integer openShortLink;
}
