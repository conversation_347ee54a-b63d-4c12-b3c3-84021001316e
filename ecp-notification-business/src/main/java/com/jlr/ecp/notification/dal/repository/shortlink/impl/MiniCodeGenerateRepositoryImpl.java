package com.jlr.ecp.notification.dal.repository.shortlink.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.notification.dal.dataobject.shortlink.MiniCodeGenerateRecordDO;
import com.jlr.ecp.notification.dal.mysql.shortlink.MiniCodeGenerateRecordDOMapper;
import com.jlr.ecp.notification.dal.repository.shortlink.MiniCodeGenerateRepository;
import org.springframework.stereotype.Component;

@Component
public class MiniCodeGenerateRepositoryImpl extends ServiceImpl<MiniCodeGenerateRecordDOMapper, MiniCodeGenerateRecordDO>
        implements MiniCodeGenerateRepository {

}
