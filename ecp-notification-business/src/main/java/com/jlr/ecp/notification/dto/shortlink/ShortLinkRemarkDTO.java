package com.jlr.ecp.notification.dto.shortlink;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Schema(description = "短链中备注的DTO")
@Data
public class ShortLinkRemarkDTO {
    @Schema(description = "configId")
    @NotNull(message = "configId不能为空")
    private Long configId;

    @Schema(description = "备注信息")
    @Size(min = 0, max = 20, message = "备注信息长度必须在0到20字符之间")
    private String remarks;
}
