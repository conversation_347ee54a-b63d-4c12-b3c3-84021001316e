package com.jlr.ecp.notification.excel.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

import static com.jlr.ecp.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@Schema(description = "上传excel返回的vo")
public class UploadExcelRespVO {

    @Schema(description = "文件上传的地址")
    private String uploadExcelPath;

    @Schema(description = "错误详情地址")
    private String errorDetailPath;

    @Schema(description = "文件名")
    private String fileName;

    @Schema(description = "校验状态 true:校验完成")
    private Boolean checkStatus;

    @Schema(description = "校验结果 true:通过 false:未通过")
    private Boolean checkResult;
}
