package com.jlr.ecp.notification.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.common.util.json.JsonUtils;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.notification.config.RedisService;
import com.jlr.ecp.notification.constant.Constants;
import com.jlr.ecp.notification.dal.dataobject.task.auto.NotificationAutoTaskDO;
import com.jlr.ecp.notification.dal.dataobject.template.MessageTemplate;
import com.jlr.ecp.notification.dal.dataobject.template.TemplateModifyLog;
import com.jlr.ecp.notification.dal.repository.MessageTemplateRepository;
import com.jlr.ecp.notification.dal.repository.NotificationAutoTaskRepository;
import com.jlr.ecp.notification.dal.repository.TemplateModifyLogRepository;
import com.jlr.ecp.notification.dto.template.AutoTemplateDTO;
import com.jlr.ecp.notification.enums.ErrorCodeConstants;
import com.jlr.ecp.notification.enums.IsDeleteEnum;
import com.jlr.ecp.notification.enums.SortEnum;
import com.jlr.ecp.notification.enums.task.ManualNotificationTaskFieldEnum;
import com.jlr.ecp.notification.enums.template.*;
import com.jlr.ecp.notification.req.template.SmsTemplatePageReq;
import com.jlr.ecp.notification.req.template.TemplateHistoryPageReq;
import com.jlr.ecp.notification.service.SmsTemplateService;
import com.jlr.ecp.notification.req.template.SmsTemplateReq;
import com.jlr.ecp.notification.util.CommonUtil;
import com.jlr.ecp.notification.util.OperatorUtil;
import com.jlr.ecp.notification.util.sms.TemplateUtil;
import com.jlr.ecp.notification.util.sms.TimeFormatUtil;
import com.jlr.ecp.notification.vo.template.AutoTemplateVariableVO;
import com.jlr.ecp.notification.vo.template.SmsTemplateDetailVo;
import com.jlr.ecp.notification.vo.template.SmsTemplateHistoryVo;
import com.jlr.ecp.notification.vo.template.SmsTemplateVo;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 *  sms模板管理实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SmsTemplateServiceImpl implements SmsTemplateService {

    @Resource
    private MessageTemplateRepository templateRepository;

    @Resource
    private TemplateModifyLogRepository templateModifyLogRepository;

    @Resource
    private RedisService redisService;

    @Resource
    private NotificationAutoTaskRepository taskRepository;

    @Resource
    private Snowflake snowflake;

    /**
     *  模板编辑
     * @param smsTemplateReq 模板编辑入参
     * @return CommonResult<Boolean>
     * */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean>smsTemplateModify(SmsTemplateReq smsTemplateReq) {
        CommonResult<Boolean> checkArg = checkSmsTemplateReq(smsTemplateReq);
        if (!checkArg.isSuccess()) {
            return checkArg;
        }
        MessageTemplate template = templateRepository.getMessageTemplateByCode(smsTemplateReq.getTemplateCode());
        if (Objects.isNull(template)) {
            return CommonResult.error(ErrorCodeConstants.TEMPLATE_NO_EXIST);
        }
        // 复制template 为originalMessageTemplateFromDB
        MessageTemplate originalMessageTemplateFromDB = new MessageTemplate();
        BeanUtils.copyProperties(template, originalMessageTemplateFromDB);
        log.info("自动模版编辑smsTemplateModify, originalMessageTemplateFromDB:{}", JSON.toJSONString(originalMessageTemplateFromDB));

        List<MessageTemplate> messageTemplateList = templateRepository
                .getTemplateByTypeName(smsTemplateReq.getTemplateName(), smsTemplateReq.getBusinessCode(),
                        SmsTemplateTypeEnum.AUTO.getCode());
        if (CollUtil.isNotEmpty(messageTemplateList)) {
            for (MessageTemplate messageTemplate : messageTemplateList) {
                if (!smsTemplateReq.getTemplateCode().equals(messageTemplate.getTemplateCode())) {
                    return CommonResult.error(ErrorCodeConstants.TEMPLATE_NAME_REPEATABLE);
                }
            }
        }
        if (!checkTemplateVariable(template.getTemplateVariables(), smsTemplateReq.getTemplateContent())) {
            return CommonResult.error(ErrorCodeConstants.TEMPLATE_VAR_ERROR);
        }
        modifyTemplate(template, smsTemplateReq);
        addTemplateModifyLog(originalMessageTemplateFromDB, smsTemplateReq);
        return CommonResult.success(true);
    }

    /**
     *  校验模板的参数
     * @param templateReq 模板入参
     * @return CommonResult<Boolean>
     * */
    public CommonResult<Boolean> checkSmsTemplateReq(SmsTemplateReq templateReq) {
        if (Objects.isNull(templateReq)) {
            return CommonResult.error(ErrorCodeConstants.TEMPLATE_REQ_NULL);
        }
        if (StringUtils.isBlank(templateReq.getTemplateCode())) {
            return CommonResult.error(ErrorCodeConstants.TEMPLATE_CODE_NULL);
        }
        if (StringUtils.isBlank(templateReq.getTemplateContent())) {
            return CommonResult.error(ErrorCodeConstants.TEMPLATE_CONTENT_NULL);
        }
        if (StringUtils.isBlank(templateReq.getTemplateName())) {
            return CommonResult.error(ErrorCodeConstants.TEMPLATE_REQ_NULL);
        }
        if (StringUtils.isBlank(templateReq.getTemplateRemarks())) {
            return CommonResult.error(ErrorCodeConstants.TEMPLATE_REMARKS_NULL);
        }
        if (templateReq.getTemplateContent().length() >= 900) {
            return CommonResult.error(ErrorCodeConstants.TEMPLATE_CONTENT_OUT_LIMIT);
        }
        if (templateReq.getTemplateRemarks().length() >= 900) {
            return CommonResult.error(ErrorCodeConstants.TEMPLATE_REMARK_OUT_LIMIT);
        }
        return CommonResult.success(true);
    }

    /**
     *  修改模板
     * @param messageTemplate 当前模板数据
     * @param smsTemplateReq  模板请求入参
     * */
    public void modifyTemplate(MessageTemplate messageTemplate, SmsTemplateReq smsTemplateReq) {
        if (Objects.isNull(messageTemplate) || Objects.isNull(smsTemplateReq)) {
            log.info("编辑模板，当前模板数据或模板编辑入参为空");
            return ;
        }
        if (StringUtils.isNotBlank(smsTemplateReq.getTemplateContent())) {
            messageTemplate.setTemplateContent(smsTemplateReq.getTemplateContent());
        }
        if (StringUtils.isNotBlank(smsTemplateReq.getTemplateRemarks())) {
            messageTemplate.setTemplateRemarks(smsTemplateReq.getTemplateRemarks());
        }
        if (StringUtils.isNotBlank(smsTemplateReq.getTemplateName())) {
            messageTemplate.setTemplateName(smsTemplateReq.getTemplateName());
        }
        messageTemplate.setUpdatedBy(OperatorUtil.getOperator());
        messageTemplate.setUpdatedTime(LocalDateTime.now());
        templateRepository.updateById(messageTemplate);
    }

    /**
     * 添加模板编辑日志
     * @param messageTemplate 当前模板数据
     * @param smsTemplateReq 模板编辑入参
     * */
    public void addTemplateModifyLog(MessageTemplate messageTemplate, SmsTemplateReq smsTemplateReq) {
        log.info("添加模板编辑日志, 当前模板数据messageTemplate:{}, 模板编辑入参smsTemplateReq:{}",
                JSON.toJSONString(messageTemplate), JSON.toJSONString(smsTemplateReq));

        if (Objects.isNull(smsTemplateReq)) {
            log.info("添加模板编辑日志，模板编辑入参为空，无需添加日志");
            return;
        }

        // 初始化模块日志收集器
        Map<String, ModuleLogInfo> moduleLogs = new HashMap<>();

        // 检查模板名称修改
        compareAndLogField(
                messageTemplate.getTemplateName(),
                smsTemplateReq.getTemplateName(),
                NotificationTemplateFieldEnum.EDIT_TEMPLATE_NAME,
                moduleLogs
        );

        // 检查通知内容修改
        compareAndLogField(
                messageTemplate.getTemplateContent(),
                smsTemplateReq.getTemplateContent(),
                NotificationTemplateFieldEnum.EDIT_NOTIFY_CONTENT,
                moduleLogs
        );

        // 检查场景说明修改
        compareAndLogField(
                messageTemplate.getTemplateRemarks(),
                smsTemplateReq.getTemplateRemarks(),
                NotificationTemplateFieldEnum.EDIT_SCENE_DESCRIPTION,
                moduleLogs
        );

        // 生成并插入日志记录
        moduleLogs.forEach((moduleName, logInfo) -> {
            TemplateModifyLog log = TemplateModifyLog.builder()
                    .templateCode(messageTemplate.getTemplateCode())
                    .modifyModule(moduleName)
                    .modifyFieldCount(logInfo.getFieldCount())
                    .modifyFieldOldValue(JSON.toJSONString(logInfo.getOldValues()))
                    .modifyFieldNewValue(JSON.toJSONString(logInfo.getNewValues()))
                    .operateUser(OperatorUtil.getOperator())
                    .operateTime(LocalDateTime.now())
//                    .tenantId(messageTemplate.getTenantId())
                    .build();

            templateModifyLogRepository.insert(log);
        });
    }

    // 辅助方法：比较字段并记录变更
    private <T> void compareAndLogField(T oldValue, T newValue,
                                        NotificationTemplateFieldEnum fieldEnum,
                                        Map<String, ModuleLogInfo> moduleLogs) {
        if (!Objects.equals(oldValue, newValue)) {
            String moduleName = fieldEnum.getModuleName();
            moduleLogs.computeIfAbsent(moduleName, k -> new ModuleLogInfo())
                    .addField(fieldEnum.getFieldName(), oldValue, newValue);
        }
    }

    // 辅助类：模块日志信息收集器
    private static class ModuleLogInfo {
        private final Map<String, Object> oldValues = new HashMap<>();
        private final Map<String, Object> newValues = new HashMap<>();
        private int fieldCount = 0;

        public void addField(String fieldName, Object oldValue, Object newValue) {
            oldValues.put(fieldName, oldValue != null ? oldValue : "");
            newValues.put(fieldName, newValue != null ? newValue : "");
            fieldCount++;
        }

        public Map<String, Object> getOldValues() {
            return oldValues;
        }

        public Map<String, Object> getNewValues() {
            return newValues;
        }

        public int getFieldCount() {
            return fieldCount;
        }
    }

    /**
     *  按照typCode返回模板操作内容
     * @param typeCode 模板修改类型code
     * @return String
     * */
    public String getTemplateModifyContent(Integer typeCode) {
        TemplateModifyTypeEnum typeEnum = TemplateModifyTypeEnum.getTemplateModifyTypeEnumByCode(typeCode);
        if (Objects.isNull(typeEnum)) {
            log.info("按照code获取模板修改枚举为空，typeCode：{}", typeCode);
            return "";
        }
        return typeEnum.getDesc();
    }

    /**
     * 模板编辑详情页
     * @param templateCode 模板code
     * @return CommonResult<SmsTemplateDetailVo>
     * */
    @Override
    public CommonResult<SmsTemplateDetailVo> getSmsTemplateDetail(String templateCode) {
        MessageTemplate messageTemplate = templateRepository.getMessageTemplateByCode(templateCode);
        if (Objects.isNull(messageTemplate)) {
            return CommonResult.error(ErrorCodeConstants.TEMPLATE_NO_EXIST);
        }
        SmsTemplateDetailVo smsTemplateDetailVo = buildSmsTemplateDetailVo(messageTemplate);
        return CommonResult.success(smsTemplateDetailVo);
    }

    /**
     *  模板编辑历史记录
     * @param historyPageReq 历史记录入参
     * @return CommonResult<PageResult<SmsTemplateHistoryVo>>
     * */
    @Override
    public PageResult<SmsTemplateHistoryVo> getTemplateHistory(TemplateHistoryPageReq historyPageReq) {
        Page<TemplateModifyLog> templateModifyLogPage = templateModifyLogRepository.queryTemplateModifyLogPage(historyPageReq);
        if (Objects.isNull(templateModifyLogPage)) {
            log.info("模板编辑历史记录为空, historyPageReq:{}", historyPageReq);
            return null;
        }
        List<TemplateModifyLog> templateModifyLogs = templateModifyLogPage.getRecords();
        List<SmsTemplateHistoryVo> historyVos = buildTemplateHistoryVoList(templateModifyLogs);
        return new PageResult<>(historyVos, templateModifyLogPage.getTotal());
    }

    /**
     * 批量构建SmsTemplateHistoryVo
     * @param modifyLogs 模板编辑记录
     * @return List<SmsTemplateHistoryVo>
     * */
    public List<SmsTemplateHistoryVo> buildTemplateHistoryVoList(List<TemplateModifyLog> modifyLogs) {
        List<SmsTemplateHistoryVo> historyVos = new ArrayList<>();
        for (TemplateModifyLog modifyLog : modifyLogs) {
            historyVos.add(buildTemplateHistoryVo(modifyLog));
        }
        return historyVos;
    }

    /**
     * 构建SmsTemplateHistoryVo
     * @param modifyLog 模板编辑记录
     * @return List<SmsTemplateHistoryVo>
     * */
    public SmsTemplateHistoryVo buildTemplateHistoryVo(TemplateModifyLog modifyLog) {
        if (Objects.isNull(modifyLog)) {
            log.info("构建SmsTemplateHistoryVo, 模板编辑历史记录为空");
            return SmsTemplateHistoryVo.builder().build();
        }
        SmsTemplateHistoryVo vo = new SmsTemplateHistoryVo();
        BeanUtils.copyProperties(modifyLog, vo);

        // 获取所有需要显示详情的字段
        Set<String> allDetailFields = NotificationTemplateFieldEnum.getAllDetailFields();

        // set setModifyField
        String oldValue = modifyLog.getModifyFieldOldValue();
        if(StrUtil.isNotBlank(oldValue)){
            Set<String> oldSet = JSON.parseObject(oldValue, Feature.OrderedField).keySet();
            vo.setModifyField(String.join("、", oldSet));
            // set modifyDetail 是否显示 修改详情
            boolean modifyDetail = oldSet.stream().anyMatch(allDetailFields::contains);
            vo.setModifyDetail(modifyDetail);
        }

        return vo;
    }

    /**
     * 获取编辑日志，模板内容的操作
     * @param templateCode 模板code
     * @return String
     * */
    public String acquireModifyContent(String templateCode) {
        MessageTemplate messageTemplate = templateRepository.getMessageTemplateByCode(templateCode);
        if (Objects.isNull(messageTemplate)) {
            log.info("获取编辑日志，模板内容的操作, 查询模板为空, templateCode:{}", templateCode);
            return "";
        }
        return getTemplateModifyContent(messageTemplate.getTemplateType());
    }


    /**
     * 构建编辑模板的详情页
     * @param messageTemplate 模板实体数据
     * @return SmsTemplateDetailVo
     * */
    public SmsTemplateDetailVo buildSmsTemplateDetailVo(MessageTemplate messageTemplate) {
        if (Objects.isNull(messageTemplate)) {
            log.warn("构建编辑模板的详情，模板内容为空");
            return null;
        }
        return SmsTemplateDetailVo.builder()
                .templateCode(messageTemplate.getTemplateCode())
                .templateName(messageTemplate.getTemplateName())
                .businessCode(messageTemplate.getBusinessCode())
                .businessName(CommonUtil.getBusinessNameFromRedis(redisService, Constants.BUSINESS_CACHE_KEY,
                        messageTemplate.getBusinessCode()))
                .templateType(getTemplateVoType(messageTemplate.getTemplateType()))
                .templateContent(messageTemplate.getTemplateContent())
                .templateRemarks(messageTemplate.getTemplateRemarks())
                .templateVariables(messageTemplate.getTemplateVariables())
                .build();
    }

    /**
     *  模板列表页
     * @param smsTemplatePageReq 模板列表页请入参
     * @return PageResult<SmsTemplateVo>
     * */
    @Override
    public PageResult<SmsTemplateVo> getTemplateList(SmsTemplatePageReq smsTemplatePageReq) {
        Page<MessageTemplate> pages = queryTemplatePage(smsTemplatePageReq);
        List<MessageTemplate> messageTemplates = pages.getRecords();
        List<SmsTemplateVo> smsTemplateVos = buildSmsTemplateVoList(messageTemplates);
        return new PageResult<>(smsTemplateVos, pages.getTotal());
    }

    /**
     * 查询自动模板变量列表
     *
     * @return 返回一个包含自动模板变量信息的列表
     */
    @Override
    public List<AutoTemplateVariableVO> queryAutoTemplateVariableList(String businessCode) {
        List<AutoTemplateVariableVO> resp = new ArrayList<>();

        if (BusinessIdEnum.LRE.getCode().equals(businessCode)) {
            // 当 businessCode 是 LRE 返回 BG_ORDER_NUMBER WX_URL
            resp.add(buildTemplateVariableVO(AutoTemplateVariableEnum.BG_ORDER_NUMBER));
            resp.add(buildTemplateVariableVO(AutoTemplateVariableEnum.WX_URL));
        } else if (BusinessIdEnum.BRAND_GOODS.getCode().equals(businessCode)) {
            // 当 businessCode 是 BrandedGoods 返回 BG_ORDER_NUMBER PACKAGE_ORDER WX_URL
            resp.add(buildTemplateVariableVO(AutoTemplateVariableEnum.BG_ORDER_NUMBER));
            resp.add(buildTemplateVariableVO(AutoTemplateVariableEnum.LOGISTICS_NUMBER));
            resp.add(buildTemplateVariableVO(AutoTemplateVariableEnum.WX_URL));
        } else if (BusinessIdEnum.VCS.getCode().equals(businessCode)) {
            // 当 businessCode 是 VCS 返回 BRAND
            resp.add(buildTemplateVariableVO(AutoTemplateVariableEnum.BRAND));
            resp.add(buildTemplateVariableVO(AutoTemplateVariableEnum.SERVICE_NAME));
            resp.add(buildTemplateVariableVO(AutoTemplateVariableEnum.EXPIRE_DATE));
            resp.add(buildTemplateVariableVO(AutoTemplateVariableEnum.WX_URL));
        } else {
            // 对于其他业务代码，返回所有模板变量
            for (AutoTemplateVariableEnum variableEnum : AutoTemplateVariableEnum.values()) {
                resp.add(buildTemplateVariableVO(variableEnum));
            }
        }
        log.info("查询自动模板变量列表, businessCode:{}, resp:{}", businessCode, resp);
        return resp;
    }

    /**
     * 构建模板变量VO对象
     * 
     * @param variableEnum 模板变量枚举
     * @return 构建好的AutoTemplateVariableVO对象
     */
    private AutoTemplateVariableVO buildTemplateVariableVO(AutoTemplateVariableEnum variableEnum) {
        return AutoTemplateVariableVO.builder()
                .variableDesc(variableEnum.getDesc())
                .variableName(variableEnum.getName())
                .build();
    }

    /**
     * 添加短信自动模板
     *
     * @param autoTemplateDTO 自动模板DTO，包含模板的必要信息
     * @return CommonResult<Boolean> 添加模板的结果，包括成功或错误信息
     */
    @Override
    public CommonResult<Boolean> addSmsAutoTemplate(AutoTemplateDTO autoTemplateDTO) {
        List<MessageTemplate> messageTemplateList = templateRepository
                .getTemplateByTypeName(autoTemplateDTO.getTemplateName(), autoTemplateDTO.getBusinessCode(),
                        SmsTemplateTypeEnum.AUTO.getCode());
        if (CollUtil.isNotEmpty(messageTemplateList)) {
            return CommonResult.error(ErrorCodeConstants.TEMPLATE_NAME_REPEATABLE);
        }
        MessageTemplate messageTemplate = buildMessageAutoTemplate(autoTemplateDTO);
        Boolean resp = templateRepository.addMessageTemplate(messageTemplate);
        if (resp) {
            return CommonResult.success(true);
        }
        return CommonResult.success(false);
    }

    /**
     * 删除自动模板
     *
     * @param templateCode 模板code
     * @return CommonResult<Boolean>
     * */
    @Override
    public CommonResult<Boolean> deleteAutoTemplate(String templateCode) {
        if (org.apache.commons.lang3.StringUtils.isBlank(templateCode)) {
            return CommonResult.error(ErrorCodeConstants.TEMPLATE_CODE_NULL);
        }
        MessageTemplate template = templateRepository.getMessageTemplateByCode(templateCode);
        if (Objects.isNull(template)) {
            return CommonResult.error(ErrorCodeConstants.TEMPLATE_NO_EXIST);
        }
        List<NotificationAutoTaskDO> notificationTaskList = taskRepository.queryAutoTaskByTemplateCode(templateCode);
        if (!CollectionUtils.isEmpty(notificationTaskList)) {
            return CommonResult.error(ErrorCodeConstants.TEMPLATE_DELETE_ERROR);
        }
        Boolean resp = templateRepository.deleteTemplateByCode(templateCode);
        return CommonResult.success(resp);
    }

    /**
     * 根据模板类型查询短信模板
     *
     * @param templateType 模板类型，用于筛选短信模板的类型
     * @param autoType 自动类型，用于筛选短信模板的自动发送类型
     * @param businessCode 业务编码
     * @return 返回一个CommonResult对象，其中包含查询到的SmsTemplateVo对象列表
     */
    @Override
    public CommonResult<List<SmsTemplateVo>> queryTemplateByType(Integer templateType, Integer autoType, String businessCode) {
        log.info("根据模板类型查询短信模板, templateType:{}, autoType:{}", templateType, autoType);
        List<MessageTemplate> messageTemplateList = templateRepository.getTemplateListByType(templateType, autoType, businessCode);
        if (CollectionUtils.isEmpty(messageTemplateList)) {
            return CommonResult.success(null);
        }
        List<SmsTemplateVo>  smsTemplateVoList = buildSmsTemplateVoList(messageTemplateList);
        return CommonResult.success(smsTemplateVoList);
    }


    /**
     * 根据自动模板DTO构建消息模板
     *
     * @param autoTemplateDTO 自动模板DTO，包含创建消息模板所需的信息
     * @return 返回构建完成的消息模板对象，如果输入参数为空，则返回null
     */
    public MessageTemplate buildMessageAutoTemplate(AutoTemplateDTO autoTemplateDTO) {
        if (Objects.isNull(autoTemplateDTO)) {
            return null;
        }
        MessageTemplate messageTemplate = MessageTemplate.builder()
                .businessCode(autoTemplateDTO.getBusinessCode())
                .templateName(autoTemplateDTO.getTemplateName())
                .templateCode(snowflake.nextIdStr())
                .templateType(autoTemplateDTO.getTemplateType())
                .autoType(AutoTemplateTimeType.AUTO_CONFIGURATION.getCode())
                .templateContent(autoTemplateDTO.getTemplateContent())
                .templateRemarks(autoTemplateDTO.getTemplateRemarks())
                .templateVariables(autoTemplateDTO.getTemplateVariables())
                .build();
        messageTemplate.setUpdatedTime(LocalDateTime.now());
        messageTemplate.setUpdatedBy(WebFrameworkUtils.getLoginUserName());
        return messageTemplate;
    }


    /**
     * 构建模板VO列表数据
     * @param messageTemplates 模板模板列表
     * @return List<SmsTemplateVo>
     * */
    public List<SmsTemplateVo> buildSmsTemplateVoList(List<MessageTemplate> messageTemplates) {
        List<SmsTemplateVo> resp = new ArrayList<>();
        if (CollectionUtils.isEmpty(messageTemplates)) {
            return resp;
        }
        for (MessageTemplate messageTemplate : messageTemplates) {
            resp.add(buildSmsTemplateVo(messageTemplate));
        }
        return resp;
    }

    /**
     *  构建短信模板vo
     * @param messageTemplate 短信模板数据
     * @return SmsTemplateVo
     **/
    public SmsTemplateVo buildSmsTemplateVo(MessageTemplate messageTemplate) {
        if (Objects.isNull(messageTemplate)) {
            return null;
        }
        return SmsTemplateVo.builder()
                .templateCode(messageTemplate.getTemplateCode())
                .businessCode(messageTemplate.getBusinessCode())
                .businessName(CommonUtil.getBusinessNameFromRedis(redisService, Constants.BUSINESS_CACHE_KEY,
                        messageTemplate.getBusinessCode()))
                .templateName(messageTemplate.getTemplateName())
                .templateType(getTemplateVoType(messageTemplate.getTemplateType()))
                .templateContent(messageTemplate.getTemplateContent())
                .templateVariables(messageTemplate.getTemplateVariables())
                .modifyTime(TimeFormatUtil.localDateToString(messageTemplate.getUpdatedTime()))
                .modifyBy(messageTemplate.getUpdatedBy())
                .build();
    }


    /**
     *  按照模板类型code返回模板类型描述
     * @param typeCode 模板类型code
     * @return String
     * */
    public String getTemplateVoType(Integer typeCode) {
        SmsTemplateTypeEnum typeEnum = SmsTemplateTypeEnum.getTemplateByCode(typeCode);
        if (Objects.isNull(typeEnum)) {
            log.error("短信模板类型不存在，smsTemplateTypeCode:{}", typeCode);
            return "";
        }
        return typeEnum.getDesc();
    }

    /**
     *  分页查询模板列表页
     * @param smsTemplatePageReq 模板分页查询参数
     * @return Page<MessageTemplate>
     * */
    public Page<MessageTemplate> queryTemplatePage(SmsTemplatePageReq smsTemplatePageReq) {
        Page<MessageTemplate> pageReq = new Page<>(smsTemplatePageReq.getPageNo(), smsTemplatePageReq.getPageSize());
        LambdaQueryWrapper<MessageTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(!CollectionUtils.isEmpty(smsTemplatePageReq.getBusinessCode()),
                        MessageTemplate::getBusinessCode, smsTemplatePageReq.getBusinessCode())
                .eq(Objects.nonNull(smsTemplatePageReq.getTemplateType()),MessageTemplate::getTemplateType,smsTemplatePageReq.getTemplateType())
                .eq(MessageTemplate::getIsDeleted, IsDeleteEnum.NO.getStatus());
        if (SortEnum.ASC.getSortType().equals(smsTemplatePageReq.getTimeSortType())) {
            wrapper.orderByAsc(MessageTemplate::getUpdatedTime);
            wrapper.orderByAsc(MessageTemplate::getId);
        } else if (SortEnum.DESC.getSortType().equals(smsTemplatePageReq.getTimeSortType())) {
            //默认倒叙排序
            wrapper.orderByDesc(MessageTemplate::getUpdatedTime);
            wrapper.orderByAsc(MessageTemplate::getId);
        }
        if (SortEnum.ASC.getSortType().equals(smsTemplatePageReq.getTemplateTypeSort())) {
            wrapper.orderByAsc(MessageTemplate::getTemplateType);
            wrapper.orderByAsc(MessageTemplate::getId);
        } else if (SortEnum.DESC.getSortType().equals(smsTemplatePageReq.getTemplateTypeSort())) {
            wrapper.orderByDesc(MessageTemplate::getTemplateType);
            wrapper.orderByAsc(MessageTemplate::getId);
        }
        return templateRepository.selectAutoTemplatePage(pageReq, wrapper);
    }

    /**
     *  校验输入模板的内容的变量
     * @param variable 模板中的变量
     * @param modifyContent 要修改的模板内容
     * @return boolean
     * */
    public boolean checkTemplateVariable(String variable, String modifyContent) {
        Map<String, String> templateVar = TemplateUtil.variablesToMap(variable);
        List<String> modifyVar = TemplateUtil.extractVariable(modifyContent);
        for (String var : modifyVar) {
            if (!templateVar.containsKey(var)) {
                return false;
            }
        }
        return true;
    }

}
