package com.jlr.ecp.notification.dal.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.notification.dal.dataobject.shortlink.ShortLinkCreateRecordDO;
import com.jlr.ecp.notification.dal.mysql.shortlink.ShortLinkCreateRecordDOMapper;
import com.jlr.ecp.notification.dal.repository.ShortLinkCreateRepository;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkCreateListDTO;
import com.jlr.ecp.notification.enums.SortEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class ShortLinkCreateRepositoryImpl extends ServiceImpl<ShortLinkCreateRecordDOMapper, ShortLinkCreateRecordDO>
        implements ShortLinkCreateRepository {

    @Resource
    private ShortLinkCreateRecordDOMapper shortLinkCreateMapper;

    /**
     * 根据条件分页查询短链创建记录列表
     *
     * @param listDTO 包含分页和排序信息的查询条件对象
     * @return 包含短链创建记录的分页对象
     */
    @Override
    public Page<ShortLinkCreateRecordDO> pageList(ShortLinkCreateListDTO listDTO) {
        Page<ShortLinkCreateRecordDO> page = new Page<>(listDTO.getPageNo(), listDTO.getPageSize());
        LambdaQueryWrapper<ShortLinkCreateRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShortLinkCreateRecordDO::getIsDeleted, false);
        if (SortEnum.ASC.getSort().equals(listDTO.getOperatorTimeSort())) {
            queryWrapper.orderByAsc(ShortLinkCreateRecordDO::getUpdatedTime);
        } else {
            queryWrapper.orderByDesc(ShortLinkCreateRecordDO::getUpdatedTime);
        }
        return shortLinkCreateMapper.selectPage(page, queryWrapper);
    }

    /**
     * 根据配置ID获取短链创建记录对象
     *
     * @param configId 配置ID，用于查询短链创建记录
     * @return ShortLinkCreateRecordDO 短链创建记录对象，如果不存在则返回null
     */
    @Override
    public ShortLinkCreateRecordDO getRecordDoByConfigId(Long configId) {
        log.info("根据配置ID获取短链创建记录对象, configId:{}", configId);
        LambdaQueryWrapper<ShortLinkCreateRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShortLinkCreateRecordDO::getConfigId, configId)
                .eq(ShortLinkCreateRecordDO::getIsDeleted, false);
        return getOne(queryWrapper);
    }
}
