package com.jlr.ecp.notification.enums.task;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 短信通知任务发送类型枚举
 *
 * <AUTHOR>
 * */

@Getter
@AllArgsConstructor
public enum TaskTypeEnum {
    AUTO_TASK(1, "自动通知任务"),

    MANUAL(2, "手动通知任务");

    /**
     *  任务code
     * */
    private final Integer type;

    /**
     * 类型描述
     * */
    private final String desc;

    /**
     *  根据任务类型code返回任务类型枚举
     * @param typeCode 任务类型code
     * @return TaskTypeEnum
     * */
    public static TaskTypeEnum taskTypeEnumByCode(Integer typeCode) {
        TaskTypeEnum[] taskTypeEnum = TaskTypeEnum.values();
        for (TaskTypeEnum typeEnum : taskTypeEnum) {
            if (typeEnum.type.equals(typeCode)) {
                return typeEnum;
            }
        }
        return null;
    }
}
