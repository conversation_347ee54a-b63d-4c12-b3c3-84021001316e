package com.jlr.ecp.notification.service.crc.impl;


import cn.hutool.core.lang.Snowflake;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.dal.dataobject.crc.CrcMessagesDO;
import com.jlr.ecp.notification.dal.repository.CrcUnreadMessageRepository;
import com.jlr.ecp.notification.dto.crc.ReadCrcMessageDTO;
import com.jlr.ecp.notification.dto.crc.SaveCrcUnreadMsgDTO;
import com.jlr.ecp.notification.enums.crc.CrcMessageStatusEnum;
import com.jlr.ecp.notification.service.crc.CrcUnreadMessageService;
import com.jlr.ecp.notification.vo.crc.SaveCrcUnreadMsgVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
@Slf4j
public class CrcUnreadMessageServiceImpl implements CrcUnreadMessageService {
    @Resource
    private Snowflake snowflake;

    @Resource
    private CrcUnreadMessageRepository crcMessageRepository;


    /**
     * 添加CRC未读消息
     *
     * @param saveCrcUnreadMsgDTO 包含未读消息信息的DTO对象
     * @return 返回一个通用结果对象，表示消息是否成功发送
     */
    @Override
    public CommonResult<SaveCrcUnreadMsgVO> addCrcUnreadMessage(SaveCrcUnreadMsgDTO saveCrcUnreadMsgDTO) {
        log.info("添加CRC未读消息, saveCrcUnreadMsgDTO:{}", saveCrcUnreadMsgDTO);
        CrcMessagesDO crcMessagesDO = buildCrcMessagesDO(saveCrcUnreadMsgDTO);
        String status = crcMessageRepository.saveCrcMessagesDO(crcMessagesDO) ? "success" : "failed";
        SaveCrcUnreadMsgVO saveCrcUnreadMsgVO = SaveCrcUnreadMsgVO.builder()
                .jlrid(saveCrcUnreadMsgDTO.getJlrid())
                .businessCode(saveCrcUnreadMsgDTO.getBusinessCode())
                .messageId(saveCrcUnreadMsgDTO.getMessageId())
                .status(status)
                .build();
        return CommonResult.success(saveCrcUnreadMsgVO);
    }


    /**
     * 查询crc未读消息的数量
     *
     * @param jilId 用于标识消息接收者的ID
     * @param messageStatus 消息的状态，用于过滤消息（如未读状态）
     * @return 返回一个CommonResult对象，其中包含未读消息的数量
     */
    @Override
    public CommonResult<Integer> findCrcUnreadMessage(String jilId, Integer messageStatus, String businessCode) {
        log.info("查询crc未读消息的数量, jilId:{}, messageStatus:{}", jilId, messageStatus);
        Long msgCount = crcMessageRepository.findCrcMsgByJlrIdAndStatus(jilId, messageStatus, businessCode);
        return CommonResult.success(msgCount.intValue());
    }

    /**
     * 阅读并更新CRC未读消息的状态
     *
     * @param readCrcMsgDTO 包含更新消息状态所需信息的DTO对象
     * @return 返回一个CommonResult对象，包含更新操作的结果信息
     */
    @Override
    public CommonResult<String> readCrcAllMessage(ReadCrcMessageDTO readCrcMsgDTO) {
        log.info("阅读crc未读消息, readCrcMsgDTO:{}", readCrcMsgDTO);
        String resp = crcMessageRepository.updateCrcAllMsgStatusByJlrId(readCrcMsgDTO.getJlrid(), readCrcMsgDTO.getBusinessCode()) ? "success" : "failed";
        return CommonResult.success(resp);
    }

    /**
     * 构建CrcMessagesDO对象
     *
     * @param saveCrcUnreadMsgDTO 保存未读消息的DTO对象，包含了创建DO对象所需的信息
     * @return 返回一个构建好的CrcMessagesDO对象，它包含了从DTO转换过来的字段值，
     *         以及一些在业务逻辑中指定的固定值，如消息状态为未读
     */
    private CrcMessagesDO buildCrcMessagesDO(SaveCrcUnreadMsgDTO saveCrcUnreadMsgDTO) {
        return CrcMessagesDO.builder()
                .messageId(snowflake.nextId())
                .crcMessageId(saveCrcUnreadMsgDTO.getMessageId())
                .businessCode(saveCrcUnreadMsgDTO.getBusinessCode())
                .jlrId(saveCrcUnreadMsgDTO.getJlrid())
                .messageType(Integer.parseInt(saveCrcUnreadMsgDTO.getMessageType()))
                .messageStatus(CrcMessageStatusEnum.UNREAD.getCode())
                .messageContent(saveCrcUnreadMsgDTO.getMessageContent())
                .messageTime(saveCrcUnreadMsgDTO.getMessageTime())
                .build();
    }
}
