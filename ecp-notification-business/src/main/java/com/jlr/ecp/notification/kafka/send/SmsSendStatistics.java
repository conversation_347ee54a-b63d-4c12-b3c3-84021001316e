package com.jlr.ecp.notification.kafka.send;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.notification.dal.dataobject.history.NotificationHistory;
import com.jlr.ecp.notification.dal.dataobject.history.SendHistoryDetail;
import com.jlr.ecp.notification.dal.dataobject.task.auto.NotificationAutoTaskDO;
import com.jlr.ecp.notification.dal.dataobject.valet.OrderNotificationDetailDO;
import com.jlr.ecp.notification.dal.mysql.history.HistoryDetailMapper;
import com.jlr.ecp.notification.dal.mysql.history.SendHistoryMapper;
import com.jlr.ecp.notification.dal.repository.NotificationAutoTaskRepository;
import com.jlr.ecp.notification.dal.repository.OrderNotificationDetailRepository;
import com.jlr.ecp.notification.enums.IsDeleteEnum;
import com.jlr.ecp.notification.enums.SmsResultErrorEnum;
import com.jlr.ecp.notification.enums.SmsSendResultEnum;
import com.jlr.ecp.notification.enums.task.InstanceCodeTypeEnum;
import com.jlr.ecp.notification.util.InstanceCodeGenerator;
import com.jlr.ecp.notification.util.sms.TimeFormatUtil;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 更新短信发送后的结果
 *
 * <AUTHOR>
 * */

@Component
@Slf4j
public class SmsSendStatistics {

    @Resource
    private HistoryDetailMapper historyDetailMapper;

    @Resource
    private SendHistoryMapper sendHistoryMapper;

    @Resource
    private InstanceCodeGenerator instanceCodeGenerator;

    @Resource
    private OrderNotificationDetailRepository orderDetailRepository;

    @Resource
    private NotificationAutoTaskRepository autoTaskRepository;

    /**
     * 单条短信发送前的准备工作
     * @param taskCode 任务code
     * @param singleSms 单条短信信息
     * @return SendHistoryDetail
     * */
    @Transactional(rollbackFor = Exception.class)
    public SendHistoryDetail sendBeforeWork(String taskCode, SingleSendMessage singleSms) {
        NotificationHistory notificationHistory = addNotificationHistory(taskCode);
        return addSingleSendHistory(notificationHistory, singleSms);
    }

    /**
     * 更新结果
     * @param detail 单条发送信息
     * @param sendSmsResp 发送结果
     * */
    @Transactional(rollbackFor = Exception.class)
    public void updateSendResult(SendHistoryDetail detail, SendSmsResp sendSmsResp) {
        if (Objects.isNull(detail) || Objects.isNull(sendSmsResp)) {
            return ;
        }
        NotificationHistory history = queryHistoryByCodeAndInstance(detail.getTaskCode(), detail.getTaskInstanceCode());
        if (Objects.isNull(history)) {
            history = addNotificationHistory(detail.getTaskCode());
            history.setTaskInstanceCode(detail.getTaskInstanceCode());
        }
        //更新单条信息发送的结果
        detail.setSendTime(LocalDateTime.now());
        if (SmsResultErrorEnum.SUCCESS.getCode().equals(sendSmsResp.getResult())) {
            detail.setSendResult(SmsSendResultEnum.SUCCESS.getCode());
            history.setSendSuccessCount(history.getSendSuccessCount() + 1);
        } else {
            detail.setSendResult(SmsSendResultEnum.FAIL.getCode());
            detail.setSubmitErrorCode(sendSmsResp.getResult());
            detail.setErrorMessage(sendSmsResp.getDesc());
            history.setSendFailCount(history.getSendFailCount() + 1);
        }
        history.setSendTotalCount(history.getSendTotalCount() + 1);
        detail.setUpdatedTime(LocalDateTime.now());
        history.setUpdatedTime(LocalDateTime.now());
        historyDetailMapper.updateById(detail);
        sendHistoryMapper.updateById(history);
    }

    /**
     * 添加代客下单通知详情
     *
     * @param singleSms 短信发送信息，包含发送短信的相关信息
     * @param brandCode 品牌代码，用于区分不同的品牌
     * @param wxUrl 微信链接，用于在通知中包含的微信相关链接
     * @return 返回插入后的OrderNotificationDetailDO对象，如果构建通知详情失败则返回null
     */
    public OrderNotificationDetailDO addOrderNotificationDetail(SingleSendMessage singleSms, String brandCode,
                                                                 String wxUrl) {
        OrderNotificationDetailDO detail = buiildOrderNotificationDetailDO(singleSms, brandCode, wxUrl);
        if (Objects.isNull(detail)) {
            return null;
        }
        orderDetailRepository.insert(detail);
        return detail;
    }

    /**
     * 构建订单通知详情对象
     *
     * @param singleSms 单次发送短信对象，包含短信发送的相关信息如果为null
     * @param brandCode 品牌代码，用于标识短信发送的品牌方
     * @param wxUrl 微信链接，用于订单通知中的附加信息
     * @return 返回构建的OrderNotificationDetailDO对象
     */
    private OrderNotificationDetailDO buiildOrderNotificationDetailDO(SingleSendMessage singleSms,
                                                                      String brandCode,
                                                                      String wxUrl) {
        if (Objects.isNull(singleSms)) {
            return null;
        }
        return OrderNotificationDetailDO.builder()
                .msgId(singleSms.getMsgid())
                .sendMessage(singleSms.getContent())
                .sendPhone(singleSms.getPhones())
                .carVin(singleSms.getCarVin())
                .sendTime(LocalDateTime.now())
                .sendResult(SmsSendResultEnum.WAITED.getCode())
                .brandCode(brandCode)
                .shortLink(wxUrl)
                .build();
    }

    /**
     * 更新代客下单发送结果
     *
     * @param detail 订单通知详情对象，包含订单的相关信息
     * @param sendSmsResp 短信发送响应对象，包含发送结果和描述
     */
    public void updateValetSendResult(OrderNotificationDetailDO detail, SendSmsResp sendSmsResp) {
        if (Objects.isNull(detail) || Objects.isNull(sendSmsResp)) {
            return ;
        }
        //更新单条信息发送的结果
        detail.setSendTime(LocalDateTime.now());
        if (SmsResultErrorEnum.SUCCESS.getCode().equals(sendSmsResp.getResult())) {
            detail.setSendResult(SmsSendResultEnum.SUCCESS.getCode());
        } else {
            detail.setSendResult(SmsSendResultEnum.FAIL.getCode());
            detail.setSubmitErrorCode(sendSmsResp.getResult());
            detail.setErrorMessage(sendSmsResp.getDesc());
        }
        detail.setUpdatedTime(LocalDateTime.now());
        orderDetailRepository.updateById(detail);
    }

    /**
     * 添加单条短信
     * @param history 一次发送记录
     * @param singleSms 单条下发的消息
     * @return SendHistoryDetail
     * */
    private SendHistoryDetail addSingleSendHistory(NotificationHistory history, SingleSendMessage singleSms) {
        SendHistoryDetail sendHistoryDetail = buildSingleSendDetail(history, singleSms);
        if (Objects.isNull(sendHistoryDetail)) {
            return null;
        }
        historyDetailMapper.insert(sendHistoryDetail);
        return sendHistoryDetail;
    }

    /**
     * 构建单条消息发送记录详情
     * @param history 任务一次的发送记录
     * @param message 消息
     * */
    private SendHistoryDetail buildSingleSendDetail(NotificationHistory history, SingleSendMessage message) {
        if (Objects.isNull(history)) {
            return null;
        }
        SendHistoryDetail historyDetail = new SendHistoryDetail();
        historyDetail.setBusinessCode(history.getBusinessCode());
        historyDetail.setTaskInstanceCode(history.getTaskInstanceCode());
        historyDetail.setMsgId(message.getMsgid());
        historyDetail.setSendMessage(message.getContent());
        historyDetail.setSendPhone(message.getPhones());
        historyDetail.setSendResult(SmsSendResultEnum.WAITED.getCode());
        historyDetail.setTaskCode(history.getTaskCode());
        historyDetail.setCarVin(message.getCarVin());
        return historyDetail;
    }

    /**
     *  添加任务一次总统计
     * @param taskCode 任务code
     * */
    private NotificationHistory addNotificationHistory(String taskCode) {
        NotificationHistory notificationHistory = buildNotificationHistory(taskCode);
        sendHistoryMapper.insert(notificationHistory);
        return notificationHistory;
    }

    /**
     * 构建历史发送数据
     * @param taskCode 发送任务Code
     * @return NotificationHistory
     * */
    private NotificationHistory buildNotificationHistory(String taskCode) {
        if (Objects.isNull(taskCode)) {
            log.info("构建历史发送数据, taskCod为空");
            return NotificationHistory.builder().build();
        }
        NotificationHistory history = NotificationHistory.builder()
                .taskCode(taskCode)
                .taskInstanceCode(instanceCodeGenerator.generateTaskBatchId(InstanceCodeTypeEnum.AUTO_REAL_TIME.getType()))
                .taskSendTime(TimeFormatUtil.changeTimeFormat(LocalDateTime.now()))
                .sendTotalCount(0)
                .sendSuccessCount(0)
                .sendFailCount(0)
                .reachUserSuccessCount(0)
                .reachUserFailCount(0)
                .reachResultStatus(0)
                .build();
        NotificationAutoTaskDO autoTaskDO = autoTaskRepository.queryAutoTaskByCode(taskCode);
        if (Objects.isNull(autoTaskDO)) {
            log.info("依据任务code获取任务为空, taskCode:{}", taskCode);
        } else {
            history.setBusinessCode(autoTaskDO.getBusinessCode());
        }
        return history;
    }


    /**
     * 按照任务id查询任务数据
     * @param taskCode 任务code
     * @param instanceId 实例Id
     * @return NotificationHistory
     * */
    private NotificationHistory queryHistoryByCodeAndInstance(String taskCode, String instanceId) {
        if (StringUtils.isBlank(taskCode) || StringUtils.isBlank(instanceId)) {
            return null;
        }
        LambdaQueryWrapper<NotificationHistory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NotificationHistory::getTaskCode, taskCode)
                .eq(NotificationHistory::getTaskInstanceCode, instanceId)
                .eq(NotificationHistory::getIsDeleted, IsDeleteEnum.NO.getStatus());
        List<NotificationHistory> taskList = sendHistoryMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(taskList)) {
            return null;
        }
        if (taskList.size() > 1) {
            log.info("按照任务id查询任务历史数据, 数据有多条:{}", taskCode);
        }
        return taskList.get(0);
    }
}
