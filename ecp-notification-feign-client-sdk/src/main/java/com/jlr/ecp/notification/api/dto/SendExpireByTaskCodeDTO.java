package com.jlr.ecp.notification.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SendExpireByTaskCodeDTO {

    /**
     *  任务code
     * */
    private String taskCode;

    /**
     *  到期短信记录id列表
     * */
    private List<Long> detailIdList;

    /**
     *  发送开始时间
     * */
    private LocalDateTime startSendTime;

    /**
     *  发送结束时间
     * */
    private LocalDateTime endSendTime;
}
