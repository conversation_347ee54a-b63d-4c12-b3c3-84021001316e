package com.jlr.ecp.notification.excel.service;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.dto.VinAndPhoneDTO;
import com.jlr.ecp.notification.excel.vo.UploadExcelRespVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface NotificationExcelService {

    /**
     *  获取excel模板地址
     * @param excelTemplateType 模板类型
     * @return String 模板地址
     * */
    String getTemplateUrl(Integer excelTemplateType);

    /**
     * 上传Excel文件。
     *
     * @param file 需要上传的Excel文件，使用MultipartFile类型，代表上传的文件。
     * @param excelTemplateType Excel模板类型，通过整数来区分不同的模板类型。
     * @return CommonResult<UploadExcelRespVO>
     */
    CommonResult<UploadExcelRespVO> uploadExcel(MultipartFile file, Integer excelTemplateType);

    /**
     * 从指定的Excel文件中获取电话号码列表。
     * @param uploadExcelPath 上传的Excel文件的路径
     *
     * @return 一个包含电话号码列表的二维字符串数组，每个子列表代表一行电话号码数据
     */
    List<List<String>> getPhoneList(String uploadExcelPath);

    /**
     * 从指定的Excel文件中获取电话号码列表。
     * @param uploadExcelPath 上传的Excel文件的路径
     * @return 一个包含电话号码列表的二维字符串数组，每个子列表代表一行电话号码数据
     */
    List<List<VinAndPhoneDTO>> getVinPhoneList(String uploadExcelPath);

    /**
     * 通过Excel文件获取电话号码列表。
     *
     * @param bytes Excel文件的字节数组。
     * @return 返回一个包含电话号码信息的列表，每个子列表代表一行数据，其中包含的字段根据excelType的不同而变化。
     */
    List<List<String>> getPhoneListByExcel(byte[] bytes);

    /**
     * 通过Excel文件获取电话号码列表。
     *
     * @param bytes Excel文件的字节数组。
     * @return 返回一个包含电话号码信息的列表，每个子列表代表一行数据，其中包含的字段根据excelType的不同而变化。
     */
    List<List<VinAndPhoneDTO>> getVinPhoneListByExcel(byte[] bytes);

    /**
     * 删除指定路径的Excel文件。
     *
     * @param filePath 要删除的文件路径。
     * @return 返回操作结果，成功返回true，失败返回false。
     */
    CommonResult<Boolean> deleteExcel(String filePath);

}
