package com.jlr.ecp.notification.service;

import com.jlr.ecp.notification.dto.UpdateSmsSendStatusDTO;
import com.jlr.ecp.notification.resp.ReportResp;
import com.jlr.ecp.notification.resp.UpdateSmsSendStatusResp;

import java.util.List;

public interface UpdateSmsUserReachService {


    /**
     * 更新短信发送状态。
     *
     * @param updateSmsSendStatusDTO 包含需要更新的短信发送状态信息的数据传输对象。
     * @return 返回更新短信发送状态的响应对象，包含更新结果等信息。
     */
    UpdateSmsSendStatusResp updateSmsSendStatus(UpdateSmsSendStatusDTO updateSmsSendStatusDTO);

    /**
     * 更新短信用户达到信息。
     * 该方法会遍历传入的报告响应列表，并更新相应的短信发送详情结果。
     *
     * @param reportRespList 包含报告响应信息的列表，每个报告响应对应一条短信的发送详情。
     *                       通过更新这个列表中的信息，可以更新数据库中相应的短信发送状态
     */
    void updateSmsUserReach(List<ReportResp> reportRespList);
}
