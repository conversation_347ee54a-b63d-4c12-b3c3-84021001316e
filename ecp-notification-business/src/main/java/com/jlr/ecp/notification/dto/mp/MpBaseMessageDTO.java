package com.jlr.ecp.notification.dto.mp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Data
@Schema(description = "小程序模板消息订阅DTO")
public class MpBaseMessageDTO {
    @JsonProperty("clientId")
    private String clientId;

    @JsonProperty("jlrId")
    private String jlrId;

    @JsonProperty("eventType")
    private String eventType;

    @JsonProperty("businessNo")
    private String businessNo;

    @JsonProperty("app")
    private String app;

    @JsonProperty("openId")
    private String openId;
}
