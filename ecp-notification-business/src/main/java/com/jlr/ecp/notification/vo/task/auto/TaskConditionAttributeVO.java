package com.jlr.ecp.notification.vo.task.auto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;


@Data
@Schema(description = "自动任务属性VO")
@Builder
public class TaskConditionAttributeVO {

    @Schema(description = "按钮名称")
    private String buttonName;

    @Schema(description = "按钮注释")
    private String buttonDesc;

    @Schema(description = "是否有属性值")
    private Boolean hasAttribute;

    @Schema(description = "请求名称")
    private String requestName;

    @Schema(description = "每个属性值")
    private List<ConditionAttributeVO> conditionAttributeVOList;



}
