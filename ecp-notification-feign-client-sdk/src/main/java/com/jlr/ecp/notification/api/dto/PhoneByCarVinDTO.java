package com.jlr.ecp.notification.api.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class PhoneByCarVinDTO {
    /**
     * 租户号
     */
    private Integer tenantId;

    /**
     * 订单编码;订单编码，编码规则：业务线code+时间戳(日期+时分秒)+6位随机数
     */
    private String orderCode;


    /**
     * 微信授权手机
     */
    private String wxPhone;


    /**
     * 客户留言信息;
     */
    private String customerRemark;

    /**
     * 客户联系手机;
     */
    private String contactPhone;


    /**
     * incontrol账号;
     */
    private String incontrolId;


    /**
     * 车辆VIN;车辆VIN
     */
    private String carVin;

    /**
     * 到期时间
     * */
    private LocalDateTime expireDate;

    /**
     * 产品code
     * */
    private String productCode;


    /**
     * 车辆型号;
     */
    private String seriesCode;

    /**
     * 车辆名称;
     */
    private String seriesName;

    /**
     * 品牌CODE; LALANDJAG
     */
    private String brandCode;

    /**
     * 品牌名;车型品牌
     */
    private String brandName;

    /**
     *  到期类型: @com.jlr.ecp.notification.enums.ExpireTypeEnum
     * */
    private Integer expireType;

    /**
     * 指定日期
     * */
    private LocalDate appointedDate;

    /**
     *  任务code
     * */
    private String taskCode;
}

