package com.jlr.ecp.notification.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
@Getter
@Slf4j
public enum BrandToUrlEum {
    LAN_PDP("LAN", "BS0001","路虎小程序","PDP","路虎", "商品详情页","ecp/pages/subscribeTo/shopDetails/shopDetails"),
    LAN_PLP("LAN", "BS0001","路虎小程序","PLP","路虎", "商品列表页", "ecp/pages/subscribeTo/subscribeTo"),
    JAG_PDP("JAG","BS0002","捷豹小程序" ,"PDP","捷豹", "商品详情页", "ecp/ecpJaguar/pages/subscribeTo/shopDetails/shopDetails"),
    JAG_PLP("JAG","BS0002", "捷豹小程序","PLP","捷豹", "商品列表页","ecp/ecpJaguar/pages/subscribeTo/subscribeTo");

    private final String brandCode;

    private final String brandId;

    private final String miniProgramName;

    private final String urlType;

    private final String brandName;

    private final String urlName;

    private final String brandToUrl;

    /**
     * 根据品牌代码获取对应的URL
     *
     * @param brandCode 品牌代码，用于查找对应的URL
     * @param urlType   URL类型，用于区分不同的URL
     * @return 如果找到对应的品牌代码，则返回相应的URL；否则返回空字符串
     */
    public static String getUrlByBrandCodeUrlType(String brandCode, String urlType) {
        log.info("根据品牌代码获取对应的URL, brandCode:{}, urlType:{}", brandCode, urlType);
        for (BrandToUrlEum brandTOUrlEum : BrandToUrlEum.values()) {
            if (brandTOUrlEum.getBrandCode().equals(brandCode) && brandTOUrlEum.getUrlType().equals(urlType)) {
                return brandTOUrlEum.brandToUrl;
            }
        }
        return "";
    }


    /**
     * 根据品牌代码获取路径名称
     *
     * @param brandCode 品牌代码，用于查找对应的路径名称
     * @return 对应的路径名称或空字符串（如果未找到匹配项）
     */
    public static List<BrandToUrlEum> getPathInfoByBrandCode(String brandCode) {
        log.info("根据品牌代码获取路径名称, brandCode:{}", brandCode);
        List<BrandToUrlEum> resp = new ArrayList<>();
        for (BrandToUrlEum brandToUrlEum : BrandToUrlEum.values()) {
            if (brandToUrlEum.getBrandCode().equals(brandCode)) {
                resp.add(brandToUrlEum);
            }
        }
        return resp;
    }

    /**
     * 根据品牌代码获取对应的BrandToUrlEum枚举对象
     *
     * @param brandCode 品牌代码，用于查找对应的枚举对象
     * @param urlType   URL类型，用于区分不同的URL
     * @return 匹配品牌代码的BrandToUrlEum枚举对象，如果找不到匹配项则返回null
     */
    public static BrandToUrlEum getBrandToUrlEumByCodeUrlType(String brandCode, String urlType) {
        log.info("根据品牌代码获取对应的BrandToUrlEum枚举对象, brandCode:{}, urlType:{}", brandCode, urlType);
        for (BrandToUrlEum brandToUrlEum : BrandToUrlEum.values()) {
            if (brandToUrlEum.getBrandCode().equals(brandCode) && brandToUrlEum.getUrlType().equals(urlType)) {
                return brandToUrlEum;
            }
        }
        return null;
    }

    /**
     * 根据品牌编码获取对应的小程序名称
     *
     * @param brandCode 品牌编码，用于匹配枚举中的品牌标识
     * @return 匹配到的品牌对应的小程序名称；若未找到匹配项则返回空字符串
     */
    public static String getMiniProgramNameByBrandCode(String brandCode) {
        log.info("根据品牌编码获取对应的小程序名称, brandCode:{}", brandCode);
        for (BrandToUrlEum brandTOUrlEum : BrandToUrlEum.values()) {
            if (brandTOUrlEum.getBrandCode().equals(brandCode)) {
                return brandTOUrlEum.miniProgramName;
            }
        }
        return "";
    }

    /**
     * 根据品牌编码获取对应的URL名称
     *
     * @param urlType  URL类型，用于匹配枚举中的URL标识
     * @return 匹配到的URL名称；若未找到匹配项则返回空字符串
     */
    public static String getUrlNameByUrlType(String urlType) {
        log.info("根据品牌编码获取对应的URL名称, urlType:{}", urlType);
        for (BrandToUrlEum brandTOUrlEum : BrandToUrlEum.values()) {
            if (brandTOUrlEum.getUrlType().equals(urlType)) {
                return brandTOUrlEum.urlName;
            }
        }
        return "";
    }
}
