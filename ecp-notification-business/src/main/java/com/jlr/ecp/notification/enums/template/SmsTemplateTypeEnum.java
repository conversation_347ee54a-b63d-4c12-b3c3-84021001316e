package com.jlr.ecp.notification.enums.template;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *  模板类型枚举
 *
 * @autor dehyu
 * */
@AllArgsConstructor
@Getter
public enum SmsTemplateTypeEnum {
    AUTO(1, "自动配置"),
    MANUAL(2, "手动配置");

    /**
     * 模板类型code
     * */
    private final Integer code;

    /**
     * 模板类型描述
     * */
    private final String desc;

    /**
     * 按照code返回模板枚举
     * @param  code 模板code
     * @return SmsTemplateTypeEnum
     * */
    public static SmsTemplateTypeEnum getTemplateByCode(Integer code) {
        SmsTemplateTypeEnum[] smsTemplateTypeEnums = SmsTemplateTypeEnum.values();
        for (SmsTemplateTypeEnum typeEnum : smsTemplateTypeEnums) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
