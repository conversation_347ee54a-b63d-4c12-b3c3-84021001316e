package com.jlr.ecp.notification.vo.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Schema(description = "模板管理 - 编辑详情和编辑历史")
@Builder
public class SmsTemplateDetailVo {
    @Schema(description = "业务线编码")
    private String businessCode;

    @Schema(description = "业务线名称")
    private String businessName;

    @Schema(description = "模板编码")
    private String templateCode;

    @Schema(description = "模板名称")
    private String templateName;

    /**
     * 模板类型, @see com.jlr.ecp.notification.enums.template.SmsTemplateTypeEnum
     * */
    @Schema(description = "模板类型")
    private String templateType;

    @Schema(description = "模板内容")
    private String templateContent;

    @Schema(description = "模板场景说明")
    private String templateRemarks;

    @Schema(description = "模板中的变量")
    private String templateVariables;

}
