package com.jlr.ecp.notification.enums.task;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务状态枚举
 *
 * <AUTHOR>
 * */

@Getter
@AllArgsConstructor
public enum TaskStatusEnum {
    STOP(0, "已停用"),
    START(1, "已启用"),
    TO_BE_ACTIVATED(2, "待启用"),
    FINISH_SEND(3, "已发送");

    /**
     * 任务状态码
     * */
    private final Integer status;

    /**
     * 任务状态描述
     * */
    private final String desc;

    /**
     * 按照状态code返回任务通知枚举
     * @param  status 状态吗
     * @return TaskStatusEnum
     * */
    public static TaskStatusEnum getTaskStatusEnumByCode(Integer status) {
        TaskStatusEnum[] taskStatusEnums = TaskStatusEnum.values();
        for (TaskStatusEnum taskStatusEnum : taskStatusEnums) {
            if (taskStatusEnum.getStatus().equals(status)) {
                return taskStatusEnum;
            }
        }
        return null;
    }
}
