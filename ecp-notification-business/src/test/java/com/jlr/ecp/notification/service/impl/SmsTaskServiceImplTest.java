package com.jlr.ecp.notification.service.impl;

import cn.hutool.core.lang.Snowflake;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.executor.api.ScheduleJobService;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.notification.constant.BusinessConstants;
import com.jlr.ecp.notification.dal.dataobject.task.NotificationTask;
import com.jlr.ecp.notification.dal.dataobject.task.TaskModifyLog;
import com.jlr.ecp.notification.dal.dataobject.task.auto.AutoTaskTriggerMapDO;
import com.jlr.ecp.notification.dal.dataobject.template.MessageTemplate;
import com.jlr.ecp.notification.dal.mysql.task.NotificationTaskMapper;
import com.jlr.ecp.notification.dal.mysql.task.TaskModifyMapper;
import com.jlr.ecp.notification.dal.mysql.template.SmsTemplateMapper;
import com.jlr.ecp.notification.dal.repository.AutoTaskTriggerMapRepository;
import com.jlr.ecp.notification.dal.repository.MessageTaskRepository;
import com.jlr.ecp.notification.dal.repository.MessageTemplateRepository;
import com.jlr.ecp.notification.dal.repository.TaskModifyRepository;
import com.jlr.ecp.notification.enums.BusinessEnum;
import com.jlr.ecp.notification.enums.ErrorCodeConstants;
import com.jlr.ecp.notification.enums.task.*;
import com.jlr.ecp.notification.excel.service.NotificationExcelService;
import com.jlr.ecp.notification.req.task.*;
import com.jlr.ecp.notification.service.manual.ManualTaskSendService;
import com.jlr.ecp.notification.util.OperatorUtil;
import com.jlr.ecp.notification.util.PIPLDataUtil;
import com.jlr.ecp.notification.util.sms.TimeFormatUtil;
import com.jlr.ecp.notification.vo.task.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class SmsTaskServiceImplTest {

    @Mock
    private NotificationTaskMapper mockTaskMapper;
    @Mock
    private SmsTemplateMapper mockTemplateMapper;
    @Mock
    private Snowflake mockEcpIdUtil;
    @Mock
    private ScheduleJobService mockScheduleJobService;
    @Mock
    private ManualTaskSendService mockManualTaskSendService;

    @InjectMocks
    @Spy
    private SmsTaskServiceImpl smsTaskService;

    @InjectMocks
    private SmsAutoTaskServiceImpl smsAutoTaskServiceImplUnderTest;

    @Mock
    private MessageTemplateRepository templateRepository;

    @Mock
    private NotificationTaskMapper taskMapper;

    @Mock
    private NotificationExcelService excelService;


    @Mock
    private TaskModifyRepository taskModifyRepository;

    @Mock
    private ApplicationContext applicationContext;

    @Before
    public void setUp() {
        smsTaskService.ecpIdUtil = mockEcpIdUtil;
        smsTaskService.scheduleJobService = mockScheduleJobService;
        smsTaskService.manualTaskSendService = mockManualTaskSendService;
        MockitoAnnotations.openMocks(this);
    }


    @Test
    public void testUpdateTaskStatus() {
        // Setup
        final NotificationTask notificationTask = NotificationTask.builder()
                .taskCode("taskCode")
                .taskName("taskName")
                .triggerAction("triggerAction")
                .taskSendScheduleTime("taskSendScheduleTime")
                .taskSendType(0)
                .messageTemplateCode("messageTemplateCode")
                .status(0)
                .draftVersion(0)
                .messageFile("messageFile")
                .activateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .deactivateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .taskSendRealTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .taskType(0)
                .errorMsgFile("errorMsgFile")
                .submitFileType(0)
                .signBrandText(0)
                .sendChannel(0)
                .autoTaskType(0)
                .build();
        final TaskModifyStatusReq modifyStatusReq = new TaskModifyStatusReq();
        modifyStatusReq.setTaskCode("taskCode");
        modifyStatusReq.setModifyStatus(0);

        // Run the test
        smsTaskService.updateTaskStatus(notificationTask, modifyStatusReq);
    }


    @Test
    public void testGetTaskModifyContent() {
        assertThat(smsTaskService.getTaskModifyContent(0)).isEqualTo("停用自动通知任务");
        assertThat(smsTaskService.getTaskModifyContent(5)).isEqualTo("");
    }

    @Test
    public void testGetManualTaskModifyContent() {
        assertThat(smsTaskService.getManualTaskModifyContent(0)).isEqualTo("停用手动通知任务");
        assertThat(smsTaskService.getManualTaskModifyContent(1)).isEqualTo("启用手动通知任务");
        assertThat(smsTaskService.getManualTaskModifyContent(2)).isEqualTo("");
    }

    @Test
    public void testBuildTaskHistoryPageList() {
//        // Setup
//        final List<TaskModifyLog> taskHistoryList = List.of(TaskModifyLog.builder()
//                .taskCode("taskCode")
//                .modifyContent("desc")
//                .modifyUser("modifyUser")
//                .modifyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .build());
//        final List<TaskHistoryPageListVo> expectedResult = List.of(TaskHistoryPageListVo.builder()
//                .taskCode("taskCode")
//                .modifyTime("modifyTime")
//                .modifyContent("desc")
//                .modifyUser("modifyUser")
//                .modifyTime("2020-01-01 00:00:00")
//                .build());
//
//        // Run the test
//        final List<TaskHistoryPageListVo> result = smsTaskService.buildTaskHistoryPageList(
//                taskHistoryList);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testBuildTaskHistoryPageVo() {
//        // Setup
//        final TaskModifyLog taskHistory = TaskModifyLog.builder()
//                .taskCode("taskCode")
//                .modifyContent("desc")
//                .modifyUser("modifyUser")
//                .modifyTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .build();
//        final TaskHistoryPageListVo expectedResult = TaskHistoryPageListVo.builder()
//                .taskCode("taskCode")
//                .modifyTime("modifyTime")
//                .modifyContent("desc")
//                .modifyUser("modifyUser")
//                .modifyTime("2020-01-01 00:00:00")
//                .build();
//
//        // Run the test
//        final TaskHistoryPageListVo result = smsTaskService.buildTaskHistoryPageVo(taskHistory);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetTaskTemplateType() {
        assertThat(smsTaskService.getTaskTemplateType(2)).isEqualTo("手动配置");
        assertThat(smsTaskService.getTaskTemplateType(3)).isEqualTo("");
    }

    @Test
    public void testGetTaskSendScheduleTime() {
        // Setup
        final NotificationTask notificationTask1 = NotificationTask.builder()
                .taskSendScheduleTime("taskSendScheduleTime")
                .taskType(2)
                .taskSendType(1)
                .build();

        // Run the test
        final String result1 = smsTaskService.getTaskSendScheduleTime(notificationTask1);
        // Verify the results
        assertThat(result1).isEqualTo("taskSendScheduleTime");

        final String result2 = smsTaskService.getTaskSendScheduleTime(null);
        assertThat(result2).isEqualTo("");

        final NotificationTask notificationTask3 = NotificationTask.builder()
                .taskType(1)
                .taskSendType(1)
                .build();
        final String result3 = smsTaskService.getTaskSendScheduleTime(notificationTask3);
        assertThat(result3).isEqualTo("实时");
    }


    @Test
    public void testGetTaskDeactivateTime() {
        assertThat(
                smsTaskService.getTaskDeactivateTime(0, LocalDateTime.of(2021, 1, 1, 0, 0, 0)))
                .isEqualTo("2021-01-01 00:00:00");
        assertThat(
                smsTaskService.getTaskDeactivateTime(1, LocalDateTime.of(2021, 1, 1, 0, 0, 0)))
                .isEqualTo(null);
    }

    @Test
    public void testGetTaskStatus() {
        assertThat(smsTaskService.getTaskStatus(0)).isEqualTo("已停用");
        assertThat(smsTaskService.getTaskStatus(5)).isEqualTo(null);
    }


    @Mock
    private TaskModifyMapper taskModifyMapper;

    /**
     * 测试用例1：正常情况，查询到非空的任务编辑历史记录
     */
    @Test
    public void testGetTaskHistoryPageList_NormalCase() {
//        // 准备测试数据
//        TaskHistoryReq taskHistoryReq = new TaskHistoryReq();
//        taskHistoryReq.setTaskCode("testTaskCode");
//        taskHistoryReq.setPageNo(1);
//        taskHistoryReq.setPageSize(10);
//
//        TaskModifyLog log = new TaskModifyLog();
//        log.setTaskCode("testTaskCode");
//        log.setModifyContent("修改内容");
//        log.setModifyUser("修改人");
//        log.setModifyTime(LocalDateTime.now());
//
//        Page<TaskModifyLog> mockPage = new Page<>();
//        mockPage.setRecords(Collections.singletonList(log));
//        mockPage.setTotal(1L);
//
//        // Mock依赖方法
//        when(taskModifyMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class))).thenReturn(mockPage);
//
//        // 执行测试
//        PageResult<TaskHistoryPageListVo> result = smsTaskService.getTaskHistoryPageList(taskHistoryReq);
//
//        // 验证结果
//        assertThat(result).isNotNull();
//        assertThat(result.getList()).hasSize(1);
//        assertThat(result.getTotal()).isEqualTo(1L);
//        TaskHistoryPageListVo vo = result.getList().get(0);
//        assertThat(vo.getTaskCode()).isEqualTo("testTaskCode");
//        assertThat(vo.getModifyContent()).isEqualTo("修改内容");
//        assertThat(vo.getModifyUser()).isEqualTo("修改人");
//
//        // 验证Mock调用
//        verify(taskModifyMapper, times(1)).selectPage(any(Page.class), any(LambdaQueryWrapper.class));
    }

    /**
     * 测试用例2：查询结果为空
     */
    @Test
    public void testGetTaskHistoryPageList_EmptyResult() {
        // 准备测试数据
        TaskHistoryReq taskHistoryReq = new TaskHistoryReq();
        taskHistoryReq.setTaskCode("testTaskCode");
        taskHistoryReq.setPageNo(1);
        taskHistoryReq.setPageSize(10);

        Page<TaskModifyLog> mockPage = new Page<>();
        mockPage.setRecords(Collections.emptyList());
        mockPage.setTotal(0L);

        // Mock依赖方法
        when(taskModifyMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class))).thenReturn(mockPage);

        // 执行测试
        PageResult<TaskHistoryPageListVo> result = smsTaskService.getTaskHistoryPageList(taskHistoryReq);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getList()).isEmpty();
        assertThat(result.getTotal()).isEqualTo(0L);

        // 验证Mock调用
        verify(taskModifyMapper, times(1)).selectPage(any(Page.class), any(LambdaQueryWrapper.class));
    }


    @Mock
    private MessageTaskRepository taskRepository;


    @Mock
    private PIPLDataUtil piplDataUtil;


    /**
     * 测试用例1: 任务信息不存在
     */
    @Test
    public void testGetManualTaskDetail_TaskNotFound() {
        // Mock任务信息查询返回null
        when(taskRepository.queryTaskByCode(anyString())).thenReturn(null);

        // 构造请求参数
        TaskDetailReq taskDetailReq = new TaskDetailReq();
        taskDetailReq.setTaskCode("nonexistentTaskCode");

        // 执行被测函数
        CommonResult<ManualTaskDetailVo> result = smsTaskService.getManualTaskDetail(taskDetailReq);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getTaskCode()).isNull();
    }

    @Test
    public void testGetManualTaskDetail_RealTimeSendType() {
        // Mock任务信息
        NotificationTask notificationTask = new NotificationTask();
        notificationTask.setTaskCode("testTaskCode");
        notificationTask.setTaskName("Test Task");
        notificationTask.setTaskSendType(2); // 实时发送
        notificationTask.setTaskSendRealTime(LocalDateTime.of(2023, 10, 1, 12, 0, 0));
        when(taskRepository.queryTaskByCode(anyString())).thenReturn(notificationTask);

        // Mock静态方法
        try (var mockedStatic = mockStatic(TimeFormatUtil.class)) {
            mockedStatic.when(() -> TimeFormatUtil.localDateToStringWithoutSecond(any()))
                    .thenReturn("2023-10-01 12:00");

            // 构造请求参数
            TaskDetailReq taskDetailReq = new TaskDetailReq();
            taskDetailReq.setTaskCode("testTaskCode");

            // 执行被测函数
            CommonResult<ManualTaskDetailVo> result = smsTaskService.getManualTaskDetail(taskDetailReq);

            // 验证结果
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
        }
    }

    /**
     * 测试用例4: 消息文件不为空
     */
    @Test
    public void testGetManualTaskDetail_MessageFileNotEmpty() {
        // Mock任务信息
        NotificationTask notificationTask = new NotificationTask();
        notificationTask.setTaskCode("testTaskCode");
        notificationTask.setMessageFile("/path/to/messageFile.txt");
        when(taskRepository.queryTaskByCode(anyString())).thenReturn(notificationTask);

        // Mock加密工具方法
        when(piplDataUtil.getEncryptText(anyString())).thenReturn("encryptedMessageFile");

        // 构造请求参数
        TaskDetailReq taskDetailReq = new TaskDetailReq();
        taskDetailReq.setTaskCode("testTaskCode");

        // 执行被测函数
        CommonResult<ManualTaskDetailVo> result = smsTaskService.getManualTaskDetail(taskDetailReq);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getFileName()).isEqualTo("messageFile.txt");
        assertThat(result.getData().getCheckStatus()).isTrue();
        assertThat(result.getData().getCheckResult()).isTrue();
    }

    /**
     * 测试用例5: 错误消息文件不为空
     */
    @Test
    public void testGetManualTaskDetail_ErrorMsgFileNotEmpty() {
        // Mock任务信息
        NotificationTask notificationTask = new NotificationTask();
        notificationTask.setTaskCode("testTaskCode");
        notificationTask.setErrorMsgFile("/path/to/errorMsgFile.txt");
        when(taskRepository.queryTaskByCode(anyString())).thenReturn(notificationTask);

        // 构造请求参数
        TaskDetailReq taskDetailReq = new TaskDetailReq();
        taskDetailReq.setTaskCode("testTaskCode");

        // 执行被测函数
        CommonResult<ManualTaskDetailVo> result = smsTaskService.getManualTaskDetail(taskDetailReq);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getFileName()).isEqualTo("errorMsgFile.txt");
        assertThat(result.getData().getCheckStatus()).isTrue();
        assertThat(result.getData().getCheckResult()).isFalse();
    }

    /**
     * 测试用例6: 模板信息存在
     */
    @Test
    public void testGetManualTaskDetail_TemplateExists() {
        // Mock任务信息
        NotificationTask notificationTask = new NotificationTask();
        notificationTask.setTaskCode("testTaskCode");
        notificationTask.setMessageTemplateCode("templateCode");
        when(taskRepository.queryTaskByCode(anyString())).thenReturn(notificationTask);

        // Mock模板信息
        MessageTemplate messageTemplate = new MessageTemplate();
        messageTemplate.setTemplateCode("templateCode");
        messageTemplate.setTemplateName("Test Template");
        messageTemplate.setTemplateContent("Test Content");
        messageTemplate.setTemplateType(1);
        when(templateRepository.getMessageTemplateByCode(anyString())).thenReturn(messageTemplate);

        // 构造请求参数
        TaskDetailReq taskDetailReq = new TaskDetailReq();
        taskDetailReq.setTaskCode("testTaskCode");

        // 执行被测函数
        CommonResult<ManualTaskDetailVo> result = smsTaskService.getManualTaskDetail(taskDetailReq);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getTemplateCode()).isEqualTo("templateCode");
        assertThat(result.getData().getTemplateName()).isEqualTo("Test Template");
        assertThat(result.getData().getTemplateContent()).isEqualTo("Test Content");
        assertThat(result.getData().getTemplateType()).isEqualTo("1");
    }

    @Test
    public void getManualTaskDetail_TaskFound_ReturnsCorrectResult() {
        TaskDetailReq taskDetailReq = new TaskDetailReq();
        taskDetailReq.setTaskCode("existingTask");

        NotificationTask notificationTask = new NotificationTask();
        notificationTask.setTaskCode("existingTask");
        notificationTask.setTaskName("Test Task");
        notificationTask.setTriggerAction("Test Action");
        notificationTask.setTaskSendType(0);
        notificationTask.setMessageFile("testMessageFile");
        notificationTask.setTaskSendRealTime(java.time.LocalDateTime.now());
        notificationTask.setMessageTemplateCode("templateCode");

        MessageTemplate messageTemplate = new MessageTemplate();
        messageTemplate.setTemplateCode("templateCode");
        messageTemplate.setTemplateName("Test Template");
        messageTemplate.setTemplateType(1);

        when(taskRepository.queryTaskByCode("existingTask")).thenReturn(notificationTask);
        when(templateRepository.getMessageTemplateByCode("templateCode")).thenReturn(messageTemplate);
        when(piplDataUtil.getEncryptText("testMessageFile")).thenReturn("encryptedMessageFile");

        CommonResult<ManualTaskDetailVo> result = smsTaskService.getManualTaskDetail(taskDetailReq);

        assertNotNull(result);
        assertEquals("existingTask", result.getData().getTaskCode());
        assertEquals("Test Task", result.getData().getTaskName());
        assertEquals("encryptedMessageFile", result.getData().getMessageFile());
    }

    /**
     * 测试正常情况下的分页查询
     */
    @Test
    public void testGetManualTaskPageList_NormalCase() {
        // 模拟 queryTaskPageList 返回的任务分页数据
        Page<NotificationTask> mockTaskPage = new Page<>();
        List<NotificationTask> taskList = Arrays.asList(
                createNotificationTask("taskCode1", "templateCode1"),
                createNotificationTask("taskCode2", "templateCode2")
        );
        mockTaskPage.setRecords(taskList);
        mockTaskPage.setTotal(2L);

        doReturn(mockTaskPage).when(smsTaskService).queryTaskPageList(any(TaskPageListReq.class));

        // 模拟 templateRepository.queryTemplateListByCodes 返回的模板内容
        Map<String, String> templateMap = new HashMap<>();
        templateMap.put("templateCode1", "模板内容1");
        templateMap.put("templateCode2", "模板内容2");
        when(templateRepository.queryTemplateListByCodes(anySet()))
                .thenReturn(Arrays.asList(
                        createMessageTemplate("templateCode1", "模板内容1"),
                        createMessageTemplate("templateCode2", "模板内容2")
                ));

        // 模拟静态方法 TimeFormatUtil.localDateToStringWithoutSecond
        try (var mockedStatic = Mockito.mockStatic(TimeFormatUtil.class)) {
            mockedStatic.when(() -> TimeFormatUtil.localDateToStringWithoutSecond(any()))
                    .thenAnswer(invocation -> "2023/01/01 12:00");

            // 执行测试
            TaskPageListReq req = new TaskPageListReq();
            PageResult<ManualTaskPageListVo> result = smsTaskService.getManualTaskPageList(req);

            // 验证结果
            assertThat(result.getList()).hasSize(2);
            assertThat(result.getTotal()).isEqualTo(2L);
            assertThat(result.getList().get(0).getTemplateContent()).isEqualTo("模板内容1");
            assertThat(result.getList().get(1).getTemplateContent()).isEqualTo("模板内容2");
        }
    }


    /**
     * 创建一个 NotificationTask 对象用于测试
     */
    private NotificationTask createNotificationTask(String taskCode, String templateCode) {
        return NotificationTask.builder()
                .taskCode(taskCode)
                .messageTemplateCode(templateCode)
                .taskSendType(1) // 实时发送
                .taskSendRealTime(LocalDateTime.now())
                .status(1) // 已启用
                .draftVersion(0)
                .signBrandText(1)
                .sendChannel(2)
                .build();
    }

    /**
     * 创建一个 MessageTemplate 对象用于测试
     */
    private MessageTemplate createMessageTemplate(String templateCode, String templateContent) {
        return MessageTemplate.builder()
                .templateCode(templateCode)
                .templateContent(templateContent)
                .build();
    }


    @Mock
    private ScheduleJobService scheduleJobService;

    @Mock
    private ManualTaskSendService manualTaskSendService;

    @Test
    public void openManualTask_NullRequest_ReturnsError() {
        CommonResult<Boolean> result = smsTaskService.openManualTask(null);
        assertFalse(result.isSuccess());
    }

    @Test
    public void openManualTask_EmptyTaskCode_ReturnsError() {
        TaskModifyStatusReq req = new TaskModifyStatusReq();
        req.setTaskCode("");
        req.setModifyStatus(1);

        CommonResult<Boolean> result = smsTaskService.openManualTask(req);
        assertFalse(result.isSuccess());
    }

    @Test
    public void openManualTask_NullModifyStatus_ReturnsError() {
        TaskModifyStatusReq req = new TaskModifyStatusReq();
        req.setTaskCode("taskCode");

        CommonResult<Boolean> result = smsTaskService.openManualTask(req);
        assertFalse(result.isSuccess());
    }

    @Test
    public void openManualTask_InvalidModifyStatus_ReturnsError() {
        TaskModifyStatusReq req = new TaskModifyStatusReq();
        req.setTaskCode("taskCode");
        req.setModifyStatus(2);

        CommonResult<Boolean> result = smsTaskService.openManualTask(req);
        assertFalse(result.isSuccess());
    }

    @Test
    public void openManualTask_TaskNotFound_ReturnsError() {
        TaskModifyStatusReq req = new TaskModifyStatusReq();
        req.setTaskCode("taskCode");
        req.setModifyStatus(1);

        when(taskRepository.queryTaskByCode("taskCode")).thenReturn(null);

        CommonResult<Boolean> result = smsTaskService.openManualTask(req);
        assertFalse(result.isSuccess());
    }

    @Test
    public void openManualTask_MessageFileBlank_ThrowsException() {
        TaskModifyStatusReq req = new TaskModifyStatusReq();
        req.setTaskCode("taskCode");
        req.setModifyStatus(1);

        NotificationTask task = new NotificationTask();
        task.setMessageFile("");

        when(taskRepository.queryTaskByCode("taskCode")).thenReturn(task);

        assertThrows(ServiceException.class, () -> smsTaskService.openManualTask(req));
    }


    /**
     * 测试异常情况：数据库查询抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testGetTaskHistoryPageList_ExceptionCase() {
        // 准备测试数据
        TaskHistoryReq taskHistoryReq = new TaskHistoryReq();
        taskHistoryReq.setTaskCode("testTaskCode");
        taskHistoryReq.setPageNo(1);
        taskHistoryReq.setPageSize(10);

        // 模拟 queryTaskHistoryPageList 抛出异常
        when(smsTaskService.queryTaskHistoryPageList(any(TaskHistoryReq.class))).thenThrow(new RuntimeException("数据库查询失败"));

        // 执行测试
        smsTaskService.getTaskHistoryPageList(taskHistoryReq);

        // 验证 Mock 方法调用
        verify(smsTaskService, times(1)).queryTaskHistoryPageList(any(TaskHistoryReq.class));
        verify(smsTaskService, never()).buildTaskHistoryPageList(anyList());
    }


    /**
     * 测试用例2: modifyStatusReq 非空，且 modifyStatus 对应的任务修改类型存在
     */
//    @Test
//   public  void testAddTaskModifyStatusLog_ValidInput_ExistingModifyType() {
//        // 准备测试数据
//        TaskModifyStatusReq modifyStatusReq = new TaskModifyStatusReq();
//        modifyStatusReq.setTaskCode("testTaskCode");
//        modifyStatusReq.setModifyStatus(1); // 假设对应 "启用自动通知任务"
//
//        // Mock OperatorUtil.getOperator()
//        try (MockedStatic<OperatorUtil> mockedOperatorUtil = Mockito.mockStatic(OperatorUtil.class)) {
//            mockedOperatorUtil.when(OperatorUtil::getOperator).thenReturn("testOperator");
//
//            // 执行函数
////            smsTaskService.addTaskModifyStatusLog(modifyStatusReq);
//
//            // 验证 taskModifyMapper.insert 被调用
//            ArgumentCaptor<TaskModifyLog> captor = ArgumentCaptor.forClass(TaskModifyLog.class);
//            verify(taskModifyMapper, times(1)).insert(captor.capture());
//
//            // 验证插入的日志对象内容是否正确
//            TaskModifyLog capturedLog = captor.getValue();
//            assert capturedLog != null;
//            assert capturedLog.getTaskCode().equals("testTaskCode");
////            assert capturedLog.getModifyContent().equals("启用自动通知任务");
////            assert capturedLog.getModifyUser().equals("testOperator");
////            assert capturedLog.getModifyTime().isBefore(LocalDateTime.now().plusSeconds(1)); // 确保时间接近当前时间
//        }
//    }

    /**
     * 测试用例3: modifyStatusReq 非空，但 modifyStatus 对应的任务修改类型不存在
     */
//    @Test
//    public void testAddTaskModifyStatusLog_ValidInput_NonExistingModifyType() {
//        // 准备测试数据
//        TaskModifyStatusReq modifyStatusReq = new TaskModifyStatusReq();
//        modifyStatusReq.setTaskCode("testTaskCode");
//        modifyStatusReq.setModifyStatus(999); // 假设不存在对应的任务修改类型
//
//        // Mock OperatorUtil.getOperator()
//        try (MockedStatic<OperatorUtil> mockedOperatorUtil = Mockito.mockStatic(OperatorUtil.class)) {
//            mockedOperatorUtil.when(OperatorUtil::getOperator).thenReturn("testOperator");
//
//            // 执行函数
//            smsTaskService.addTaskModifyStatusLog(modifyStatusReq);
//
//            // 验证 taskModifyMapper.insert 被调用
//            ArgumentCaptor<TaskModifyLog> captor = ArgumentCaptor.forClass(TaskModifyLog.class);
//            verify(taskModifyMapper, times(1)).insert(captor.capture());
//
//            // 验证插入的日志对象内容是否正确
//            TaskModifyLog capturedLog = captor.getValue();
//            assert capturedLog != null;
//            assert capturedLog.getTaskCode().equals("testTaskCode");
//            assert capturedLog.getModifyContent().isEmpty(); // 修改内容为空
//            assert capturedLog.getModifyUser().equals("testOperator");
//            assert capturedLog.getModifyTime().isBefore(LocalDateTime.now().plusSeconds(1)); // 确保时间接近当前时间
//        }
//    }


    /**
     * 测试用例1：modifyStatusReq 为 null，验证函数是否正确处理空输入
     */
//    @Test
//    public void testAddManualTaskModifyStatusLog_NullInput() {
//        // 执行测试
//        smsTaskService.addManualTaskModifyStatusLog(null);
//
//        // 验证 taskModifyMapper.insert 未被调用
//        verify(taskModifyMapper, never()).insert(any(TaskModifyLog.class));
//    }



    /**
     * 测试用例1：taskModifyReq为null，验证函数是否正确处理空输入
     */
//    @Test
//    public void testAddTaskModifyContentLog_NullTaskModifyReq() {
//        // 执行函数
//        smsTaskService.addTaskModifyContentLog(null, 1);
//
//        // 验证taskModifyMapper.insert未被调用
//        verify(taskModifyMapper, never()).insert(any(TaskModifyLog.class));
//    }

    /**
     * 测试发送时间格式错误的情况
     */
    @Test
   public  void testAddManualTask_SendTimeFormatError() {
        TaskCreatReq taskCreatReq = new TaskCreatReq();
        taskCreatReq.setTaskSendType(TaskSendTypeEnum.SCHEDULED.getCode());
        taskCreatReq.setSendTime("invalid-time");

        ServiceException exception = assertThrows(ServiceException.class, () -> smsTaskService.addManualTask(taskCreatReq));
        assertEquals(ErrorCodeConstants.TASK_SEND_TIME_FORMAT_ERROR.getCode(), exception.getCode());
    }

    /**
     * 测试通知发送时间小于当前时间的情况
     */
    @Test
   public  void testAddManualTask_SendTimeBeforeNow() {
        try (MockedStatic<TimeFormatUtil> mockedStatic = Mockito.mockStatic(TimeFormatUtil.class)) {
            mockedStatic.when(() -> TimeFormatUtil.timeToStringByFormat(any(LocalDateTime.class), eq(TimeFormatUtil.formatter_4)))
                    .thenReturn("2023-10-01 12:00");

            TaskCreatReq taskCreatReq = new TaskCreatReq();
            taskCreatReq.setTaskSendType(TaskSendTypeEnum.SCHEDULED.getCode());
            taskCreatReq.setSendTime("2023-10-01 11:59");

            ServiceException exception = assertThrows(ServiceException.class, () -> smsTaskService.addManualTask(taskCreatReq));
            assertEquals(ErrorCodeConstants.MANUAL_TASK_SEND_TIME_ERROR.getCode(), exception.getCode());
        }
    }

    /**
     * 测试操作类型为保存并提交时，签名为空的情况
     */

    @Test
   public  void testAddManualTask_SignBrandEmpty() {
        TaskCreatReq taskCreatReq = new TaskCreatReq();
        taskCreatReq.setAddDataType(BusinessEnum.OperationType.SAVE_AND_SUBMIT.getType());
        taskCreatReq.setSignBrandType(null);

        ServiceException exception = assertThrows(ServiceException.class, () -> smsTaskService.addManualTask(taskCreatReq));
        assertEquals(ErrorCodeConstants.MANUAL_TASK_SIGN_EMPTY.getCode(), exception.getCode());
    }

    /**
     * 测试模板代码无效或不存在的情况
     */
    @Test
   public  void testAddManualTask_TemplateCodeInvalid() {
        when(templateRepository.existsTemplateCodeByType(anyString(), anyInt())).thenReturn(false);

        TaskCreatReq taskCreatReq = new TaskCreatReq();
        taskCreatReq.setMessageTemplateCode("invalid-template-code");

        ServiceException exception = assertThrows(ServiceException.class, () -> smsTaskService.addManualTask(taskCreatReq));
        assertEquals(ErrorCodeConstants.TASK_SELECTED_ERROR.getCode(), exception.getCode());
    }

    /**
     * 测试正常流程
     */
    @Test
    public void testAddManualTask_Success() {
        when(mockEcpIdUtil.nextIdStr()).thenReturn("test-task-code");
        when(templateRepository.existsTemplateCodeByType(anyString(), anyInt())).thenReturn(true);
        doReturn(1).when(taskMapper).insert(any(NotificationTask.class));

        TaskCreatReq taskCreatReq = new TaskCreatReq();
        taskCreatReq.setTaskName("Test Task");
        taskCreatReq.setTaskSendType(TaskSendTypeEnum.REALTIME.getCode());
        taskCreatReq.setMessageTemplateCode("valid-template-code");
        taskCreatReq.setSubmitFileType(1);

        CommonResult<Boolean> result = smsTaskService.addManualTask(taskCreatReq);

        assertTrue(result.isSuccess());
    }



    /**
     * 测试用例2：实时发送任务
     */
//    @Test
//   public void testUpdateManualTask_RealtimeSend() {
//        TaskModifyReq taskModifyReq = new TaskModifyReq();
//        taskModifyReq.setTaskCode("testTaskCode");
//        taskModifyReq.setTaskSendType(1); // 实时发送
//
//        NotificationTask taskExist = new NotificationTask();
//        taskExist.setTaskCode("testTaskCode");
//        taskExist.setStatus(TaskStatusEnum.STOP.getStatus());
//
//        when(taskMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(taskExist);
//        doReturn(1).when(taskMapper).updateById(any(NotificationTask.class));
//
//        smsTaskService.updateManualTask(taskModifyReq);
//
//        assertNull(taskExist.getTaskSendScheduleTime());
//        verify(taskMapper, times(1)).updateById(taskExist);
//    }

    /**
     * 测试用例3：任务发送时间格式错误
     */
    @Test
   public  void testUpdateManualTask_InvalidSendTimeFormat() {
        TaskModifyReq taskModifyReq = new TaskModifyReq();
        taskModifyReq.setTaskCode("testTaskCode");
        taskModifyReq.setTaskSendType(2); // 定时发送
        taskModifyReq.setSendTime("sendTime");

        NotificationTask taskExist = new NotificationTask();
        taskExist.setTaskCode("testTaskCode");
        taskExist.setStatus(TaskStatusEnum.STOP.getStatus());

        Exception exception = assertThrows(ServiceException.class, () -> {
            smsTaskService.updateManualTask(taskModifyReq);
        });

        assertEquals("任务发送时间格式错误", exception.getMessage());
    }

    /**
     * 测试用例4：通知模板不存在
     */
    @Test
   public  void testUpdateManualTask_TemplateNotExists() {
        TaskModifyReq taskModifyReq = new TaskModifyReq();
        taskModifyReq.setTaskCode("testTaskCode");
        taskModifyReq.setMessageTemplateCode("nonExistentTemplate");

        NotificationTask taskExist = new NotificationTask();
        taskExist.setTaskCode("testTaskCode");
        taskExist.setStatus(TaskStatusEnum.STOP.getStatus());

        when(templateRepository.existsTemplateCode("nonExistentTemplate")).thenReturn(false);

        Exception exception = assertThrows(ServiceException.class, () -> {
            smsTaskService.updateManualTask(taskModifyReq);
        });

        assertEquals("模板不存在或模板已经删除", exception.getMessage());
    }

    @Test
    public void getTemplateContentByCode_TemplateExists_ReturnsTemplateContent() {
        String templateCode = "existingTemplateCode";
        String expectedContent = "This is the template content.";

        MessageTemplate messageTemplate = new MessageTemplate();
        messageTemplate.setTemplateContent(expectedContent);

        when(templateRepository.getMessageTemplateByCode(templateCode)).thenReturn(messageTemplate);

        String result = smsTaskService.getTemplateContentByCode(templateCode);

        assertEquals(expectedContent, result);
    }

    @Test
    public void getTemplateContentByCode_TemplateDoesNotExist_ReturnsNull() {
        String templateCode = "nonExistingTemplateCode";

        when(templateRepository.getMessageTemplateByCode(templateCode)).thenReturn(null);

        String result = smsTaskService.getTemplateContentByCode(templateCode);

        assertNull(result);
    }

    @Test
    public void queryTaskPageList_TaskCodePresent_ShouldApplyTaskCodeCondition() {
        TaskPageListReq req = new TaskPageListReq();
        req.setTaskCode("testCode");
        req.setPageNo(1);
        req.setPageSize(10);

        Page<NotificationTask> page = new Page<>(1, 10);

        Page<NotificationTask> result = smsTaskService.queryTaskPageList(req);

        assertThat(result).isEqualTo(null);
    }

    @Test
    public void getTaskSendTimeType_ValidSendType_ReturnsCorrectDescription() {
        Integer sendType = TaskSendTypeEnum.REALTIME.getCode();
        String expectedDescription = TaskSendTypeEnum.REALTIME.getDesc();

        String result = smsTaskService.getTaskSendTimeType(sendType);

        assertEquals(expectedDescription, result);
    }

    @Test
    public void getTaskSendTimeType_InvalidSendType_ReturnsEmptyString() {
        Integer sendType = 999; // 假设999不在TaskSendTypeEnum中定义

        String result = smsTaskService.getTaskSendTimeType(sendType);

        assertEquals("", result);
    }


    /**
     * 测试用例1：参数为空
     */
    @Test
    public void testModifyManualTaskStatus_NullRequest() {
        CommonResult<Boolean> result = smsTaskService.modifyManualTaskStatus(null);
        assertFalse(result.isSuccess());
        assertEquals(ErrorCodeConstants.TASK_REQ_NULL.getCode(), result.getCode());
    }

    /**
     * 测试用例2：任务代码为空
     */
    @Test
   public  void testModifyManualTaskStatus_EmptyTaskCode() {
        TaskModifyStatusReq request = new TaskModifyStatusReq();
        request.setTaskCode("");
        request.setModifyStatus(1);

        CommonResult<Boolean> result = smsTaskService.modifyManualTaskStatus(request);
        assertFalse(result.isSuccess());
        assertEquals(ErrorCodeConstants.TASK_CODE_NULL.getCode(), result.getCode());
    }

    /**
     * 测试用例3：任务状态为空
     */
    @Test
   public void testModifyManualTaskStatus_NullModifyStatus() {
        TaskModifyStatusReq request = new TaskModifyStatusReq();
        request.setTaskCode("testTaskCode");
        request.setModifyStatus(null);

        CommonResult<Boolean> result = smsTaskService.modifyManualTaskStatus(request);
        assertFalse(result.isSuccess());
        assertEquals(ErrorCodeConstants.TASK_STATUS_NULL.getCode(), result.getCode());
    }

    /**
     * 测试用例4：任务状态非法
     */
    @Test
   public void testModifyManualTaskStatus_InvalidModifyStatus() {
        TaskModifyStatusReq request = new TaskModifyStatusReq();
        request.setTaskCode("testTaskCode");
        request.setModifyStatus(2); // 非法状态

        CommonResult<Boolean> result = smsTaskService.modifyManualTaskStatus(request);
        assertFalse(result.isSuccess());
        assertEquals(ErrorCodeConstants.TASK_STATUS_ERROR.getCode(), result.getCode());
    }

    /**
     * 测试用例5：任务不存在
     */
    @Test
   public void testModifyManualTaskStatus_TaskNotFound() {
        TaskModifyStatusReq request = new TaskModifyStatusReq();
        request.setTaskCode("nonExistentTaskCode");
        request.setModifyStatus(1);

        when(taskRepository.queryTaskByCode("nonExistentTaskCode")).thenReturn(null);

        CommonResult<Boolean> result = smsTaskService.modifyManualTaskStatus(request);
        assertFalse(result.isSuccess());
        assertEquals(ErrorCodeConstants.TASK_NO_EXIST.getCode(), result.getCode());
    }

    /**
     * 测试用例6：任务已发送
     */
    @Test
   public void testModifyManualTaskStatus_TaskAlreadySent() {
        TaskModifyStatusReq request = new TaskModifyStatusReq();
        request.setTaskCode("sentTaskCode");
        request.setModifyStatus(1);

        NotificationTask task = new NotificationTask();
        task.setStatus(TaskStatusEnum.FINISH_SEND.getStatus());

        when(taskRepository.queryTaskByCode("sentTaskCode")).thenReturn(task);

        CommonResult<Boolean> result = smsTaskService.modifyManualTaskStatus(request);
        assertFalse(result.isSuccess());
        assertEquals(ErrorCodeConstants.TASK_STATUS_FINISH_SEND.getCode(), result.getCode());
    }

    /**
     * 测试用例7：正常启用任务
     */
    @Test
   public void testModifyManualTaskStatus_EnableTask() {
        TaskModifyStatusReq request = new TaskModifyStatusReq();
        request.setTaskCode("enableTaskCode");
        request.setModifyStatus(1);

        NotificationTask task = new NotificationTask();
        task.setStatus(TaskStatusEnum.STOP.getStatus());

        when(taskRepository.queryTaskByCode("enableTaskCode")).thenReturn(task);
        doReturn(1).when(taskMapper).updateById(any(NotificationTask.class));
        doReturn(1).when(taskModifyMapper).insert(any());

        CommonResult<Boolean> result = smsTaskService.modifyManualTaskStatus(request);
        assertTrue(result.isSuccess());
    }


    /**
     * 测试任务不存在的情况
     */
    @Test
    public void testDeleteFile_TaskNotExist() {
        // Mock 查询任务返回 null
        when(taskRepository.queryTaskByCode("nonexistentTaskCode")).thenReturn(null);

        // 执行方法
        CommonResult<Boolean> result = smsTaskService.deleteFile("nonexistentTaskCode");

        // 验证结果
        assertThat(result).isEqualTo(CommonResult.error(ErrorCodeConstants.MANUAL_TASK_NOT_EXIST));

        // 验证未调用其他方法
        verify(taskMapper, never()).updateById(any());
        verify(excelService, never()).deleteExcel(any());
    }

    /**
     * 测试任务状态为已启用的情况
     */
    @Test
    public void testDeleteFile_TaskStarted() {
        // Mock 查询任务返回已启用的任务
        NotificationTask task = new NotificationTask();
        task.setStatus(TaskStatusEnum.START.getStatus());
        when(taskRepository.queryTaskByCode("startedTaskCode")).thenReturn(task);

        // 执行方法
        CommonResult<Boolean> result = smsTaskService.deleteFile("startedTaskCode");

        // 验证结果
        assertThat(result).isEqualTo(CommonResult.error(ErrorCodeConstants.MANUAL_TASK_STATUS_NOT_STOP));

        // 验证未调用其他方法
        verify(taskMapper, never()).updateById(any());
        verify(excelService, never()).deleteExcel(any());
    }

    /**
     * 测试任务状态为已停用，文件均为空的情况
     */
    @Test
    public void testDeleteFile_TaskStopped_NoFiles() {
        // Mock 查询任务返回已停用的任务
        NotificationTask task = new NotificationTask();
        task.setStatus(TaskStatusEnum.STOP.getStatus());
        task.setMessageFile(null);
        task.setErrorMsgFile(null);
        when(taskRepository.queryTaskByCode("stoppedTaskCode")).thenReturn(task);

        // Mock 更新任务成功
        doReturn(1).when(taskMapper).updateById(any());

        // 执行方法
        CommonResult<Boolean> result = smsTaskService.deleteFile("stoppedTaskCode");

        // 验证结果
        assertThat(result).isEqualTo(CommonResult.success(true));
    }



//    @Test
//   public void testGetManualTaskModifyDetail_LogIdIsNull() {
//        // Act
//        ManualTaskHistoryDetailVO result = smsTaskService.getManualTaskModifyDetail(null);
//
//        // Assert
//        assertNull(result);
//        verify(taskModifyRepository, never()).getById(any());
//    }

    @Test
   public void testGetManualTaskModifyDetail_RecordNotFound() {
        // Arrange
        Long logId = 1L;
        when(taskModifyRepository.getById(logId)).thenReturn(null);

        // Act
        ManualTaskHistoryDetailVO result = smsTaskService.getManualTaskModifyDetail(logId);

        // Assert
        assertNull(result);
        verify(taskModifyRepository, times(1)).getById(logId);
    }

    @Test
   public void testGetManualTaskModifyDetail_RecordExists() {
        // Arrange
        Long logId = 1L;
        LocalDateTime operateTime = LocalDateTime.of(2023, 10, 1, 12, 0, 0);
        TaskModifyLog taskModifyLog = new TaskModifyLog();
        taskModifyLog.setId(logId);
        taskModifyLog.setModifyFieldCount(2);
        taskModifyLog.setModifyFieldNewValue("{\"name\":\"new\",\"desc\":\"newDesc\"}");
        taskModifyLog.setModifyFieldOldValue("{\"name\":\"old\",\"desc\":\"oldDesc\"}");
        taskModifyLog.setModifyModule("testModule");
        taskModifyLog.setOperateTime(operateTime);
        taskModifyLog.setOperateUser("testUser");

        when(taskModifyRepository.getById(logId)).thenReturn(taskModifyLog);

        // Act
        ManualTaskHistoryDetailVO result = smsTaskService.getManualTaskModifyDetail(logId);

        // Assert
        assertNotNull(result);
        assertEquals(taskModifyLog.getId(), result.getId());
        assertEquals(taskModifyLog.getModifyFieldCount(), result.getModifyFieldCount());
        assertEquals(taskModifyLog.getModifyFieldNewValue(), result.getModifyFieldNewValue());
        assertEquals(taskModifyLog.getModifyFieldOldValue(), result.getModifyFieldOldValue());
        assertEquals(taskModifyLog.getModifyModule(), result.getModifyModule());
        assertEquals(taskModifyLog.getOperateTime(), result.getOperateTime());
        assertEquals(taskModifyLog.getOperateUser(), result.getOperateUser());
        verify(taskModifyRepository, times(1)).getById(logId);
    }

    @Test
   public void testBuildTaskHistoryPageVo_NullTaskHistory() {
        // Act
        TaskHistoryPageListVo result = smsTaskService.buildTaskHistoryPageVo(null);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getId()).isNull();
        assertThat(result.getModifyField()).isNull();
        assertThat(result.getModifyDetail()).isNull();
    }

    @Test
   public void testBuildTaskHistoryPageVo_EmptyOldValue() {
        // Arrange
        TaskModifyLog taskHistory = new TaskModifyLog();
        taskHistory.setId(1L);
        taskHistory.setTaskCode("TEST001");
        taskHistory.setModifyFieldOldValue(""); // 空值

        Set<String> detailFields = new HashSet<>();
        detailFields.add("通知发送时间");

        // Act & Assert
        try (MockedStatic<ManualNotificationTaskFieldEnum> mockedStatic = mockStatic(ManualNotificationTaskFieldEnum.class)) {
            mockedStatic.when(ManualNotificationTaskFieldEnum::getAllDetailFields).thenReturn(detailFields);

            TaskHistoryPageListVo result = smsTaskService.buildTaskHistoryPageVo(taskHistory);

            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo(1L);
            assertThat(result.getTaskCode()).isEqualTo("TEST001");
            assertThat(result.getModifyField()).isNull();
        }
    }

    @Test
   public void testBuildTaskHistoryPageVo_WithDetailFields() {
        // Arrange
        TaskModifyLog taskHistory = new TaskModifyLog();
        taskHistory.setId(2L);
        taskHistory.setTaskCode("TEST002");
        taskHistory.setModifyFieldOldValue("{\"通知发送时间\":\"旧值\",\"模板名称\":\"旧模板\"}");
        taskHistory.setOperateTime(LocalDateTime.now());

        Set<String> detailFields = new HashSet<>();
        detailFields.add("通知发送时间");

        // Act & Assert
        try (MockedStatic<ManualNotificationTaskFieldEnum> mockedStatic = mockStatic(ManualNotificationTaskFieldEnum.class)) {
            mockedStatic.when(ManualNotificationTaskFieldEnum::getAllDetailFields).thenReturn(detailFields);

            TaskHistoryPageListVo result = smsTaskService.buildTaskHistoryPageVo(taskHistory);

            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo(2L);
            assertThat(result.getTaskCode()).isEqualTo("TEST002");
            assertThat(result.getModifyField()).isEqualTo("通知发送时间、模板名称");
            assertThat(result.getModifyDetail()).isTrue();
            assertThat(result.getOperateTime()).isEqualTo(taskHistory.getOperateTime());
        }
    }

    @Test
   public void testBuildTaskHistoryPageVo_NoDetailFields() {
        // Arrange
        TaskModifyLog taskHistory = new TaskModifyLog();
        taskHistory.setId(3L);
        taskHistory.setModifyFieldOldValue("{\"任务名称\":\"旧名称\",\"模板名称\":\"旧模板\"}");

        Set<String> detailFields = new HashSet<>();
        detailFields.add("通知签名");

        // Act & Assert
        try (MockedStatic<ManualNotificationTaskFieldEnum> mockedStatic = mockStatic(ManualNotificationTaskFieldEnum.class)) {
            mockedStatic.when(ManualNotificationTaskFieldEnum::getAllDetailFields).thenReturn(detailFields);

            TaskHistoryPageListVo result = smsTaskService.buildTaskHistoryPageVo(taskHistory);

            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo(3L);
            assertThat(result.getModifyField()).isEqualTo("任务名称、模板名称");
            assertThat(result.getModifyDetail()).isFalse();
        }
    }

    /**
     * 测试getTaskHistoryPageList方法 - 正常情况
     */
    @Test
    public void testGetTaskHistoryPageList_Success() {
        // 准备测试数据
        TaskHistoryReq taskHistoryReq = new TaskHistoryReq();
        taskHistoryReq.setTaskCode("testTaskCode");
        taskHistoryReq.setPageNo(1);
        taskHistoryReq.setPageSize(10);

        TaskModifyLog log1 = new TaskModifyLog();
        log1.setId(1L);
        log1.setTaskCode("testTaskCode");
        log1.setModifyContent("修改内容1");
        log1.setModifyUser("用户1");
        log1.setOperateTime(LocalDateTime.now());

        TaskModifyLog log2 = new TaskModifyLog();
        log2.setId(2L);
        log2.setTaskCode("testTaskCode");
        log2.setModifyContent("修改内容2");
        log2.setModifyUser("用户2");
        log2.setOperateTime(LocalDateTime.now());

        Page<TaskModifyLog> mockPage = new Page<>();
        mockPage.setRecords(Arrays.asList(log1, log2));
        mockPage.setTotal(2L);

        // Mock依赖方法
        when(taskModifyMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class))).thenReturn(mockPage);

        // 执行测试
        PageResult<TaskHistoryPageListVo> result = smsTaskService.getTaskHistoryPageList(taskHistoryReq);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getList()).hasSize(2);
        assertThat(result.getTotal()).isEqualTo(2L);
        assertThat(result.getList().get(0).getId()).isEqualTo(1L);
        assertThat(result.getList().get(1).getId()).isEqualTo(2L);
    }

    /**
     * 测试updateManualTask方法 - 正常更新
     */
    @Test
    public void testUpdateManualTask_Success() {
        // 准备测试数据
        TaskModifyReq taskModifyReq = new TaskModifyReq();
        taskModifyReq.setTaskCode("testTaskCode");
        taskModifyReq.setTaskName("Updated Task Name");
        taskModifyReq.setTaskSendType(TaskSendTypeEnum.REALTIME.getCode());
        taskModifyReq.setMessageTemplateCode("validTemplateCode");
        taskModifyReq.setMessageFile("messageFile");
        taskModifyReq.setErrorMsgFile("errorMsgFile");
        taskModifyReq.setSignBrandType("signBrand");
        taskModifyReq.setSendChannelType("sendChannel");

        NotificationTask existingTask = new NotificationTask();
        existingTask.setTaskCode("testTaskCode");
        existingTask.setTaskName("Original Task Name");
        existingTask.setStatus(TaskStatusEnum.STOP.getStatus());

        // Mock依赖方法
        when(taskRepository.queryTaskByCode("testTaskCode")).thenReturn(existingTask);
        when(templateRepository.existsTemplateCode("validTemplateCode")).thenReturn(true);
        when(piplDataUtil.getDecodeText("messageFile")).thenReturn("decodedMessageFile");
        doReturn(1).when(taskMapper).updateById(any(NotificationTask.class));

        // 执行测试
        CommonResult<Boolean> result = smsTaskService.updateManualTask(taskModifyReq);

        // 验证结果
        assertTrue(result.isSuccess());
        verify(taskMapper, times(1)).updateById(existingTask);
        assertEquals("Updated Task Name", existingTask.getTaskName());
    }

    /**
     * 测试updateManualTask方法 - 任务不存在
     */
    @Test
    public void testUpdateManualTask_TaskNotFound() {
        // 准备测试数据
        TaskModifyReq taskModifyReq = new TaskModifyReq();
        taskModifyReq.setTaskCode("nonExistentTaskCode");

        // Mock taskRepository.queryTaskByCode 返回 null
        when(taskRepository.queryTaskByCode("nonExistentTaskCode")).thenReturn(null);

        // 执行测试并验证异常
        Exception exception = assertThrows(ServiceException.class, () -> {
            smsTaskService.updateManualTask(taskModifyReq);
        });

        assertEquals("任务不存在或任务已经删除", exception.getMessage());
    }

    /**
     * 测试batchStartManualTask方法 - 空列表
     */
    @Test
    public void testBatchStartManualTask_EmptyList() {
        // 准备测试数据
        BatchTaskModifyStatusReq batchRequest = new BatchTaskModifyStatusReq();
        batchRequest.setTaskModifyStatusReqList(Collections.emptyList());

        // 执行测试
        CommonResult<String> result = smsTaskService.batchStartManualTask(batchRequest);

        // 验证结果
        assertTrue(result.isSuccess());
        assertTrue(result.getData().contains("要开启的任务数量为空"));
    }

    /**
     * 测试batchStartManualTask方法 - 全部成功
     */
    @Test
    public void testBatchStartManualTask_AllSuccess() {
        // 准备测试数据
        TaskModifyStatusReq req1 = new TaskModifyStatusReq();
        req1.setTaskCode("task1");
        TaskModifyStatusReq req2 = new TaskModifyStatusReq();
        req2.setTaskCode("task2");

        BatchTaskModifyStatusReq batchRequest = new BatchTaskModifyStatusReq();
        batchRequest.setTaskModifyStatusReqList(Arrays.asList(req1, req2));

        // Mock applicationContext.getBean 返回当前实例
        when(applicationContext.getBean(SmsTaskServiceImpl.class)).thenReturn(smsTaskService);

        // Mock openManualTask 方法，都成功
        doReturn(CommonResult.success(true)).when(smsTaskService).openManualTask(req1);
        doReturn(CommonResult.success(true)).when(smsTaskService).openManualTask(req2);

        // 执行测试
        CommonResult<String> result = smsTaskService.batchStartManualTask(batchRequest);

        // 验证结果
        assertTrue(result.isSuccess());
        assertTrue(result.getData().contains("2条启用成功"));
    }

    /**
     * 测试batchStartManualTask方法 - 部分失败
     */
    @Test
    public void testBatchStartManualTask_PartialFailure() {
        // 准备测试数据
        TaskModifyStatusReq req1 = new TaskModifyStatusReq();
        req1.setTaskCode("task1");
        TaskModifyStatusReq req2 = new TaskModifyStatusReq();
        req2.setTaskCode("task2");

        BatchTaskModifyStatusReq batchRequest = new BatchTaskModifyStatusReq();
        batchRequest.setTaskModifyStatusReqList(Arrays.asList(req1, req2));

        // Mock applicationContext.getBean 返回当前实例
        when(applicationContext.getBean(SmsTaskServiceImpl.class)).thenReturn(smsTaskService);

        // Mock openManualTask 方法，第一个成功，第二个失败
        doReturn(CommonResult.success(true)).when(smsTaskService).openManualTask(req1);
        doReturn(CommonResult.error(ErrorCodeConstants.TASK_NO_EXIST)).when(smsTaskService).openManualTask(req2);

        // 执行测试
        CommonResult<String> result = smsTaskService.batchStartManualTask(batchRequest);

        // 验证结果
        assertFalse(result.isSuccess());
        assertTrue(result.getData().contains("1条启用成功"));
        assertTrue(result.getData().contains("1条启用失败"));
    }

    /**
     * 测试batchStopManualTask方法 - 空列表
     */
    @Test
    public void testBatchStopManualTask_EmptyList() {
        // 准备测试数据
        BatchTaskModifyStatusReq batchRequest = new BatchTaskModifyStatusReq();
        batchRequest.setTaskModifyStatusReqList(Collections.emptyList());

        // 执行测试
        CommonResult<String> result = smsTaskService.batchStopManualTask(batchRequest);

        // 验证结果
        assertTrue(result.isSuccess());
        assertTrue(result.getData().contains("要停用的任务数量为空"));
    }

    /**
     * 测试batchStopManualTask方法 - 全部成功
     */
    @Test
    public void testBatchStopManualTask_AllSuccess() {
        // 准备测试数据
        TaskModifyStatusReq req1 = new TaskModifyStatusReq();
        req1.setTaskCode("task1");
        TaskModifyStatusReq req2 = new TaskModifyStatusReq();
        req2.setTaskCode("task2");

        BatchTaskModifyStatusReq batchRequest = new BatchTaskModifyStatusReq();
        batchRequest.setTaskModifyStatusReqList(Arrays.asList(req1, req2));

        // Mock applicationContext.getBean 返回当前实例
        when(applicationContext.getBean(SmsTaskServiceImpl.class)).thenReturn(smsTaskService);

        // Mock modifyManualTaskStatus 方法，都成功
        doReturn(CommonResult.success(true)).when(smsTaskService).modifyManualTaskStatus(req1);
        doReturn(CommonResult.success(true)).when(smsTaskService).modifyManualTaskStatus(req2);

        // 执行测试
        CommonResult<String> result = smsTaskService.batchStopManualTask(batchRequest);

        // 验证结果
        assertTrue(result.isSuccess());
        assertTrue(result.getData().contains("2条停用成功"));
    }

    /**
     * 测试queryTaskPageList方法 - 带任务名称条件
     */
    @Test
    public void testQueryTaskPageList_WithTaskName() {
        TaskPageListReq req = new TaskPageListReq();
        req.setTaskName("testTaskName");
        req.setPageNo(1);
        req.setPageSize(10);

        Page<NotificationTask> result = smsTaskService.queryTaskPageList(req);

        // 由于方法内部使用了复杂的查询逻辑，这里主要验证方法能正常执行
        assertThat(result).isNotNull();
    }

    /**
     * 测试queryTaskPageList方法 - 带状态条件
     */
    @Test
    public void testQueryTaskPageList_WithStatus() {
        TaskPageListReq req = new TaskPageListReq();
        req.setStatus(TaskStatusEnum.START.getStatus());
        req.setPageNo(1);
        req.setPageSize(10);

        Page<NotificationTask> result = smsTaskService.queryTaskPageList(req);

        // 由于方法内部使用了复杂的查询逻辑，这里主要验证方法能正常执行
        assertThat(result).isNotNull();
    }

    /**
     * 测试getManualTaskStatusVO方法
     */
    @Test
    public void testGetManualTaskStatusVO() {
        // 执行测试
        List<TaskStatusVO> result = smsTaskService.getManualTaskStatusVO();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.size() > 0);

        // 验证包含所有状态
        boolean hasStart = result.stream()
                .anyMatch(vo -> TaskStatusEnum.START.getStatus().equals(vo.getStatus()));
        assertTrue(hasStart);

        boolean hasStop = result.stream()
                .anyMatch(vo -> TaskStatusEnum.STOP.getStatus().equals(vo.getStatus()));
        assertTrue(hasStop);
    }

    /**
     * 测试deleteFile方法 - 正常删除文件
     */
    @Test
    public void testDeleteFile_Success() {
        // Mock 查询任务返回已停用的任务
        NotificationTask task = new NotificationTask();
        task.setStatus(TaskStatusEnum.STOP.getStatus());
        task.setMessageFile("messageFile.xlsx");
        task.setErrorMsgFile("errorFile.xlsx");
        when(taskRepository.queryTaskByCode("stoppedTaskCode")).thenReturn(task);

        // Mock 更新任务成功
        doReturn(1).when(taskMapper).updateById(any());

        // 执行方法
        CommonResult<Boolean> result = smsTaskService.deleteFile("stoppedTaskCode");

        // 验证结果
        assertThat(result).isEqualTo(CommonResult.success(true));
        verify(taskMapper, times(1)).updateById(task);
        verify(excelService, times(1)).deleteExcel("messageFile.xlsx");
        verify(excelService, times(1)).deleteExcel("errorFile.xlsx");
    }

    /**
     * 测试checkAdd方法 - 模板代码为空但需要提交
     */
    @Test
    public void testCheckAdd_TemplateCodeNullButSubmit() {
        TaskCreatReq taskCreatReq = new TaskCreatReq();
        taskCreatReq.setAddDataType(BusinessEnum.OperationType.SAVE_AND_SUBMIT.getType());
        taskCreatReq.setMessageFile("messageFile");
        taskCreatReq.setMessageTemplateCode(null); // 模板代码为空

        Exception exception = assertThrows(ServiceException.class, () -> {
            smsTaskService.checkAdd(taskCreatReq);
        });

        assertEquals("模板编码不能为空", exception.getMessage());
    }

    /**
     * 测试checkAdd方法 - 消息文件为空但需要提交
     */
    @Test
    public void testCheckAdd_MessageFileNullButSubmit() {
        TaskCreatReq taskCreatReq = new TaskCreatReq();
        taskCreatReq.setAddDataType(BusinessEnum.OperationType.SAVE_AND_SUBMIT.getType());
        taskCreatReq.setMessageFile(null); // 消息文件为空
        taskCreatReq.setMessageTemplateCode("templateCode");

        Exception exception = assertThrows(ServiceException.class, () -> {
            smsTaskService.checkAdd(taskCreatReq);
        });

        assertEquals("任务无法提交，请先上传手机号文件", exception.getMessage());
    }

    /**
     * 测试checkAdd方法 - 模板不存在
     */
    @Test
    public void testCheckAdd_TemplateNotExists() {
        TaskCreatReq taskCreatReq = new TaskCreatReq();
        taskCreatReq.setAddDataType(BusinessEnum.OperationType.SAVE_AND_SUBMIT.getType());
        taskCreatReq.setMessageFile("messageFile");
        taskCreatReq.setMessageTemplateCode("nonExistentTemplate");

        when(templateRepository.existsTemplateCodeByType("nonExistentTemplate",
                SmsTemplateTypeEnum.MANUAL.getCode())).thenReturn(false);

        Exception exception = assertThrows(ServiceException.class, () -> {
            smsTaskService.checkAdd(taskCreatReq);
        });

        assertEquals("选择的手动通知模板无效或不存在", exception.getMessage());
    }

    /**
     * 测试createManualTaskDo方法
     */
    @Test
    public void testCreateManualTaskDo() {
        TaskCreatReq taskCreatReq = new TaskCreatReq();
        taskCreatReq.setTaskName("Test Task");
        taskCreatReq.setTaskSendType(TaskSendTypeEnum.REALTIME.getCode());
        taskCreatReq.setMessageTemplateCode("templateCode");
        taskCreatReq.setSubmitFileType(1);
        taskCreatReq.setSignBrandType("signBrand");
        taskCreatReq.setSendChannelType("sendChannel");
        taskCreatReq.setMessageFile("messageFile");
        taskCreatReq.setErrorMsgFile("errorMsgFile");

        when(mockEcpIdUtil.nextIdStr()).thenReturn("generatedTaskCode");
        when(piplDataUtil.getDecodeText("messageFile")).thenReturn("decodedMessageFile");

        NotificationTask result = smsTaskService.createManualTaskDo(taskCreatReq);

        assertNotNull(result);
        assertEquals("generatedTaskCode", result.getTaskCode());
        assertEquals("Test Task", result.getTaskName());
        assertEquals(TaskSendTypeEnum.REALTIME.getCode(), result.getTaskSendType());
        assertEquals("templateCode", result.getMessageTemplateCode());
    }

}
