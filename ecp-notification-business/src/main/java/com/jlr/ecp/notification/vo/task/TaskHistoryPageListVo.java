package com.jlr.ecp.notification.vo.task;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 *  任务编辑历史的vo
 *
 * <AUTHOR>
 * */

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Schema(description = "任务编辑历史的vo")
public class TaskHistoryPageListVo {

    @Schema(description = "任务编辑记录ID")
    private Long id;

    @Schema(description = "任务code")
    private String taskCode;

    @Schema(description = "修改模块")
    private String modifyModule;

    @Schema(description = "修改字段数量")
    private Integer modifyFieldCount;

    /**
     * 修改字段
     */
    @Schema(description = "修改字段")
    private String modifyField;

    @Schema(description = "是否显示 修改详情")
    private Boolean modifyDetail;

    @Schema(description = "修改前字段值（JSON格式）")
    private String modifyFieldOldValue;

    @Schema(description = "修改后字段值（JSON格式）")
    private String modifyFieldNewValue;

    @Schema(description = "修改时间", required = true)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime operateTime;

    @Schema(description = "操作人")
    private String operateUser;
}
