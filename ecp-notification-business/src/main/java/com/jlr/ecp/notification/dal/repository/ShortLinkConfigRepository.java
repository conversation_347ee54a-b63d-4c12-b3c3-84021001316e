package com.jlr.ecp.notification.dal.repository;

import com.jlr.ecp.notification.dal.dataobject.shortlink.NotificationConfigDO;

public interface ShortLinkConfigRepository {
    /**
     * 插入短链接配置
     *
     * @param notificationConfigDO 短链接配置信息对象，包含待插入的配置数据
     * @return 操作结果，true表示成功插入，false表示插入失败
     */
    Boolean insertShortLinkConfig(NotificationConfigDO notificationConfigDO);

    /**
     * 更新短链接配置
     *
     * @param notificationConfigDO 待更新的通知配置对象，包含需要更新的短链接设置信息
     * @return Boolean 表示通知配置更新操作是否成功的布尔值
     */
    Boolean updateShortLinkConfig(NotificationConfigDO notificationConfigDO);

    /**
     * 根据ID和品牌代码查询短链接配置
     *
     * @param brandCode 品牌代码，用于筛选特定品牌的配置
     * @return 返回查询到的短链接配置信息，如果未找到则可能返回null
     */
    NotificationConfigDO queryShortLinkConfigByBrandCode(String brandCode);
}
