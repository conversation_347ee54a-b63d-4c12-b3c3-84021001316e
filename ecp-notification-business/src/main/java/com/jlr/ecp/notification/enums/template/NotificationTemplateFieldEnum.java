package com.jlr.ecp.notification.enums.template;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

@AllArgsConstructor
@Getter
public enum NotificationTemplateFieldEnum {

    // 编辑模板信息模块
    EDIT_TEMPLATE_NAME(1, "模板名称", "编辑模板信息", true),
    EDIT_NOTIFY_CONTENT(2, "通知内容", "编辑模板信息", true),

    // 编辑场景说明模块
    EDIT_SCENE_DESCRIPTION(3, "编辑场景说明", "编辑场景说明", true);

    private final Integer code;
    private final String fieldName;     // 字段名（用于日志、展示）
    private final String moduleName;    // 修改模块
    private final Boolean showDetail;   // 是否显示修改详情

    /**
     * 获取所有需要显示修改详情的字段名集合
     */
    public static Set<String> getAllDetailFields() {
        return Arrays.stream(values())
                .filter(e -> e.showDetail)
                .map(e -> e.fieldName)
                .collect(Collectors.toSet());
    }
}