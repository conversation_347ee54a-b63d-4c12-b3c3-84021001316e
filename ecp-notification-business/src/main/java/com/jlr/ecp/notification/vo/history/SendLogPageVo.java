package com.jlr.ecp.notification.vo.history;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *  发送日志分页VO
 *
 * <AUTHOR>
 * */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "发送日志分页VO")
public class SendLogPageVo {
    @Schema(description = "任务批次号")
    private String instanceCode;

    @Schema(description = "任务名称")
    private String taskName;

    @Schema(description = "发送类型 1自动任务 2手动任务")
    private String taskType;

    @Schema(description = "定时发送时间CRON;当发送类型为定时发送时的时间的CRON表达式")
    private String taskSendTime;

    @Schema(description = "总推送条数")
    private Integer sendTotalCount;


    @Schema(description = "触达成功条数")
    private Integer sendSuccessCount;


    @Schema(description = "触达失败条数")
    private Integer sendFailCount;

    @Schema(description = "短链点击数")
    private Integer openShortLinkCount;

    @Schema(description = "短链点击率")
    private String openShortLinkRate;
}
