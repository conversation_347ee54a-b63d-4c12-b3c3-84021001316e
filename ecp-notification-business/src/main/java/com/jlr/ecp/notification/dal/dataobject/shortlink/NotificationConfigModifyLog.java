package com.jlr.ecp.notification.dal.dataobject.shortlink;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_notification_config_modify_log")
public class NotificationConfigModifyLog extends BaseDO {
    @TableId
    private Long id;

    /**
     *  配置主键ID
     * */
    @TableField("config_id")
    private Long configId;

    /**
     * 修改内容
     * */
    @TableField("modify_content")
    private String modifyContent;

    /**
     *  修改时间
     * */
    @TableField("modify_date")
    private LocalDateTime modifyDate;

    /**
     * 修改人
     * */
    @TableField("modify_user")
    private String modifyUser;

    /**
     *  租户号
     * */
    @TableField("tenant_id")
    private Integer tenantId;
}
