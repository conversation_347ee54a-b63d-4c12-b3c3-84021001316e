package com.jlr.ecp.notification.dal.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.notification.dal.dataobject.task.TaskModifyLog;
import com.jlr.ecp.notification.req.task.TaskHistoryReq;

public interface TaskModifyRepository extends IService<TaskModifyLog> {

    /**
     * 分页查询编辑发送任务历史记录
     * @param taskHistoryReq 发送任务请求入参
     * @return  Page<NotificationHistory>
     * */
    Page<TaskModifyLog> queryTaskHistoryPageList(TaskHistoryReq taskHistoryReq);

    /**
     * 插入任务修改日志
     *
     * @param taskModifyLog 要插入的任务修改日志对象，包含任务修改的相关信息
     * @return 返回一个布尔值，表示日志是否成功插入true表示成功，false表示失败
     */
    boolean insertTaskModifyLog(TaskModifyLog taskModifyLog);
}
