package com.jlr.ecp.notification.dto.crc;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Schema(description = "保存CRC未读取的消息")
public class SaveCrcUnreadMsgDTO {

    /**
     *  用户全局唯一JLRID
     * */
    @Schema(description = "用户全局唯一JLRID")
    @NotBlank
    private String jlrid;

    /**
     *  唯一ID, UUID或雪花算法ID
     * */
    @Schema(description = "唯一ID, UUID或雪花算法ID")
    @NotBlank
    private String messageId;

    /**
     *  1：文字 2：图片 3：视频 4：音频
     * */
    @Schema(description = "1：文字 2：图片 3：视频 4：音频")
    @NotBlank
    private String messageType;

    /**
     *  文字时有内容，图片\视频\音频需要跟CRC沟通是否是URL
     * */
    @Schema(description = "文字时有内容，图片、视频、音频需要跟CRC沟通是否是URL")
    private String messageContent;

    /**
     *  消息时间UTC8,YYYY-MM-DD HH:mm:ss
     * */
    @Schema(description = "文字时有内容，图片、视频、音频需要跟CRC沟通是否是URL")
    @NonNull
    private LocalDateTime messageTime;

    /**
     * 业务线编码
     * 用于标识消息所属的业务线
     * 可选值:
     * - VCS: BUSINESS:001
     * - LRE: LRE
     * - BrandedGoods: BrandedGoods
     * TODO: 该字段通过小程序URL参数传递，由CRC系统透传给消息服务
     */
    @Schema(description = "业务线编码")
    private String businessCode;
}
