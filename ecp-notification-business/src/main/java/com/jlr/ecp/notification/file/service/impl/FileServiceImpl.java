package com.jlr.ecp.notification.file.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.jlr.ecp.framework.common.util.io.FileUtils;
import com.jlr.ecp.framework.file.core.utils.FileTypeUtils;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.file.core.client.FileClient;
import com.jlr.ecp.notification.file.dao.FileDO;
import com.jlr.ecp.notification.file.mapper.FileMapper;
import com.jlr.ecp.notification.file.service.FileConfigService;
import com.jlr.ecp.notification.file.service.FileService;
import com.jlr.ecp.notification.file.vo.FilePageReqVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.jlr.ecp.system.enums.ErrorCodeConstants.FILE_NOT_EXISTS;

/**
 * 文件 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FileServiceImpl implements FileService {

    @Resource
    private FileConfigService fileConfigService;

    @Resource
    private FileMapper fileMapper;

    @Override
    public PageResult<FileDO> getFilePage(FilePageReqVO pageReqVO) {
        return fileMapper.selectPage(pageReqVO);
    }

    @Override
    @SneakyThrows
    public String createFile(String name, String path, byte[] content, String code) {
        // 计算默认的 path 名
        String type = FileTypeUtils.getMineType(content, name);
        if (StrUtil.isEmpty(path)) {
            path = FileUtils.generatePath(content, name);
        }
        // 如果 name 为空，则使用 path 填充
        if (StrUtil.isEmpty(name)) {
            name = path;
        }
        FileClient client = null;

        // 上传到文件存储器
        if (StrUtil.isEmpty(code)) {
            client =  fileConfigService.getMasterFileClient();
        }else {
            client = fileConfigService.getFileClient(code);
        }
        Assert.notNull(client, "客户端(master) 不能为空");
        String url = client.upload(content, path, type);

        // 保存到数据库
        FileDO file = new FileDO();
        file.setConfigId(client.getId());
        file.setName(name);
        file.setPath(path);
        file.setUrl(url);
        file.setType(type);
        file.setSize(content.length);
        fileMapper.insert(file);
        return url;
    }

    @Override
    public void deleteFile(Long id) throws Exception {
        // 校验存在
        FileDO file = validateFileExists(id);

        // 从文件存储器中删除
        FileClient client = fileConfigService.getFileClient(file.getConfigId());
        Assert.notNull(client, "客户端({}) 不能为空", file.getConfigId());
        client.delete(file.getPath());

        // 删除记录
        fileMapper.deleteById(id);
    }

    private FileDO validateFileExists(Long id) {
        FileDO fileDO = fileMapper.selectById(id);
        if (fileDO == null) {
            throw exception(FILE_NOT_EXISTS);
        }
        return fileDO;
    }

    @Override
    public byte[] getFileContent(String configCode, String path) throws Exception {
        FileClient client = null;
        if (StrUtil.isEmpty(configCode)) {
            client =  fileConfigService.getMasterFileClient();
        }else {
            client = fileConfigService.getFileClient(configCode);
        }
        Assert.notNull(client, "客户端({}) 不能为空", configCode);
        return client.getContent(path);
    }

    @Override
    public Boolean deleteFile(String configCode, String path) {
        FileClient client = null;
        try {
            // 上传到文件存储器
            if (StrUtil.isEmpty(configCode)) {
                client =  fileConfigService.getMasterFileClient();
            }else {
                client = fileConfigService.getFileClient(configCode);
            }
            //发送删除
            client.delete(path);
        } catch (Exception e) {
            log.error("从s3删除文件失败, configCode:{}, path:{}, 原因：", configCode, path, e);
            return false;
        }
        return true;
    }

}
