package com.jlr.ecp.notification.controller.admin;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.notification.dto.NotificationLogSingleDTO;
import com.jlr.ecp.notification.req.history.SendLogPageReq;
import com.jlr.ecp.notification.service.SendHistoryService;
import com.jlr.ecp.notification.vo.history.NotificationLogSingleVO;
import com.jlr.ecp.notification.vo.history.SendLogPageVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


@Tag(name = "通知日志历史记录")
@RestController
@RequestMapping("/send/history")
@Slf4j
public class SendHistoryLogController {
    @Resource
    private SendHistoryService historyService;

    @PostMapping("/page/list")
    @Operation(summary = "通知日志分页列表")
    @PreAuthorize("@ss.hasPermission('report:statisticsnotification:list')")
    public CommonResult<PageResult<SendLogPageVo>> historyPageList(@RequestBody SendLogPageReq sendLogPageReq) {
        log.info("通知日志分页列表, sendLogPageReq:{}", sendLogPageReq);
        return historyService.sendHistoryPageList(sendLogPageReq);
    }


    @GetMapping("/query/instance/code")
    @Operation(summary = "查询instanceCode")
    @PreAuthorize("@ss.hasPermission('report:statisticsnotification:list')")
    public CommonResult<String> historyPageList(@RequestParam String instanceCode) {
        log.info("通知日志分页列表, instanceCode:{}", instanceCode);
        String resp = historyService.queryByInstanceCode(instanceCode);
        return CommonResult.success(resp);
    }


    @GetMapping("/names")
    @Operation(summary = "过滤自动事件型，通知任务名称下拉列表")
    @Parameter(name = "taskType", description = "任务类型 1自动任务 2手动任务 默认不传", required = false, example = "1")
    @PreAuthorize("@ss.hasPermission('report:statisticsnotification:list')")
    public CommonResult<List<String>> getTaskNameList(@RequestParam(required = false) Integer taskType) {
        return CommonResult.success(historyService.queryTaskNameList(taskType));
    }

    @PostMapping("/single")
    @Operation(summary = "个案查询")
    @PreAuthorize("@ss.hasAnyPermissions('report:sourcedata-notification:forms','report:sourcedata-notification-bg:forms','report:sourcedata-notification-lre:forms')")
    public CommonResult<List<NotificationLogSingleVO>> getSmsRecordListBySingle(@RequestBody NotificationLogSingleDTO singleDTO ) {
        return historyService.querySmsRecordListBySingle(singleDTO);
    }
}
