package com.jlr.ecp.notification.kafka.listener;

import com.alibaba.fastjson.JSON;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.enums.BrandCodeEnum;
import com.jlr.ecp.notification.kafka.message.ValetOrderMessage;
import com.jlr.ecp.notification.kafka.send.template.SmsSendTemplate;
import com.jlr.ecp.notification.service.ShortLinkService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class ValetOrderListener {

    @Resource
    private SmsSendTemplate smsSendTemplate;

    @Resource
    private ShortLinkService shortLinkService;

    @KafkaListener(topics = "valet-order-topic", groupId = "valet-order-group", batch = "true",
            properties = "max.poll.records:100")
    public void listen(List<ConsumerRecord<String, String >> records) {
        log.info("代客下单消息，records:{}", records);
        List<ValetOrderMessage> valetOrderMessageList = buildValetOrderMessageList(records);
        for (ValetOrderMessage valetOrderMessage : valetOrderMessageList) {
            smsSendTemplate.sendValetOrderMsg(valetOrderMessage);
        }
    }


    /**
     *  构建代客下单消息列表
     *
     * @param records 消费者记录列表，包含主题、分区、偏移量、键和值等信息
     * @return 礼宾订单消息列表，包含解析后的礼宾订单消息对象
     */
    public List<ValetOrderMessage> buildValetOrderMessageList(List<ConsumerRecord<String, String >> records) {
        List<ValetOrderMessage> valetOrderMessageList = new ArrayList<>();
        if (CollectionUtils.isEmpty(records)) {
            return valetOrderMessageList;
        }
        for (ConsumerRecord<String, String> record : records) {
            ValetOrderMessage valetOrderMessage = JSON.parseObject(record.value(), ValetOrderMessage.class);
            valetOrderSetWxUrl(valetOrderMessage);
            valetOrderMessageList.add(valetOrderMessage);
        }
        return valetOrderMessageList;
    }

    /**
     * 根据品牌Code设置代客下单微信订单链接
     *
     * @param valetOrderMessage 代驾订单消息对象，包含订单相关信息，如品牌代码、订单号、短链接等
     */
    public void valetOrderSetWxUrl(ValetOrderMessage valetOrderMessage) {
        CommonResult<String> wxUrlResp = null;
        if (BrandCodeEnum.LAND_ROVER.getCode().equals(valetOrderMessage.getBrandCode())) {
            String path = valetOrderMessage.getUrl() + "?orderCode=" + valetOrderMessage.getOrderNumber()
                    + "&shortLink=" + valetOrderMessage.getShortLink()
                    + "&orderChannel=" + valetOrderMessage.getOrderChannel();
            wxUrlResp = shortLinkService.genShortLink(path);
        } else if (BrandCodeEnum.JAGUAR.getCode().equals(valetOrderMessage.getBrandCode())) {
            String query = "orderCode=" + valetOrderMessage.getOrderNumber()
                    + "&shortLink=" + valetOrderMessage.getShortLink()
                    + "&orderChannel=" + valetOrderMessage.getOrderChannel();
            wxUrlResp = shortLinkService.genJaguarLink(valetOrderMessage.getUrl(), query);
        }
        log.info("根据品牌Code设置代客下单微信订单链接, wxUrlResp:{}", wxUrlResp);
        if (Objects.nonNull(wxUrlResp)) {
            valetOrderMessage.setWxUrl(wxUrlResp.getData());
        }
    }
}
