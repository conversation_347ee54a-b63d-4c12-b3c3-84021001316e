spring:
  application:
    name: ecp-notification-service
  profiles:
    active: ${profiles.active}
#    active: local
##  autoconfigure:
##    exclude: org.redisson.spring.starter.RedissonAutoConfiguration
server:
  port: 8080

# 日志文件配置。注意，如果 logging.file.name 不放在 bootstrap.yaml 配置文件，而是放在 application.yaml 中，会导致出现 LOG_FILE_IS_UNDEFINED 文件
logging:
  file:
    name: ${user.home}/logs/${spring.application.name}.log # 日志文件名，全路径

