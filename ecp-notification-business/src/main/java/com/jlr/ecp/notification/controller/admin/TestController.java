//package com.jlr.ecp.notification.controller.admin;
//
//import com.alibaba.fastjson.JSON;
//import com.jlr.ecp.framework.common.pojo.CommonResult;
//import com.jlr.ecp.framework.kafka.producer.ProducerTool;
//import com.jlr.ecp.notification.api.NotificationServiceApiImpl;
//import com.jlr.ecp.notification.api.dto.MsgBlackListDTO;
//import com.jlr.ecp.notification.api.notification.NotificationServiceApi;
//import com.jlr.ecp.notification.dal.dataobject.history.SendHistoryDetail;
//import com.jlr.ecp.notification.dal.mysql.history.HistoryDetailMapper;
//import com.jlr.ecp.notification.enums.sms.ShortLinkPathEnum;
//import com.jlr.ecp.notification.kafka.message.ValetOrderMessage;
//import com.jlr.ecp.notification.resp.ReportResp;
//import com.jlr.ecp.notification.service.ShortLinkService;
//import com.jlr.ecp.notification.service.UpdateSmsUserReachService;
//import com.jlr.ecp.notification.service.manual.ManualTaskSendService;
//import com.jlr.ecp.notification.util.InstanceCodeGenerator;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.web.multipart.MultipartFile;
//
//import javax.annotation.Resource;
//import javax.annotation.security.PermitAll;
//import java.net.URLEncoder;
//import java.time.LocalDateTime;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//import java.util.UUID;
//
//@RestController
//@Slf4j
//public class TestController {
//
//    @Autowired
//    ProducerTool producerTool;
//
//    @Resource
//    ShortLinkService shortLinkService;
//
//    @Resource
//    private HistoryDetailMapper historyDetailMapper;
//
//    @Resource
//    private UpdateSmsUserReachService reachService;
//
//    @Resource
//    private InstanceCodeGenerator instanceCodeGenerator;
//
//    @Resource
//    private NotificationServiceApiImpl notificationServiceApi;
//
//
//    @PermitAll
//    @GetMapping(value = "/testmsg")
//    public String testmsg()
//    {
//        String str = String.format("data is %s",new Date().toString());
//        producerTool.sendMsg("test-topic","",
//                str);
//        return str;
//    }
//
//    @PermitAll
//    @GetMapping(value = "/testShorLink")
//    public String testShorLink()
//    {
//        // CommonResult<String> result = shortLinkService.genExpireRemindSMSShortLink(URLEncoder.encode(ShortLinkPathEnum.RENEWAL_REMINDER.getPath()), "product:001", "vinCode:003");
//        String productCode = "BS003465B0FE9DE4B078B86251CB34";
//        String vinCode = "vinCode:003";
//        String carType = URLEncoder.encode("路虎");
//        System.out.println("转义后的值为：" + carType);
//
//        String path = ShortLinkPathEnum.RENEWAL_REMINDER.getPath();
//        path = path + "?productCode=" + productCode + "&vinCode=" + vinCode + "&carType=" + carType;
//        CommonResult<String> result = shortLinkService.genShortLink(path);
//
//        return result.getCheckedData();
//    }
//
//    @PermitAll
//    @PostMapping()
//    public CommonResult<List<MsgBlackListDTO>> getMsgBlackList(@RequestBody List<String> carVinList) {
//        return notificationServiceApi.checkCarVinBlackList(carVinList);
//    }
//
//    @PermitAll
//    @GetMapping("/batch/insert")
//    public void testBatchInsert(){
//        List<SendHistoryDetail> detailList = getDetailList();
//        historyDetailMapper.insertBatch(detailList);
//    }
//
//    @PermitAll
//    @PostMapping("/test/reach/update")
//    public void testReachUpdate(@RequestBody List<ReportResp> reportRespList) {
//        reachService.updateSmsUserReach(reportRespList);
//    }
//
//    private List<SendHistoryDetail> getDetailList() {
//        List<SendHistoryDetail> detailList = new ArrayList<>();
//        SendHistoryDetail detail1 = SendHistoryDetail.builder()
//                .taskInstanceCode("123")
//                .sendMessage("qwer")
//                .sendPhone("123")
//                .sendTime(LocalDateTime.now())
//                .sendResult(2)
//                .taskSendType(1)
//                .tenantId(1)
//                .build();
//
//        SendHistoryDetail detail2 = SendHistoryDetail.builder()
//                .sendTime(LocalDateTime.now())
//                .sendResult(2)
//                .taskSendType(1)
//                .tenantId(1)
//                .build();
//        detailList.add(detail1);
//        detailList.add(detail2);
//        return detailList;
//    }
//
//
//    @GetMapping("/test/instance/code")
//    @PermitAll
//    public String getInstanceCode(String taskType) {
//        return instanceCodeGenerator.generateTaskBatchId(taskType);
//    }
//
//    @GetMapping("/test/lan/shortLink/")
//    @PermitAll
//    public CommonResult<String> lanShortLink(@RequestParam("productCode") String productCode) {
//        log.info("路虎pdp的短链生成, productCode:{}", productCode);
//        String path = "ecp/pages/subscribeTo/shopDetails/shopDetails";
//        path = path + "?productCode=" + productCode;
//        CommonResult<String> lanUrl = shortLinkService.genShortLink(path);
//        log.info("路虎pdp的短链生成, path:{}, 结果lanUrl:{}", path, lanUrl);
//        return lanUrl;
//    }
//
//    @GetMapping("/test/jar/shortLink/")
//    @PermitAll
//    public CommonResult<String> jarSHortLink(@RequestParam("productCode") String productCode) {
//        log.info("捷豹pdp的短链生成, productCode:{}", productCode);
//        String path = "ecp/pages/subscribeTo/shopDetails/shopDetails";
//        String query = "productCode=" + productCode;
//        CommonResult<String> jarUrl = shortLinkService.genJaguarLink(path, query);
//        log.info("捷豹pdp的短链生成, path:{}, query:{}, 结果jarUrl:{}", path, query, jarUrl);
//        return jarUrl;
//    }
//
//    @GetMapping("/test/valetOrder/")
//    @PermitAll
//    public void testValetOrder() {
//        ValetOrderMessage valetOrderMessage = buildValetOrderMessage();
//        log.info("开始发送代客下单消息, valetOrderMessage:{}", valetOrderMessage);
//        producerTool.sendMsg("valet-order-topic", valetOrderMessage.getPhoneNumber(), JSON.toJSONString(valetOrderMessage));
//        log.info("发送代客下单消息成功");
//    }
//
//    private ValetOrderMessage buildValetOrderMessage() {
//        return ValetOrderMessage.builder()
//                .url("ecp/pages/mine/order/detail/index")
//                .shortLink(1)
//                .messageId(UUID.randomUUID().toString().replace("-", ""))
//                .phoneNumber("15310360914")
//                .templateCode("ValetOrderMessageTemplate")
//                .tenantId(1L)
//                .serviceName("代客下单服务")
//                .brandCode(1)
//                .orderNumber("LRC00120250114182045001_ORDER")
//                .carVin("ECPTESTING0000050")
//                .build();
//    }
//}
