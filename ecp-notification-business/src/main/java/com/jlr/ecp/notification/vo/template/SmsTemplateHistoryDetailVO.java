package com.jlr.ecp.notification.vo.template;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Schema(description = "模板编辑详情")
public class SmsTemplateHistoryDetailVO {
    @Schema(description = "操作时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime operateTime;

    @Schema(description = "操作人")
    private String operateUser;

    @Schema(description = "日志ID")
    private Long id;

    @Schema(description = "修改前字段值（JSON格式）")
    private String modifyFieldOldValue;

    @Schema(description = "修改后字段值（JSON格式）")
    private String modifyFieldNewValue;



//    @Schema(description = "字段变更明细")
//    private List<FieldChange> changes;

//    @Data
//    public static class FieldChange {
//        @Schema(description = "字段名称")
//        private String fieldName;
//
//        @Schema(description = "旧值")
//        private String oldValue;
//
//        @Schema(description = "新值")
//        private String newValue;
//
//        public FieldChange(String fieldName, String oldValue, String newValue) {
//            this.fieldName = fieldName;
//            this.oldValue = oldValue;
//            this.newValue = newValue;
//        }
//    }
}