package com.jlr.ecp.notification.enums;

import com.jlr.ecp.framework.common.enums.RpcConstants;
/**
 * API 相关的枚举
 *
 * <AUTHOR>
 */
public class ApiConstants {

    /**
     * 服务名
     *
     * 注意，需要保证和 spring.application.name 保持一致
     */
   public static final String NAME = "ecp-notification-service";

   // public static final String NAME = "127.0.0.1:8999";


    public static final String PREFIX = RpcConstants.RPC_API_PREFIX +  "/notification";

    public static final String VERSION = "1.0.0";

}
