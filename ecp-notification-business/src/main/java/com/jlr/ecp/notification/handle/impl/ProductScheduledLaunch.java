package com.jlr.ecp.notification.handle.impl;

//import com.jlr.ecp.subscription.service.spu.ProductSpuInfoService;

/**
 * 创建商品定时上架处理类
 */
//@Component("productScheduledLaunchBean")
//@Slf4j
//public class ProductScheduledLaunch implements RedisDelayQueueHandle<String> {
//
//    /**
//     * 这是处理商品相关操作的服务
//     */
//    @Resource
//    private ProductSpuInfoService productSpuInfoService;
//
//    /**
//     * 调用商品上架方法
//     *
//     * @param value
//     */
//    @Override
//    public void execute(String value) {
//        log.info("Processing scheduled product launch: {}", value);
//
//        // 假设value的格式是 "product:up:" 加上 productCode
//        String prefix = "product:up:";
//
//        // 检查value是否确实以预期的前缀开始
//        if (value.startsWith(prefix)) {
//            // 提取productCode
//            String productCode = value.substring(prefix.length());
//
//            // 调用服务方法
//            productSpuInfoService.launchProducts(productCode);
//        }
//    }
//}