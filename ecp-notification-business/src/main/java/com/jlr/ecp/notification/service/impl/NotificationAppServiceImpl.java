package com.jlr.ecp.notification.service.impl;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.dal.repository.NotificationHistoryRepository;
import com.jlr.ecp.notification.dal.repository.SendHistoryDetailRepository;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkDTO;
import com.jlr.ecp.notification.service.NotificationAppService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class NotificationAppServiceImpl implements NotificationAppService {
    @Resource
    private SendHistoryDetailRepository detailRepository;

    @Resource
    private NotificationHistoryRepository historyRepository;

    /**
     * 更新短链状态。
     *
     * @param shortLinkDTO 短链数据传输对象，包含实例代码和电话号码。
     * @return 返回操作成功与否的布尔值。如果输入参数不合法或更新过程中发生异常，则返回false；否则返回true。
     */
    @Override
    public CommonResult<Boolean> updateSmsShortLink(ShortLinkDTO shortLinkDTO) {
        Boolean resp = detailRepository.updateShortLinkStatus(shortLinkDTO);
        Long openCount = detailRepository.queryOpenCountByInstanceCode(shortLinkDTO.getInstanceCode());
        historyRepository.updateHistoryOpenShortLinkCount(shortLinkDTO.getInstanceCode(), openCount.intValue());
        return CommonResult.success(resp);
    }
}
