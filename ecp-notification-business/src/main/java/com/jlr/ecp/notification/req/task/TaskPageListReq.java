package com.jlr.ecp.notification.req.task;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 *  通知任务列表页请求入参
 *
 * <AUTHOR>
 * */
@Data
@Schema(description = "任务分页查询入参")
public class TaskPageListReq extends PageParam {

    @Schema(description = "任务编码")
    private String taskCode;

    @Schema(description = "任务类型 1自动任务 2手动任务")
    private Integer taskType;

    @Schema(description = "启用时间排序 0-正序 1-倒叙")
    private Integer startTimeSortType;

    @Schema(description = "停用时间排序 0-正序 1-倒叙")
    private Integer endTimeSortType;

    @Schema(description = "状态排序 0-正序 1-倒叙")
    private Integer statusSortType;

    @Schema(description = "任务名称")
    private String taskName;

    @Schema(description = "业务线")
    private String businessCode;

    @Schema(description = "任务状态列表")
    private List<Integer> statusList;

    @Schema(description = "是否是草稿 0否 1是")
    private Integer draftVersion;
}
