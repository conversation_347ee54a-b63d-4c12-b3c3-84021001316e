package com.jlr.ecp.notification.util.sign;

import cn.hutool.core.collection.CollUtil;
import jodd.util.StringUtil;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.binary.StringUtils;
import org.springframework.http.HttpMethod;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;

public abstract class AbstractSignature {
    //参与签名的系统Header前缀,只有指定前缀的Header才会参与到签名中
    public static final String CA_HEADER_TO_SIGN_PREFIX_SYSTEM = "ss-";
    public final static String CA_KEY = "ss-app-key";
    public final static String CA_SIGNATURE_METHOD = "ss-signature-method";
    public final static String CA_SIGNATURE_HEADERS = "ss-signature-headers";
    public final static String CA_SIGNATURE = "ss-signature";
    public final static String CA_NONCE = "ss-nonce";
    public final static String CA_TIMESTAMP = "ss-timestamp";
    //请求Body内容MD5 Header
    public static final String CA_SIGNATURE_CONTENT_MD5 = "ss-content-md5";
    public static char LF = '\n';
    public static final String ENCODING = "UTF-8";
    //签名算法HmacSha256
    public static final String HMAC_SHA256 = "HmacSHA256";
    //请求Header Accept
    public static final String HTTP_HEADER_ACCEPT = "accept";
    //请求Body内容MD5 Header
    public static final String HTTP_HEADER_CONTENT_MD5 = "content-md5";
    //请求Header Content-Type
    public static final String HTTP_HEADER_CONTENT_TYPE = "content-type";
    //请求Header UserAgent
    public static final String HTTP_HEADER_USER_AGENT = "user-agent";
    //请求Header Date
    public static final String HTTP_HEADER_DATE = "date";
    //请求Header Host
    public static final String HTTP_HEADER_HOST = "host";

    public static final int HTTP_SUCCESS_CODE = 200;

    // 表单类型Content-Type
    public static final String CONTENT_TYPE_FORM = "application/x-www-form-urlencoded; charset=utf-8";
    // 流类型Content-Type
    public static final String CONTENT_TYPE_STREAM = "application/octet-stream; charset=utf-8";
    // JSON类型Content-Type
    public static final String CONTENT_TYPE_JSON = "application/json; charset=utf-8";
    // XML类型Content-Type
    public static final String CONTENT_TYPE_XML = "application/xml; charset=utf-8";
    // 文本类型Content-Type
    public static final String CONTENT_TYPE_TEXT = "application/text; charset=utf-8";
    // smarter sales sign error header name
    public static final String SS_HEADER_ERROR_MESSAGE = "ss-sign-error-msg";

    protected String path;
    protected String httpMethod;
    protected Map<String, String> headers;
    protected String secret;
    protected String signatureMethod;

    public AbstractSignature() {
    }

    public AbstractSignature(String path, String httpMethod, Map<String, String> headers, String signatureMethod) {
        this.path = path;
        this.httpMethod = httpMethod;
        this.headers = (null == headers) ? new HashMap<>() : headers;
        this.signatureMethod = (null == signatureMethod) ? HMAC_SHA256 : signatureMethod;
    }

    public abstract void build(Map<String, Object> paramsMap, Map<String, Object> formParamsMap) throws NoSuchAlgorithmException, InvalidKeyException, UnsupportedEncodingException;

    public void build(Map<String, Object> paramsMap) throws NoSuchAlgorithmException, InvalidKeyException, UnsupportedEncodingException {
        build(paramsMap, null);
    }

    public void build() throws NoSuchAlgorithmException, InvalidKeyException, UnsupportedEncodingException {
        build(null, null);
    }

    protected String stringToSign(String stringToSign, String signatureMethod, String secret) throws NoSuchAlgorithmException, UnsupportedEncodingException, InvalidKeyException {
        Mac hmacSha = Mac.getInstance(signatureMethod);
        byte[] keyBytes = secret.getBytes(ENCODING);
        hmacSha.init(new SecretKeySpec(keyBytes, 0, keyBytes.length, signatureMethod));
        return new String(Base64.encodeBase64(hmacSha.doFinal(stringToSign.getBytes(ENCODING))), ENCODING);
    }

    protected String base64AndMD5(byte[] bytes) {
        if (bytes == null) {
            throw new IllegalArgumentException("bytes can not be null");
        }
        try {
            final MessageDigest md = MessageDigest.getInstance("MD5");
            md.reset();
            md.update(bytes);
            byte[] md5Result = md.digest();
            String base64Result = Base64.encodeBase64String(md5Result);
            /*
             * 正常情况下，base64的结果为24位，因与服务器有约定，在超过24位的情况下，截取前24位
             */
            return base64Result.length() > 24 ? base64Result.substring(0, 24) : base64Result;
        } catch (final NoSuchAlgorithmException e) {
            throw new IllegalArgumentException("unknown algorithm MD5");
        }
    }

    protected String buildStringToSign(Map<String, Object> paramsMap, Map<String, Object> formParamsMap) {
        StringBuilder sb = new StringBuilder();
        sb.append(httpMethod.toUpperCase()).append(LF);

        //如果有@"Content-MD5"头，这个头需要参与签名
        if (this.headers.get(CA_SIGNATURE_CONTENT_MD5) != null) {
            sb.append(this.headers.get(CA_SIGNATURE_CONTENT_MD5));
        }
        sb.append(LF);

        //如果有@"Content-Type"头，这个头需要参与签名
        if (this.headers.get(HTTP_HEADER_CONTENT_TYPE) != null) {
            sb.append(this.headers.get(HTTP_HEADER_CONTENT_TYPE));
        }
        sb.append(LF);

        //签名优先读取HTTP_CA_HEADER_DATE，因为通过浏览器过来的请求不允许自定义Date（会被浏览器认为是篡改攻击）
        if (this.headers.get(HTTP_HEADER_DATE) != null) {
            sb.append(this.headers.get(HTTP_HEADER_DATE));
        }
        sb.append(LF);

        //将headers合成一个字符串
        sb.append(buildHeaders()); //ss-signature-method,ss-timestamp

        //将path、queryParam、formParam合成一个字符串
        sb.append(buildResource(this.path, paramsMap, formParamsMap));

        return sb.toString();
    }
    protected String buildStringToSignToWxma(Map<String, Object> paramsMap, Map<String, Object> formParamsMap) {
        StringBuilder sb = new StringBuilder();
        sb.append(httpMethod.toUpperCase()).append(LF);

        //如果有@"Content-MD5"头，这个头需要参与签名
        if (this.headers.get(CA_SIGNATURE_CONTENT_MD5) != null) {
            sb.append(this.headers.get(CA_SIGNATURE_CONTENT_MD5));
        }
        sb.append(LF);

        //如果有@"Content-Type"头，这个头需要参与签名
        if (this.headers.get(HTTP_HEADER_CONTENT_TYPE) != null
                && !Objects.equals(httpMethod.toUpperCase(), HttpMethod.DELETE.toString())
                && !Objects.equals(httpMethod.toUpperCase(),HttpMethod.GET.toString())) {
            sb.append(this.headers.get(HTTP_HEADER_CONTENT_TYPE));
        }
        sb.append(LF);

        //签名优先读取HTTP_CA_HEADER_DATE，因为通过浏览器过来的请求不允许自定义Date（会被浏览器认为是篡改攻击）
        if (this.headers.get(HTTP_HEADER_DATE) != null) {
            sb.append(this.headers.get(HTTP_HEADER_DATE));
        }
        sb.append(LF);

        //将headers合成一个字符串
        sb.append(buildHeaders()); //ss-signature-method,ss-timestamp

        //将path、queryParam、formParam合成一个字符串
        sb.append(buildResource(this.path, paramsMap, formParamsMap));

        return sb.toString();

    }

    /**
     * 将headers合成一个字符串
     * 需要注意的是，HTTP头需要按照字母排序加入签名字符串
     */
    protected abstract String buildHeaders();

    /**
     * 将path、queryParam、formParam合成一个字符串
     */
    private String buildResource(String path, Map<String, Object> paramsMap, Map<String, Object> formParamsMap) {
        StringBuilder result = new StringBuilder();
        result.append(path);
        //使用TreeMap,默认按照字母排序
        TreeMap<String, String> parameter = new TreeMap<>();
        TreeMap<String, String> addParamsMap = addMapParam(paramsMap);
        TreeMap<String, String> addFormParamsMap = addMapParam(formParamsMap);
        if (CollUtil.isNotEmpty(addParamsMap)) {
            parameter.putAll(addParamsMap);
        }
        if (CollUtil.isNotEmpty(addFormParamsMap)) {
            parameter.putAll(addFormParamsMap);
        }
        if (CollUtil.isEmpty(parameter)) {
            return result.toString();
        }
        int idx = 0;
        for (String key : parameter.keySet()) {
            if (idx == 0) {
                result.append("?");
            } else {
                result.append("&");
            }
            result.append(key);
            String value = parameter.get(key);
            if (StringUtil.isNotEmpty(value)) {
                result.append("=").append(value);
            }
            idx++;
        }
        return result.toString();
    }

    private TreeMap<String, String> addMapParam(Map<String, Object> paramMap) {
        TreeMap<String, String> resp = new TreeMap<>();
        if (CollUtil.isEmpty(paramMap)) {
            return resp;
        }
        for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
            if (entry.getValue() != null) {
                resp.put(entry.getKey(), entry.getValue().toString());
            }
        }
        return resp;
    }

    protected void addCaToHeaders(String key, String value) {
        this.headers.put(key, value);
    }

    public Map<String, String> getHeaders() {
        return headers;
    }

    public String getSign() {
        return this.headers.remove(CA_SIGNATURE);
    }

    public String getKey() {
        return this.headers.get(CA_KEY);
    }

    protected String getContentMD5() {
        return this.headers.get(CA_SIGNATURE_CONTENT_MD5);
    }

    protected String getTimestamp() {
        return this.headers.getOrDefault(CA_TIMESTAMP, "0");
    }
}