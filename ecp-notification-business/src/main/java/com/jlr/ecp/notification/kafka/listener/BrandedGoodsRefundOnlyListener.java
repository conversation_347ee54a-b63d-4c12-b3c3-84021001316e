package com.jlr.ecp.notification.kafka.listener;

import com.jlr.ecp.notification.kafka.message.BrandedGoodsRefundOnlyMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 精选好物仅退款通知监听器
 */
@Component
@Slf4j
public class BrandedGoodsRefundOnlyListener extends BaseNotificationListener<BrandedGoodsRefundOnlyMessage> {
    
    @KafkaListener(topics = "${branded-goods.kafka.topics.refund-only}", 
                  groupId = "${branded-goods.kafka.groups.refund-only}", 
                  batch = "true",
                  properties = "max.poll.records:100")
    public void listen(List<ConsumerRecord<String, String>> records) {
        log.info("精选好物仅退款通知消息，records:{}", records);
        processMessages(records, BrandedGoodsRefundOnlyMessage.class);
    }
} 