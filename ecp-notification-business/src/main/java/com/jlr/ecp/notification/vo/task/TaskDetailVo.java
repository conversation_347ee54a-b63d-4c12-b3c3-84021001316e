package com.jlr.ecp.notification.vo.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *  发送任务详情vo
 *
 * <AUTHOR>
 * */

@Schema(description = "手动任务详情vo")
@Data
public class TaskDetailVo {
    @Schema(description = "任务code")
    private String taskCode;

    @Schema(description = "任务名称")
    private String taskName;

    @Schema(description = "触发逻辑")
    private String triggerAction;

    @Schema(description = "定时发送时间CRON;当发送类型为定时发送时的时间的CRON表达式")
    private String taskSendTime;

    @Schema(description = "模板code")
    private String templateCode;

    @Schema(description = "模板类型")
    private String templateType;

    @Schema(description = "模板名称")
    private String templateName;

    @Schema(description = "模板内容")
    private String templateContent;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "任务发送类型")
    private String taskSendTimeType;

    @Schema(description = "上传文件类型 1：手机号 2：VIN+手机号")
    private Integer submitFileType;


    @Schema(description = "消息发送文件URL")
    private String messageFile;

}
