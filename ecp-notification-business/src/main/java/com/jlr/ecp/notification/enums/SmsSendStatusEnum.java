package com.jlr.ecp.notification.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/***
 * 短信发送第三方后返回的状态码
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum SmsSendStatusEnum {
    SUCCESS(200, "Success"),
    CREATED(201, "Created"),
    ACCEPTED(202, "Accepted"),
    BAD_REQUEST(400, "Bad Request"),
    UNAUTHORIZED(401, "Unauthorized"),
    PAYMENT_REQUIRED(402, "Payment Required"),
    METHOD_NOT_ALLOWED(405, "Method Not Allowed"),
    UNSUPPORTED_MEDIA_TYPE(415, "Unsupported Media Type"),
    INTERNAL_ERROR(500, "Internal Server Error"),
    BAD_GATEWAY(502, "Bad Gateway");

    private final Integer code;

    private final String desc;
}
