package com.jlr.ecp.notification.dal.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.notification.dal.dataobject.history.SendHistoryDetail;
import com.jlr.ecp.notification.dto.HistoryDetailIdempotentDTO;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkDTO;

import java.time.LocalDateTime;
import java.util.List;

public interface SendHistoryDetailRepository extends IService<SendHistoryDetail> {

    /**
     * 批量插入发送历史详情记录。
     *
     * @param sendHistoryDetailList 包含待插入的发送历史详情记录的列表。列表中的每个元素都应是一个SendHistoryDetail类型的对象，
     *                             该方法不返回任何值，即没有返回值。
     */
    void batchInsertDetail(List<SendHistoryDetail> sendHistoryDetailList);

    /**
     * 批量通过手机号和实例编码查询发送历史详情
     * @param phoneNumbers 手机号列表
     * @param instanceCode 实例编码
     * @return SendHistoryDetail列表，包含匹配的发送历史详情
     */
    List<SendHistoryDetail> batchQueryDetailByPhoneAndInstanceCode(List<String> phoneNumbers, String instanceCode);

    /**
     * 批量更新发送历史详情记录。
     *
     * @param sendHistoryDetailList 包含待更新的发送历史详情记录的列表。列表中的每个元素都应是一个SendHistoryDetail类型的对象，
     *                              表示一条发送详情记录。该方法将批量更新这些记录到相应的数据库表中
     */
    void batchUpdateByIds(List<SendHistoryDetail> sendHistoryDetailList);


    /**
     * 根据消息ID列表查询发送历史详情列表。
     *
     * @param msgIdList 消息ID列表，用于查询的条件之一。
     * @return 返回匹配查询条件的发送历史详情列表。如果查询过程中出现异常，则返回空列表。
     */
     List<SendHistoryDetail> queryByMsgIdList(List<String> msgIdList);

    /**
     * 获取当天全部需要发送的数据
     * @param taskCode 任务code
     * @param sendResult 发送状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return List<SendHistoryDetail>
     * */
     List<SendHistoryDetail> querySmsDetailByTaskCodeAndTime(String taskCode, Integer sendResult,
                                                             LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取当天全部需要发送的数据
     * @param idList 任务列表
     * @param sendResult 发送状态
     * @return List<SendHistoryDetail>
     * */
    List<SendHistoryDetail> querySmsDetailByIds(List<Long> idList, Integer sendResult);


    /**
     * 查询指定天数内的重试消息详情。
     * 如果提供了天数参数，则查询该天数内的消息详情，若未提供或提供天数大于3，则默认查询最近3天的详情。
     * 如果提供的天数小于等于0，则查询当天的详情。
     * @param day 指定的天数，可用于查询相应时间范围内的消息重试详情。
     * @return 返回一个消息详情列表，包含指定时间范围内的所有等待重试的消息详情。
     */
     List<SendHistoryDetail> queryRetryMsgDetail(Integer day);

    /**
     * 更新短链状态。
     *
     * @param shortLinkDTO 短链数据传输对象，包含实例代码和电话号码。
     * @return 返回操作成功与否的布尔值。如果输入参数不合法或更新过程中发生异常，则返回false；否则返回true。
     */
     Boolean updateShortLinkStatus(ShortLinkDTO shortLinkDTO);

    /**
     * 根据实例编码查询打开次数。
     *
     * @param instanceCode 任务实例编码，用于查询相应的发送历史详情。
     * @return 返回符合条件的打开短链接次数，如果实例编码为空或查询结果为空，则返回0L。
     */
     Long queryOpenCountByInstanceCode(String instanceCode);

    /**
     * 根据幂等性参数查询发送详情
     *
     * @param detailIdempotentDTO 请求的幂等性参数对象，包含任务码、手机号、时间范围和发送状态等信息
     * @return 返回一个列表，包含符合条件的发送历史详情对象
     */
     List<SendHistoryDetail> queryHistoryDetailByIdempotent(HistoryDetailIdempotentDTO detailIdempotentDTO);

    /**
     * 根据手机号查询发送详情(过滤待发送)
     *
     * @param phone 手机号
     * @param businessCode 业务编码
     * @return 返回一个列表，包含符合条件的发送历史详情对象
     */
     List<SendHistoryDetail> getHistoryDetailByPhone(String phone, String businessCode);

    /**
     * 根据VIN查询发送详情(过滤待发)
     *
     * @param carVin 车辆编码
     * @param businessCode 业务编码
     * @return 返回一个列表，包含符合条件的发送历史详情对象
     */
     List<SendHistoryDetail> getHistoryDetailByVin(String carVin, String businessCode);

    Integer querySendCountByInstanceCode(String instanceCode);

    /**
     * 根据实例编码和触达状态查询触达详情的数量
     *
     * @param instanceCode 实例编码，用于标识特定的实例
     * @param reachStatus 触达状态列表，用于过滤查询结果
     * @return 返回触达详情的数量如果查询条件不满足，则返回0
     */
     Integer queryReachByInstanceAndStatus(String instanceCode, List<String> reachStatus);

}
