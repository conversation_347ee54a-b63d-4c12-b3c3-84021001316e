package com.jlr.ecp.notification.kafka.send.template;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.framework.forgeRock.core.dto.AccessTokenResponse;
import com.jlr.ecp.framework.forgeRock.core.util.AccessTokenUtil;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.notification.constant.Constants;
import com.jlr.ecp.notification.dal.dataobject.blacklist.MsgBlackListDO;
import com.jlr.ecp.notification.dal.dataobject.history.SendHistoryDetail;
import com.jlr.ecp.notification.dal.dataobject.task.auto.NotificationAutoTaskDO;
import com.jlr.ecp.notification.dal.dataobject.template.MessageTemplate;
import com.jlr.ecp.notification.dal.dataobject.valet.OrderNotificationDetailDO;
import com.jlr.ecp.notification.dal.mysql.blacklist.MsgBlackListDOMapper;
import com.jlr.ecp.notification.dal.mysql.template.SmsTemplateMapper;
import com.jlr.ecp.notification.dal.repository.NotificationAutoTaskRepository;
import com.jlr.ecp.notification.dto.UpdateSmsSendStatusDTO;
import com.jlr.ecp.notification.enums.BrandCodeEnum;
import com.jlr.ecp.notification.enums.IsDeleteEnum;
import com.jlr.ecp.notification.enums.SmsResultErrorEnum;
import com.jlr.ecp.notification.enums.SmsSendStatusEnum;
import com.jlr.ecp.notification.enums.task.TaskStatusEnum;
import com.jlr.ecp.notification.kafka.message.ActivateMessage;
import com.jlr.ecp.notification.kafka.message.SmsMessage;
import com.jlr.ecp.notification.kafka.message.ValetOrderMessage;
import com.jlr.ecp.notification.kafka.send.SendSmsResp;
import com.jlr.ecp.notification.kafka.send.SingleSendMessage;
import com.jlr.ecp.notification.kafka.send.SmsSendStatistics;
import com.jlr.ecp.notification.resp.UpdateSmsSendStatusResp;
import com.jlr.ecp.notification.service.UpdateSmsUserReachService;
import com.jlr.ecp.notification.util.HttpClientUtil;
import com.jlr.ecp.notification.util.sign.SignatureUtil;
import com.jlr.ecp.notification.util.sms.TemplateUtil;
import com.jlr.ecp.notification.util.sms.TimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class SmsSendTemplate {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private NotificationAutoTaskRepository autoTaskRepository;

    @Resource
    private MsgBlackListDOMapper msgBlackListDOMapper;

    @Resource
    private SmsTemplateMapper templateMapper;

    @Resource
    private SmsSendStatistics smsSendStatistics;

    @Resource
    private AccessTokenUtil accessTokenUtil;

    @Resource
    private UpdateSmsUserReachService updateSmsUserReachService;

    @Resource(name = "scheduledThreadPool")
    private ThreadPoolTaskScheduler taskScheduler;

    private static final String OK = "OK";

    @Value("${send.smsUrl}")
    private String smsBaseUrl;


    @Value("${send.apiKey:NZyNtPIWWCanL2oKTFGizjnIZaJSNTK8MhQLQUD7}")
    private String apiKey;

    @Value("${send.apiSecret:Iunxd1xDDepOTT3VaMHRxeBEZZYNenPtUQod-5Fg}")
    private String apiSecret;

    @Value("${send.path:/business/sms/send/v1.0.0}")
    private String path;

    private static final int SOCKET_TIMEOUT = 60000; // 设置连接和读取超时时间为60秒
    private static final int CONNECT_TIMEOUT = 60000;

    /**
     * 短信通知账号
     **/
    @Value("${sms.notification.account}")
    private String smsNotificationAccount;

    /***
     * 短信通知账号密码
     */
    @Value("${sms.notification.password}")
    private String smsNotificationPassword;

    /**
     * 短信通知账号
     **/
    @Value("${sms.bg-lre.notify.account}")
    private String bgSmsNotifyAccount;

    /***
     * 短信通知账号密码
     */
    @Value("${sms.bg-lre.notify.password}")
    private String bgSmsNotifyPassword;


    private static final String LAND_ROVER_SIGN = "【路虎中国】";

    private static final String JAGUAR_SIGN = "【捷豹路虎中国】";

    private static final String JAGUAR_LAND_SIGN = "【捷豹路虎中国】";

    private static final String NULL = "#null#";

    @Value("${sms.response.updateFlag}")
    private Boolean updateSmsResponseFlag;


    public void sendRemoveVinToBlackListMsg(ActivateMessage msg) {
        log.info("发送移除VIN到黑名单的消息，message:{}", msg);
        TenantContextHolder.setTenantId(msg.getTenantId());
        //1. 按照mq中的消息id幂等校验
        boolean idempotent = idempotentCheck(msg.getMessageId());
        if (!idempotent) {
            log.warn("重复消费msg：{}", msg);
            return;
        }
        //2. 将msg里的vin匹配t_msg_black_list的数据 进行逻辑删除
        try {
            String carVin = msg.getCarVin();
            MsgBlackListDO msgBlackListDO = null;
            if (StringUtils.isNotBlank(carVin)) {
                msgBlackListDO = msgBlackListDOMapper.selectOne(new LambdaQueryWrapper<MsgBlackListDO>()
                        .eq(MsgBlackListDO::getCarVin, carVin)
                        .eq(BaseDO::getIsDeleted, false)
                );
            }
            if (!Objects.isNull(msgBlackListDO)) {
                msgBlackListDO.setIsDeleted(true);
                msgBlackListDOMapper.updateById(msgBlackListDO);
            }
        } catch (Exception e) {
            log.info("移除黑名单VIN异常:", e);
        } finally {
            TenantContextHolder.clear();
        }
    }

    /**
     * 发送短信消息
     *
     * @param message 短信消息体
     */
    public void sendSmsMsg(SmsMessage message) {
        TenantContextHolder.setTenantId(message.getTenantId());
        //1、按照mq中的消息id幂等校验
        boolean idempotent = idempotentCheck(message.getMessageId());
        if (!idempotent) {
            log.warn("重复消费，smsMessage：{}", message);
            return;
        }
        //2、校验消息的是否为启用
        boolean sendState = checkSendTaskState(message.getTaskCode());
        if (!sendState) {
            log.warn("当前消费状态为禁用，smsMessage：{}", message);
            return;
        }
        try {
            //3、封装发送的消息的内容, 标记短信的发送状态为未知
            SingleSendMessage singleSms = buildSingleSendSmsMsg(message);
            //4、发送前准备工作
            SendHistoryDetail historyDetail = smsSendStatistics.sendBeforeWork(message.getTaskCode(), singleSms);
            //5、使用第三方接口发送短信
            SendSmsResp sendResp = singleSendSms(singleSms);
            //6、按照发送结果，更新短信消息的结果
            smsSendStatistics.updateSendResult(historyDetail, sendResp);
            //7、更新通知类商状态报告
            if (Boolean.TRUE.equals(updateSmsResponseFlag)) {
                updateNotifySmsSendReport();
            }
        } catch (Exception e) {
            log.info("立即发送短信异常：{}", e.getMessage());
        } finally {
            TenantContextHolder.clear();
        }
    }

    /**
     * 发送BG、LRE短信消息
     *
     * @param message 短信消息体
     */
    public void sendBgOrLreSmsMsg(SmsMessage message) {
        TenantContextHolder.setTenantId(message.getTenantId());
        //1、按照mq中的消息id幂等校验
        boolean idempotent = idempotentCheck(message.getMessageId());
        if (!idempotent) {
            log.warn("发送BG、LRE短信消息, 重复消费smsMessage：{}", message);
            return;
        }
        //2、校验消息的是否为启用
        boolean sendState = checkSendTaskState(message.getTaskCode());
        if (!sendState) {
            log.warn("发送BG、LRE短信消息, 消费状态为禁用smsMessage：{}", message);
            return;
        }
        try {
            //3、封装发送的消息的内容, 标记短信的发送状态为未知
            SingleSendMessage singleSms = buildBgAndLreSingleMsg(message);
            //4、发送前准备工作
            SendHistoryDetail historyDetail = smsSendStatistics.sendBeforeWork(message.getTaskCode(), singleSms);
            //5、使用第三方接口发送短信
            SendSmsResp sendResp = singleSendSms(singleSms);
            //6、按照发送结果，更新短信消息的结果
            smsSendStatistics.updateSendResult(historyDetail, sendResp);
            //7、更新通知类商状态报告
            if (Boolean.TRUE.equals(updateSmsResponseFlag)) {
                updateBgLreNotifySendReport();
            }
        } catch (Exception e) {
            log.info("发送BG、LRE短信消息, 发送短信异常：", e);
        } finally {
            TenantContextHolder.clear();
        }
    }


    /**
     * 代客下单发送短信消息
     *
     * @param message 短信消息体
     */
    public void sendValetOrderMsg(ValetOrderMessage message) {
        TenantContextHolder.setTenantId(message.getTenantId());
        //1、按照mq中的消息id幂等校验
        boolean idempotent = idempotentCheck(message.getMessageId());
        if (!idempotent) {
            log.warn("代客下单发送消息, 重复消费, smsMessage：{}", message);
            return;
        }
        try {
            //3、封装发送的消息的内容, 标记短信的发送状态为未知
            SingleSendMessage singleSms = buildValetOrderMsg(message);
            //4、发送前准备工作
            OrderNotificationDetailDO detail = smsSendStatistics.addOrderNotificationDetail(singleSms,
                    BrandCodeEnum.getBrandByCode(message.getBrandCode()), message.getWxUrl());
            //5、使用第三方接口发送短信
            SendSmsResp sendResp = singleSendSms(singleSms);
            //6、按照发送结果，更新短信消息的结果
            smsSendStatistics.updateValetSendResult(detail, sendResp);
            //7、更新通知类商状态报告
            if (Boolean.TRUE.equals(updateSmsResponseFlag)) {
                updateNotifySmsSendReport();
            }
        } catch (Exception e) {
            log.info("立即发送短信异常：{}", e.getMessage());
        } finally {
            TenantContextHolder.clear();
        }
    }


    public SingleSendMessage buildValetOrderMsg(ValetOrderMessage message) {
        if (Objects.isNull(message)) {
            return SingleSendMessage.builder().build();
        }
        return SingleSendMessage.builder()
                .account(smsNotificationAccount)
                .password(smsNotificationPassword)
                .msgid(message.getMessageId())
                .phones(message.getPhoneNumber())
                .content(buildValetMsgContent(message))
                .sign(getSign(message.getBrandCode()))
                .sendtime(TimeFormatUtil.changeToSendTime(LocalDateTime.now()))
                .carVin(message.getCarVin())
                .build();
    }

    /**
     * 构建消息的发送内容
     *
     * @param message 消息体
     * @return String
     */
    public String buildValetMsgContent(ValetOrderMessage message) {
        MessageTemplate template = queryMessageTemplate(message.getTemplateCode());
        if (Objects.isNull(template)) {
            return "";
        }
        List<String> contents = TemplateUtil.contentClassify(template.getTemplateContent());
        if (CollectionUtils.isEmpty(contents)) {
            return "";
        }
        Map<String, String> map = MessageContentUtil.buildValetVariableMap(template.getTemplateVariables(), message);
        return MessageContentUtil.getReallyContent(contents, map);
    }


    /**
     * 更新短信发送报告。
     * 此方法不接受参数且无返回值。
     * 方法将当前时间延迟5分钟后执行一个任务，该任务用于更新短信发送状态。
     */
    private void updateNotifySmsSendReport() {
        // 获取当前时间，并添加5分钟的延迟
        Date fiveMinutesLater = new Date(System.currentTimeMillis() + 5 * 60 * 1000);
        Runnable task = () -> {
            UpdateSmsSendStatusDTO updateSmsSendStatusDTO = UpdateSmsSendStatusDTO.builder()
                    .account(smsNotificationAccount)
                    .password(smsNotificationPassword)
                    .build();
            // 执行具体的业务逻辑
            UpdateSmsSendStatusResp resp = updateSmsUserReachService.updateSmsSendStatus(updateSmsSendStatusDTO);
            log.info("自动模板通知，实时更新短信状态报告, UpdateSmsSendStatusDTO:{}, UpdateSmsSendStatusResp:{}",
                    updateSmsSendStatusDTO, resp);
            if (Objects.nonNull(resp) && !CollectionUtils.isEmpty(resp.getReports())) {
                updateSmsUserReachService.updateSmsUserReach(resp.getReports());
            }
        };
        // 使用TaskScheduler安排任务在5分钟后执行一次
        taskScheduler.schedule(task, fiveMinutesLater);
    }

    /**
     * bg、lre定时触发短信状态报告更新任务
     */
    public  void updateBgLreNotifySendReport() {
        // 获取当前时间，并添加5分钟的延迟
        Date fiveMinutesLater = new Date(System.currentTimeMillis() + 5 * 60 * 1000);
        Runnable task = () -> {
            UpdateSmsSendStatusDTO updateSmsSendStatusDTO = UpdateSmsSendStatusDTO.builder()
                    .account(bgSmsNotifyAccount)
                    .password(bgSmsNotifyPassword)
                    .build();
            // 执行具体的业务逻辑
            UpdateSmsSendStatusResp resp = updateSmsUserReachService.updateSmsSendStatus(updateSmsSendStatusDTO);
            log.info("bg、lre自动模板通知，实时更新短信状态报告, UpdateSmsSendStatusDTO:{}, UpdateSmsSendStatusResp:{}",
                    updateSmsSendStatusDTO, resp);
            if (Objects.nonNull(resp) && !CollectionUtils.isEmpty(resp.getReports())) {
                updateSmsUserReachService.updateSmsUserReach(resp.getReports());
            }
        };
        // 使用TaskScheduler安排任务在5分钟后执行一次
        taskScheduler.schedule(task, fiveMinutesLater);
    }

    /**
     * 单条消息发送
     *
     * @param singleSms 单条消息发送
     * @return SendSmsResp
     */
    public SendSmsResp singleSendSms(SingleSendMessage singleSms) {
        if (!checkMsgSendContent(singleSms)) {
            return SendSmsResp.builder()
                    .result(SmsResultErrorEnum.MESSAGE_CONTENT_ERROR.getCode())
                    .desc(SmsResultErrorEnum.MESSAGE_CONTENT_ERROR.getDesc())
                    .failPhones(singleSms.getPhones())
                    .build();
        }
        CloseableHttpClient httpClient = HttpClientUtil.getNoSslHttpClient(SOCKET_TIMEOUT, CONNECT_TIMEOUT);
        String requestBodyJson = JSON.toJSONString(singleSms);
        HttpPost httpPost = getHttpPost(requestBodyJson);
        SendSmsResp sendSmsResp = new SendSmsResp();
        String smsApiUrl = smsBaseUrl + path;
        log.info("请求短信代理商, head:{}, body:{}, path:{}", httpPost.getAllHeaders(), requestBodyJson, smsApiUrl);
        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            if (SmsSendStatusEnum.SUCCESS.getCode().equals(response.getStatusLine().getStatusCode())) {
                String responseJson = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                JSONObject jsonObject = JSON.parseObject(responseJson);
                sendSmsResp = JSON.toJavaObject(jsonObject, SendSmsResp.class);
                log.info("请求短信代理商成功, responseJson：{}", responseJson);
            } else {
                // 处理错误响应
                log.error("消息发送失败, response:{}, responseBody:{}", response,
                        EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8));
            }
        } catch (Exception e) {
            log.error("请求第三方短信代理商异常：", e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("关闭http请求异常：", e);
            }
        }
        return sendSmsResp;
    }

    /**
     * 校验模板发送内容
     *
     * @param singleSms sms短信消息体
     * @return boolean
     */
    public boolean checkMsgSendContent(SingleSendMessage singleSms) {
        if (Objects.isNull(singleSms)) {
            return false;
        }
        if (StringUtils.isBlank(singleSms.getContent())) {
            return false;
        }
        return !singleSms.getContent().contains(NULL);
    }

    /**
     * 返回httpPost
     *
     * @param body 请求体
     * @return HttpPost
     */
    public HttpPost getHttpPost(String body) {
        HttpPost httpPost = new HttpPost(smsBaseUrl + path);
        // 使用正确的字符集创建StringEntity
        StringEntity stringEntity = new StringEntity(body, StandardCharsets.UTF_8);
        stringEntity.setContentType("application/json"); // 设置Content-Type头
        httpPost.setEntity(stringEntity);
        // 获取并设置access_token
        String accessToken = getToken();
        httpPost.setHeader("auth-token", "Bearer " + accessToken);
        String timestamp = String.valueOf(Instant.now().toEpochMilli() / 1000);
        String sign = SignatureUtil.generateSignature("POST", path, timestamp, apiSecret);
        httpPost.setHeader("Timestamp", timestamp);
        httpPost.setHeader("X-API-Key", apiKey);
//        httpPost.setHeader("X-Signature", sign);
        return httpPost;
    }

    /**
     * 获取token
     *
     * @return String
     */
    public String getToken() {
        AccessTokenResponse tokenResponse = new AccessTokenResponse();
        try {
            tokenResponse = accessTokenUtil.fetchAccessToken();
        } catch (Exception e) {
            log.error("获取token异常:", e);
        }
        return tokenResponse.getAccessToken();
    }


    /**
     * 构建单条发送的消息体
     *
     * @param message 上游服务发送的消息体
     * @return SingleSendMessage
     */
    public SingleSendMessage buildSingleSendSmsMsg(SmsMessage message) {
        if (Objects.isNull(message)) {
            return SingleSendMessage.builder().build();
        }
        return SingleSendMessage.builder()
                .account(smsNotificationAccount)
                .password(smsNotificationPassword)
                .msgid(message.getMessageId())
                .phones(message.getPhoneNumber())
                .content(buildMsgContent(message))
                .sign(getSign(message.getBrandCode()))
                .sendtime(TimeFormatUtil.changeToSendTime(LocalDateTime.now()))
                .carVin(message.getCarVin())
                .build();
    }

    /**
     * 构建单条发送的消息体
     *
     * @param message 上游服务发送的消息体
     * @return SingleSendMessage
     */
    public SingleSendMessage buildBgAndLreSingleMsg(SmsMessage message) {
        if (Objects.isNull(message)) {
            return SingleSendMessage.builder().build();
        }
        return SingleSendMessage.builder()
                .account(bgSmsNotifyAccount)
                .password(bgSmsNotifyPassword)
                .msgid(message.getMessageId())
                .phones(message.getPhoneNumber())
                .content(buildMsgContent(message))
                .sign(JAGUAR_LAND_SIGN)
                .sendtime(TimeFormatUtil.changeToSendTime(LocalDateTime.now()))
                .carVin(message.getCarVin())
                .build();
    }

    /**
     * 获取sign
     *
     * @param brandCode 品牌code
     * @return String
     */
    public String getSign(Integer brandCode) {
        if (BrandCodeEnum.JAGUAR.getCode().equals(brandCode)) {
            return JAGUAR_SIGN;
        }
        return LAND_ROVER_SIGN;
    }

    /**
     * 构建消息的发送内容
     *
     * @param message 消息体
     * @return String
     */
    public String buildMsgContent(SmsMessage message) {
        NotificationAutoTaskDO task = autoTaskRepository.queryAutoTaskByCode(message.getTaskCode());
        if (Objects.isNull(task)) {
            return "";
        }
        MessageTemplate template = queryMessageTemplate(task.getMessageTemplateCode());
        if (Objects.isNull(template)) {
            return "";
        }
        List<String> contents = TemplateUtil.contentClassify(template.getTemplateContent());
        if (CollectionUtils.isEmpty(contents)) {
            return "";
        }
        Map<String, String> map = MessageContentUtil.buildVariableMap(template.getTemplateVariables(), message);
        return MessageContentUtil.getReallyContent(contents, map);
    }


    /**
     * 幂等校验
     *
     * @param messageId 消息Id
     * @return boolean
     */
    public boolean idempotentCheck(String messageId) {
        if (redisTemplate == null || StringUtils.isEmpty(messageId)) {
            return false;
        }
        Boolean result = redisTemplate.opsForValue().setIfAbsent(Constants.NOTIFICATION_IDEMPOTENT_KEY + messageId,
                OK, 3600, TimeUnit.SECONDS);
        return Objects.nonNull(result) && result;
    }

    /**
     * 检查任务的状态是否能发送
     *
     * @param taskCode 任务id
     * @return boolean
     */
    public boolean checkSendTaskState(String taskCode) {
        //查询任务
        NotificationAutoTaskDO notificationTask = autoTaskRepository.queryAutoTaskByCode(taskCode);
        if (Objects.isNull(notificationTask)) {
            return false;
        }
        return TaskStatusEnum.START.getStatus().equals(notificationTask.getStatus());
    }

    /**
     * 按照模板Code查询模板数据
     *
     * @param templateCode 模板code
     * @return MessageTemplate
     */
    public MessageTemplate queryMessageTemplate(String templateCode) {
        if (StringUtils.isBlank(templateCode)) {
            return null;
        }
        LambdaQueryWrapper<MessageTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MessageTemplate::getTemplateCode, templateCode)
                .eq(MessageTemplate::getIsDeleted, IsDeleteEnum.NO.getStatus());
        List<MessageTemplate> templates = templateMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(templates)) {
            return null;
        }
        if (templates.size() > 1) {
            log.error("按照模板Code查询模板数据, templateCode:{}", templateCode);
        }
        return templates.get(0);
    }
}
