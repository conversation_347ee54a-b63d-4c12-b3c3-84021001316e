package com.jlr.ecp.notification.util;

import com.jlr.ecp.notification.config.RedisService;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 公共工具类
 *
 * <AUTHOR>
 * */

@Slf4j
public class CommonUtil {

    /**
     处理传入参数list参数
     */
    public static List<String> handleListParam(String params){
        List<String> paramList = new ArrayList<>();
        if(params !=null && !params.isEmpty()){
            paramList = Arrays.stream(params.split(","))
                    .map(String::trim)
                    .collect(Collectors.toList());
        }
        return paramList;
    }

    /**
     * 根据code获取name
     * @param redisService redis工具类
     * @param cacheKey   缓存key
     * @param businessCode  业务线code
     * @return String
     */
    public static String getBusinessNameFromRedis(RedisService redisService, String cacheKey, String businessCode) {
        try {
            String cacheMapValue = redisService.getCacheMapValue(cacheKey, businessCode);
            if(cacheMapValue !=null && !cacheMapValue.isEmpty()){
                return cacheMapValue;
            }
        }catch (Exception e){
            log.info("根据业务线code获取name，redis获取缓存异常：", e);
        }

        return businessCode;
    }

    /**
     * 从缓存判断业务线是否存在
     * @param redisService redis工具类
     * @param cacheKey    缓存key
     * @param businessCode 业务线code
     * @return Boolean
     */
    public static Boolean jungleBusinessExist(RedisService redisService, String cacheKey, String businessCode) {
        try {
            String cacheMapValue = redisService.getCacheMapValue(cacheKey, businessCode);
            if(cacheMapValue !=null && !cacheMapValue.isEmpty()){
                return false;
            }
        }catch (Exception e){
            log.info("从缓存判断业务线是否存在, 发生异常:", e);
            return true;
        }
        return true;
    }

}