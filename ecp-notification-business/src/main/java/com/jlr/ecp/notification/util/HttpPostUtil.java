package com.jlr.ecp.notification.util;

import com.jlr.ecp.notification.util.sign.SignatureUtil;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;

import java.nio.charset.StandardCharsets;
import java.time.Instant;

public class HttpPostUtil {

    /**
     * 构建一个HttpPost请求
     * @param body 请求体的内容
     * @param smsUrl 短信服务的URL
     * @param signPath 签名路径，用于生成签名
     * @param accessToken 访问令牌，用于认证
     * @param apiKey API密钥，用于认证
     * @param apiSecret API密钥 secret，用于生成签名
     * @return 配置好的HttpPost请求对象
     */
    public static HttpPost getHttpPost(String body, String smsUrl, String signPath, String accessToken,
                                String apiKey, String apiSecret) {
        HttpPost httpPost = new HttpPost(smsUrl);
        // 使用正确的字符集创建StringEntity
        StringEntity stringEntity = new StringEntity(body, StandardCharsets.UTF_8);
        stringEntity.setContentType("application/json"); // 设置Content-Type头
        httpPost.setEntity(stringEntity);
        // 并设置access_token
        httpPost.setHeader("auth-token", "Bearer " + accessToken);
        String timestamp = String.valueOf(Instant.now().toEpochMilli() / 1000);
    //    String sign =  SignatureUtil.generateSignature("POST", signPath, timestamp, apiSecret);
        httpPost.setHeader("Timestamp", timestamp);
        httpPost.setHeader("X-API-Key", apiKey);
//        httpPost.setHeader("X-Signature", sign);
        return httpPost;
    }
}
