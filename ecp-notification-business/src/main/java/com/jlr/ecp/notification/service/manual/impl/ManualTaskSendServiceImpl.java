package com.jlr.ecp.notification.service.manual.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jlr.ecp.framework.forgeRock.core.dto.AccessTokenResponse;
import com.jlr.ecp.framework.forgeRock.core.util.AccessTokenUtil;
import com.jlr.ecp.notification.dal.dataobject.history.NotificationHistory;
import com.jlr.ecp.notification.dal.dataobject.history.SendHistoryDetail;
import com.jlr.ecp.notification.dal.dataobject.task.NotificationTask;
import com.jlr.ecp.notification.dal.dataobject.template.MessageTemplate;
import com.jlr.ecp.notification.dal.repository.MessageTaskRepository;
import com.jlr.ecp.notification.dal.repository.MessageTemplateRepository;
import com.jlr.ecp.notification.dal.repository.NotificationHistoryRepository;
import com.jlr.ecp.notification.dal.repository.SendHistoryDetailRepository;
import com.jlr.ecp.notification.dto.UpdateSmsSendStatusDTO;
import com.jlr.ecp.notification.dto.VinAndPhoneDTO;
import com.jlr.ecp.notification.enums.*;
import com.jlr.ecp.notification.enums.task.InstanceCodeTypeEnum;
import com.jlr.ecp.notification.enums.task.SendChannelEnum;
import com.jlr.ecp.notification.enums.task.SignBrandTextEnum;
import com.jlr.ecp.notification.enums.task.TaskStatusEnum;
import com.jlr.ecp.notification.excel.service.NotificationExcelService;
import com.jlr.ecp.notification.kafka.send.*;
import com.jlr.ecp.notification.resp.UpdateSmsSendStatusResp;
import com.jlr.ecp.notification.service.UpdateSmsUserReachService;
import com.jlr.ecp.notification.service.manual.ManualTaskSendService;
import com.jlr.ecp.notification.util.HttpClientUtil;
import com.jlr.ecp.notification.util.InstanceCodeGenerator;
import com.jlr.ecp.notification.util.sms.TimeFormatUtil;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ManualTaskSendServiceImpl implements ManualTaskSendService {

    @Resource
    private MessageTaskRepository messageTaskRepository;

    @Resource
    private MessageTemplateRepository templateRepository;

    @Resource
    private AccessTokenUtil accessTokenUtil;

    @Resource
    private NotificationHistoryRepository historyRepository;

    @Resource
    private SendHistoryDetailRepository detailRepository;

    @Resource
    private InstanceCodeGenerator instanceCodeGenerator;

    @Resource
    private ManualTaskSendServiceImpl manualTaskSendServiceImpl;

    @Value("${send.smsUrl}")
    private String smsBaseUrl;

    @Value("${send.apiKey}")
    private String apiKey;

    @Value("${send.apiSecret}")
    private String apiSecret;

    @Value("${send.batchPath:/business/sms/send/v1.0.0}")
    private String batchPath;

    private static final int SOCKET_TIMEOUT = 60000; // 设置连接和读取超时时间为60秒
    private static final int CONNECT_TIMEOUT = 60000;

    /**
     *  路虎电商通知-账号
     * */
    @Value("${sms.notification.account}")
    private String smsNotificationAccount;

    /**
     *  路虎电商通知-密码
     * */
    @Value("${sms.notification.password}")
    private String smsNotificationPassword;

    /**
     *  路虎电商营销-账号
     * */
    @Value("${sms.market.account}")
    private String smsMarketAccount;

    /**
     * 路虎电商营销-密码
     * */
    @Value("${sms.market.password}")
    private String smsMarketPassword;

    @Resource
    private NotificationExcelService notificationExcelService;

    @Resource
    private UpdateSmsUserReachService updateSmsUserReachService;

    @Resource(name = "scheduledThreadPool")
    private ThreadPoolTaskScheduler taskScheduler;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    private static final String  OK = "OK";

    private static final Integer EXPIRE_TIME_SECOND = 60 * 60;

    @Value("${sms.response.updateFlag}")
    private Boolean updateSmsResponseFlag;

    // 使用有界队列的线程池 (容量1000)
    private final ThreadPoolExecutor sequentialExecutor = new ThreadPoolExecutor(
            1,
            1,
            0L,  // 明确设置为0，表明不需要回收线程
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(1000),
            Executors.defaultThreadFactory(),
            new ThreadPoolExecutor.CallerRunsPolicy()
    );

    /**
     * 发送手动任务通知的短信。
     *
     * @param taskCode 任务编码，用于查询任务信息并发送相应的短信通知。
     *                 如果任务状态不是启用、没有要发送的文件、没有对应的手机号、或模板不存在/内容为空，则不发送短信。
     */
    @Override
    public void sendTask(String taskCode) {
        log.info("发送手动任务通知的短信, taskCode:{}", taskCode);
        String lockKey = "manual:task:send:lock:" + taskCode;
        if (Boolean.FALSE.equals(redisTemplate.opsForValue().setIfAbsent(lockKey, "1", 20, TimeUnit.MINUTES))) {
            log.info("发送手动任务通知的短信，任务重复提交被拒绝: {}", taskCode);
            return;
        }
        sequentialExecutor.submit(() -> {
            try {
                sendManualTask(taskCode); // 任务实际执行
            } finally {
                redisTemplate.delete(lockKey); // 任务完成才释放锁
                log.info("发送手动任务通知的短信，任务完成清理锁: {}", taskCode);
            }
        });
    }

    /**
     * 手动任务发送短信，包含任务校验、文件解析及消息分发逻辑。
     *
     * @param taskCode 手动任务唯一标识码，用于查询任务配置和执行上下文
     *
     */
    public void sendManualTask(String taskCode) {
        log.info("手动任务发送短信, taskCode:{}", taskCode);
        if (Boolean.FALSE.equals(manualTaskIdempotentCheck(taskCode))) {
            log.info("发送手动任务通知的短信，任务重复执行, taskCode:{}", taskCode);
            return ;
        }
        NotificationTask notificationTask = messageTaskRepository.queryTaskByCode(taskCode);
        log.info("手动任务发送短信, notificationTask:{}", notificationTask);
        if (!TaskStatusEnum.START.getStatus().equals(notificationTask.getStatus())) {
            log.info("任务状态不是启用，无法发送短信， notificationTask：{}", notificationTask);
            return ;
        }
        if (StringUtils.isBlank(notificationTask.getMessageFile())) {
            log.info("任务没有要发送的excel文件, notificationTask：{}", notificationTask);
            return ;
        }
        List<List<String>> phoneList = new ArrayList<>();
        List<List<VinAndPhoneDTO>> vinPhoneList = new ArrayList<>();
        if (ExcelTemplateTypeEnum.PHONE_EXCEL.getType().equals(notificationTask.getSubmitFileType())) {
            phoneList = notificationExcelService.getPhoneList(notificationTask.getMessageFile());
        } else if (ExcelTemplateTypeEnum.VIN_PHONE_EXCEL.getType().equals(notificationTask.getSubmitFileType())) {
            vinPhoneList = notificationExcelService.getVinPhoneList(notificationTask.getMessageFile());
        }
        if (CollectionUtils.isEmpty(phoneList) && CollectionUtils.isEmpty(vinPhoneList)) {
            log.info("没有需要发送的手机号，任务：{}", notificationTask);
            return ;
        }
        MessageTemplate template = templateRepository.getMessageTemplateByCode(notificationTask.getMessageTemplateCode());
        if (Objects.isNull(template) || StringUtils.isBlank(template.getTemplateContent())) {
            log.info("模板不存在，或已经被删除，template：{}, taskCode:{}", template, taskCode);
            return ;
        }
        if (ExcelTemplateTypeEnum.PHONE_EXCEL.getType().equals(notificationTask.getSubmitFileType())) {
            batchSendPhoneMsg(phoneList, template, notificationTask);
        } else if (ExcelTemplateTypeEnum.VIN_PHONE_EXCEL.getType().equals(notificationTask.getSubmitFileType())) {
            batchSendVinPhoneMsg(vinPhoneList, template, notificationTask);
        }
    }

    /**
     * 执行手动任务的幂等性检查。
     *
     * @param taskCode 任务的唯一标识码。
     * @return 返回一个布尔值，如果任务代码在Redis中不存在且设置成功，则返回true，表示可以执行任务；
     *          如果任务代码已经存在或者设置失败，则返回false，表示不应执行任务。
     */
     private Boolean manualTaskIdempotentCheck(String taskCode) {
        if (StringUtils.isBlank(taskCode)) {
            return false;
        }
        Boolean result = redisTemplate.opsForValue().setIfAbsent(taskCode, OK, EXPIRE_TIME_SECOND, TimeUnit.SECONDS);
        return Objects.nonNull(result) && result;
    }

    /**
     * 批量发送phone消息。
     * @param phoneList 手机号码列表，每个元素是一个手机号码的列表。
     * @param template 消息模板，包含短信内容等信息。
     * @param notificationTask 通知任务，包含任务代码等信息。
     *此方法首先创建一个通知历史记录并将其保存到数据库中，然后遍历手机号码列表，对于每个手机号码列表：
     *1. 封装发送的消息的内容, 并将短信的发送状态标记为未知。
     *2. 准备发送前的工作，把数据批量添加到detail表中。
     *3. 使用第三方接口发送短信。
     *4. 根据发送结果，批量更新history表。
     */
    public void batchSendPhoneMsg(List<List<String>> phoneList, MessageTemplate template,
                                  NotificationTask notificationTask) {
        log.info("手动任务批量发送只有phone消息, template:{}, notificationTask:{}", template, notificationTask);
        try {
            NotificationHistory notificationHistory = buildNotificationHistory(notificationTask.getTaskCode());
            historyRepository.insertHistory(notificationHistory);
            for (List<String> phoneNumbers : phoneList) {
                //1、封装发送的消息的内容, 标记短信的发送状态为未知
                BatchSendMessage batchSendMessage = buildBatchSendSmsMsg(phoneNumbers, template.getTemplateContent(),
                        notificationTask);
                //2、发送前准备工作，把数据批量添加到detail表中。
                List<SendHistoryDetail> sendHistoryDetails =
                        batchInsertPhoneDetail(phoneNumbers, notificationHistory.getTaskInstanceCode(),
                                getTemplateContent(template.getTemplateContent(), notificationTask),
                                notificationTask.getTaskCode(),batchSendMessage.getData(),
                                SignBrandTextEnum.getBrandCodeBySignCode(notificationTask.getSignBrandText()));
                //3、使用第三方接口发送短信
                BatchSendSmsResp batchSendSmsResp = batchSendSms(batchSendMessage);
                //4、按照发送结果，批量更新history表
                manualTaskSendServiceImpl.updateBatchSendResult(notificationHistory, sendHistoryDetails, batchSendSmsResp, notificationTask);
            }
            if (Boolean.TRUE.equals(updateSmsResponseFlag)) {
                updateSmsSendReport(notificationTask);
            }
        } catch (Exception e) {
            log.error("批量发送phone消息异常：", e);
        }
    }

    /**
     * 批量发送vinAndPhone消息。
     * @param vinPhoneGroupList 手机号和vin的列表
     * @param template 消息模板，包含短信内容等信息。
     * @param notificationTask 通知任务，包含任务代码等信息。
     *此方法首先创建一个通知历史记录并将其保存到数据库中，然后遍历手机号码列表，对于每个手机号码列表：
     *1. 封装发送的消息的内容, 并将短信的发送状态标记为未知。
     *2. 准备发送前的工作，把数据批量添加到detail表中。
     *3. 使用第三方接口发送短信。
     *4. 根据发送结果，批量更新history表。
     */
    public void batchSendVinPhoneMsg(List<List<VinAndPhoneDTO>> vinPhoneGroupList, MessageTemplate template,
                                  NotificationTask notificationTask) {
        log.info("手动任务批量发送短信消息, template:{}, notificationTask:{}", template, notificationTask);
        try {
            NotificationHistory notificationHistory = buildNotificationHistory(notificationTask.getTaskCode());
            historyRepository.insertHistory(notificationHistory);
            for (List<VinAndPhoneDTO> vinPhoneList : vinPhoneGroupList) {
                List<String> phoneNumbers = vinPhoneList.stream()
                        .map(VinAndPhoneDTO::getPhone)
                        .collect(Collectors.toList());
                //1、封装发送的消息的内容, 标记短信的发送状态为未知
                BatchSendMessage batchSendMessage = buildBatchSendSmsMsg(phoneNumbers, template.getTemplateContent(),
                        notificationTask);
                //2、发送前准备工作，把数据批量添加到detail表中。
                List<SendHistoryDetail> sendHistoryDetails =
                        batchInsertVinPhoneDetail(vinPhoneList, notificationHistory.getTaskInstanceCode(),
                                getTemplateContent(template.getTemplateContent(), notificationTask),
                                notificationTask.getTaskCode(),batchSendMessage.getData(),
                                SignBrandTextEnum.getBrandCodeBySignCode(notificationTask.getSignBrandText()));
                //3、使用第三方接口发送短信
                BatchSendSmsResp batchSendSmsResp = batchSendSms(batchSendMessage);
                //4、按照发送结果，批量更新history表
                manualTaskSendServiceImpl.updateBatchSendResult(notificationHistory, sendHistoryDetails, batchSendSmsResp, notificationTask);
            }
            if (Boolean.TRUE.equals(updateSmsResponseFlag)) {
                updateSmsSendReport(notificationTask);
            }
        } catch (Exception e) {
            log.error("批量发送vinAndPhone消息异常：", e);
        }
    }

    /**
     * 更新短信发送报告。
     * 此方法不接受参数且无返回值。
     * 方法将当前时间延迟5分钟后执行一个任务，该任务用于更新短信发送状态。
     */
    public void updateSmsSendReport(NotificationTask notificationTask) {
        // 获取当前时间，并添加5分钟的延迟
        Date fiveMinutesLater = new Date(System.currentTimeMillis() + 5 * 60 * 1000);
        Runnable task = () -> {
            String accout = smsNotificationAccount;
            String password = smsNotificationPassword;
            if (SendChannelEnum.MARKET.getChannelCode().equals(notificationTask.getSendChannel())) {
                accout = smsMarketAccount;
                password = smsMarketPassword;
            }
            UpdateSmsSendStatusDTO updateSmsSendStatusDTO = UpdateSmsSendStatusDTO.builder()
                    .account(accout)
                    .password(password)
                    .build();
            // 执行具体的业务逻辑
            UpdateSmsSendStatusResp resp = updateSmsUserReachService.updateSmsSendStatus(updateSmsSendStatusDTO);
            log.info("手动模板，实时更新短信状态报告, UpdateSmsSendStatusDTO:{}", updateSmsSendStatusDTO);
            if (Objects.nonNull(resp) && !CollectionUtils.isEmpty(resp.getReports())) {
                updateSmsUserReachService.updateSmsUserReach(resp.getReports());
            }
        };
        // 使用TaskScheduler安排任务在5分钟后执行一次
        taskScheduler.schedule(task, fiveMinutesLater);
    }

    /**
     * 更新批量发送短信的结果信息。
     *
     * @param history 通知历史记录，用于记录发送的总体状态。
     * @param sendHistoryDetails 发送历史的详细信息列表，包含每个手机号的发送详情。
     * @param batchSendSmsResp 批量发送短信的响应结果，包含每个手机号的发送结果。
     * @param notificationTask 通知任务，用于更新任务的发送状态。
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchSendResult(NotificationHistory history,  List<SendHistoryDetail> sendHistoryDetails,
                                      BatchSendSmsResp batchSendSmsResp, NotificationTask notificationTask) {
        if (Objects.isNull(batchSendSmsResp) || Objects.isNull(history)) {
            return ;
        }
        List<SendResp> sendRespList = batchSendSmsResp.getData();
        if (CollectionUtils.isEmpty(sendRespList)) {
            //更新发送任务状态-已经发送
            updateTaskSendStatus(notificationTask);
            return ;
        }
        List<String> phoneNumbers = sendHistoryDetails.stream()
                .map(SendHistoryDetail::getSendPhone).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(phoneNumbers)) {
            return ;
        }
        List<SendHistoryDetail> detailList = detailRepository.batchQueryDetailByPhoneAndInstanceCode(phoneNumbers,
                history.getTaskInstanceCode());
        if (CollectionUtils.isEmpty(detailList)) {
            log.info("查询的detailList为空，phoneNumbers:{}, instanceCode:{}", phoneNumbers, history.getTaskInstanceCode());
            return ;
        }
        int total = Math.min(detailList.size(), sendRespList.size());
        List<SendHistoryDetail> detailUpdateList = new ArrayList<>(total);
        int success = 0;
        int fail = 0;
        for (int i = 0; i < total; i++) {
            SendHistoryDetail sendHistoryDetail = detailList.get(i);
            if (SmsResultErrorEnum.SUCCESS.getCode().equals(sendRespList.get(i).getResult())) {
                sendHistoryDetail.setSendResult(SmsSendResultEnum.SUCCESS.getCode());
                detailUpdateList.add(sendHistoryDetail);
                success++;
            } else {
                sendHistoryDetail.setSendResult(SmsSendResultEnum.FAIL.getCode());
                sendHistoryDetail.setSubmitErrorCode(sendRespList.get(i).getResult());
                sendHistoryDetail.setErrorMessage(sendRespList.get(i).getDesc());
                detailUpdateList.add(sendHistoryDetail);
                fail++;
            }
            sendHistoryDetail.setSendTime(LocalDateTime.now());
            sendHistoryDetail.setUpdatedTime(LocalDateTime.now());
        }
        history.setSendTotalCount(total + history.getSendTotalCount());
        history.setSendSuccessCount(success + history.getSendSuccessCount());
        history.setSendFailCount(fail + history.getSendFailCount());
        history.setUpdatedTime(LocalDateTime.now());
        historyRepository.updateByInstanceCode(history);
        batchUpdateDetail(detailUpdateList);
        //更新发送任务状态-已经发送
        updateTaskSendStatus(notificationTask);
    }

    /**
     * 更新任务的发送状态。
     *
     * @param notificationTask 通知任务对象，其发送状态将被更新。
     *                         该参数不应为null，并且必须是一个有效的通知任务实例。
     */
    public void updateTaskSendStatus(NotificationTask notificationTask) {
        notificationTask.setStatus(TaskStatusEnum.FINISH_SEND.getStatus());
        messageTaskRepository.updateTaskById(notificationTask);
    }

    /**
     * 批量更新发送历史详情信息。
     * @param detailUpdateList 需要更新的发送历史详情列表
     */
    public void batchUpdateDetail(List<SendHistoryDetail> detailUpdateList) {
        if (CollectionUtils.isEmpty(detailUpdateList)) {
            return ;
        }
        detailRepository.batchUpdateByIds(detailUpdateList);
    }

    /**
     * 批量插入发送记录的详细信息。
     *
     * @param phoneNumbers 手机号码列表，这些手机号码将分别记录发送信息。
     * @param instanceCode 实例编码，用于标识不同的发送实例。
     * @param content 发送的内容。
     * @param taskCode 任务编码，用于标识不同的发送任务。
     * @param sendMessages  消息体
     * @return 返回一个包含所有插入的发送历史详细信息的列表。
     */
    public List<SendHistoryDetail> batchInsertPhoneDetail(List<String> phoneNumbers, String instanceCode,
                                                          String content, String taskCode, List<SendMessage> sendMessages,
                                                          String brandCode) {
        List<SendHistoryDetail> sendHistoryDetails = new ArrayList<>();
        for (int i = 0; i < phoneNumbers.size(); i++) {
            sendHistoryDetails.add(buildPhoneHistoryDetail(instanceCode, content, phoneNumbers.get(i),
                    taskCode, sendMessages.get(i).getMsgid(), brandCode));
        }
        detailRepository.batchInsertDetail(sendHistoryDetails);
        return sendHistoryDetails;
    }

    /**
     * 构建发送历史详情对象。
     *
     * @param instanceCode 任务实例编码。
     * @param content 发送的内容。
     * @param phone 发送的目标手机号。
     * @param taskCode 任务编码。
     * @param msgId 消息id
     * @param brandCode 品牌code
     * @return 返回构建好的发送历史详情对象。
     */
    public SendHistoryDetail buildPhoneHistoryDetail(String instanceCode, String content, String phone,
                                                     String taskCode, String msgId, String brandCode) {
        return SendHistoryDetail.builder()
                .msgId(msgId)
                .businessCode(BusinessIdEnum.VCS.getCode())
                .taskInstanceCode(instanceCode)
                .taskCode(taskCode)
                .sendMessage(content)
                .sendPhone(phone)
                .brandCode(brandCode)
                .sendResult(SmsSendResultEnum.WAITED.getCode())
                .build();
    }

    /**
     * 批量插入发送记录的详细信息。
     *
     * @param phoneNumbers 手机号码列表，这些手机号码将分别记录发送信息。
     * @param instanceCode 实例编码，用于标识不同的发送实例。
     * @param content 发送的内容。
     * @param taskCode 任务编码，用于标识不同的发送任务。
     * @param sendMessages  消息体
     * @return 返回一个包含所有插入的发送历史详细信息的列表。
     */
    public List<SendHistoryDetail> batchInsertVinPhoneDetail(List<VinAndPhoneDTO> phoneNumbers, String instanceCode,
                                                          String content, String taskCode, List<SendMessage> sendMessages,
                                                          String brandCode) {
        List<SendHistoryDetail> sendHistoryDetails = new ArrayList<>();
        for (int i = 0; i < phoneNumbers.size(); i++) {
            sendHistoryDetails.add(buildVinPhoneHistoryDetail(instanceCode, content, phoneNumbers.get(i),
                    taskCode, sendMessages.get(i).getMsgid(), brandCode));
        }
        detailRepository.batchInsertDetail(sendHistoryDetails);
        return sendHistoryDetails;
    }

    /**
     * 构建发送历史详情对象。
     *
     * @param instanceCode 任务实例编码。
     * @param content 发送的内容。
     * @param vinPhone 发送的目标手机号和carVin。
     * @param taskCode 任务编码。
     * @param msgId 消息id
     * @param brandCode 品牌code
     * @return 返回构建好的发送历史详情对象。
     */
    public SendHistoryDetail buildVinPhoneHistoryDetail(String instanceCode, String content, VinAndPhoneDTO vinPhone,
                                                     String taskCode, String msgId, String brandCode) {
        return SendHistoryDetail.builder()
                .msgId(msgId)
                .businessCode(BusinessIdEnum.VCS.getCode())
                .taskInstanceCode(instanceCode)
                .taskCode(taskCode)
                .sendMessage(content)
                .sendPhone(vinPhone.getPhone())
                .carVin(vinPhone.getCarVin())
                .brandCode(brandCode)
                .sendResult(SmsSendResultEnum.WAITED.getCode())
                .build();
    }

    /**
     * 构建一个通知历史记录对象。
     *
     * @param taskCode 任务代码，用于标识特定的任务。
     * @return 返回一个构建好的通知历史记录对象，其中包含了任务代码、任务实例代码、任务发送时间、发送总次数等信息。
     */
    public NotificationHistory buildNotificationHistory(String taskCode) {
        return NotificationHistory.builder()
                .businessCode(BusinessIdEnum.VCS.getCode())
                .taskCode(taskCode)
                .taskInstanceCode(instanceCodeGenerator.generateTaskBatchId(InstanceCodeTypeEnum.MANUAL.getType()))
                .taskSendTime(LocalDateTime.now())
                .sendTotalCount(0)
                .sendSuccessCount(0)
                .sendFailCount(0)
                .reachUserSuccessCount(0)
                .reachUserFailCount(0)
                .reachResultStatus(0)
                .build();
    }

    /**
     *  单条消息发送
     * @param batchSendMessage 批量消息发送
     * @return SendSmsResp
     */
    public BatchSendSmsResp batchSendSms(BatchSendMessage batchSendMessage) {
        CloseableHttpClient httpClient = HttpClientUtil.getNoSslHttpClient(SOCKET_TIMEOUT, CONNECT_TIMEOUT);
        String requestBodyJson = JSON.toJSONString(batchSendMessage);
        String smsBatchApiUrl = smsBaseUrl + batchPath;
        HttpPost httpPost = getHttpPost(requestBodyJson, smsBatchApiUrl);
        BatchSendSmsResp batchSendSmsResp  = new BatchSendSmsResp();
        log.info("批量发送请求短信代理商, head:{}, body:{}, path:{}", httpPost.getAllHeaders(), requestBodyJson, smsBatchApiUrl);
        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            String responseJson = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            log.info("批量发送请求短信代理商, responseJson:{}", responseJson);
            if (SmsSendStatusEnum.SUCCESS.getCode().equals(response.getStatusLine().getStatusCode())) {
                JSONObject jsonObject = JSON.parseObject(responseJson);
                batchSendSmsResp = JSON.toJavaObject(jsonObject, BatchSendSmsResp.class);
            } else {
                // 处理错误响应
                log.error("批量发送，消息发送失败, response:{}, responseBody:{}", response, requestBodyJson);
            }
        } catch (Exception e) {
            log.info("批量发送，请求第三方短信代理商异常：{}", e.getMessage());
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("批量发送，关闭http请求异常：", e);
            }
        }
        return batchSendSmsResp;
    }

    /**
     *  返回httpPost
     *  @param body 请求体
     *  @return HttpPost
     * */
    public HttpPost getHttpPost(String body, String smsBatchApiUrl) {
        HttpPost httpPost = new HttpPost(smsBatchApiUrl);
        // 使用正确的字符集创建StringEntity
        StringEntity stringEntity = new StringEntity(body, StandardCharsets.UTF_8);
        stringEntity.setContentType("application/json"); // 设置Content-Type头
        httpPost.setEntity(stringEntity);
        // 获取并设置access_token
        String accessToken = getToken();
        httpPost.setHeader("auth-token", "Bearer " + accessToken);
        String timestamp = String.valueOf(Instant.now().toEpochMilli() / 1000);
  //      String sign =  SignatureUtil.generateSignature("POST", batchPath, timestamp, API_SECRET);
        httpPost.setHeader("Timestamp", timestamp);
        httpPost.setHeader("X-API-Key", apiKey);
     //   httpPost.setHeader("X-Signature", sign);
        return httpPost;
    }

    /**
     *   获取token
     *   @return String
     * */
    public String getToken() {
        AccessTokenResponse tokenResponse = new AccessTokenResponse();
        try {
            tokenResponse = accessTokenUtil.fetchAccessToken();
        } catch (Exception e) {
            log.error("获取token异常:", e);
        }
        return tokenResponse.getAccessToken();
    }


    /**
     * 构建批量条发送的消息体
     * @param phoneNumbers 电话号码列表
     * @param templateContent 模板内容
     * @param notificationTask 通知任务对象，用于获取任务相关的信息，如任务编码、任务实例编码等。
     * @return  BatchSendMessage
     * */
    public BatchSendMessage buildBatchSendSmsMsg(List<String> phoneNumbers, String templateContent,
                                                 NotificationTask notificationTask) {
        if (CollectionUtils.isEmpty(phoneNumbers)) {
            return BatchSendMessage.builder().build();
        }
        String accout = smsNotificationAccount;
        String password = smsNotificationPassword;
        if (SendChannelEnum.MARKET.getChannelCode().equals(notificationTask.getSendChannel())) {
            accout = smsMarketAccount;
            password = smsMarketPassword;
        }
        return BatchSendMessage.builder()
                .account(accout)
                .password(password)
                .data(getSendData(phoneNumbers, templateContent, notificationTask))
                .build();
    }

    /**
     * 根据提供的电话号码列表和模板内容，生成发送数据的列表。
     *
     * @param phoneNumbers 电话号码列表，用于指定消息发送的目标手机号。
     * @param templateContent 消息模板的内容，将被发送到每个指定的电话号码。
     * @param task 用于获取任务相关的信息，如任务编码、任务实例编码等。
     * @return 返回一个包含为每个电话号码构建的发送数据的列表。每个发送数据包含消息ID、电话号码、消息内容、签名和发送时间。
     */
    public List<SendMessage> getSendData(List<String> phoneNumbers, String templateContent, NotificationTask task) {
        List<SendMessage> sendData = new ArrayList<>();
        if (CollectionUtils.isEmpty(phoneNumbers)) {
            return sendData;
        }
        for (String phoneNumber : phoneNumbers) {
            sendData.add(SendMessage.builder()
                    .msgid(UUID.randomUUID().toString().replace("-", ""))
                    .phones(phoneNumber)
                    .content(getTemplateContent(templateContent, task))
                    .sign(getSign(task))
                    .sendtime(TimeFormatUtil.changeToSendTime(LocalDateTime.now()))
                    .build());
        }
        return sendData;
    }

    /**
     * 获取模板内容。
     * @param templateContent 模板的初始内容。
     * @param task 通知任务，包含发送渠道等信息。
     * @return 处理后的模板内容。
     */
    private String getTemplateContent(String templateContent, NotificationTask task) {
        if (SendChannelEnum.MARKET.getChannelCode().equals(task.getSendChannel())) {
            templateContent += " 拒收请回复R";
        }
        return templateContent;
    }

    /**
     * 根据通知任务中的品牌文本获取对应的标志描述。
     *
     * @param notificationTask 通知任务对象，其中包含了品牌文本信息。
     * @return 返回对应品牌的标志描述，如果没有匹配的品牌，则返回空字符串。
     */
    private String getSign(NotificationTask notificationTask) {
        String sign = SignBrandTextEnum.JAGUAR_LAND_ROVER.getSignDesc();
        if (SignBrandTextEnum.LAND_ROVER.getSignCode().equals(notificationTask.getSignBrandText())) {
            sign = SignBrandTextEnum.LAND_ROVER.getSignDesc();
        } else if (SignBrandTextEnum.JAGUAR.getSignCode().equals(notificationTask.getSignBrandText())) {
            sign = SignBrandTextEnum.JAGUAR.getSignDesc();
        }
        return sign;
    }

}
