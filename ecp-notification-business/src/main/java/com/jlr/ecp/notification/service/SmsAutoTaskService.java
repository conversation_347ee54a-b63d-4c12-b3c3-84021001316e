package com.jlr.ecp.notification.service;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.notification.req.task.*;
import com.jlr.ecp.notification.req.task.auto.AutoTaskCreatReq;
import com.jlr.ecp.notification.req.task.auto.AutoTaskModifyTimeReq;
import com.jlr.ecp.notification.vo.task.SmsTaskPageListVo;
import com.jlr.ecp.notification.vo.task.TaskHistoryDetailVO;
import com.jlr.ecp.notification.vo.task.TaskHistoryPageListVo;
import com.jlr.ecp.notification.vo.task.TaskStatusVO;
import com.jlr.ecp.notification.vo.task.auto.AutoTaskConditionVO;
import com.jlr.ecp.notification.vo.task.auto.AutoTaskDetailVO;
import com.jlr.ecp.notification.vo.task.auto.AutoTemplateNameListVO;

import java.util.List;

public interface SmsAutoTaskService {
    /**
     *  返回短信通知任务的列表页面
     * @param taskPageListReq 任务列表页请求入参
     * @return  PageResult<SmsTaskPageListVo>
     * */
    PageResult<SmsTaskPageListVo> getTaskPageList(TaskPageListReq taskPageListReq);

    /**
     * 获取自动任务状态视图对象列表。
     *
     * @return 包含任务状态码和描述信息的视图对象列表
     */
    List<TaskStatusVO> getAutoTaskStatusVO();

    /**
     * 任务详情页查询
     * @param taskDetailReq 任务详情页入参
     * @return CommonResult<TaskDetailVo>
     *
     */
    CommonResult<AutoTaskDetailVO> getAutoTaskDetail(TaskDetailReq taskDetailReq);

    /**
     * 根据任务类型获取任务名称列表
     *
     * @param taskType 任务类型，如果为null，则获取所有类型的任务名称列表；
     *                 如果是特定的任务类型，则获取该类型的任务名称列表
     * @return 任务名称列表
     */
    List<String>  getTaskNameList(Integer taskType);


    /**
     *  任务编辑历史分页查询
     * @param taskHistoryReq 编辑任务请求入参
     * PageResult<TaskHistoryPageListVo>
     * */
    PageResult<TaskHistoryPageListVo> getTaskHistoryPageList(TaskHistoryReq taskHistoryReq);

    /**
     * 修改发送任务的定时发送时间
     * @param taskModifyReq 发送任务入参
     * @return CommonResult<Boolean>
     * */
    CommonResult<Boolean> modifySendTime(AutoTaskModifyTimeReq taskModifyReq);

    /**
     * 根据模板类型获取模板名称列表
     * @param businessCode 业务代码
     * @return 返回一个字符串列表，包含所有指定类型的模板的名称
     */
    List<AutoTemplateNameListVO> getAutoTemplateNameList(String businessCode);

    /**
     * 根据模板代码获取自动模板内容
     *
     * @param templateCode 模板代码，用于唯一标识一个模板
     * @return 返回与模板代码对应的模板内容
     */
    String getAutoTemplateContent(String templateCode);

    /**
     * 编辑发送任务启用、禁用
     * @param modifyStatusReq 任务状态编辑
     * @return CommonResult<Boolean>
     * */
    CommonResult<Boolean> modifyTaskStatus(TaskModifyStatusReq modifyStatusReq);

    /**
     * 查询自动任务条件信息
     *
     * @return AutoTaskConditionVO 包含各种自动任务条件信息的视图对象
     */
    AutoTaskConditionVO queryAutoTaskCondition();

    /**
     * 添加自动任务
     *
     * @param autoTaskCreatReq 自动任务创建请求对象，包含创建任务所需的信息
     * @return 返回一个CommonResult对象，包含操作是否成功的布尔值
     */
    CommonResult<Boolean> addAutoTask(AutoTaskCreatReq autoTaskCreatReq);


    /**
     * 根据任务ID获取自动任务修改详情
     *
     * @param id 任务ID
     * @return 返回一个TaskHistoryDetailVO对象，包含修改任务的详情信息
     */
    TaskHistoryDetailVO getAutoTaskModifyDetail(Long id);

    /**
     * 批量开启自动任务
     *
     * @param batchStatusReq 包含多个任务状态修改请求参数的列表
     * @return CommonResult<String> 操作结果信息，包含成功或失败的提示
     */
     CommonResult<String> batchStartTaskStatus(BatchTaskModifyStatusReq batchStatusReq);


    /**
     * 批量停用任务状态处理。
     *
     * @param batchStatusReq 任务状态修改请求列表，包含需要停止的任务ID及对应的状态变更信息
     * @return CommonResult<String> 操作结果封装对象
     */
    CommonResult<String> batchStopTaskStatus(BatchTaskModifyStatusReq batchStatusReq);
}
