package com.jlr.ecp.notification.dto.shortlink;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetUnlimitedQRCodeDTO {
    private String scene;            // 必填，最大32字符

    private String page;             // 非必填，默认首页

    @JsonProperty("check_path")
    private Boolean checkPath;       // 是否检查页面路径

    @JsonProperty("env_version")
    private String envVersion;       // 环境版本：release, trial, develop

    private Integer width;           // 宽度 px

    @JsonProperty("auto_color")
    private Boolean autoColor;       // 自动颜色

    @JsonProperty("is_hyaline")
    private Boolean isHyaline;
}
