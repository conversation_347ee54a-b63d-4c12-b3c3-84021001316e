package com.jlr.ecp.notification.service.manual;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.notification.dto.template.ManualTemplateDTO;
import com.jlr.ecp.notification.req.template.SmsTemplatePageReq;
import com.jlr.ecp.notification.req.template.TemplateHistoryPageReq;
import com.jlr.ecp.notification.vo.template.ManualTemplateDetailVo;
import com.jlr.ecp.notification.vo.template.SmsTemplateHistoryDetailVO;
import com.jlr.ecp.notification.vo.template.SmsTemplateHistoryVo;
import com.jlr.ecp.notification.vo.template.SmsTemplateVo;

public interface ManualTemplateService {

    /**
     * 获取手动模板列表
     *
     * @param pageReq 页面请求对象，包含分页信息和查询条件
     * @return 包含短信模板Vo列表和总记录数的分页结果对象
     */
    PageResult<SmsTemplateVo> getManualTemplateList(SmsTemplatePageReq pageReq);

    /**
     * 手动添加短信模板
     *
     * @param manualTemplateDTO 包含手动模板信息的数据传输对象
     * @return 返回操作结果，成功返回true，失败返回false
     */
    CommonResult<Boolean> addSmsManualTemplate(ManualTemplateDTO manualTemplateDTO);

    /**
     * 获取短信模板的手动编辑历史记录
     *
     * @param historyPageReq 包含查询条件和分页信息的请求对象
     * @return 返回一个PageResult对象，其中包含SmsTemplateHistoryVo对象列表和总记录数
     */
    PageResult<SmsTemplateHistoryVo> getManualTemplateHistory(TemplateHistoryPageReq historyPageReq);

    /**
     * 手动、自动模板编辑详情
     * @param id
     * @return
     */
    SmsTemplateHistoryDetailVO getManualTemplateModifyDetail(Long id);

    /**
     * 修改手动短信模板
     *
     * @param manualTemplateDTO 包含手动模板信息的数据传输对象
     * @return 返回操作结果，成功返回true，失败返回false
     */
    CommonResult<Boolean> updateSmsManualTemplate(ManualTemplateDTO manualTemplateDTO);


    /**
     * 手动模板详情
     *
     * @param templateCode 模板code
     * @return ManualTemplateDetailVo
     * */
    CommonResult<ManualTemplateDetailVo> smsManualTemplateDetail(String templateCode);


    /**
     * 删除手动模板
     *
     * @param templateCode 模板code
     * @return CommonResult<Boolean>
     * */
    CommonResult<Boolean> deleteSmsManualTemplate(String templateCode);
}
