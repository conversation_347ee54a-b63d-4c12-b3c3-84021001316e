package com.jlr.ecp.notification.service.impl;

import com.jlr.ecp.notification.dal.dataobject.shortlink.NotificationConfigDO;
import com.jlr.ecp.notification.dal.dataobject.shortlink.NotificationConfigModifyLog;
import com.jlr.ecp.notification.dal.repository.ShortLinkConfigRepository;
import com.jlr.ecp.notification.dal.repository.ShortLinkModifyLogRepository;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkConfigDTO;
import com.jlr.ecp.notification.vo.shortlink.ShortLinkConfigDetailVO;
import com.jlr.ecp.notification.vo.shortlink.ShortLinkConfigModifyLogVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ShortLinkConfigServiceImplTest {

    @Mock
    private ShortLinkConfigRepository mockShortLinkConfigRepository;
    @Mock
    private ShortLinkModifyLogRepository mockShortLinkModifyLogRepository;

    @InjectMocks
    private ShortLinkConfigServiceImpl shortLinkConfigServiceImplUnderTest;

    @Test
    public void testSaveOrUpdateSmsShortLinkConfig() {
        // Setup
        final ShortLinkConfigDTO shortLinkConfigDTO = new ShortLinkConfigDTO();
        shortLinkConfigDTO.setConfigId(0L);
        shortLinkConfigDTO.setRemoteServiceSpu("remoteServiceSpu");
        shortLinkConfigDTO.setRemoteServiceSpuName("remoteServiceSpuName");
        shortLinkConfigDTO.setPiviServiceSpu("piviServiceSpu");
        shortLinkConfigDTO.setPiviServiceSpuName("piviServiceSpuName");
        shortLinkConfigDTO.setBrandCode("brandCode");

        // Configure ShortLinkConfigRepository.queryShortLinkConfigByBrandCode(...).
        final NotificationConfigDO notificationConfigDO = NotificationConfigDO.builder()
                .id(0L)
                .remoteServiceSpu("remoteServiceSpu")
                .remoteServiceSpuName("remoteServiceSpuName")
                .piviServiceSpu("piviServiceSpu")
                .piviServiceSpuName("piviServiceSpuName")
                .brandCode("brandCode")
                .build();
        when(mockShortLinkConfigRepository.queryShortLinkConfigByBrandCode("brandCode"))
                .thenReturn(notificationConfigDO);

      //  when(mockShortLinkConfigRepository.insertShortLinkConfig(any(NotificationConfigDO.class))).thenReturn(true);
      //  when(mockShortLinkConfigRepository.updateShortLinkConfig(any(NotificationConfigDO.class))).thenReturn(true);

        // Run the test
        final Boolean result = shortLinkConfigServiceImplUnderTest.saveOrUpdateSmsShortLinkConfig(shortLinkConfigDTO);

        // Verify the results
        assertEquals(result, true);

    }

    @Test
    public void testSaveOrUpdateSmsShortLinkConfig_ShortLinkConfigRepositoryInsertShortLinkConfigReturnsTrue() {
        // Setup
        final ShortLinkConfigDTO shortLinkConfigDTO = new ShortLinkConfigDTO();
        shortLinkConfigDTO.setConfigId(0L);
        shortLinkConfigDTO.setRemoteServiceSpu("remoteServiceSpu");
        shortLinkConfigDTO.setRemoteServiceSpuName("remoteServiceSpuName");
        shortLinkConfigDTO.setPiviServiceSpu("piviServiceSpu");
        shortLinkConfigDTO.setPiviServiceSpuName("piviServiceSpuName");
        shortLinkConfigDTO.setBrandCode("brandCode");

//        when(mockShortLinkConfigRepository.insertShortLinkConfig(NotificationConfigDO.builder()
//                .id(0L)
//                .remoteServiceSpu("remoteServiceSpu")
//                .remoteServiceSpuName("remoteServiceSpuName")
//                .piviServiceSpu("piviServiceSpu")
//                .piviServiceSpuName("piviServiceSpuName")
//                .brandCode("brandCode")
//                .build())).thenReturn(true);

        // Run the test
        final Boolean result = shortLinkConfigServiceImplUnderTest.saveOrUpdateSmsShortLinkConfig(shortLinkConfigDTO);

        // Verify the results
        assertEquals(result, false);
    }

    @Test
    public void testSaveOrUpdateSmsShortLinkConfig_ShortLinkConfigRepositoryUpdateShortLinkConfigReturnsTrue() {
        // Setup
        final ShortLinkConfigDTO shortLinkConfigDTO = new ShortLinkConfigDTO();
        shortLinkConfigDTO.setConfigId(0L);
        shortLinkConfigDTO.setRemoteServiceSpu("remoteServiceSpu");
        shortLinkConfigDTO.setRemoteServiceSpuName("remoteServiceSpuName");
        shortLinkConfigDTO.setPiviServiceSpu("piviServiceSpu");
        shortLinkConfigDTO.setPiviServiceSpuName("piviServiceSpuName");
        shortLinkConfigDTO.setBrandCode("brandCode");

        // Configure ShortLinkConfigRepository.queryShortLinkConfigByBrandCode(...).
        final NotificationConfigDO notificationConfigDO = NotificationConfigDO.builder()
                .id(0L)
                .remoteServiceSpu("remoteServiceSpu")
                .remoteServiceSpuName("remoteServiceSpuName")
                .piviServiceSpu("piviServiceSpu")
                .piviServiceSpuName("piviServiceSpuName")
                .brandCode("brandCode")
                .build();
        when(mockShortLinkConfigRepository.queryShortLinkConfigByBrandCode("brandCode"))
                .thenReturn(notificationConfigDO);

//        when(mockShortLinkConfigRepository.updateShortLinkConfig(NotificationConfigDO.builder()
//                .id(0L)
//                .remoteServiceSpu("remoteServiceSpu")
//                .remoteServiceSpuName("remoteServiceSpuName")
//                .piviServiceSpu("piviServiceSpu")
//                .piviServiceSpuName("piviServiceSpuName")
//                .brandCode("brandCode")
//                .build())).thenReturn(true);

        // Run the test
        final Boolean result = shortLinkConfigServiceImplUnderTest.saveOrUpdateSmsShortLinkConfig(shortLinkConfigDTO);

        // Verify the results
        assertEquals(result, true);
    }

    @Test
    public void testGetSmsShortConfig() {
        // Setup
        final ShortLinkConfigDetailVO expectedResult = new ShortLinkConfigDetailVO();
        expectedResult.setConfigId(0L);
        expectedResult.setRemoteServiceSpu("remoteServiceSpu");
        expectedResult.setRemoteServiceSpuName("remoteServiceSpuName");
        expectedResult.setPiviServiceSpu("piviServiceSpu");
        expectedResult.setPiviServiceSpuName("piviServiceSpuName");
        expectedResult.setBrandCode("brandCode");

        // Configure ShortLinkConfigRepository.queryShortLinkConfigByBrandCode(...).
        final NotificationConfigDO notificationConfigDO = NotificationConfigDO.builder()
                .id(0L)
                .remoteServiceSpu("remoteServiceSpu")
                .remoteServiceSpuName("remoteServiceSpuName")
                .piviServiceSpu("piviServiceSpu")
                .piviServiceSpuName("piviServiceSpuName")
                .brandCode("brandCode")
                .build();
        when(mockShortLinkConfigRepository.queryShortLinkConfigByBrandCode("brandCode"))
                .thenReturn(notificationConfigDO);

        // Run the test
        final ShortLinkConfigDetailVO result = shortLinkConfigServiceImplUnderTest.getSmsShortConfig("brandCode");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryModifyLog() {
        // Setup
        final ShortLinkConfigModifyLogVO shortLinkConfigModifyLogVO = new ShortLinkConfigModifyLogVO();
        shortLinkConfigModifyLogVO.setConfigId(0L);
        shortLinkConfigModifyLogVO.setModifyContent("modifyContent");
        shortLinkConfigModifyLogVO.setModifyDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        shortLinkConfigModifyLogVO.setModifyUser("modifyUser");
        final List<ShortLinkConfigModifyLogVO> expectedResult = List.of(shortLinkConfigModifyLogVO);

        // Configure ShortLinkModifyLogRepository.queryShortLinkModifyLog(...).
        final List<NotificationConfigModifyLog> notificationConfigModifyLogs = List.of(
                NotificationConfigModifyLog.builder()
                        .configId(0L)
                        .modifyContent("modifyContent")
                        .modifyDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .modifyUser("modifyUser")
                        .build());
        when(mockShortLinkModifyLogRepository.queryShortLinkModifyLog(0L)).thenReturn(notificationConfigModifyLogs);

        // Run the test
        final List<ShortLinkConfigModifyLogVO> result = shortLinkConfigServiceImplUnderTest.queryModifyLog(0L);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryModifyLog_ShortLinkModifyLogRepositoryReturnsNoItems() {
        // Setup
        when(mockShortLinkModifyLogRepository.queryShortLinkModifyLog(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ShortLinkConfigModifyLogVO> result = shortLinkConfigServiceImplUnderTest.queryModifyLog(0L);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }
}
