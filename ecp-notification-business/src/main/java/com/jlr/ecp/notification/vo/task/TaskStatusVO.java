package com.jlr.ecp.notification.vo.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Schema(description = "任务状态VO")
public class TaskStatusVO {

    /**
     * 任务状态码
     * */
    @Schema(description = "任务状态码")
    private Integer status;

    /**
     * 任务状态描述
     * */
    @Schema(description = "任务状态描述")
    private String desc;
}
