package com.jlr.ecp.notification.service.manual;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.notification.req.task.*;
import com.jlr.ecp.notification.vo.task.*;

import java.util.List;

/**
 *  短信通知任务管理service
 *
 * <AUTHOR>
 * */
public interface SmsManualTaskService {

    /**
     *  返回手动任务的列表页面
     * @return  PageResult<SmsTaskPageListVo>
     * */
    PageResult<ManualTaskPageListVo> getManualTaskPageList(TaskPageListReq taskPageListReq);

    /**
     * 获取手动任务状态视图对象列表。
     *
     * @return 包含任务状态码和描述信息的视图对象列表
     */
    List<TaskStatusVO> getManualTaskStatusVO();

    /**
     *  任务编辑历史分页查询
     * @param taskHistoryReq 编辑任务请求入参
     * PageResult<TaskHistoryPageListVo>
     * */
    PageResult<TaskHistoryPageListVo> getTaskHistoryPageList(TaskHistoryReq taskHistoryReq);

    /**
     * 手动任务详情页查询
     * @param taskDetailReq 任务详情页入参
     * @return CommonResult<TaskDetailVo>
     *
     */
    CommonResult<ManualTaskDetailVo> getManualTaskDetail(TaskDetailReq taskDetailReq);

    /**
     * 新增手动任务
     * @param taskCreatReq 手动任务入参
     * @return CommonResult<Boolean>
     * */
    CommonResult<Boolean> addManualTask(TaskCreatReq taskCreatReq);

    /**
     * 编辑手动任务
     * @param taskModifyReq 编辑手动任务入参
     * @return CommonResult<Boolean>
     * */
    CommonResult<Boolean> updateManualTask(TaskModifyReq taskModifyReq);


    /**
     * 编辑手动发送任务禁用
     * @param modifyStatusReq 任务状态编辑
     * @return CommonResult<Boolean>
     * */
    CommonResult<Boolean> modifyManualTaskStatus(TaskModifyStatusReq modifyStatusReq);

    /**
     * 启用手动通知任务并进行发送
     * @param modifyStatusReq 任务状态编辑
     * @return CommonResult<Boolean>
     * */
    CommonResult<Boolean> openManualTask(TaskModifyStatusReq modifyStatusReq);

    /**
     * 删除上传文件
     * @param taskCode taskCode
     * @return CommonResult<Boolean>
     * */
    CommonResult<Boolean> deleteFile(String taskCode);

    /**
     * 获取手动任务编辑记录详情
     * @param id
     * @return
     */
    ManualTaskHistoryDetailVO getManualTaskModifyDetail(Long id);

    /**
     * 批量启动手动任务处理
     *
     * @param batchStatusReq 批量任务状态修改请求参数
     * @return CommonResult<String> 操作结果包装对象
     */
    CommonResult<String> batchStartManualTask(BatchTaskModifyStatusReq batchStatusReq);

    /**
     * 批量停用手动任务操作接口
     *
     * @param batchStatusReq 批量任务状态修改请求参数
     * @return CommonResult<String> 操作结果封装对象
     */
    CommonResult<String> batchStopManualTask(BatchTaskModifyStatusReq batchStatusReq);
}
