package com.jlr.ecp.notification.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * System 字典类型的枚举类
 *
 * <AUTHOR>
 */

@AllArgsConstructor
@Getter
public enum DictTypeConstants {
    USER_TYPE("user_type"), // 用户类型
    COMMON_STATUS("common_status"), // 系统状态

    // ========== SYSTEM 模块 ==========

    USER_SEX("system_user_sex"), // 用户性别
    OPERATE_TYPE("system_operate_type"), // 操作类型
    LOGIN_TYPE("system_login_type"), // 登录日志的类型
    LOGIN_RESULT("system_login_result"), // 登录结果
    ERROR_CODE_TYPE("system_error_code_type"), // 错误码的类型枚举
    SMS_CHANNEL_CODE("system_sms_channel_code"), // 短信渠道编码
    SMS_TEMPLATE_TYPE("system_sms_template_type"), // 短信模板类型
    SMS_SEND_STATUS("system_sms_send_status"), // 短信发送状态
    SMS_RECEIVE_STATUS("system_sms_receive_status"); // 短信接收状态

    private final  String code;

    public String getCode() {
        return code;
    }

}
