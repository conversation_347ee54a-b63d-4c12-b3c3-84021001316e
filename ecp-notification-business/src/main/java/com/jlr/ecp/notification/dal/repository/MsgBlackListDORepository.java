package com.jlr.ecp.notification.dal.repository;

import com.jlr.ecp.notification.api.dto.MsgBlackListDTO;
import java.util.List;

public interface MsgBlackListDORepository {

    /**
     * 根据车辆VIN列表检查黑名单
     *
     * @param carVinList 车辆VIN码的列表，用于查询黑名单状态。
     * @return 返回一个MsgBlackListDTO对象的列表，表示查询到的每个车辆的黑名单状态。
     */
    List<MsgBlackListDTO> checkBlackListByCarVinList(List<String> carVinList);
}
