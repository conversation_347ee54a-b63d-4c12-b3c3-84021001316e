package com.jlr.ecp.notification.dal.repository.shortlink.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.notification.dal.dataobject.shortlink.ShortLinkClickTotalDO;
import com.jlr.ecp.notification.dal.mysql.shortlink.ShortLinkClickTotalDOMapper;
import com.jlr.ecp.notification.dal.repository.shortlink.ShortLinkClickTotalRepository;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class ShortLinkClickTotalRepositoryImpl extends ServiceImpl<ShortLinkClickTotalDOMapper, ShortLinkClickTotalDO>
        implements ShortLinkClickTotalRepository {

    @Override
    public ShortLinkClickTotalDO selectTotalByUrlCode(String urlCode) {
        if (Objects.isNull(urlCode)) {
            return null;
        }
        LambdaQueryWrapper<ShortLinkClickTotalDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShortLinkClickTotalDO::getUrlCode, urlCode)
                .eq(ShortLinkClickTotalDO::getIsDeleted, false);
        return getOne(queryWrapper);
    }
}
