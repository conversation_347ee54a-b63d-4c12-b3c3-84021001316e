package com.jlr.ecp.notification.dal.repository.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.notification.dal.dataobject.task.NotificationTask;
import com.jlr.ecp.notification.dal.mysql.task.NotificationTaskMapper;
import com.jlr.ecp.notification.dal.repository.MessageTaskRepository;
import com.jlr.ecp.notification.enums.IsDeleteEnum;
import com.jlr.ecp.notification.enums.SortEnum;
import com.jlr.ecp.notification.enums.task.AutoTaskTypeEnum;
import com.jlr.ecp.notification.enums.task.TaskTypeEnum;
import com.jlr.ecp.notification.req.task.TaskPageListReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MessageTaskRepositoryImpl implements MessageTaskRepository {

    @Resource
    private NotificationTaskMapper taskMapper;

    /**
     * 按照任务code查询任务数据
     * @param taskCode 任务code
     * */
    public NotificationTask queryTaskByCode(String taskCode) {
        if (StringUtils.isBlank(taskCode)) {
            log.info("按照任务code查询任务数据为空, taskCode:{}", taskCode);
            return null;
        }
        LambdaQueryWrapper<NotificationTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NotificationTask::getTaskCode, taskCode)
                .eq(NotificationTask::getIsDeleted, IsDeleteEnum.NO.getStatus());
        List<NotificationTask> taskList = taskMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(taskList)) {
            return null;
        }
        if (taskList.size() > 1) {
            log.error("按照任务id查询任务数据, 数据有多条:{}", taskCode);
        }
        return taskList.get(0);
    }

    /**
     * 根据任务ID更新通知任务信息。
     *
     * @param notificationTask 通知任务对象，不可为null。
     * @return 返回更新操作的成功与否，成功返回true，失败返回false。
     */
    @Override
    public Boolean updateTaskById(NotificationTask notificationTask) {
        if (Objects.isNull(notificationTask)) {
            return false;
        }
        try {
            taskMapper.updateById(notificationTask);
        } catch (Exception e) {
            log.error("更新任务状态异常：", e);
            return false;
        }
        return true;
    }

    /**
     * 根据模板代码查询通知任务。
     *
     * @param templateCode 消息模板的代码，用于查询相应的通知任务。
     * @return 返回一个通知任务列表。如果模板代码为空或无效，则返回空列表。
     */
    @Override
    public List<NotificationTask> queryTasksByTemplateCode(String templateCode) {
        if (StringUtils.isBlank(templateCode)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<NotificationTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NotificationTask::getMessageTemplateCode, templateCode)
                .eq(NotificationTask::getDraftVersion, 0)
                .eq(NotificationTask::getIsDeleted, false);
        return taskMapper.selectList(wrapper);
    }

    /**
     * 查询任务名称列表，根据任务类型过滤。
     *
     * @param taskType 任务类型。可为空，为空时查询所有非删除状态的任务；如果提供任务类型，则只查询指定类型的任务。
     * @return 返回一个任务名称的列表。如果查询不到任何任务，则返回空列表。
     */
    @Override
    public List<String> queryTaskNameListFilterAutoEvent(Integer taskType) {
        List<NotificationTask> notificationTasks = new ArrayList<>();
        LambdaQueryWrapper<NotificationTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NotificationTask::getIsDeleted, false);

        if (Objects.isNull(taskType)) {
            queryWrapper.and(wrapper -> wrapper
                    .eq(NotificationTask::getTaskType, TaskTypeEnum.AUTO_TASK.getType())
                    .eq(NotificationTask::getAutoTaskType, AutoTaskTypeEnum.PERIODIC_NOTIFICATION.getAutoType())
            ).or().eq(NotificationTask::getTaskType, TaskTypeEnum.MANUAL.getType());
        } else if (TaskTypeEnum.AUTO_TASK.getType().equals(taskType)) {
            queryWrapper.eq(NotificationTask::getTaskType, taskType)
                    .eq(NotificationTask::getAutoTaskType, AutoTaskTypeEnum.PERIODIC_NOTIFICATION.getAutoType());
        } else if (TaskTypeEnum.MANUAL.getType().equals(taskType)) {
            queryWrapper.eq(NotificationTask::getTaskType, taskType);
        }
        try {
            notificationTasks = taskMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.error("询任务名称列表，根据任务类型过滤, 异常: ", e);
        }
        if (CollectionUtils.isEmpty(notificationTasks)) {
            return new ArrayList<>();
        }
        return notificationTasks.stream().map(NotificationTask::getTaskName).collect(Collectors.toList());
    }


    /**
     * 查询任务Map，根据任务编码列表
     *
     * @param taskCodeList 任务编码列表
     * @return 返回一个任务名称的列表。如果查询不到任何任务，则返回空列表。
     */
    @Override
    public Map<String, NotificationTask> getTaskMapByCodes(List<String> taskCodeList) {
        Map<String, NotificationTask> map = new HashMap<>();
        List<NotificationTask> taskList = getTaskListByCodes(taskCodeList);
        if (CollUtil.isEmpty(taskList)) {
            log.info("查询任务Map，根据任务编码列表查询结果为空");
            return map;
        }
        log.info("查询任务Map，根据任务编码列表查询结果, 查询的数量：{}", taskCodeList.size());
        for (NotificationTask task : taskList) {
            map.put(task.getTaskCode(), task);
        }
        return map;
    }

    /**
     * 查询任务名称列表，根据任务编码列表
     *
     * @param taskCodeList 任务编码列表
     * @return 返回一个任务名称的列表。如果查询不到任何任务，则返回空列表。
     */
    @Override
    public List<NotificationTask> getTaskListByCodes(List<String> taskCodeList) {
        log.info("查询任务名称列表，根据任务编码列表, taskCodeList:{}", taskCodeList);
        if (CollUtil.isEmpty(taskCodeList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<NotificationTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(NotificationTask::getTaskCode, taskCodeList)
                .eq(NotificationTask::getIsDeleted, false);
        return taskMapper.selectList(queryWrapper);
    }

    /**
     *  分页查询短信通知任务列表页
     * @param taskPageListReq 分页插叙通知任务列表页入参
     * @return Page<NotificationTask>
     * */
    @Override
    public Page<NotificationTask> queryTaskPageList(TaskPageListReq taskPageListReq) {
        Page<NotificationTask> page = new Page<>(taskPageListReq.getPageNo(), taskPageListReq.getPageSize());
        LambdaQueryWrapper<NotificationTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(org.apache.commons.lang.StringUtils.isNotBlank(taskPageListReq.getTaskCode()),
                        NotificationTask::getTaskCode, taskPageListReq.getTaskCode())
                .eq(Objects.nonNull(taskPageListReq.getTaskType()),NotificationTask::getTaskType,taskPageListReq.getTaskType())
                .eq(NotificationTask::getIsDeleted, IsDeleteEnum.NO.getStatus());
        String taskName = processTaskName(taskPageListReq.getTaskName());
        if (org.apache.commons.lang.StringUtils.isNotBlank(taskName)) {
            wrapper.like(NotificationTask::getTaskName, taskName);
        }
        //启用时间排序
        if (Objects.nonNull(taskPageListReq.getStartTimeSortType())) {
            if (SortEnum.ASC.getSortType().equals(taskPageListReq.getStartTimeSortType())) {
                wrapper.orderByAsc(NotificationTask::getActivateTime);
            } else {
                wrapper.orderByDesc(NotificationTask::getActivateTime);
            }
            //停用时间排序
        }else if (Objects.nonNull(taskPageListReq.getEndTimeSortType())) {
            if (SortEnum.ASC.getSortType().equals(taskPageListReq.getEndTimeSortType())) {
                wrapper.orderByAsc(NotificationTask::getDeactivateTime);
            } else {
                wrapper.orderByDesc(NotificationTask::getDeactivateTime);
            }
            // 状态排序
        }else if (Objects.nonNull(taskPageListReq.getStatusSortType())) {
            if (SortEnum.ASC.getSortType().equals(taskPageListReq.getStatusSortType())) {
                wrapper.orderByAsc(NotificationTask::getStatus);
            } else {
                wrapper.orderByDesc(NotificationTask::getStatus);
            }
        }
        // 默认根据编辑时间倒序
        wrapper.orderByDesc(NotificationTask::getUpdatedTime);
        wrapper.orderByDesc(NotificationTask::getId);
        return taskMapper.selectPage(page, wrapper);
    }

    /**
     * 根据任务名称和发送类型获取手动任务列表
     *
     * @param taskType 任务类型，用于筛选特定类型的任務
     * @param taskSendType 任务发送类型，用于筛选特定发送类型的任务
     * @param taskName 任务名称，用于模糊匹配任务名称
     * @return 返回符合筛选条件的手动任务列表
     */
    @Override
    public List<NotificationTask> getManualTaskByNameAndSendType(Integer taskType,
                                                                 Integer taskSendType, String taskName) {
        log.info("根据发送类型获取手动任务列表,  taskType:{}, taskSendType:{}, taskName:{}", taskType, taskSendType, taskName);
        if (Objects.isNull(taskType) && Objects.isNull(taskName) && Objects.isNull(taskSendType)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<NotificationTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(taskType), NotificationTask::getTaskType, taskType)
                .eq(Objects.nonNull(taskSendType), NotificationTask::getTaskSendType, taskSendType)
                .like(StringUtils.isNotBlank(taskName), NotificationTask::getTaskName, "%" + taskName + "%")
                .eq(NotificationTask::getIsDeleted, false);
        return taskMapper.selectList(queryWrapper);
    }

    /**
     * 处理任务名称，确保特定字符被转义
     *
     * @param taskName 原始任务名称，可能包含需要转义的特殊字符
     * @return 转义后的任务名称如果输入的任务名称为空或只包含空白字符，则返回原始任务名称
     */
    private String processTaskName(String taskName) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(taskName)) {
            // 正确地替换字符串并返回新字符串
            return taskName.replace("%", "\\%").replace("_", "\\_");
        }
        return taskName;
    }

}
