package com.jlr.ecp.notification.req.template;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;


/**
 *  模板编辑历史入参
 *
 * <AUTHOR>
 * */
@Schema(description = "模板编辑历史请求入参")
@Data
public class TemplateHistoryPageReq extends PageParam {

    @Schema(description = "模板code")
    private String templateCode;
}
