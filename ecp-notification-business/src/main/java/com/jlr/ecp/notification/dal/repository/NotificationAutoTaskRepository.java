package com.jlr.ecp.notification.dal.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.notification.dal.dataobject.task.auto.NotificationAutoTaskDO;
import com.jlr.ecp.notification.req.task.TaskPageListReq;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface NotificationAutoTaskRepository extends IService<NotificationAutoTaskDO> {

    /**
     *  分页查询自动通知任务列表页
     * @param taskPageListReq 分页查询自动通知任务列表页入参
     * @return Page<NotificationAutoTaskDO>
     * */
    Page<NotificationAutoTaskDO> queryAutoTaskPageList(TaskPageListReq taskPageListReq);

    /**
     * 根据任务代码查询自动任务信息
     *
     * @param taskCode 任务代码，用于唯一标识一个自动任务
     * @return 返回一个NotificationAutoTaskDO对象，包含任务详细信息如果找不到匹配的任务，则返回null
     */
    NotificationAutoTaskDO queryAutoTaskByCode(String taskCode);

    /**
     * 根据ID更新自动任务信息
     *
     * @param autoTaskDO 包含自动任务新信息的对象，用于更新数据库中的记录
     * @return boolean 表示更新操作是否成功true表示成功，false表示失败
     */
    boolean updateAutoTaskById(NotificationAutoTaskDO autoTaskDO);

    /**
     * 查询自动任务名称列表
     *
     * @return 返回自动任务名称列表，如果查询结果为空，则返回空列表
     */
    List<String> querytAutoTaskNameList();

    /**
     * 插入自动任务通知记录
     *
     * @param autoTaskDO 自动任务通知对象，包含需要插入的自动任务通知的相关信息
     * @return 返回一个布尔值，表示自动任务通知记录是否成功插入
     *         - true：表示记录成功插入
     *         - false：表示记录插入失败
     */
    boolean insertAutoTask(NotificationAutoTaskDO autoTaskDO);

    /**
     * 根据模板代码查询自动任务
     *
     * @param templateCode 消息模板代码，用于查询自动任务
     * @return 返回符合查询条件的自动任务列表
     */
    List<NotificationAutoTaskDO> queryAutoTaskByTemplateCode(String templateCode);

    /**
     * 获取自动任务到期通知
     *
     * @param  expireDate 到期日
     * @return 自动任务到期通知列表
     */
    List<NotificationAutoTaskDO> getExpireNotifyAutoTask(LocalDateTime expireDate);

    /**
     * 更新指定任务的开始和结束日期为null
     *
     * @param taskCode 任务代码，用于标识特定的任务
     * @return 返回一个Boolean值，表示更新操作是否成功
     */
    Boolean updateBeginEndDateNull(String taskCode);

    /**
     * 根据发送类型和任务名称获取自动任务列表
     *
     * @param taskSendType 任务发送类型，用于筛选特定发送类型的任务
     * @param taskName 任务名称，用于模糊匹配任务名称
     * @return 返回筛选后的自动任务列表如果taskSendType和taskName都为空，则返回空列表
     */
    List<NotificationAutoTaskDO> getAutoTaskBySendTypeAndName(Integer taskSendType,
                                                              String taskName) ;

    /**
     * 根据任务代码列表获取自动任务映射
     *
     * @param taskCodeList 任务代码列表，用于查询自动任务信息
     * @return 返回一个映射，键是任务代码，值是对应的NotificationAutoTaskDO实例
     */
    Map<String, NotificationAutoTaskDO> getAutoTaskMapByCodes(List<String> taskCodeList);

    /**
     * 根据任务代码列表查询自动任务信息
     *
     * @param taskCodeList 任务代码列表，用于查询自动任务信息
     * @return 返回查询到的自动任务信息列表如果无数据，返回空列表
     */
    List<NotificationAutoTaskDO> queryAutoTaskByCodes(List<String> taskCodeList);
}
