package com.jlr.ecp.notification.service.manual.impl;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.dal.dataobject.shortlink.MiniCodeConfigDO;
import com.jlr.ecp.notification.dal.dataobject.shortlink.ShortLinkClickTotalDO;
import com.jlr.ecp.notification.dal.dataobject.shortlink.ShortLinkClickUserDO;
import com.jlr.ecp.notification.dal.repository.shortlink.MiniCodeConfigRepository;
import com.jlr.ecp.notification.dal.repository.shortlink.ShortLinkClickTotalRepository;
import com.jlr.ecp.notification.dal.repository.shortlink.ShortLinkClickUserRepository;
import com.jlr.ecp.notification.dto.shortlink.ManualShortLinkClickDTO;
import com.jlr.ecp.notification.service.manual.ManualShortLinkClickService;
import com.jlr.ecp.notification.vo.shortlink.QrCodeConfigParamVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

@Service
@Slf4j
public class ManualShortLinkClickServiceImpl implements ManualShortLinkClickService {

    @Resource
    private ShortLinkClickTotalRepository shortLinkClickTotalRepository;

    @Resource
    private ShortLinkClickUserRepository shortLinkClickUserRepository;

    @Resource
    private MiniCodeConfigRepository miniCodeConfigRepository;


    /**
     * 更新手动短链点击率信息。
     *
     * @param clickDTO 包含短链点击相关信息的DTO对象，包括短链代码(urlCode)和用户ID(jlrId)等。
     */
    @Override
    public CommonResult<String> updateShortLinkClick(ManualShortLinkClickDTO clickDTO) {
        log.info("手动短链点击率更新, clickDTO:{}", clickDTO);
        String urlCode = clickDTO.getUrlCode();
        if (StringUtils.isBlank(clickDTO.getJlrId())) {
            ShortLinkClickTotalDO clickTotalDO = shortLinkClickTotalRepository.selectTotalByUrlCode(urlCode);
            saveOrUpdateShortLinkClickTotal(clickTotalDO, urlCode);
        } else {
            ShortLinkClickUserDO clickUserDO = shortLinkClickUserRepository.selectUserByJlrId(urlCode, clickDTO.getJlrId());
            if (Boolean.TRUE.equals(clickDTO.getIsFirstAuthorize()) && Objects.isNull(clickUserDO)) {
                saveOrUpdateShortLinkClickUser(null, clickDTO);
            } else {
                if (!Boolean.TRUE.equals(clickDTO.getIsFirstAuthorize())) {
                    ShortLinkClickTotalDO clickTotalDO = shortLinkClickTotalRepository.selectTotalByUrlCode(urlCode);
                    saveOrUpdateShortLinkClickTotal(clickTotalDO, urlCode);
                }
                if (Objects.isNull(clickUserDO)) {
                    saveOrUpdateShortLinkClickUser(null, clickDTO);
                }
            }
        }
        return CommonResult.success("手动短链点击率更新成功");
    }

    /**
     * 保存或更新短链接点击总数的记录。
     *
     * @param clickTotalDO 短链接点击总数的数据对象，可能为 null
     * @param urlCode      短链接的唯一标识码，如果为空则直接返回
     */
    public void saveOrUpdateShortLinkClickTotal(ShortLinkClickTotalDO clickTotalDO, String urlCode) {
        log.info("保存或更新短链接点击总数的记录, clickTotalDO:{}, urlCode:{}", clickTotalDO, urlCode);
        if (StringUtils.isBlank(urlCode)) {
            return;
        }
        if (Objects.isNull(clickTotalDO)) {
            clickTotalDO = new ShortLinkClickTotalDO();
            clickTotalDO.setUrlCode(urlCode);
            clickTotalDO.setClickTotal(0L);
        } else if (Objects.isNull(clickTotalDO.getClickTotal())) {
            clickTotalDO.setClickTotal(0L);
        }
        clickTotalDO.setClickTotal(clickTotalDO.getClickTotal() + 1);
        clickTotalDO.setUpdatedTime(LocalDateTime.now());
        shortLinkClickTotalRepository.saveOrUpdate(clickTotalDO);
    }

    /**
     * 保存或更新短链接点击用户信息。
     *
     * @param clickUserDO 短链接点击用户数据对象，表示需要创建新记录
     * @param clickDTO 手动点击数据对象，包含点击相关信息
     */
    public void saveOrUpdateShortLinkClickUser(ShortLinkClickUserDO clickUserDO, ManualShortLinkClickDTO clickDTO) {
        log.info("保存或更新短链接点击用户信息, clickUserDO:{}, clickDTO:{}", clickUserDO, clickDTO);
        if (Objects.isNull(clickDTO) || StringUtils.isBlank(clickDTO.getUrlCode()) ||
                StringUtils.isBlank(clickDTO.getJlrId())) {
            log.info("保存或更新短链接点击用户信息, urlCode、jlrId等核心信息为空，不保存");
            return;
        }
        if (Objects.isNull(clickUserDO)) {
            clickUserDO = new ShortLinkClickUserDO();
            clickUserDO.setUrlCode(clickDTO.getUrlCode());
            clickUserDO.setJlrId(clickDTO.getJlrId());
            clickUserDO.setPhone(clickDTO.getPhone());
            clickUserDO.setClickCount(0);
        } else if (Objects.isNull(clickUserDO.getClickCount())) {
            clickUserDO.setClickCount(0);
        }
        clickUserDO.setClickCount(clickUserDO.getClickCount() + 1);
        clickUserDO.setUpdatedTime(LocalDateTime.now());
        shortLinkClickUserRepository.saveOrUpdate(clickUserDO);
    }

    /**
     * 获取太阳码配置参数并拼接为字符串结果
     *
     * @param configId 配置ID，用于查询对应的太阳码配置数据
     * @return 包含拼接后的配置参数字符串的通用返回对象
     *         当配置不存在或无有效参数时返回空字符串
     */
    @Override
    public CommonResult<QrCodeConfigParamVO> getQrCodeConfigParam(String configId) {
        log.info("获取太阳码的配置参数, configId:{}", configId);
        MiniCodeConfigDO miniCodeConfigDO = miniCodeConfigRepository.selectByConfigId(configId);
        QrCodeConfigParamVO paramVO = new QrCodeConfigParamVO();
        if (Objects.isNull(miniCodeConfigDO)) {
            log.info("获取太阳码的配置参数, configId:{}不存在", configId);
            return CommonResult.success(paramVO);
        }
        if (StringUtils.isNotBlank(miniCodeConfigDO.getProductCodeParam())) {
            paramVO.setProductCode(miniCodeConfigDO.getProductCodeParam());
        }
        if (StringUtils.isNotBlank(miniCodeConfigDO.getParam1())) {
            paramVO.setParam1(miniCodeConfigDO.getParam1());
        }
        if (StringUtils.isNotBlank(miniCodeConfigDO.getParam2())) {
            paramVO.setParam2(miniCodeConfigDO.getParam2());
        }
        if (StringUtils.isNotBlank(miniCodeConfigDO.getParam3())) {
            paramVO.setParam3(miniCodeConfigDO.getParam3());
        }
        if (StringUtils.isNotBlank(miniCodeConfigDO.getParam4())) {
            paramVO.setParam4(miniCodeConfigDO.getParam4());
        }
        if (StringUtils.isNotBlank(miniCodeConfigDO.getParam5())) {
            paramVO.setParam5(miniCodeConfigDO.getParam5());
        }
        return CommonResult.success(paramVO);
    }

}
