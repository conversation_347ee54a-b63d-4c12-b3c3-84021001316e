package com.jlr.ecp.notification.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.notification.constant.Constants;
import com.jlr.ecp.notification.enums.template.AutoTemplateVariableEnum;
import com.jlr.ecp.notification.enums.template.NotificationTemplateFieldEnum;
import com.jlr.ecp.notification.enums.template.SmsTemplateTypeEnum;
import com.jlr.ecp.notification.enums.template.TemplateModifyTypeEnum;
import com.jlr.ecp.notification.util.CommonUtil;
import com.jlr.ecp.notification.util.OperatorUtil;
import com.jlr.ecp.notification.config.RedisService;
import com.jlr.ecp.notification.dal.dataobject.template.MessageTemplate;
import com.jlr.ecp.notification.dal.dataobject.template.TemplateModifyLog;
import com.jlr.ecp.notification.dal.mysql.template.SmsTemplateMapper;
import com.jlr.ecp.notification.dal.mysql.template.TemplateModifyLogMapper;
import com.jlr.ecp.notification.dal.repository.MessageTemplateRepository;
import com.jlr.ecp.notification.dal.repository.TemplateModifyLogRepository;
import com.jlr.ecp.notification.dal.repository.NotificationAutoTaskRepository;
import com.jlr.ecp.notification.dto.template.AutoTemplateDTO;
import com.jlr.ecp.notification.enums.ErrorCodeConstants;
import com.jlr.ecp.notification.req.template.SmsTemplatePageReq;
import com.jlr.ecp.notification.req.template.SmsTemplateReq;
import com.jlr.ecp.notification.req.template.TemplateHistoryPageReq;
import com.jlr.ecp.notification.vo.template.AutoTemplateVariableVO;
import com.jlr.ecp.notification.vo.template.SmsTemplateDetailVo;
import com.jlr.ecp.notification.vo.template.SmsTemplateHistoryVo;
import com.jlr.ecp.notification.vo.template.SmsTemplateVo;
import cn.hutool.core.lang.Snowflake;
import com.jlr.ecp.notification.dal.dataobject.task.auto.NotificationAutoTaskDO;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class SmsTemplateServiceImplTest {

    @Mock
    private SmsTemplateMapper mockSmsTemplateMapper;
    @Mock
    private TemplateModifyLogMapper mockTemplateModifyLogMapper;
    @Mock
    private MessageTemplateRepository mockTemplateRepository;
    @Mock
    private TemplateModifyLogRepository mockTemplateModifyLogRepository;
    @Mock
    private RedisService mockRedisService;
    @Mock
    private Snowflake mockSnowflake;
    @Mock
    private NotificationAutoTaskRepository mockNotificationAutoTaskRepository;

    @InjectMocks
    private SmsTemplateServiceImpl smsTemplateServiceImplUnderTest;

    private MessageTemplate mockTemplate;
    private SmsTemplateReq mockTemplateReq;

    @Before
    public void setUp() {
        // 初始化通用的测试数据
        mockTemplate = MessageTemplate.builder()
                .businessCode("businessCode")
                .templateCode("templateCode")
                .templateName("templateName")
                .templateType(0)
                .templateContent("尊敬的车主，您购买${serviceName}，(订单号${orderNumber})已激活成功，服务有效期为${validityDate}，请前往InControl APP查看使用。")
                .templateVariables("{\"serviceName\":\"服务名称\",\"orderNumber\":\"订单号\",\"validityDate\":\"服务有效时间\"}")
                .templateRemarks("templateRemarks")
                .tenantId(0)
                .build();

        mockTemplateReq = new SmsTemplateReq();
        mockTemplateReq.setTemplateCode("templateCode");
        mockTemplateReq.setTemplateContent("尊敬的车主，您购买${serviceName}，(订单号${orderNumber})已激活成功，服务有效期为${validityDate}，请前往InControl APP查看使用。");
        mockTemplateReq.setTemplateName("templateName");
        mockTemplateReq.setTemplateRemarks("templateRemarks");

        historyPageReq = new TemplateHistoryPageReq();
        historyPageReq.setPageNo(1);
        historyPageReq.setPageSize(10);
        historyPageReq.setTemplateCode("templateCode");
    }

    @Test
    public void testSmsTemplateModify_WhenTemplateExists_ShouldSuccess() {
        // Setup
        mockTemplateReq.setBusinessCode("businessCode");
        mockTemplateReq.setTemplateName("templateName");
        mockTemplate.setTenantId(1);

        when(mockTemplateRepository.getMessageTemplateByCode(any(String.class)))
                .thenReturn(mockTemplate);
        when(mockTemplateRepository.getTemplateByTypeName(any(String.class), any(String.class), any(Integer.class)))
                .thenReturn(Collections.emptyList());
        when(mockTemplateRepository.updateById(any(MessageTemplate.class)))
                .thenReturn(1);
//        when(mockTemplateModifyLogRepository.insert(any(TemplateModifyLog.class)))
//                .thenReturn(true);
        
        // Mock OperatorUtil
        try (MockedStatic<OperatorUtil> operatorUtilMockedStatic = mockStatic(OperatorUtil.class)) {
            operatorUtilMockedStatic.when(OperatorUtil::getOperator).thenReturn("testUser");

            // Run the test
            final CommonResult<Boolean> result = smsTemplateServiceImplUnderTest.smsTemplateModify(mockTemplateReq);

            // Verify the results
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getData()).isTrue();
        }
    }

    @Test
    public void testSmsTemplateModify_WhenTemplateNotExists_ShouldReturnError() {
        // Setup
        when(mockTemplateRepository.getMessageTemplateByCode(any(String.class)))
                .thenReturn(null);

        // Run the test
        final CommonResult<Boolean> result = smsTemplateServiceImplUnderTest.smsTemplateModify(mockTemplateReq);

        // Verify the results
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ErrorCodeConstants.TEMPLATE_NO_EXIST.getCode());
    }

    @Test
    public void testSmsTemplateModify_WhenTemplateNameExists_ShouldReturnError() {
        // Setup
        mockTemplateReq.setBusinessCode("businessCode");
        mockTemplateReq.setTemplateName("templateName");

        MessageTemplate existingTemplate = MessageTemplate.builder()
                .templateCode("differentTemplateCode")
                .templateName("templateName")
                .businessCode("businessCode")
                .build();

        when(mockTemplateRepository.getMessageTemplateByCode(any(String.class)))
                .thenReturn(mockTemplate);
        when(mockTemplateRepository.getTemplateByTypeName(any(String.class), any(String.class), any(Integer.class)))
                .thenReturn(Collections.singletonList(existingTemplate));

        // Run the test
        final CommonResult<Boolean> result = smsTemplateServiceImplUnderTest.smsTemplateModify(mockTemplateReq);

        // Verify the results
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ErrorCodeConstants.TEMPLATE_NAME_REPEATABLE.getCode());
    }

    @Test
    public void testSmsTemplateModify_WhenTemplateVariableInvalid_ShouldReturnError() {
        // Setup
        mockTemplateReq.setBusinessCode("businessCode");
        mockTemplateReq.setTemplateName("templateName");
        // 设置包含无效变量的内容
        mockTemplateReq.setTemplateContent("尊敬的车主，您购买${serviceName}，(订单号${orderNumber})已激活成功，服务有效期为${validityDate}，请前往InControl APP查看使用。${invalidVar}");
        
        // 设置模板变量
        mockTemplate.setTemplateVariables("{\"serviceName\":\"服务名称\",\"orderNumber\":\"订单号\",\"validityDate\":\"服务有效时间\"}");

        when(mockTemplateRepository.getMessageTemplateByCode(any(String.class)))
                .thenReturn(mockTemplate);
        when(mockTemplateRepository.getTemplateByTypeName(any(String.class), any(String.class), any(Integer.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final CommonResult<Boolean> result = smsTemplateServiceImplUnderTest.smsTemplateModify(mockTemplateReq);

        // Verify the results
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ErrorCodeConstants.TEMPLATE_VAR_ERROR.getCode());
    }

    @Test
    public void testCheckSmsTemplateReq_WhenAllFieldsValid_ShouldReturnSuccess() {
        // Run the test
        final CommonResult<Boolean> result = smsTemplateServiceImplUnderTest.checkSmsTemplateReq(mockTemplateReq);

        // Verify the results
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isTrue();
    }

    @Test
    public void testCheckSmsTemplateReq_WhenTemplateCodeIsNull_ShouldReturnError() {
        // Setup
        mockTemplateReq.setTemplateCode(null);

        // Run the test
        final CommonResult<Boolean> result = smsTemplateServiceImplUnderTest.checkSmsTemplateReq(mockTemplateReq);

        // Verify the results
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ErrorCodeConstants.TEMPLATE_CODE_NULL.getCode());
    }

    @Test
    public void testCheckSmsTemplateReq_WhenTemplateContentIsNull_ShouldReturnError() {
        // Setup
        mockTemplateReq.setTemplateContent(null);

        // Run the test
        final CommonResult<Boolean> result = smsTemplateServiceImplUnderTest.checkSmsTemplateReq(mockTemplateReq);

        // Verify the results
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ErrorCodeConstants.TEMPLATE_CONTENT_NULL.getCode());
    }

    @Test
    public void testCheckSmsTemplateReq_WhenTemplateContentExceedsLimit_ShouldReturnError() {
        // Setup
        mockTemplateReq.setTemplateContent("a".repeat(901));

        // Run the test
        final CommonResult<Boolean> result = smsTemplateServiceImplUnderTest.checkSmsTemplateReq(mockTemplateReq);

        // Verify the results
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ErrorCodeConstants.TEMPLATE_CONTENT_OUT_LIMIT.getCode());
    }

    @Test
    public void testGetSmsTemplateDetail_WhenTemplateExists_ShouldReturnSuccess() {
        // Setup
        when(mockTemplateRepository.getMessageTemplateByCode(any(String.class)))
                .thenReturn(mockTemplate);
        
        // Mock CommonUtil
        try (MockedStatic<CommonUtil> commonUtilMockedStatic = mockStatic(CommonUtil.class)) {
            commonUtilMockedStatic.when(() -> CommonUtil.getBusinessNameFromRedis(eq(mockRedisService), any(String.class), any(String.class)))
                    .thenReturn("businessName");

            // Run the test
            final CommonResult<SmsTemplateDetailVo> result = smsTemplateServiceImplUnderTest.getSmsTemplateDetail("templateCode");

            // Verify the results
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getData()).isNotNull();
            assertThat(result.getData().getTemplateCode()).isEqualTo("templateCode");
            assertThat(result.getData().getTemplateName()).isEqualTo("templateName");
            assertThat(result.getData().getBusinessName()).isEqualTo("businessName");
        }
    }

//    @Test
//    public void testGetTemplateHistory_WhenHistoryExists_ShouldReturnSuccess() {
//        // Setup
//        final TemplateHistoryPageReq historyPageReq = new TemplateHistoryPageReq();
//        historyPageReq.setPageNo(1);
//        historyPageReq.setPageSize(10);
//        historyPageReq.setTemplateCode("templateCode");
//
//        final TemplateModifyLog modifyLog = TemplateModifyLog.builder()
//                .templateCode("templateCode")
//                .modifyContent("编辑通知模板")
//                .modifyUser("testUser")
//                .modifyTime(LocalDateTime.now())
//                .tenantId(0)
//                .build();
//
//        final Page<TemplateModifyLog> expectedPage = new Page<>(1L, 10L, 1L, true);
//        expectedPage.setRecords(Collections.singletonList(modifyLog));
//
//        when(mockTemplateModifyLogRepository.queryTemplateModifyLogPage(any(TemplateHistoryPageReq.class)))
//                .thenReturn(expectedPage);
//
//        // Run the test
//        final PageResult<SmsTemplateHistoryVo> result = smsTemplateServiceImplUnderTest.getTemplateHistory(historyPageReq);
//
//        // Verify the results
//        assertThat(result).isNotNull();
//        assertThat(result.getTotal()).isEqualTo(1L);
//        assertThat(result.getList()).isNotEmpty();
//        assertThat(result.getList().get(0).getTemplateCode()).isEqualTo("templateCode");
//        assertThat(result.getList().get(0).getModifyUser()).isEqualTo("testUser");
//    }

    @Test
    public void testGetTemplateList_WithValidParameters_ShouldReturnSuccess() {
        // Setup
        final SmsTemplatePageReq pageReq = new SmsTemplatePageReq();
        pageReq.setPageNo(1);
        pageReq.setPageSize(10);
        pageReq.setBusinessCode(Collections.singletonList("businessCode"));
        pageReq.setTimeSortType(0);
        pageReq.setTemplateType(1);

        final Page<MessageTemplate> expectedPage = new Page<>(1L, 10L, 1L, true);
        expectedPage.setRecords(Collections.singletonList(mockTemplate));

        when(mockTemplateRepository.selectAutoTemplatePage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(expectedPage);

        // Run the test
        final PageResult<SmsTemplateVo> result = smsTemplateServiceImplUnderTest.getTemplateList(pageReq);

        // Verify the results
        assertThat(result).isNotNull();
        assertThat(result.getTotal()).isEqualTo(1L);
        assertThat(result.getList()).isNotEmpty();
        assertThat(result.getList().get(0).getTemplateCode()).isEqualTo("templateCode");
    }

    @Test
    public void testBuildSmsTemplateVo_WithValidTemplate_ShouldReturnSuccess() {
        // Run the test
        final SmsTemplateVo result = smsTemplateServiceImplUnderTest.buildSmsTemplateVo(mockTemplate);

        // Verify the results
        assertThat(result).isNotNull();
        assertThat(result.getTemplateCode()).isEqualTo("templateCode");
        assertThat(result.getTemplateName()).isEqualTo("templateName");
        assertThat(result.getTemplateContent()).isEqualTo(mockTemplate.getTemplateContent());
    }

    @Test
    public void testGetTemplateVoType_WithValidType_ShouldReturnCorrectDescription() {
        // Run the test
        final String result = smsTemplateServiceImplUnderTest.getTemplateVoType(1);

        // Verify the results
        assertThat(result).isEqualTo("自动配置");
    }

    @Test
    public void testQueryTemplatePage_WithValidParameters_ShouldReturnSuccess() {
        // Setup
        final SmsTemplatePageReq pageReq = new SmsTemplatePageReq();
        pageReq.setPageNo(1);
        pageReq.setPageSize(10);
        pageReq.setBusinessCode(Collections.singletonList("businessCode"));
        pageReq.setTimeSortType(0);
        pageReq.setTemplateType(1);
        pageReq.setTemplateTypeSort(0);

        final Page<MessageTemplate> expectedPage = new Page<>(1L, 10L, 1L, true);
        expectedPage.setRecords(Collections.singletonList(mockTemplate));

        when(mockTemplateRepository.selectAutoTemplatePage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(expectedPage);

        // Run the test
        final Page<MessageTemplate> result = smsTemplateServiceImplUnderTest.queryTemplatePage(pageReq);

        // Verify the results
        assertThat(result).isNotNull();
        assertThat(result.getTotal()).isEqualTo(1L);
        assertThat(result.getRecords()).isNotEmpty();
        assertThat(result.getRecords().get(0).getTemplateCode()).isEqualTo("templateCode");
    }

    @Test
    public void testQueryTemplatePage_WithNullParameters_ShouldReturnEmptyPage() {
        // Setup
        final SmsTemplatePageReq pageReq = new SmsTemplatePageReq();
        pageReq.setPageNo(1);
        pageReq.setPageSize(10);

        final Page<MessageTemplate> expectedPage = new Page<>(1L, 10L, 0L, true);
        expectedPage.setRecords(Collections.emptyList());

        when(mockTemplateRepository.selectAutoTemplatePage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(expectedPage);

        // Run the test
        final Page<MessageTemplate> result = smsTemplateServiceImplUnderTest.queryTemplatePage(pageReq);

        // Verify the results
        assertThat(result).isNotNull();
        assertThat(result.getTotal()).isEqualTo(0L);
        assertThat(result.getRecords()).isEmpty();
    }

    @Test
    public void testGetTemplateList_WithInvalidParameters_ShouldReturnEmptyPage() {
        // Setup
        final SmsTemplatePageReq pageReq = new SmsTemplatePageReq();
        pageReq.setPageNo(-1);
        pageReq.setPageSize(0);

        final Page<MessageTemplate> expectedPage = new Page<>(1L, 10L, 0L, true);
        expectedPage.setRecords(Collections.emptyList());

        when(mockTemplateRepository.selectAutoTemplatePage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(expectedPage);

        // Run the test
        final PageResult<SmsTemplateVo> result = smsTemplateServiceImplUnderTest.getTemplateList(pageReq);

        // Verify the results
        assertThat(result).isNotNull();
        assertThat(result.getTotal()).isEqualTo(0L);
        assertThat(result.getList()).isEmpty();
    }

    @Test
    public void testGetTemplateList_WithSortParameters_ShouldReturnSuccess() {
        // Setup
        final SmsTemplatePageReq pageReq = new SmsTemplatePageReq();
        pageReq.setPageNo(1);
        pageReq.setPageSize(10);
        pageReq.setTimeSortType(1);
        pageReq.setTemplateTypeSort(1);

        final Page<MessageTemplate> expectedPage = new Page<>(1L, 10L, 1L, true);
        expectedPage.setRecords(Collections.singletonList(mockTemplate));

        when(mockTemplateRepository.selectAutoTemplatePage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(expectedPage);

        // Run the test
        final PageResult<SmsTemplateVo> result = smsTemplateServiceImplUnderTest.getTemplateList(pageReq);

        // Verify the results
        assertThat(result).isNotNull();
        assertThat(result.getTotal()).isEqualTo(1L);
        assertThat(result.getList()).isNotEmpty();
    }

    @Test
    public void testAddSmsAutoTemplate_WhenSuccess_ShouldReturnSuccess() {
        // Setup
        AutoTemplateDTO autoTemplateDTO = new AutoTemplateDTO();
        autoTemplateDTO.setTemplateName("autoTemplate");
        autoTemplateDTO.setBusinessCode("businessCode");
        autoTemplateDTO.setTemplateContent("content");
        autoTemplateDTO.setTemplateType(1);

        when(mockTemplateRepository.addMessageTemplate(any(MessageTemplate.class)))
                .thenReturn(true);
        
        // Mock snowflake
        when(mockSnowflake.nextIdStr())
                .thenReturn("123456789");
        
        // Mock WebFrameworkUtils
        try (MockedStatic<WebFrameworkUtils> webFrameworkUtilsMockedStatic = mockStatic(WebFrameworkUtils.class)) {
            webFrameworkUtilsMockedStatic.when(WebFrameworkUtils::getLoginUserName)
                    .thenReturn("testUser");

            // Run the test
            final CommonResult<Boolean> result = smsTemplateServiceImplUnderTest.addSmsAutoTemplate(autoTemplateDTO);

            // Verify the results
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getData()).isTrue();
        }
    }

    @Test
    public void testDeleteAutoTemplate_WhenSuccess_ShouldReturnSuccess() {
        // Setup
        when(mockTemplateRepository.getMessageTemplateByCode(any(String.class)))
                .thenReturn(mockTemplate);
        when(mockNotificationAutoTaskRepository.queryAutoTaskByTemplateCode(any(String.class)))
                .thenReturn(Collections.emptyList());
        when(mockTemplateRepository.deleteTemplateByCode(any(String.class)))
                .thenReturn(true);

        // Run the test
        final CommonResult<Boolean> result = smsTemplateServiceImplUnderTest.deleteAutoTemplate("templateCode");

        // Verify the results
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isTrue();
        
        // Verify the interactions
        verify(mockTemplateRepository).getMessageTemplateByCode("templateCode");
        verify(mockNotificationAutoTaskRepository).queryAutoTaskByTemplateCode("templateCode");
        verify(mockTemplateRepository).deleteTemplateByCode("templateCode");
    }

    @Test
    public void testDeleteAutoTemplate_WhenTemplateCodeNull_ShouldReturnError() {
        // Run the test
        final CommonResult<Boolean> result = smsTemplateServiceImplUnderTest.deleteAutoTemplate("");

        // Verify the results
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ErrorCodeConstants.TEMPLATE_CODE_NULL.getCode());
    }

    @Mock
    private TemplateModifyLogRepository templateModifyLogRepository;


    private TemplateHistoryPageReq historyPageReq;

    @Test
    public void testGetTemplateHistory_WhenQueryReturnsNull_ShouldReturnNull() {
        // Arrange
        when(templateModifyLogRepository.queryTemplateModifyLogPage(any(TemplateHistoryPageReq.class))).thenReturn(null);

        // Act
        PageResult<SmsTemplateHistoryVo> result = smsTemplateServiceImplUnderTest.getTemplateHistory(historyPageReq);

        // Assert
        assertThat(result).isNull();
        verify(templateModifyLogRepository).queryTemplateModifyLogPage(historyPageReq);
    }

    @Test
    public void testGetTemplateHistory_WhenNoRecordsInPage_ShouldReturnEmptyPageResult() {
        // Arrange
        Page<TemplateModifyLog> emptyPage = new Page<>(1, 10, 0, true);
        emptyPage.setRecords(Collections.emptyList());
        when(templateModifyLogRepository.queryTemplateModifyLogPage(any(TemplateHistoryPageReq.class))).thenReturn(emptyPage);

        // Act
        PageResult<SmsTemplateHistoryVo> result = smsTemplateServiceImplUnderTest.getTemplateHistory(historyPageReq);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getTotal()).isEqualTo(0);
        assertThat(result.getList()).isEmpty();
        verify(templateModifyLogRepository).queryTemplateModifyLogPage(historyPageReq);
    }

    @Test
    public void testGetTemplateHistory_WhenRecordsExist_ShouldConvertSuccessfully() {
        // Arrange
        TemplateModifyLog modifyLog = TemplateModifyLog.builder()
                .id(1L)
                .templateCode("templateCode")
                .modifyModule("模块A")
                .modifyFieldCount(2)
                .modifyFieldOldValue("{\"字段1\":\"旧值\"}")
                .modifyFieldNewValue("{\"字段1\":\"新值\"}")
                .operateUser("testUser")
                .operateTime(LocalDateTime.now())
                .tenantId(1)
                .build();

        Page<TemplateModifyLog> page = new Page<>(1, 10, 1, true);
        page.setRecords(Collections.singletonList(modifyLog));

        when(templateModifyLogRepository.queryTemplateModifyLogPage(any(TemplateHistoryPageReq.class))).thenReturn(page);

        Set<String> detailFields = Collections.singleton("字段1");
        try (MockedStatic<NotificationTemplateFieldEnum> mocked = mockStatic(NotificationTemplateFieldEnum.class)) {
            mocked.when(NotificationTemplateFieldEnum::getAllDetailFields).thenReturn(detailFields);

            // Act
            PageResult<SmsTemplateHistoryVo> result = smsTemplateServiceImplUnderTest.getTemplateHistory(historyPageReq);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getTotal()).isEqualTo(1);
            assertThat(result.getList()).hasSize(1);

            SmsTemplateHistoryVo vo = result.getList().get(0);
            assertThat(vo.getId()).isEqualTo(modifyLog.getId());
            assertThat(vo.getTemplateCode()).isEqualTo(modifyLog.getTemplateCode());
            assertThat(vo.getModifyModule()).isEqualTo(modifyLog.getModifyModule());
            assertThat(vo.getModifyFieldCount()).isEqualTo(modifyLog.getModifyFieldCount());
            assertThat(vo.getModifyFieldNewValue()).isEqualTo(modifyLog.getModifyFieldNewValue());
            assertThat(vo.getOperateUser()).isEqualTo(modifyLog.getOperateUser());
            assertThat(vo.getOperateTime()).isEqualTo(modifyLog.getOperateTime());
            assertThat(vo.getModifyField()).isEqualTo("字段1");
            assertThat(vo.getModifyDetail()).isTrue(); // 字段1在detailFields中
        }
    }

    @Test
    public void testGetTemplateHistory_WhenOldValueIsNull_ShouldHandleGracefully() {
        // Arrange
        TemplateModifyLog modifyLog = TemplateModifyLog.builder()
                .id(1L)
                .templateCode("templateCode")
                .modifyModule("模块A")
                .modifyFieldCount(0)
                .modifyFieldOldValue(null)
                .modifyFieldNewValue(null)
                .operateUser("testUser")
                .operateTime(LocalDateTime.now())
                .tenantId(1)
                .build();

        Page<TemplateModifyLog> page = new Page<>(1, 10, 1, true);
        page.setRecords(Collections.singletonList(modifyLog));

        when(templateModifyLogRepository.queryTemplateModifyLogPage(any(TemplateHistoryPageReq.class))).thenReturn(page);

        try (MockedStatic<NotificationTemplateFieldEnum> mocked = mockStatic(NotificationTemplateFieldEnum.class)) {
            mocked.when(NotificationTemplateFieldEnum::getAllDetailFields).thenReturn(Collections.emptySet());

            // Act
            PageResult<SmsTemplateHistoryVo> result = smsTemplateServiceImplUnderTest.getTemplateHistory(historyPageReq);

            // Assert
            assertThat(result).isNotNull();
            SmsTemplateHistoryVo vo = result.getList().get(0);
            assertThat(vo.getModifyField()).isNull();
        }
    }

    @Test
    public void testAcquireModifyContent_TemplateNotFound() {
        // Arrange
        when(mockTemplateRepository.getMessageTemplateByCode(anyString()))
                .thenReturn(null);

        // Act
        String result = smsTemplateServiceImplUnderTest.acquireModifyContent("invalid_code");

        // Assert
        assertThat(result).isEmpty();
        verify(mockTemplateRepository).getMessageTemplateByCode("invalid_code");
    }

    @Test
    public void testAcquireModifyContent_ValidTemplateType() {
        // Arrange
        MessageTemplate template = MessageTemplate.builder()
                .templateCode("valid_code")
                .templateType(1)
                .build();

        when(mockTemplateRepository.getMessageTemplateByCode(anyString()))
                .thenReturn(template);

        TemplateModifyTypeEnum mockEnum = mock(TemplateModifyTypeEnum.class);
        when(mockEnum.getDesc()).thenReturn("编辑通知模板");

        // Act & Assert
        try (MockedStatic<TemplateModifyTypeEnum> mockedStatic = mockStatic(TemplateModifyTypeEnum.class)) {
            mockedStatic.when(() -> TemplateModifyTypeEnum.getTemplateModifyTypeEnumByCode(1))
                    .thenReturn(mockEnum);

            String result = smsTemplateServiceImplUnderTest.acquireModifyContent("valid_code");

            assertThat(result).isEqualTo("编辑通知模板");
            verify(mockTemplateRepository).getMessageTemplateByCode("valid_code");
        }
    }

    @Test
    public void testAcquireModifyContent_InvalidTemplateType() {
        // Arrange
        MessageTemplate template = MessageTemplate.builder()
                .templateCode("valid_code")
                .templateType(999)
                .build();

        when(mockTemplateRepository.getMessageTemplateByCode(anyString()))
                .thenReturn(template);

        // Act & Assert
        try (MockedStatic<TemplateModifyTypeEnum> mockedStatic = mockStatic(TemplateModifyTypeEnum.class)) {
            mockedStatic.when(() -> TemplateModifyTypeEnum.getTemplateModifyTypeEnumByCode(999))
                    .thenReturn(null);

            String result = smsTemplateServiceImplUnderTest.acquireModifyContent("valid_code");

            assertThat(result).isEmpty();
            verify(mockTemplateRepository).getMessageTemplateByCode("valid_code");
        }
    }

    /**
     * 测试 businessCode 为 LRE 的情况
     */
    @Test
    public void testQueryAutoTemplateVariableList_ForBusinessCodeLRE_ReturnsTwoVariables() {
        // Arrange
        String businessCode = BusinessIdEnum.LRE.getCode();

        // Act
        List<AutoTemplateVariableVO> result = smsTemplateServiceImplUnderTest.queryAutoTemplateVariableList(businessCode);

        // Assert
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getVariableDesc()).isEqualTo("用户子订单号");
        assertThat(result.get(0).getVariableName()).isEqualTo("bgorderNumber");
        assertThat(result.get(1).getVariableDesc()).isEqualTo("微信小程序链接");
        assertThat(result.get(1).getVariableName()).isEqualTo("wxUrl");
    }

    /**
     * 测试 businessCode 为 BRAND_GOODS 的情况
     */
    @Test
    public void testQueryAutoTemplateVariableList_ForBusinessCodeBrandGoods_ReturnsThreeVariables() {
        // Arrange
        String businessCode = BusinessIdEnum.BRAND_GOODS.getCode();

        // Act
        List<AutoTemplateVariableVO> result = smsTemplateServiceImplUnderTest.queryAutoTemplateVariableList(businessCode);

        // Assert
        assertThat(result).hasSize(3);
        assertThat(result.get(0).getVariableDesc()).isEqualTo("用户子订单号");
        assertThat(result.get(0).getVariableName()).isEqualTo("bgorderNumber");
        assertThat(result.get(1).getVariableDesc()).isEqualTo("订单行物流编号");
        assertThat(result.get(1).getVariableName()).isEqualTo("logisticsNumber");
        assertThat(result.get(2).getVariableDesc()).isEqualTo("微信小程序链接");
        assertThat(result.get(2).getVariableName()).isEqualTo("wxUrl");
    }

    /**
     * 测试 businessCode 为 VCS 的情况
     */
    @Test
    public void testQueryAutoTemplateVariableList_ForBusinessCodeVCS_ReturnsFourVariables() {
        // Arrange
        String businessCode = BusinessIdEnum.VCS.getCode();

        // Act
        List<AutoTemplateVariableVO> result = smsTemplateServiceImplUnderTest.queryAutoTemplateVariableList(businessCode);

        // Assert
        assertThat(result).hasSize(4);
        assertThat(result.get(0).getVariableDesc()).isEqualTo("品牌+车型");
        assertThat(result.get(0).getVariableName()).isEqualTo("brandAndModel");
        assertThat(result.get(1).getVariableDesc()).isEqualTo("服务名称");
        assertThat(result.get(1).getVariableName()).isEqualTo("serviceName");
        assertThat(result.get(2).getVariableDesc()).isEqualTo("服务到期时间");
        assertThat(result.get(2).getVariableName()).isEqualTo("expireDate");
        assertThat(result.get(3).getVariableDesc()).isEqualTo("微信小程序链接");
        assertThat(result.get(3).getVariableName()).isEqualTo("wxUrl");
    }

    @Test
    public void testQueryTemplateByType_ReturnsEmptyResult_WhenNoTemplates() {
        // Arrange
        Integer templateType = 1;
        Integer autoType = 2;
        String businessCode = "business";

        // Mock repository 返回空列表
        when(mockTemplateRepository.getTemplateListByType(templateType, autoType, businessCode))
                .thenReturn(Collections.emptyList());

        // Act
        CommonResult<List<SmsTemplateVo>> result = smsTemplateServiceImplUnderTest
                .queryTemplateByType(templateType, autoType, businessCode);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isNull();
    }

    @Test
    public void testQueryTemplateByType_ReturnsTemplates_WhenTemplatesExist() {
        // Arrange
        Integer templateType = 1;
        Integer autoType = 2;
        String businessCode = "businessCode";
        String businessName = "businessName";
        Integer enumCode = 1;
        String enumDesc = "自动配置";

        // 创建模板数据
        MessageTemplate messageTemplate = MessageTemplate.builder()
                .businessCode(businessCode)
                .templateCode("templateCode")
                .templateName("templateName")
                .templateType(enumCode)
                .templateContent("content")
                .templateVariables("variables")
                .build();

        List<MessageTemplate> templateList = Collections.singletonList(messageTemplate);

        // Mock repository 返回模板列表
        when(mockTemplateRepository.getTemplateListByType(templateType, autoType, businessCode))
                .thenReturn(templateList);

        // Mock Redis 获取 businessName
        when(mockRedisService.getCacheMapValue(Constants.BUSINESS_CACHE_KEY, businessCode))
                .thenReturn(businessName);

        // Mock 枚举获取类型描述
        try (MockedStatic<SmsTemplateTypeEnum> smsTemplateTypeMockedStatic = Mockito.mockStatic(SmsTemplateTypeEnum.class)) {
            SmsTemplateTypeEnum mockEnum = mock(SmsTemplateTypeEnum.class);
            when(mockEnum.getDesc()).thenReturn(enumDesc);
            smsTemplateTypeMockedStatic.when(() -> SmsTemplateTypeEnum.getTemplateByCode(anyInt()))
                    .thenReturn(mockEnum);

            // Act
            CommonResult<List<SmsTemplateVo>> result = smsTemplateServiceImplUnderTest
                    .queryTemplateByType(templateType, autoType, businessCode);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            List<SmsTemplateVo> voList = result.getData();
            assertThat(voList).hasSize(1);

            SmsTemplateVo vo = voList.get(0);
            assertThat(vo.getBusinessCode()).isEqualTo(businessCode);
            assertThat(vo.getBusinessName()).isEqualTo(businessName);
            assertThat(vo.getTemplateType()).isEqualTo(enumDesc);
            assertThat(vo.getTemplateContent()).isEqualTo("content");
            assertThat(vo.getTemplateVariables()).isEqualTo("variables");
        }
    }

    @Test
    public void testQueryTemplateByType_ReturnsEmptyTemplateType_WhenEnumReturnsNull() {
        // Arrange
        Integer templateType = 1;
        Integer autoType = 2;
        String businessCode = "businessCode";

        MessageTemplate messageTemplate = MessageTemplate.builder()
                .businessCode(businessCode)
                .templateCode("templateCode")
                .templateName("templateName")
                .templateType(999) // 不存在的类型
                .templateContent("content")
                .templateVariables("variables")
                .build();

        List<MessageTemplate> templateList = Collections.singletonList(messageTemplate);

        // Mock repository 返回模板列表
        when(mockTemplateRepository.getTemplateListByType(templateType, autoType, businessCode))
                .thenReturn(templateList);

        // Mock Redis
        when(mockRedisService.getCacheMapValue(Constants.BUSINESS_CACHE_KEY, businessCode))
                .thenReturn("businessName");

        // Mock 枚举返回 null
        try (MockedStatic<SmsTemplateTypeEnum> smsTemplateTypeMockedStatic = Mockito.mockStatic(SmsTemplateTypeEnum.class)) {
            smsTemplateTypeMockedStatic.when(() -> SmsTemplateTypeEnum.getTemplateByCode(999))
                    .thenReturn(null);

            // Act
            CommonResult<List<SmsTemplateVo>> result = smsTemplateServiceImplUnderTest
                    .queryTemplateByType(templateType, autoType, businessCode);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            List<SmsTemplateVo> voList = result.getData();
            assertThat(voList).hasSize(1);
            assertThat(voList.get(0).getTemplateType()).isEqualTo("");
        }
    }



}
