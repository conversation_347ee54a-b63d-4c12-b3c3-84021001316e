package com.jlr.ecp.notification.service.manual.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.notification.config.RedisService;
import com.jlr.ecp.notification.constant.Constants;
import com.jlr.ecp.notification.dal.dataobject.task.NotificationTask;
import com.jlr.ecp.notification.dal.dataobject.template.MessageTemplate;
import com.jlr.ecp.notification.dal.dataobject.template.TemplateModifyLog;
import com.jlr.ecp.notification.dal.repository.MessageTaskRepository;
import com.jlr.ecp.notification.dal.repository.MessageTemplateRepository;
import com.jlr.ecp.notification.dal.repository.TemplateModifyLogRepository;
import com.jlr.ecp.notification.dto.template.ManualTemplateDTO;
import com.jlr.ecp.notification.enums.ErrorCodeConstants;
import com.jlr.ecp.notification.enums.IsDeleteEnum;
import com.jlr.ecp.notification.enums.SortEnum;
import com.jlr.ecp.notification.enums.template.AutoTemplateTimeType;
import com.jlr.ecp.notification.enums.template.NotificationTemplateFieldEnum;
import com.jlr.ecp.notification.enums.template.SmsTemplateTypeEnum;
import com.jlr.ecp.notification.enums.template.TemplateModifyTypeEnum;
import com.jlr.ecp.notification.req.template.SmsTemplatePageReq;
import com.jlr.ecp.notification.req.template.TemplateHistoryPageReq;
import com.jlr.ecp.notification.service.manual.ManualTemplateService;
import com.jlr.ecp.notification.util.CommonUtil;
import com.jlr.ecp.notification.util.OperatorUtil;
import com.jlr.ecp.notification.util.sms.TimeFormatUtil;
import com.jlr.ecp.notification.vo.template.ManualTemplateDetailVo;
import com.jlr.ecp.notification.vo.template.SmsTemplateHistoryDetailVO;
import com.jlr.ecp.notification.vo.template.SmsTemplateHistoryVo;
import com.jlr.ecp.notification.vo.template.SmsTemplateVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

@Service
@Slf4j
public class ManualTemplateServiceImpl implements ManualTemplateService {
    @Resource
    private MessageTemplateRepository templateRepository;

    @Resource
    private TemplateModifyLogRepository templateModifyLogRepository;

    @Resource
    private MessageTaskRepository messageTaskRepository;

    @Resource
    private RedisService redisService;

    /**
     * 获取手动模板列表
     *
     * @param pageReq 页面请求对象，包含分页信息和查询条件
     * @return 包含短信模板Vo列表和总记录数的分页结果对象
     */
    @Override
    public PageResult<SmsTemplateVo> getManualTemplateList(SmsTemplatePageReq pageReq) {
        Page<MessageTemplate> pages = queryTemplatePage(pageReq);
        List<MessageTemplate> messageTemplates = pages.getRecords();
        List<SmsTemplateVo> smsTemplateVos = buildSmsTemplateVoList(messageTemplates);
        return new PageResult<>(smsTemplateVos, pages.getTotal());
    }

    /**
     * 手动添加短信模板
     *
     * @param manualTemplateDTO 包含手动模板信息的数据传输对象
     * @return 返回操作结果，成功返回true，失败返回false
     */
    @Override
    public CommonResult<Boolean> addSmsManualTemplate(ManualTemplateDTO manualTemplateDTO) {
        CommonResult<Boolean> checkResp = checkManualTemplateDTO(manualTemplateDTO);
        if (!checkResp.isSuccess()) {
            return checkResp;
        }
        List<MessageTemplate> messageTemplateList = templateRepository
                .getTemplateByTypeName(manualTemplateDTO.getTemplateName(), manualTemplateDTO.getBusinessCode(),
                        SmsTemplateTypeEnum.MANUAL.getCode());
        if (CollUtil.isNotEmpty(messageTemplateList)) {
            return CommonResult.error(ErrorCodeConstants.TEMPLATE_NAME_REPEATABLE);
        }
        String templateCode = UUID.randomUUID().toString().replace("-", "");
        manualTemplateDTO.setTemplateCode(templateCode);
        MessageTemplate messageTemplate = buildMessageTemplate(manualTemplateDTO);
        Boolean resp = templateRepository.addMessageTemplate(messageTemplate);
        if (resp) {
            return CommonResult.success(true);
        }
        return CommonResult.success(false);
    }

    /**
     * 获取短信模板的手动编辑历史记录
     *
     * @param historyPageReq 包含查询条件和分页信息的请求对象
     * @return 返回一个PageResult对象，其中包含SmsTemplateHistoryVo对象列表和总记录数
     */
    @Override
    public PageResult<SmsTemplateHistoryVo> getManualTemplateHistory(TemplateHistoryPageReq historyPageReq) {
        Page<TemplateModifyLog> templateModifyLogPage= templateModifyLogRepository.queryTemplateModifyLogPage(historyPageReq);
        if (Objects.isNull(templateModifyLogPage)) {
            log.info("模板编辑历史记录为空, historyPageReq:{}", historyPageReq);
            return null;
        }
        List<TemplateModifyLog> templateModifyLogs = templateModifyLogPage.getRecords();
        List<SmsTemplateHistoryVo> historyVos = buildTemplateHistoryVoList(templateModifyLogs);
        return new PageResult<>(historyVos, templateModifyLogPage.getTotal());
    }

    @Override
    public SmsTemplateHistoryDetailVO getManualTemplateModifyDetail(Long logId) {
        TemplateModifyLog logDO = templateModifyLogRepository.getById(logId);
        if (logDO == null) {
            log.info("模板修改记录不存在, logId:{}", logId);
            return null;
        }

        SmsTemplateHistoryDetailVO vo = new SmsTemplateHistoryDetailVO();
        BeanUtils.copyProperties(logDO, vo);

        // 解析JSON字段
//        JSONObject oldJson = JSONUtil.parseObj(logDO.getModifyFieldOldValue());
//        JSONObject newJson = JSONUtil.parseObj(logDO.getModifyFieldNewValue());
//
//        List<SmsTemplateHistoryDetailVO.FieldChange> changes = new ArrayList<>();
//        oldJson.forEach((key, oldValue) -> {
//            String newValue = newJson.getStr(key);
//            changes.add(new SmsTemplateHistoryDetailVO.FieldChange(key, oldValue.toString(), newValue));
//        });
//
//        vo.setChanges(changes);
        return vo;
    }

    /**
     * 修改手动短信模板
     *
     * @param manualTemplateDTO 包含手动模板信息的数据传输对象
     * @return 返回操作结果，成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> updateSmsManualTemplate(ManualTemplateDTO manualTemplateDTO) {
        CommonResult<Boolean> checkResp = checkManualTemplateDTO(manualTemplateDTO);
        if (!checkResp.isSuccess()) {
            return checkResp;
        }
        if (StringUtils.isBlank(manualTemplateDTO.getTemplateCode())) {
            return CommonResult.error(ErrorCodeConstants.TEMPLATE_CODE_NULL);
        }
        MessageTemplate template = templateRepository.getMessageTemplateByCode(manualTemplateDTO.getTemplateCode());
        if (Objects.isNull(template)) {
            return CommonResult.error(ErrorCodeConstants.TEMPLATE_NO_EXIST);
        }

        // 复制template为originalMessageTemplateFromDB
        MessageTemplate originalMessageTemplateFromDB = new MessageTemplate();
        BeanUtils.copyProperties(template, originalMessageTemplateFromDB);
        log.info("手动模版编辑smsTemplateModify, originalMessageTemplateFromDB:{}", JSON.toJSONString(originalMessageTemplateFromDB));

        List<MessageTemplate> messageTemplateList = templateRepository
                .getTemplateByTypeName(manualTemplateDTO.getTemplateName(), manualTemplateDTO.getBusinessCode(),
                        SmsTemplateTypeEnum.MANUAL.getCode());
        if (CollUtil.isNotEmpty(messageTemplateList)) {
            for (MessageTemplate messageTemplate : messageTemplateList) {
                if (!manualTemplateDTO.getTemplateCode().equals(messageTemplate.getTemplateCode())) {
                    return CommonResult.error(ErrorCodeConstants.TEMPLATE_NAME_REPEATABLE);
                }
            }
        }
        MessageTemplate messageTemplate = buildMessageTemplate(manualTemplateDTO);
        templateRepository.updateMessageManualTemplate(messageTemplate);
        addTemplateModifyLog(originalMessageTemplateFromDB, manualTemplateDTO);
        return CommonResult.success(true);
    }

    /**
     * 手动模板详情
     *
     * @param templateCode 模板code
     * @return ManualTemplateDetailVo
     * */
    @Override
    public CommonResult<ManualTemplateDetailVo> smsManualTemplateDetail(String templateCode) {
        if (StringUtils.isBlank(templateCode)) {
            return CommonResult.error(ErrorCodeConstants.TEMPLATE_CODE_NULL);
        }
        MessageTemplate messageTemplate = templateRepository.getManualTemplateDetail(templateCode);
        if (Objects.isNull(messageTemplate)) {
            return CommonResult.success(null);
        }
        ManualTemplateDetailVo manualTemplateDetailVo = buildManualTemplateDetailVo(messageTemplate);
        return CommonResult.success(manualTemplateDetailVo);
    }

    /**
     * 删除手动模板
     *
     * @param templateCode 模板code
     * @return CommonResult<Boolean>
     * */
    @Override
    public CommonResult<Boolean> deleteSmsManualTemplate(String templateCode) {
        if (StringUtils.isBlank(templateCode)) {
            return CommonResult.error(ErrorCodeConstants.TEMPLATE_CODE_NULL);
        }
        List<NotificationTask> notificationTaskList = messageTaskRepository.queryTasksByTemplateCode(templateCode);
        if (!CollectionUtils.isEmpty(notificationTaskList)) {
            return CommonResult.error(ErrorCodeConstants.TEMPLATE_DELETE_ERROR);
        }
        Boolean resp = templateRepository.deleteTemplateByCode(templateCode);
        return CommonResult.success(resp);
    }

    /**
     *  分页查询模板列表页
     * @param smsTemplatePageReq 模板分页查询参数
     * @return Page<MessageTemplate>
     * */
    public Page<MessageTemplate> queryTemplatePage(SmsTemplatePageReq smsTemplatePageReq) {
        Page<MessageTemplate> pageReq = new Page<>(smsTemplatePageReq.getPageNo(), smsTemplatePageReq.getPageSize());
        LambdaQueryWrapper<MessageTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(!CollectionUtils.isEmpty(smsTemplatePageReq.getBusinessCode()),
                        MessageTemplate::getBusinessCode, smsTemplatePageReq.getBusinessCode())
                .eq(Objects.nonNull(smsTemplatePageReq.getTemplateType()),MessageTemplate::getTemplateType,smsTemplatePageReq.getTemplateType())
                .eq(MessageTemplate::getIsDeleted, IsDeleteEnum.NO.getStatus());
        if (SortEnum.ASC.getSortType().equals(smsTemplatePageReq.getTimeSortType())) {
            wrapper.orderByAsc(MessageTemplate::getUpdatedTime);
            wrapper.orderByAsc(MessageTemplate::getId);
        } else if (SortEnum.DESC.getSortType().equals(smsTemplatePageReq.getTimeSortType())) {
            //默认倒叙排序
            wrapper.orderByDesc(MessageTemplate::getUpdatedTime);
            wrapper.orderByAsc(MessageTemplate::getId);
        }
        if (SortEnum.ASC.getSortType().equals(smsTemplatePageReq.getTemplateTypeSort())) {
            wrapper.orderByAsc(MessageTemplate::getTemplateType);
            wrapper.orderByAsc(MessageTemplate::getId);
        } else if (SortEnum.DESC.getSortType().equals(smsTemplatePageReq.getTemplateTypeSort())) {
            wrapper.orderByDesc(MessageTemplate::getTemplateType);
            wrapper.orderByAsc(MessageTemplate::getId);
        }
        return templateRepository.selectAutoTemplatePage(pageReq, wrapper);
    }

    /**
     * 批量构建SmsTemplateHistoryVo
     * @param modifyLogs 模板编辑记录
     * @return List<SmsTemplateHistoryVo>
     * */
    public List<SmsTemplateHistoryVo> buildTemplateHistoryVoList(List<TemplateModifyLog> modifyLogs) {
        List<SmsTemplateHistoryVo> historyVos = new ArrayList<>();
        for (TemplateModifyLog modifyLog : modifyLogs) {
            historyVos.add(buildTemplateHistoryVo(modifyLog));
        }
        return historyVos;
    }

    /**
     * 构建SmsTemplateHistoryVo
     * @param modifyLog 模板编辑记录
     * @return List<SmsTemplateHistoryVo>
     * */
    public SmsTemplateHistoryVo buildTemplateHistoryVo(TemplateModifyLog modifyLog) {
        if (Objects.isNull(modifyLog)) {
            log.info("构建SmsTemplateHistoryVo, 模板编辑历史记录为空");
            return SmsTemplateHistoryVo.builder().build();
        }
        SmsTemplateHistoryVo vo = new SmsTemplateHistoryVo();
        BeanUtils.copyProperties(modifyLog, vo);

        // 获取所有需要显示详情的字段
        Set<String> allDetailFields = NotificationTemplateFieldEnum.getAllDetailFields();

        // set setModifyField
        String oldValue = modifyLog.getModifyFieldOldValue();
        if(StrUtil.isNotBlank(oldValue)){
            Set<String> oldSet = JSON.parseObject(oldValue, Feature.OrderedField).keySet();
            vo.setModifyField(String.join("、", oldSet));
            // set modifyDetail 是否显示 修改详情
            boolean modifyDetail = oldSet.stream().anyMatch(allDetailFields::contains);
            vo.setModifyDetail(modifyDetail);
        }

        return vo;
    }


    /**
     * 获取编辑日志，模板内容的操作
     * @param templateCode 模板code
     * @return String
     * */
    public String acquireModifyContent(String templateCode) {
        MessageTemplate messageTemplate = templateRepository.getMessageTemplateByCode(templateCode);
        if (Objects.isNull(messageTemplate)) {
            log.info("获取编辑日志，模板内容的操作, 查询模板为空, templateCode:{}", templateCode);
            return "";
        }
        return getTemplateModifyContent(messageTemplate.getTemplateType());
    }

    /**
     * 构建模板VO列表数据
     * @param messageTemplates 模板模板列表
     * @return List<SmsTemplateVo>
     * */
    public List<SmsTemplateVo> buildSmsTemplateVoList(List<MessageTemplate> messageTemplates) {
        List<SmsTemplateVo> resp = new ArrayList<>();
        if (CollectionUtils.isEmpty(messageTemplates)) {
            return resp;
        }
        for (MessageTemplate messageTemplate : messageTemplates) {
            resp.add(buildSmsTemplateVo(messageTemplate));
        }
        return resp;
    }

    /**
     *  构建短信模板vo
     * @param messageTemplate 短信模板数据
     * @return SmsTemplateVo
     **/
    public SmsTemplateVo buildSmsTemplateVo(MessageTemplate messageTemplate) {
        if (Objects.isNull(messageTemplate)) {
            return null;
        }
        return SmsTemplateVo.builder()
                .templateCode(messageTemplate.getTemplateCode())
                .businessCode(messageTemplate.getBusinessCode())
                .businessName(CommonUtil.getBusinessNameFromRedis(redisService, Constants.BUSINESS_CACHE_KEY,
                        messageTemplate.getBusinessCode()))
                .templateName(messageTemplate.getTemplateName())
                .templateType(getTemplateVoType(messageTemplate.getTemplateType()))
                .templateContent(messageTemplate.getTemplateContent())
                .templateVariables(messageTemplate.getTemplateVariables())
                .modifyTime(TimeFormatUtil.localDateToString(messageTemplate.getUpdatedTime()))
                .modifyBy(messageTemplate.getUpdatedBy())
                .build();
    }

    /**
     *  按照模板类型code返回模板类型描述
     * @param typeCode 模板类型code
     * @return String
     * */
    public String getTemplateVoType(Integer typeCode) {
        SmsTemplateTypeEnum typeEnum = SmsTemplateTypeEnum.getTemplateByCode(typeCode);
        if (Objects.isNull(typeEnum)) {
            log.error("短信模板类型不存在，smsTemplateTypeCode:{}", typeCode);
            return "";
        }
        return typeEnum.getDesc();
    }


    /**
     * MessageTemplate转化为ManualTemplateDetailVo
     * @param messageTemplate 手动模板数据
     * @return ManualTemplateDetailVo
     * */
    private ManualTemplateDetailVo buildManualTemplateDetailVo(MessageTemplate messageTemplate) {

        return ManualTemplateDetailVo.builder()
                .businessCode(messageTemplate.getBusinessCode())
                .templateCode(messageTemplate.getTemplateCode())
                .templateName(messageTemplate.getTemplateName())
                .templateType(getTemplateTypeDesc(messageTemplate.getTemplateType()))
                .templateContent(messageTemplate.getTemplateContent())
                .templateRemarks(messageTemplate.getTemplateRemarks())
                .build();
    }

    /**
     * 根据模板类型获取模板类型的描述信息。
     *
     * @param templateType 模板类型的代码，来自于SmsTemplateTypeEnum定义的枚举值。
     * @return 返回对应模板类型的描述信息。如果找不到对应的模板类型，则返回空字符串。
     */
    private String getTemplateTypeDesc(Integer templateType) {
        SmsTemplateTypeEnum templateTypeEnum = SmsTemplateTypeEnum.getTemplateByCode(templateType);
        if (Objects.isNull(templateTypeEnum)) {
            log.error("模板类型不存在, templateType:{}", templateType);
            return "";
        }
        return templateTypeEnum.getDesc();
    }


    /**
     * ManualTemplateDTO转化为MessageTemplate
     * @param manualTemplateDTO 手动模板数据
     * @return MessageTemplate
     * */
    private MessageTemplate buildMessageTemplate(ManualTemplateDTO manualTemplateDTO) {
        if (Objects.isNull(manualTemplateDTO)) {
            return null;
        }
        MessageTemplate messageTemplate = MessageTemplate.builder()
                .businessCode(manualTemplateDTO.getBusinessCode())
                .templateCode(manualTemplateDTO.getTemplateCode())
                .templateName(manualTemplateDTO.getTemplateName())
                .templateType(manualTemplateDTO.getTemplateType())
                .autoType(AutoTemplateTimeType.SYSTEM_REAL_TIME.getCode())
                .templateContent(manualTemplateDTO.getTemplateContent())
                .templateRemarks(manualTemplateDTO.getTemplateRemarks())
                .templateRemarks(manualTemplateDTO.getTemplateRemarks())
                .build();
        messageTemplate.setUpdatedTime(LocalDateTime.now());
        messageTemplate.setUpdatedBy(WebFrameworkUtils.getLoginUserName());
        return messageTemplate;
    }

    /**
     * 校验手动模板DTO的合法性。
     *
     * @param manualTemplateDTO 手动模板数据传输对象，包含模板的各种信息。
     * @return 返回一个通用结果对象，如果校验通过，返回成功状态和true；如果校验失败，返回错误状态和相应的错误代码。
     */
    public CommonResult<Boolean> checkManualTemplateDTO(ManualTemplateDTO manualTemplateDTO) {
        if (Objects.isNull(manualTemplateDTO)) {
            return CommonResult.error(ErrorCodeConstants.MANUAL_TEMPLATE_ARGS_NULL);
        }
        if (StringUtils.isBlank(manualTemplateDTO.getBusinessCode())) {
            return CommonResult.error(ErrorCodeConstants.MANUAL_TEMPLATE_ARGS_NULL);
        }
        if (StringUtils.isBlank(manualTemplateDTO.getTemplateName())) {
            return CommonResult.error(ErrorCodeConstants.MANUAL_TEMPLATE_ARGS_NULL);
        }
        if (Objects.isNull(manualTemplateDTO.getTemplateType())) {
            return CommonResult.error(ErrorCodeConstants.MANUAL_TEMPLATE_ARGS_NULL);
        }
        if (StringUtils.isBlank(manualTemplateDTO.getTemplateContent())) {
            return CommonResult.error(ErrorCodeConstants.MANUAL_TEMPLATE_ARGS_NULL);
        }
        if (StringUtils.isBlank(manualTemplateDTO.getTemplateRemarks())) {
            return CommonResult.error(ErrorCodeConstants.MANUAL_TEMPLATE_ARGS_NULL);
        }
        if (manualTemplateDTO.getTemplateContent().length() >= 900) {
            return CommonResult.error(ErrorCodeConstants.TEMPLATE_CONTENT_OUT_LIMIT);
        }
        if (manualTemplateDTO.getTemplateRemarks().length() >= 900) {
            return CommonResult.error(ErrorCodeConstants.TEMPLATE_REMARK_OUT_LIMIT);
        }
        return CommonResult.success(true);
    }

    /**
     * 添加模板编辑日志
     * @param messageTemplate 当前模板数据
     * @param manualTemplateDTO 模板编辑入参
     * */
    public void addTemplateModifyLog(MessageTemplate messageTemplate, ManualTemplateDTO manualTemplateDTO) {
        log.info("添加手动模板编辑日志, 当前模板数据:{}，入参DTO:{}",
                JSON.toJSONString(messageTemplate), JSON.toJSONString(manualTemplateDTO));

        if (Objects.isNull(manualTemplateDTO)) {
            log.info("添加模板编辑日志，模板编辑入参为空，无需添加日志");
            return ;
        }

        // 初始化模块日志收集器（复用自动模板的逻辑）
        Map<String, ModuleLogInfo> moduleLogs = new HashMap<>();

        // 检查模板名称修改
        compareAndLogField(
                messageTemplate.getTemplateName(),
                manualTemplateDTO.getTemplateName(),
                NotificationTemplateFieldEnum.EDIT_TEMPLATE_NAME,
                moduleLogs
        );

        // 检查通知内容修改
        compareAndLogField(
                messageTemplate.getTemplateContent(),
                manualTemplateDTO.getTemplateContent(),
                NotificationTemplateFieldEnum.EDIT_NOTIFY_CONTENT,
                moduleLogs
        );

        // 检查场景说明修改
        compareAndLogField(
                messageTemplate.getTemplateRemarks(),
                manualTemplateDTO.getTemplateRemarks(),
                NotificationTemplateFieldEnum.EDIT_SCENE_DESCRIPTION,
                moduleLogs
        );

        // 生成并插入日志记录
        moduleLogs.forEach((moduleName, logInfo) -> {
            TemplateModifyLog log = TemplateModifyLog.builder()
                    .templateCode(messageTemplate.getTemplateCode())
                    .modifyModule(moduleName)
                    .modifyFieldCount(logInfo.getFieldCount())
                    .modifyFieldOldValue(JSON.toJSONString(logInfo.getOldValues()))
                    .modifyFieldNewValue(JSON.toJSONString(logInfo.getNewValues()))
                    .operateUser(OperatorUtil.getOperator())
                    .operateTime(LocalDateTime.now())
//                    .tenantId(messageTemplate.getTenantId())
                    .build();

            templateModifyLogRepository.insert(log);
        });
    }

    // 直接复用自动模板的辅助方法和类
    private <T> void compareAndLogField(T oldValue, T newValue,
                                        NotificationTemplateFieldEnum fieldEnum,
                                        Map<String, ModuleLogInfo> moduleLogs) {
        if (!Objects.equals(oldValue, newValue)) {
            String moduleName = fieldEnum.getModuleName();
            moduleLogs.computeIfAbsent(moduleName, k -> new ModuleLogInfo())
                    .addField(fieldEnum.getFieldName(), oldValue, newValue);
        }
    }

    // 辅助类：模块日志信息收集器
    private static class ModuleLogInfo {
        private final Map<String, Object> oldValues = new HashMap<>();
        private final Map<String, Object> newValues = new HashMap<>();
        private int fieldCount = 0;

        public void addField(String fieldName, Object oldValue, Object newValue) {
            oldValues.put(fieldName, oldValue != null ? oldValue : "");
            newValues.put(fieldName, newValue != null ? newValue : "");
            fieldCount++;
        }

        public Map<String, Object> getOldValues() {
            return oldValues;
        }

        public Map<String, Object> getNewValues() {
            return newValues;
        }

        public int getFieldCount() {
            return fieldCount;
        }
    }

    /**
     *  按照typCode返回模板操作内容
     * @param typeCode 模板修改类型code
     * @return String
     * */
    public String getTemplateModifyContent(Integer typeCode) {
        TemplateModifyTypeEnum typeEnum = TemplateModifyTypeEnum.getTemplateModifyTypeEnumByCode(typeCode);
        if (Objects.isNull(typeEnum)) {
            log.info("按照code获取模板修改枚举为空，typeCode：{}", typeCode);
            return "";
        }
        return typeEnum.getDesc();
    }
}
