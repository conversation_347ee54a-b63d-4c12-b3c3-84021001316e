package com.jlr.ecp.notification.dal.repository.shortlink.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.notification.dal.dataobject.shortlink.ShortLinkClickUserDO;
import com.jlr.ecp.notification.dal.mysql.shortlink.ShortLinkClickUserDOMapper;
import com.jlr.ecp.notification.dal.repository.shortlink.ShortLinkClickUserRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class ShortLinkClickUserRepositoryImpl extends ServiceImpl<ShortLinkClickUserDOMapper, ShortLinkClickUserDO>
        implements ShortLinkClickUserRepository {

    @Override
    public ShortLinkClickUserDO selectUserByJlrId(String urlCode, String jlrId) {
        if (Objects.isNull(urlCode) || StringUtils.isBlank(jlrId)) {
            return null;
        }
        LambdaQueryWrapper<ShortLinkClickUserDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShortLinkClickUserDO::getUrlCode, urlCode)
                .eq(ShortLinkClickUserDO::getJlrId, jlrId)
                .eq(ShortLinkClickUserDO::getIsDeleted, false);
        return getOne(queryWrapper);
    }

    @Override
    public Long queryUserCountByUrlCode(String urlCode) {
        if (StringUtils.isBlank(urlCode)) {
            return null;
        }
        LambdaQueryWrapper<ShortLinkClickUserDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShortLinkClickUserDO::getUrlCode, urlCode)
                .eq(ShortLinkClickUserDO::getIsDeleted, false);
        return count(queryWrapper);
    }
}
