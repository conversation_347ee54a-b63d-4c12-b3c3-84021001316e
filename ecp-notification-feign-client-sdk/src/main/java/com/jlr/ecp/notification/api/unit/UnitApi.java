package com.jlr.ecp.notification.api.unit;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.api.unit.vo.ProductUnitListRespVO;
import com.jlr.ecp.notification.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.security.PermitAll;
import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 单位")
public interface UnitApi {
    String PREFIX = "/v1/subscription";

    @GetMapping(PREFIX+"/unit/findUnitList")
    @Operation(summary = "商品单位查询接口")
    @PermitAll
    @Parameter(name = "deptId", description = "部门ID", required = true, example = "1")
    CommonResult<List<ProductUnitListRespVO>> findUnitList(@RequestParam("deptId") String deptId);
}
