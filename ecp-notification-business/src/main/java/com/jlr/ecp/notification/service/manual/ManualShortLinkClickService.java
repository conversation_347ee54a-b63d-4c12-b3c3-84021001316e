package com.jlr.ecp.notification.service.manual;


import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.dto.shortlink.ManualShortLinkClickDTO;
import com.jlr.ecp.notification.vo.shortlink.QrCodeConfigParamVO;

public interface ManualShortLinkClickService {
    /**
     * 更新手动短链点击率信息。
     *
     * @param clickDTO 包含短链点击相关信息的DTO对象，包括短链代码(urlCode)和用户ID(jlrId)等。
     */
    CommonResult<String> updateShortLinkClick(ManualShortLinkClickDTO clickDTO);


    /**
     * 获取太阳码配置参数并拼接为字符串结果
     *
     * @param configId 配置ID，用于查询对应的太阳码配置数据
     * @return 包含拼接后的配置参数字符串的通用返回对象
     *         当配置不存在或无有效参数时返回空字符串
     */
    CommonResult<QrCodeConfigParamVO> getQrCodeConfigParam(String configId);
}
