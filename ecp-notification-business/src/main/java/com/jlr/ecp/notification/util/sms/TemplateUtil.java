package com.jlr.ecp.notification.util.sms;


import com.alibaba.fastjson.JSON;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.tika.utils.StringUtils;

import java.util.*;


/**
 *  模板工具类
 * */
public class TemplateUtil {
    private static final Logger log = LogManager.getLogger(TemplateUtil.class);

    private static final String SPLIT = "#";

    /**
     * 把模板中的变量转化为map的（key,val）结构
     *
     * @param variables 模板中的变量字符串
     * @return Map<String, Object>
     */
    public static Map<String, String> variablesToMap(String variables) {
        if (StringUtils.isBlank(variables)) {
            return new HashMap<>();
        }
        return JSON.parseObject(variables, HashMap.class);
    }

    /**
     *  提取模板内容中的变量
     * @param content 模板内容
     * @return List<String>
     * */
    public static List<String> extractVariable(String content) {
        List<String> strList = contentClassify(content);
        List<String> resp = new ArrayList<>();
        for (String str : strList) {
            if(StringUtils.isBlank(str)) {
                continue;
            }
            if (str.length() < 3) {
                continue;
            }
            if ('$' == str.charAt(0)) {
                resp.add(str.substring(2, str.length()-1));
            }
        }
        return resp;
    }


    /**
     * 模板内容中的字符和变量分
     * @param content  模板内容
     * @return List<String>
     * */
    public static List<String> contentClassify(String content) {
        List<String> strList = new ArrayList<>();
        if (StringUtils.isBlank(content)) {
            log.info("提取变量，模板内容为空");
            return strList;
        }
        StringBuilder str = new StringBuilder();
        for (int i = 0; i < content.length(); i++) {
            if ('$' == content.charAt(i)) {
                strList.add(str.toString());
                str = new StringBuilder();
                str.append(content.charAt(i));
            } else if ('}' == content.charAt(i)) {
                str.append(content.charAt(i));
                strList.add(str.toString());
                str = new StringBuilder();
            } else {
                str.append(content.charAt(i));
            }
        }
        strList.add(str.toString());
        return strList;
    }
}
