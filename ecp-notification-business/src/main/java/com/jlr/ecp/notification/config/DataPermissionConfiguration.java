package com.jlr.ecp.notification.config;

import com.jlr.ecp.framework.common.util.json.JsonUtils;
import com.jlr.ecp.framework.datapermission.core.rule.business.BusinessDataPermissionRuleCustomizer;
import com.jlr.ecp.notification.dal.dataobject.template.MessageTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration(proxyBeanMethods = false)
public class DataPermissionConfiguration {
    @Bean
    public BusinessDataPermissionRuleCustomizer productBusinessDataPermissionRuleCustomizer() {
        log.info("初始化:BusinessDataPermissionRuleCustomizer");
        return rule -> {
            //business_code
            rule.addBusinessColumn(MessageTemplate.class, "business_code");
            log.info("BusinessDataPermissionRuleCustomizer rule instance:" + JsonUtils.toJsonString(rule));
        };
    }
}
