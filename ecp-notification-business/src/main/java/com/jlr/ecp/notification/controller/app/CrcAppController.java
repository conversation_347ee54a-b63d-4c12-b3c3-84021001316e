package com.jlr.ecp.notification.controller.app;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.dto.crc.ReadCrcMessageDTO;
import com.jlr.ecp.notification.dto.crc.SaveCrcUnreadMsgDTO;
import com.jlr.ecp.notification.service.crc.CrcUnreadMessageService;
import com.jlr.ecp.notification.vo.crc.SaveCrcUnreadMsgVO;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;

@RestController
@RequestMapping("/crc")
@Tag(name = "crc - 通知未读取的消息")
@Validated
@Slf4j
public class CrcAppController {

    @Resource
    private CrcUnreadMessageService crcUnreadMessageService;

    @PostMapping("/messages")
    @PermitAll
    @Operation(summary = "CRC - 保存发送留言的消息")
    public CommonResult<SaveCrcUnreadMsgVO> saveCrcUnReadMessage(@Validated @RequestBody SaveCrcUnreadMsgDTO crcUnreadMsgDTO) {
        if (crcUnreadMsgDTO.getBusinessCode() == null) {
            crcUnreadMsgDTO.setBusinessCode(BusinessIdEnum.VCS.getCode());
        }
        return crcUnreadMessageService.addCrcUnreadMessage(crcUnreadMsgDTO);
    }

    @GetMapping("/message/query")
    @PermitAll
    @Operation(summary = "APP -查询用户消息列表API")
    public CommonResult<Integer> searchCrcUnReadMessage(@RequestParam(value = "jlrid", required = true)String jlrid,
                                                        @RequestParam(value = "status", required = true) Integer messageStatus,
                                                        @RequestParam(value = "businessCode", required = false) String businessCode) {
        if (businessCode == null) {
            businessCode = BusinessIdEnum.VCS.getCode();
        }
        return crcUnreadMessageService.findCrcUnreadMessage(jlrid, messageStatus, businessCode);
    }

    @PostMapping("/read/all")
    @PermitAll
    @Operation(summary = "APP - 阅读所有消息API")
    public CommonResult<String> CrcUnReadMessage(@Validated @RequestBody ReadCrcMessageDTO readCrcMsgDTO) {
        if (readCrcMsgDTO.getBusinessCode() == null) {
            readCrcMsgDTO.setBusinessCode(BusinessIdEnum.VCS.getCode());
        }
        return crcUnreadMessageService.readCrcAllMessage(readCrcMsgDTO);
    }
}
