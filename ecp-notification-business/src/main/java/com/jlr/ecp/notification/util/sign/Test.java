package com.jlr.ecp.notification.util.sign;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class Test {
    public static void main(String[] args) throws Exception {
        String encode = URLEncoder.encode("路虎");
        System.out.println("转义后的值为：" + encode);
        Map<String, Object> postParameters = new HashMap<>();
//        postParameters.put("path", "ecp/pages/subscribeTo/shopDetails/shopDetails?productCode=BS003465B0FE9DE4B078B86251CB34&vinCode=SAL1A2FU3RA175768&carType="+encode);
        postParameters.put("path", "ecp/pages/mine/order/detail/index?orderCode=LRMP20240221162830XXXXXX");
        HttpHeaders headers = headers(postParameters, "/wxma/wx/urlLink", HttpMethod.POST.name(), "evoque", "Ec3199a1f");
        URI uri = UriComponentsBuilder.fromUriString("https://dpit-dev.jaguarlandrover.cn/wxma/wx/urlLink").build().toUri();
        RequestEntity requestEntity = RequestEntity.post(uri).headers(headers).body(postParameters);
        RestTemplate restTemplate1 = new RestTemplate();
        ResponseEntity<JSONObject> response = restTemplate1.exchange(requestEntity, JSONObject.class);
        JSONObject body = response.getBody();
        System.out.println("执行结果是：" + body);
    }

    private static HttpHeaders headers(Object body, String path, String httpMethod, String appKey, String secret) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put(AbstractSignature.HTTP_HEADER_CONTENT_TYPE, AbstractSignature.CONTENT_TYPE_JSON);
        // 加签
        SignatureEncoder signatureEncoder = new SignatureEncoder(path, httpMethod, headers, JSON.toJSONString(body).getBytes(StandardCharsets.UTF_8), appKey, secret);
        Map<String, Object> formParamsMap = new HashMap<>(0); // no form parameter
        signatureEncoder.build(new HashMap<>(0), formParamsMap);

        final HttpHeaders httpHeaders = new HttpHeaders();
        signatureEncoder.getHeaders().forEach(httpHeaders::add);
        return httpHeaders;
    }
}