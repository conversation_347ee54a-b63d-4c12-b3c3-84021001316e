package com.jlr.ecp.notification.kafka.send.template;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jlr.ecp.notification.kafka.message.SmsMessage;
import com.jlr.ecp.notification.kafka.message.ValetOrderMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class MessageContentUtil {

    private static final String NULL = "#null#";

    /**
     *  构建变量map
     * @param variables 变量字符串
     * @param message 消息体
     * @return  Map<String, String>
     * */
    public static Map<String, String> buildVariableMap(String variables, SmsMessage message) {
        Map<String, String> map = new HashMap<>();
        if (Objects.isNull(message)) {
            return map;
        }
        try {
            Map<String, Object> varMap = JSON.parseObject(variables, Map.class);
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> msgMap = mapper.convertValue(message, Map.class);
            for (Map.Entry<String,Object> entry : msgMap.entrySet()) {
                if (varMap.containsKey(entry.getKey())) {
                    if (Objects.nonNull(entry.getValue())) {
                        map.put(entry.getKey(), entry.getValue().toString());
                    } else {
                        map.put(entry.getKey(), NULL);
                    }
                }
            }
        } catch (Exception e) {
            log.info("构建map异常:{}", e.getMessage());
        }
        return map;
    }

    /**
     *  构建代客下单变量map
     * @param variables 变量字符串
     * @param message 消息体
     * @return  Map<String, String>
     * */
    public static Map<String, String> buildValetVariableMap(String variables, ValetOrderMessage message) {
        Map<String, String> map = new HashMap<>();
        if (Objects.isNull(message)) {
            return map;
        }
        try {
            Map<String, Object> varMap = JSON.parseObject(variables, Map.class);
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> msgMap = mapper.convertValue(message, Map.class);
            for (Map.Entry<String,Object> entry : msgMap.entrySet()) {
                if (varMap.containsKey(entry.getKey())) {
                    if (Objects.nonNull(entry.getValue())) {
                        map.put(entry.getKey(), entry.getValue().toString());
                    } else {
                        map.put(entry.getKey(), NULL);
                    }
                }
            }
        } catch (Exception e) {
            log.info("构建代客下单变量map异常:{}", e.getMessage());
        }
        return map;
    }


    /**
     * 构建下发的消息内容
     * @param contents 模板内容
     * @param map 变量数据
     * @return String
     * */
    public static String getReallyContent(List<String> contents, Map<String, String> map) {
        if (CollectionUtils.isEmpty(contents)) {
            return "";
        }
        StringBuilder resp = new StringBuilder();
        for (String content : contents) {
            if (StringUtils.isBlank(content)) {
                continue;
            }
            if ('$' != content.charAt(0)) {
                resp.append(content);
            } else if (content.length() > 3) {
                String variableKey = content.substring(2, content.length()-1);
                resp.append(map.get(variableKey));
            }
        }
        return resp.toString();
    }
}
