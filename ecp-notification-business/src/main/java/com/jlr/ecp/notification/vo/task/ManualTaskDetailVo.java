package com.jlr.ecp.notification.vo.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *  手动发送任务详情vo
 *
 * <AUTHOR>
 * */

@Schema(description = "发送任务详情vo")
@Data
public class ManualTaskDetailVo {
    @Schema(description = "任务code")
    private String taskCode;

    @Schema(description = "任务名称")
    private String taskName;

    @Schema(description = "触发逻辑")
    private String triggerAction;

    @Schema(description = "定时发送时间CRON;当发送类型为定时发送时的时间的CRON表达式")
    private String taskSendScheduleTime;

    @Schema(description = "模板code")
    private String templateCode;

    @Schema(description = "模板类型")
    private String templateType;

    @Schema(description = "模板名称")
    private String templateName;

    @Schema(description = "模板内容")
    private String templateContent;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "任务发送类型")
    private Integer taskSendType;

    @Schema(description = "上传文件类型 1：手机号 2：VIN+手机号")
    private Integer submitFileType;


    @Schema(description = "消息发送文件URL")
    private String messageFile;

    @Schema(description = "错误消息文件URL 可以先保存为草稿")
    private String errorMsgFile;

    @Schema(description = "文件名")
    private String fileName;

    @Schema(description = "校验状态 true:校验完成")
    private Boolean checkStatus;

    @Schema(description = "校验结果 true:通过 false:未通过")
    private Boolean checkResult;

    @Schema(description = "品牌签名内容 1路虎中国 2捷豹路虎中国")
    private Integer signBrandType;

    @Schema(description = "发送通道 1营销短信 2通知短信")
    private Integer sendChannelType;
}
