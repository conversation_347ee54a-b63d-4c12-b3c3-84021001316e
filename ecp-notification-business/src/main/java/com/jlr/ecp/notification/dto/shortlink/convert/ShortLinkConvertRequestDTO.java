package com.jlr.ecp.notification.dto.shortlink.convert;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ShortLinkConvertRequestDTO {
    /**
     * 请求系统
     * SS, SMP, ECP等业务系统
     * MANUAL_GENERATE 比较特殊，用于特殊情况下开发协助业务手动转换，勿用
     * */
    private String sourceSystem;

    /**
     *  业务场景
     * 格式要求字母必须大写，多个单词以下划线拼接
     * 比如 CRC_IB,
     * COMMUNITY_ACTIVITY_PROMOTION，
     * */
    private String bizCase;

    /**
     * 链接类型
     * H5, WX_URL_LINK
     * */
    private String urlType;

    /**
     *  原地址
     * */
    private String originalUrl;

    /**
     *  有效类型： 短期（SHORT_TERM） 和 长期（LONG_TERM）
     * */
    private String validityType;

    /**
     * 过期天数
     * */
    private Integer expirationDays;

    /**
     * 备注
     * */
    private String remark;
}
