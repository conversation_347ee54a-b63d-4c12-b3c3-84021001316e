package com.jlr.ecp.notification.dal.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.notification.api.dto.MsgBlackListDTO;
import com.jlr.ecp.notification.dal.dataobject.blacklist.MsgBlackListDO;
import com.jlr.ecp.notification.dal.mysql.blacklist.MsgBlackListDOMapper;
import com.jlr.ecp.notification.dal.repository.MsgBlackListDORepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class MsgBlackListDORepositoryImpl implements MsgBlackListDORepository {
    @Resource
    private MsgBlackListDOMapper msgBlackListDOMapper;


    /**
     * 根据车辆VIN列表检查黑名单
     *
     * @param carVinList 车辆VIN码的列表，用于查询黑名单状态。
     * @return 返回一个MsgBlackListDTO对象的列表，表示查询到的每个车辆的黑名单状态。
     */
    @Override
    public List<MsgBlackListDTO> checkBlackListByCarVinList(List<String> carVinList) {
        log.info("根据车辆VIN列表检查黑名单, carVinList:{}", carVinList);
        List<MsgBlackListDO> msgBlackListDOList = queryMsgBlackListDOList(carVinList);
        List<MsgBlackListDTO> msgBlackListDTOS = new ArrayList<>();
        for (MsgBlackListDO msgBlackListDO : msgBlackListDOList) {
            msgBlackListDTOS.add(buildMsgBlackListDTO(msgBlackListDO));
        }
        log.info("根据车辆VIN列表检查黑名单, msgBlackListDTOS:{}", msgBlackListDTOS);
        return msgBlackListDTOS;
    }

    /**
     * 根据MsgBlackListDO对象构建MsgBlackListDTO对象。
     *
     * @param msgBlackListDO 数据操作对象，包含车辆黑名单信息。
     * @return 返回构建好的MsgBlackListDTO对象，用于数据传输。
     */
    private MsgBlackListDTO buildMsgBlackListDTO(MsgBlackListDO msgBlackListDO) {
        MsgBlackListDTO msgBlackListDTO = new MsgBlackListDTO();
        msgBlackListDTO.setCarVin(msgBlackListDO.getCarVin());
        msgBlackListDTO.setPhone(msgBlackListDO.getPhone());
        return msgBlackListDTO;
    }

    /**
     * 根据车辆识别码列表查询消息黑名单列表。
     *
     * @param carVinList 车辆识别码列表，用于查询条件。
     * @return 消息黑名单实体列表，包含指定车辆识别码的消息黑名单记录。
     */
    private List<MsgBlackListDO> queryMsgBlackListDOList(List<String> carVinList) {
        LambdaQueryWrapper<MsgBlackListDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MsgBlackListDO::getCarVin, carVinList)
                .eq(MsgBlackListDO::getIsDeleted, false);
        return msgBlackListDOMapper.selectList(queryWrapper);
    }
}
