package com.jlr.ecp.notification.vo.shortlink;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "太阳码生成记录录VO")
@Data
public class QrCodeCreateVO {
    @Schema(description = "太阳码生成时间")
    private String createTime;

    @Schema(description = "太阳码生成操作人")
    private String operator;

    @Schema(description = "太阳码生成配置内容")
    private String configContent;
}
