package com.jlr.ecp.notification.dal.dataobject.task.auto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_notification_auto_task")
public class NotificationAutoTaskDO extends BaseDO {

    /**
     * 主键id, 自增
     * */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务线编码
     * */
    @TableField("business_code")
    private String businessCode;

    /**
     *  任务code
     * */
    @TableField("task_code")
    private String taskCode;

    /**
     *  任务名称
     * */
    @TableField("task_name")
    private String taskName;

    /**
     *  触发逻辑
     * */
    @TableField("trigger_action")
    private String triggerAction;

    /**
     *  发送时间类型 1：永久循环发送 2：限定时间发送
     * */
    @TableField("task_time_type")
    private Integer taskTimeType;


    /**
     *  发送类型 1：实时发送 2：定时发送
     * */
    @TableField("task_send_type")
    private Integer taskSendType;

    /**
     * 范围开始时间
     * */
    @TableField("range_begin_date")
    private LocalDateTime rangeBeginDate;

    /**
     * 范围结束时间
     * */
    @TableField("range_end_date")
    private LocalDateTime rangeEndDate;

    /**
     *  发送模板消息编码
     * */
    @TableField("message_template_code")
    private String messageTemplateCode;

    /**
     *  指定每天的发送时间时分，如10:00
     * */
    @TableField("daily_send_time")
    private String dailySendTime;

    /**
     *  通知短信商品编码
     * */
    @TableField("notify_spu_code")
    private String notifySpuCode;

    /**
     * 通知状态  0停用 1启用 2待启用 3已发送
     * */
    @TableField("status")
    private Integer status;

    /**
     *  启用时间
     * */
    @TableField("activate_time")
    private LocalDateTime activateTime;

    /**
     *  停用时间
     * */
    @TableField("deactivate_time")
    private LocalDateTime deactivateTime;

    /**
     * 租户号
     * */
    @TableField("tenant_id")
    private Integer tenantId;
}
