package com.jlr.ecp.notification.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.notification.dal.dataobject.history.NotificationHistory;
import com.jlr.ecp.notification.dal.dataobject.history.SendHistoryDetail;
import com.jlr.ecp.notification.dal.dataobject.task.NotificationTask;
import com.jlr.ecp.notification.dal.dataobject.task.auto.NotificationAutoTaskDO;
import com.jlr.ecp.notification.dal.dataobject.valet.OrderNotificationDetailDO;
import com.jlr.ecp.notification.dal.mysql.history.SendHistoryMapper;
import com.jlr.ecp.notification.dal.repository.*;
import com.jlr.ecp.notification.dto.NotificationLogSingleDTO;
import com.jlr.ecp.notification.enums.ErrorCodeConstants;
import com.jlr.ecp.notification.enums.MsgResultEnum;
import com.jlr.ecp.notification.enums.SmsSendResultEnum;
import com.jlr.ecp.notification.enums.FailStageEnum;
import com.jlr.ecp.notification.enums.task.InstanceCodeTypeEnum;
import com.jlr.ecp.notification.enums.task.TaskSendTypeEnum;
import com.jlr.ecp.notification.enums.task.TaskTypeEnum;
import com.jlr.ecp.notification.req.history.SendLogPageReq;
import com.jlr.ecp.notification.service.SendHistoryService;
import com.jlr.ecp.notification.util.sms.TimeFormatUtil;
import com.jlr.ecp.notification.vo.history.NotificationLogSingleVO;
import com.jlr.ecp.notification.vo.history.SendLogPageVo;
import com.jlr.ecp.subscription.api.icrvehicle.IcrVehicleApi;
import com.jlr.ecp.subscription.api.icrvehicle.vo.IcrVehicleListRespVO;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 *  通知日志实现类
 *
 * <AUTHOR>
 * */
@Service
@Slf4j
public class SendHistoryServiceImpl implements SendHistoryService {

    @Resource
    private SendHistoryMapper historyMapper;

    @Resource
    private MessageTaskRepository manualTaskRepository;

    @Resource
    private SendHistoryDetailRepository detailRepository;

    @Resource
    private IcrVehicleApi icrVehicleApi;

    @Resource
    private NotificationHistoryRepository historyRepository;

    @Resource
    private NotificationAutoTaskRepository autoTaskRepository;

    @Resource
    private OrderNotificationDetailRepository valetDetailRepository;

    @Resource
    private Snowflake snowflake;

    private static final Integer ZERO = 0;

    private static final String SEND_SUCCESS = "成功";

    private static final String SEND_FAIL = "失败";

    static final Map<String, String> SMM_CODE_COMPARISON_MAP = new HashMap<>();

    private static final String APPEND_STR = "====>";

    @PostConstruct
    public void loadSmsErrorCompare() throws IOException {
        org.springframework.core.io.Resource resource = new DefaultResourceLoader().getResource("classpath:file/SMS_ERROR_COMPARE.txt");
        List<String> lines = FileUtil.readLines(resource.getURL(), StandardCharsets.UTF_8);
        for (int i = 1; i < lines.size(); i++) {
            String line = lines.get(i);
            String[] split = line.split(StringUtils.SPACE);
            List<String> lineList = Arrays.stream(split).filter(StrUtil::isNotBlank).collect(Collectors.toList());
            String errorCode = lineList.get(0);
            String msgErrorStage = lineList.get(1);
            String errorDesc = lineList.get(2);
            SMM_CODE_COMPARISON_MAP.put(errorCode + APPEND_STR + msgErrorStage, errorDesc);
        }
        log.info("对照表code数量：{}", SMM_CODE_COMPARISON_MAP.size());
    }

    /**
     * 通知日志分页接口
     * @param sendLogPageReq 通知日志入参
     * @return PageResult<SendLogPageVo>
     * */
    @Override
    public CommonResult<PageResult<SendLogPageVo>> sendHistoryPageList(SendLogPageReq sendLogPageReq) {
        log.info("通知日志分页接口, 原始入参sendLogPageReq:{}", sendLogPageReq);
        checkSendLogPageReq(sendLogPageReq);
        //设置时间精确到秒
        if (StringUtils.isNotBlank(sendLogPageReq.getStartTime())) {
            sendLogPageReq.setStartTime(sendLogPageReq.getStartTime() + " 00:00:00");
        }
        if (StringUtils.isNotBlank(sendLogPageReq.getEndTime())) {
            sendLogPageReq.setEndTime(sendLogPageReq.getEndTime() + " 23:59:59");
        }
        // 页面替换特殊字符
        String processedTaskName = processTaskName(sendLogPageReq.getTaskName());
        if (processedTaskName != null) {
            sendLogPageReq.setTaskName(processedTaskName);
        }
        log.info("通知日志分页接口, 处理后入参sendLogPageReq:{}", sendLogPageReq);
        List<NotificationAutoTaskDO> autoTaskDOList = autoTaskRepository.getAutoTaskBySendTypeAndName(
                TaskSendTypeEnum.SCHEDULED.getCode(), sendLogPageReq.getTaskName()
        );
        Map<String, NotificationAutoTaskDO> autoTaskDOMap = getAutoTaskCodeToMap(autoTaskDOList);
        List<NotificationTask> manualTaskDOList = manualTaskRepository.getManualTaskByNameAndSendType(
                TaskTypeEnum.MANUAL.getType(), null, sendLogPageReq.getTaskName()
        );
        List<NotificationTask> oldAutoTaskDOList = manualTaskRepository.getManualTaskByNameAndSendType(
                TaskTypeEnum.AUTO_TASK.getType(), TaskSendTypeEnum.SCHEDULED.getCode(), sendLogPageReq.getTaskName()
        );
        Map<String, NotificationTask> manualTaskDOMap = getNotificationTaskCodeToMap(manualTaskDOList);
        Map<String, NotificationTask> oldAutoTaskDOMap = getNotificationTaskCodeToMap(oldAutoTaskDOList);
        log.info("通知日志分页接口, autoTaskDOMap的数量:{}, manualTaskDOMap的数量:{}, oldAutoTaskDOMap的数量：{}",
                autoTaskDOMap.size(), manualTaskDOMap.size(), oldAutoTaskDOMap.size());
        List<String> allTaskCodeList = getAllTaskCodeList(autoTaskDOList, manualTaskDOList, oldAutoTaskDOList, sendLogPageReq.getTaskType());
        Page<NotificationHistory> historyPage = historyRepository.selectPageByTaskInstanceCode(allTaskCodeList, sendLogPageReq);
        if (Objects.isNull(historyPage) || CollUtil.isEmpty(historyPage.getRecords())) {
            log.info("通知日志分页接口, 查询的historyList为空, sendLogPageReq:{}", sendLogPageReq);
            return CommonResult.success(null);
        }
        log.info("通知日志分页接口, 查询的history总量:{}", historyPage.getRecords().size());
        List<SendLogPageVo> pageVos = buildSendLogPageVOList(historyPage.getRecords(), autoTaskDOMap, manualTaskDOMap,oldAutoTaskDOMap);
        return CommonResult.success(new PageResult<>(pageVos, historyPage.getTotal()));
    }

    /**
     * 获取所有任务Code列表
     *
     * @param autoTaskDOList 自动任务的列表
     * @param manualTaskList 手动任务
     * @param oldAutoTaskList 旧自动任务
     * @param taskType 任务类型，为空时默认为自动任务
     * @return 包含所有任务代码的列表
     */
    private List<String> getAllTaskCodeList(List<NotificationAutoTaskDO> autoTaskDOList,
                                            List<NotificationTask> manualTaskList,
                                            List<NotificationTask> oldAutoTaskList,
                                            Integer taskType) {
        List<String> allTaskCodeList = new ArrayList<>();
        if (CollUtil.isEmpty(autoTaskDOList) && CollUtil.isEmpty(manualTaskList) && CollUtil.isEmpty(oldAutoTaskList)) {
            log.info("获取所有任务Code列表, 所有任务类型都为空");
            return allTaskCodeList;
        }
        log.info("获取所有任务Code列表, 自动任务的数量：{}, 手动任务数量：{}, 旧自动任务数量:{}",
                autoTaskDOList.size(), manualTaskList.size(), oldAutoTaskList.size());
        List<String> autoTaskCodeList = autoTaskDOList.stream()
                .map(NotificationAutoTaskDO::getTaskCode)
                .collect(Collectors.toList());
        List<String> manualTaskCodeList = manualTaskList.stream()
                .map(NotificationTask::getTaskCode)
                .collect(Collectors.toList());
        List<String> oldAutoTaskCodeList = oldAutoTaskList.stream()
                .map(NotificationTask::getTaskCode)
                .collect(Collectors.toList());
        if (Objects.isNull(taskType) || TaskTypeEnum.AUTO_TASK.getType().equals(taskType)) {
            allTaskCodeList.addAll(autoTaskCodeList);
            allTaskCodeList.addAll(oldAutoTaskCodeList);
        }
        if (Objects.isNull(taskType) || TaskTypeEnum.MANUAL.getType().equals(taskType)) {
            allTaskCodeList.addAll(manualTaskCodeList);
        }
        log.info("获取所有任务Code列表, 获取所有任务Code总数量为:{}", allTaskCodeList.size());
        return allTaskCodeList;
    }



    /**
     * 将手动任务列表转换为任务代码到任务对象的映射
     *
     * @param manualTaskDOList 手动任务列表，包含多个NotificationTask对象
     * @return 返回一个映射，键为任务代码，值为对应的NotificationTask对象
     */
    private Map<String, NotificationTask> getNotificationTaskCodeToMap(List<NotificationTask> manualTaskDOList) {
        Map<String, NotificationTask> map = new HashMap<>();
        if (CollUtil.isEmpty(manualTaskDOList)) {
            return map;
        }
        for (NotificationTask manualTaskDO : manualTaskDOList) {
            map.put(manualTaskDO.getTaskCode(), manualTaskDO);
        }
        return map;
    }

    /**
     * 将自动任务列表转换为任务代码到任务对象的映射
     *
     * @param autoTaskDOList 自动任务列表，包含多个NotificationAutoTaskDO对象
     * @return 返回一个映射，键为任务代码，值为对应的NotificationAutoTaskDO对象
     */
    private Map<String, NotificationAutoTaskDO> getAutoTaskCodeToMap(List<NotificationAutoTaskDO> autoTaskDOList) {
        Map<String, NotificationAutoTaskDO> map = new HashMap<>();
        if (CollUtil.isEmpty(autoTaskDOList)) {
            return map;
        }
        for (NotificationAutoTaskDO autoTaskDO : autoTaskDOList) {
            map.put(autoTaskDO.getTaskCode(), autoTaskDO);
        }
        return map;
    }

    /**
     * 校验发送日志页面请求的参数。
     *
     * @param sendLogPageReq 包含日志查询条件和分页参数的请求对象。
     */
    private void checkSendLogPageReq(SendLogPageReq sendLogPageReq) {
        // 分页参数校验
        validatePageParams(sendLogPageReq.getPageNo(), sendLogPageReq.getPageSize());
        if (StringUtils.isNotBlank(sendLogPageReq.getStartTime())
                && StringUtils.isNotBlank(sendLogPageReq.getEndTime())) {
            return ;
        }
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusYears(1);
        String startTime = TimeFormatUtil.timeToStringByFormat(startDate, TimeFormatUtil.formatter_3);
        String endTime = TimeFormatUtil.timeToStringByFormat(endDate, TimeFormatUtil.formatter_3);
        sendLogPageReq.setStartTime(startTime);
        sendLogPageReq.setEndTime(endTime);
    }

    /**
     * 根据实例代码查询相关的任务代码。
     *
     * @param instanceCode 实例代码，用于查询特定的任务代码。
     * @return 如果找到相应的任务代码，则返回该任务代码；如果未找到或输入实例代码无效，则返回空字符串。
     */
    @Override
    public String queryByInstanceCode(String instanceCode) {
        if (StringUtils.isBlank(instanceCode) || instanceCode.length() < 4) {
            return "";
        }
        if (InstanceCodeTypeEnum.AUTO_REAL_TIME.getType().equals(instanceCode.substring(0, 4))) {
            return "";
        }
        LambdaQueryWrapper<NotificationHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NotificationHistory::getTaskInstanceCode, instanceCode)
                .eq(NotificationHistory::getIsDeleted, false);
        NotificationHistory notificationHistory = historyMapper.selectOne(queryWrapper);
        log.info("使用instanceCode查询日志记录结果为空, instanceCode:{}", instanceCode);
        if (Objects.isNull(notificationHistory)) {
            return "";
        }
        return notificationHistory.getTaskInstanceCode();
    }

    /**
     * 查询指定任务类型的任务名称列表。
     *
     * @param taskType 任务类型，可为空。如果提供，将过滤并返回该类型的任务名称列表。
     * @return 返回一个任务名称的列表。列表中的每个元素都是字符串类型，代表一个任务的名称。
     */
    @Override
    public List<String> queryTaskNameList(Integer taskType) {
        List<String> resp = new ArrayList<>();
        List<String> manualOldAutoNames = manualTaskRepository.queryTaskNameListFilterAutoEvent(taskType);
        List<String> autoTaskNames = autoTaskRepository.querytAutoTaskNameList();
        if (TaskTypeEnum.AUTO_TASK.getType().equals(taskType)) {
            resp.addAll(manualOldAutoNames);
            resp.addAll(autoTaskNames);
        } else if (TaskTypeEnum.MANUAL.getType().equals(taskType)) {
            resp.addAll(manualOldAutoNames);
        } else if (Objects.isNull(taskType)) {
            resp.addAll(manualOldAutoNames);
            resp.addAll(autoTaskNames);
        }
        return resp;
    }

    /**
     * 根据单个条件查询短信发送记录列表
     *
     * @param singleDTO 包含查询条件（电话或车辆识别号）的通知日志单个DTO
     * @return 包含通知日志单个视图对象列表的公共结果
     */
    @Override
    public CommonResult<List<NotificationLogSingleVO>> querySmsRecordListBySingle(NotificationLogSingleDTO singleDTO) {
        log.info("根据单个条件查询短信发送记录列表, singleDTO:{}", singleDTO);
        CommonResult<List<NotificationLogSingleVO>> resp = CommonResult.success(null);
        if (BusinessIdEnum.VCS.getCode().equals(singleDTO.getBusinessCode())) {
            resp = getVCSLogSingleVOResp(singleDTO);
        } else if (List.of(BusinessIdEnum.LRE.getCode(), BusinessIdEnum.BRAND_GOODS.getCode()).contains(singleDTO.getBusinessCode())) {
            resp = getLRELogSingleVOResp(singleDTO);
        }
        return resp;
    }

    /**
     * LRE根据查询条件获取单个通知日志记录
     *
     * @param singleDTO 查询参数数据传输对象，包含：
     *        - phone 接收人电话号码（可选）
     *        - businessCode 业务类型代码
     * @return 通用返回结果
     */
    private CommonResult<List<NotificationLogSingleVO>> getLRELogSingleVOResp(NotificationLogSingleDTO singleDTO) {
        log.info("LRE根据查询条件获取单个通知日志记录, singleDTO:{}", singleDTO);
        List<SendHistoryDetail> historyDetailList = new ArrayList<>();
        if (StringUtils.isNotBlank(singleDTO.getPhone())) {
            historyDetailList = detailRepository.getHistoryDetailByPhone(singleDTO.getPhone(), singleDTO.getBusinessCode());
        }
        List<NotificationLogSingleVO> historyDetailSingleVOList = buildSingleLogVOList(historyDetailList);
        return CommonResult.success(historyDetailSingleVOList);
    }

    /**
     * VCS获取通知日志单条记录响应结果
     *
     * @param singleDTO 查询参数对象，包含业务编码
     * @return 包含排序后通知日志记录的通用结果对象，当查询条件不满足时返回错误码
     */
    private CommonResult<List<NotificationLogSingleVO>> getVCSLogSingleVOResp(NotificationLogSingleDTO singleDTO) {
        log.info("VCS获取通知日志单条记录响应结果, singleDTO:{}", singleDTO);
        List<SendHistoryDetail> historyDetailList;
        List<OrderNotificationDetailDO> valetOrderDetailList;
        if (StringUtils.isNotBlank(singleDTO.getPhone())) {
            historyDetailList = detailRepository.getHistoryDetailByPhone(singleDTO.getPhone(), singleDTO.getBusinessCode());
            valetOrderDetailList = valetDetailRepository.getValetOrderDetailByPhone(singleDTO.getPhone());
        } else if (StringUtils.isNotBlank(singleDTO.getCarVin())) {
            historyDetailList = detailRepository.getHistoryDetailByVin(singleDTO.getCarVin(), singleDTO.getBusinessCode());
            valetOrderDetailList = valetDetailRepository.getValetOrderDetailByVin(singleDTO.getCarVin());
        } else {
            return CommonResult.error(ErrorCodeConstants.TASK_REQ_NULL);
        }
        if (CollUtil.isEmpty(historyDetailList) && CollUtil.isEmpty(valetOrderDetailList)) {
            return CommonResult.success(new ArrayList<>());
        }
        List<NotificationLogSingleVO> historyDetailSingleVOList = buildSingleLogVOList(historyDetailList);
        List<NotificationLogSingleVO> valetDetailSingleVOList = buildValetSingleLogVOList(valetOrderDetailList);
        historyDetailSingleVOList.addAll(valetDetailSingleVOList);

        // 创建合并后的列表并进行排序
        List<NotificationLogSingleVO> sortedList = historyDetailSingleVOList.stream()
                .sorted(Comparator.comparing(
                        NotificationLogSingleVO::getSendTime,
                        Comparator.nullsLast(Comparator.reverseOrder())
                ))
                .collect(Collectors.toList());

        return CommonResult.success(sortedList);
    }


    /**
     * 构建礼宾服务单个日志VO列表
     *
     * @param valetOrderDetailList 礼宾服务订单详情列表
     * @return 返回构建的NotificationLogSingleVO列表
     */
    private List<NotificationLogSingleVO> buildValetSingleLogVOList(List<OrderNotificationDetailDO> valetOrderDetailList) {
        List<NotificationLogSingleVO> resp = new ArrayList<>();
        if (CollUtil.isEmpty(valetOrderDetailList)) {
            return resp;
        }
        List<String> carVinList = valetOrderDetailList.stream()
                .map(OrderNotificationDetailDO::getCarVin)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        Map<String, IcrVehicleListRespVO> vehicleMap = getIcrVehicleMap(carVinList);
        for (OrderNotificationDetailDO valetOrderDetail : valetOrderDetailList) {
            resp.add(buildValetSingleLogVO(valetOrderDetail, vehicleMap));
        }
        return resp;
    }

    /**
     * 构建代客下单通知的单个日志视图对象
     *
     * @param detail 订单通知详情数据对象，包含通知的详细信息
     * @param vehicleMap 车辆信息映射，用于获取车辆的品牌和配置名称
     * @return 返回构建的NotificationLogSingleVO对象，代表代客下单通知的日志条目
     */
    private NotificationLogSingleVO buildValetSingleLogVO(OrderNotificationDetailDO detail,
                                                          Map<String, IcrVehicleListRespVO> vehicleMap) {
        NotificationLogSingleVO singleVO = NotificationLogSingleVO.builder()
                .taskInstanceCode(snowflake.nextIdStr())
                .taskName("代客下单支付通知")
                .sendType("代客下单通知")
                .sendContent(detail.getSendMessage())
                .sendTime(TimeFormatUtil.timeToStringByFormat(detail.getSendTime(), TimeFormatUtil.formatter_4))
                .phone(detail.getSendPhone())
                .carVin(detail.getCarVin())
                .brand(vehicleMap.getOrDefault(detail.getCarVin(), new IcrVehicleListRespVO()).getBrandName())
                .configName(vehicleMap.getOrDefault(detail.getCarVin(), new IcrVehicleListRespVO()).getConfigName())
                .sendStatus(getValetDetailSendStatus(detail))
                .openShortLink(detail.getOpenShortLink())
                .build();
        buildValetFailStageAndReasons(detail, singleVO);
        return singleVO;
    }

    /**
     * 根据发送历史详情列表构建通知日志单个对象列表
     *
     * @param historyDetailList 发送历史详情列表，这是构建视图对象的基础数据
     * @return 返回一个通知日志单个视图对象列表，用于展示或进一步处理
     */
    private List<NotificationLogSingleVO> buildSingleLogVOList(List<SendHistoryDetail> historyDetailList) {
        List<NotificationLogSingleVO> resp = new ArrayList<>();
        if (CollUtil.isEmpty(historyDetailList)) {
            return resp;
        }
        List<String> taskCodeList = historyDetailList.stream()
                .map(SendHistoryDetail::getTaskCode)
                .collect(Collectors.toList());
        List<String> carVinList = historyDetailList.stream()
                .map(SendHistoryDetail::getCarVin)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        Map<String, NotificationTask> taskMap = manualTaskRepository.getTaskMapByCodes(taskCodeList);
        Map<String, NotificationAutoTaskDO> autoTaskDOMap = autoTaskRepository.getAutoTaskMapByCodes(taskCodeList);
        Map<String, IcrVehicleListRespVO> vehicleMap = getIcrVehicleMap(carVinList);
        for (SendHistoryDetail detail : historyDetailList) {
            if (Objects.isNull(detail)) {
                continue;
            }
            resp.add(buildSingleLogVO(detail, taskMap, autoTaskDOMap, vehicleMap));
        }
        if (CollUtil.isEmpty(resp)) {
            return resp;
        }
        return  resp;
    }

    /**
     * 根据车辆VIN列表获取车辆信息映射
     *
     * @param carVinList 车辆VIN列表，用于查询车辆信息
     * @return 返回一个映射，其中键是车辆VIN，值是车辆信息对象
     */
    private Map<String, IcrVehicleListRespVO> getIcrVehicleMap(List<String> carVinList) {
        Map<String, IcrVehicleListRespVO> map = new HashMap<>();
        if (CollUtil.isEmpty(carVinList)) {
            return map;
        }
        CommonResult<List<IcrVehicleListRespVO>> resp = icrVehicleApi.vinInfo(carVinList);
        log.info("根据车辆VIN列表获取车辆信息映射, carVin的数量：{}， 查询的状态：{}, 描述：{}", carVinList.size(), resp.getCode(), resp.getMsg());
        if (CollUtil.isEmpty(resp.getData())) {
            return map;
        }
        log.info("根据车辆VIN列表获取车辆信息映射, 查询的车辆的信息的数量：{}", resp.getData().size());
        for (IcrVehicleListRespVO vehicle : resp.getData()) {
            map.put(vehicle.getCarVin(), vehicle);
        }
        return map;
    }

    /**
     * 构建单个通知日志对象
     *
     * @param detail 发送历史详情对象，包含发送历史的详细信息
     * @param taskMap 通知任务映射，键为任务代码，值为NotificationTask对象
     * @param autoTaskDOMap 自动任务映射，键为任务代码
     * @param vehicleMap 车辆信息映射，键为车辆VIN，值为IcrVehicleListRespVO对象
     * @return 根据发送历史详情和通知任务信息构建的NotificationLogSingleVO对象，如果输入无效则返回null
     */
    private NotificationLogSingleVO buildSingleLogVO(SendHistoryDetail detail, Map<String, NotificationTask> taskMap,
                                                     Map<String, NotificationAutoTaskDO> autoTaskDOMap,
                                                     Map<String, IcrVehicleListRespVO> vehicleMap) {
        NotificationLogSingleVO singleVO = NotificationLogSingleVO.builder()
                .taskInstanceCode(detail.getTaskInstanceCode())
                .taskName(getTaskName(detail.getTaskCode(), taskMap, autoTaskDOMap))
                .sendType(getTaskTypeDesc(detail.getTaskCode(), taskMap, autoTaskDOMap))
                .sendContent(detail.getSendMessage())
                .sendTime(getDetailTaskSendTime(detail.getTaskInstanceCode()))
                .phone(detail.getSendPhone())
                .carVin(detail.getCarVin())
                .brand(vehicleMap.getOrDefault(detail.getCarVin(), new IcrVehicleListRespVO()).getBrandName())
                .configName(vehicleMap.getOrDefault(detail.getCarVin(), new IcrVehicleListRespVO()).getConfigName())
                .sendStatus(getDetailSendStatus(detail))
                .openShortLink(detail.getOpenShortLink())
                .build();
        buildFailStageAndReasons(detail, singleVO);
        return singleVO;
    }


    /**
     * 根据任务实例代码获取详细的任务发送时间
     *
     * @param taskInstanceCode 任务实例的唯一代码，用于查询历史记录
     * @return 格式化后的任务发送时间字符串，如果没有找到或时间为空则返回null
     */
    private String getDetailTaskSendTime(String taskInstanceCode) {
        NotificationHistory history = historyRepository.queryHistoryByInstanceCode(taskInstanceCode);
        if (Objects.isNull(history)) {
            return null;
        }
        return getTaskSendTime(history.getTaskSendTime());
    }

    /**
     * 构建失败阶段和原因
     *
     * @param detail 发送历史详情对象，包含发送过程中的错误信息
     * @param singleVO 通知日志单个视图对象，用于展示发送结果
     */

    private void buildFailStageAndReasons(SendHistoryDetail detail, NotificationLogSingleVO singleVO) {
        if (StringUtils.isNotBlank(detail.getSubmitErrorCode())) {
            singleVO.setFailStage(FailStageEnum.ZERO.getLabel());
            singleVO.setFailReasons(detail.getSubmitErrorCode());
        } else if (!MsgResultEnum.SUCCESSFUL.getStatus().equals(detail.getMsgResult())) {
            singleVO.setFailStage(detail.getMsgResult());
            singleVO.setFailReasons(detail.getWgCode());
        }
        // 推送失败阶段
        singleVO.setFailStage(FailStageEnum.getMsgErrorStageDesc(singleVO.getFailStage()));
        if (StringUtils.isNotBlank(singleVO.getFailReasons())) {
            // 推送失败原因
            singleVO.setFailReasons(singleVO.getFailReasons() + APPEND_STR + singleVO.getFailStage());
            if (SMM_CODE_COMPARISON_MAP.containsKey(singleVO.getFailReasons())) {
                String errorDesc = SMM_CODE_COMPARISON_MAP.get(singleVO.getFailReasons());
                singleVO.setFailReasons(errorDesc);
            } else {
                log.info("错误码：{} 翻译未找到", singleVO.getFailReasons());
                singleVO.setFailReasons("其他");
            }
        }
    }

    /**
     * 构建失败阶段和原因
     *
     * @param detail 发送历史详情对象，包含发送过程中的错误信息
     * @param singleVO 通知日志单个视图对象，用于展示发送结果
     */

    private void buildValetFailStageAndReasons(OrderNotificationDetailDO detail, NotificationLogSingleVO singleVO) {
        if (StringUtils.isNotBlank(detail.getSubmitErrorCode())) {
            singleVO.setFailStage(FailStageEnum.ZERO.getLabel());
            singleVO.setFailReasons(detail.getSubmitErrorCode());
        } else if (!MsgResultEnum.SUCCESSFUL.getStatus().equals(detail.getMsgResult())) {
            singleVO.setFailStage(detail.getMsgResult());
            singleVO.setFailReasons(detail.getWgCode());
        }
        // 推送失败阶段
        singleVO.setFailStage(FailStageEnum.getMsgErrorStageDesc(singleVO.getFailStage()));
        if (StringUtils.isNotBlank(singleVO.getFailReasons())) {
            // 推送失败原因
            singleVO.setFailReasons(singleVO.getFailReasons() + APPEND_STR + singleVO.getFailStage());
            if (SMM_CODE_COMPARISON_MAP.containsKey(singleVO.getFailReasons())) {
                String errorDesc = SMM_CODE_COMPARISON_MAP.get(singleVO.getFailReasons());
                singleVO.setFailReasons(errorDesc);
            } else {
                log.info("错误码：{} 翻译未找到", singleVO.getFailReasons());
                singleVO.setFailReasons("其他");
            }
        }
    }


    /**
     * 获取发送历史详情的发送状态
     *
     * @param detail 发送历史详情对象，包含发送结果和消息结果等信息
     * @return 返回发送失败或发送成功的状态
     *
     */
    private String getDetailSendStatus(SendHistoryDetail detail) {
        if (Objects.isNull(detail)) {
            return null;
        }
        if (SmsSendResultEnum.FAIL.getCode().equals(detail.getSendResult())) {
            return SEND_FAIL;
        }
        if (StringUtils.isBlank(detail.getMsgResult())) {
            return "未获取到回执";
        }
        if (!MsgResultEnum.SUCCESSFUL.getStatus().equals(detail.getMsgResult())) {
            return SEND_FAIL;
        }
        return SEND_SUCCESS;
    }

    /**
     * 获取发送历史详情的发送状态
     *
     * @param detail 发送历史详情对象，包含发送结果和消息结果等信息
     * @return 返回发送失败或发送成功的状态
     *
     */
    private String getValetDetailSendStatus(OrderNotificationDetailDO detail) {
        if (Objects.isNull(detail)) {
            return null;
        }
        if (SmsSendResultEnum.FAIL.getCode().equals(detail.getSendResult())) {
            return SEND_FAIL;
        }
        if (StringUtils.isBlank(detail.getMsgResult())) {
            return "未获取到回执";
        }
        if (!MsgResultEnum.SUCCESSFUL.getStatus().equals(detail.getMsgResult())) {
            return SEND_FAIL;
        }
        return SEND_SUCCESS;
    }

    /**
     * 根据任务代码获取任务名称
     *
     * @param taskCode 任务代码，用于在任务映射中查找对应的任务
     * @param taskMap 包含任务代码和任务对象的映射
     * @param autoTaskDOMap 包含自动任务代码和自动任务对象的映射
     * @return 返回找到的任务名称如果无法找到，则返回null
     */
    private String getTaskName(String taskCode, Map<String, NotificationTask> taskMap,
                               Map<String, NotificationAutoTaskDO> autoTaskDOMap) {
        if (StringUtils.isBlank(taskCode)) {
            return null;
        }
        String taskName = null;
        if (CollUtil.isNotEmpty(taskMap) && Objects.nonNull(taskMap.get(taskCode))) {
            taskName = taskMap.get(taskCode).getTaskName();
        } else if (CollUtil.isNotEmpty(autoTaskDOMap) && Objects.nonNull(autoTaskDOMap.get(taskCode))) {
            taskName = autoTaskDOMap.get(taskCode).getTaskName();
        }
        return taskName;
    }

    /**
     * 验证分页参数的有效性
     *
     * @param pageNo 页码，表示请求的页面编号
     * @param pageSize 每页大小，表示每个页面包含的记录数
     * @throws IllegalArgumentException 如果页码或每页大小不是正数，则抛出此异常
     */
    private void validatePageParams(int pageNo, int pageSize) {
        if (pageNo <= 0 || pageSize <= 0) {
            throw new IllegalArgumentException("页码和页大小必须为正数");
        }
    }

    /**
     * 处理任务名称以确保其可以在SQL查询中正确使用
     *
     * @param taskName 任务名称，可能来自用户输入或其他不可信的源
     * @return 经过处理的字符串，其中百分号和下划线被转义，以防止SQL注入攻击
     */
    private String processTaskName(String taskName) {
        if (StringUtils.isNotBlank(taskName)) {
            // 正确地替换字符串并返回新字符串
            return taskName.replace("%", "\\%").replace("_", "\\_");
        }
        return taskName;
    }

    /**
     * 构建发送日志页面VO列表
     *
     * @param historyList 通知历史记录列表，代表了一段时间内或特定条件下的通知发送历史
     * @param autoTaskDOMap 自动任务映射，用于快速查询特定自动通知任务的详细信息
     * @param manualTaskDOMap 手动任务映射，用于快速查询特定手动通知任务的详细信息
     * @param oldAutoTaskDOMap 旧自动任务映射，用于快速查询特定旧自动通知任务的详细信息
     * @return 返回一个SendLogPageVo对象列表，用于在页面上展示发送日志信息
     */

    public List<SendLogPageVo> buildSendLogPageVOList(List<NotificationHistory> historyList,
                                                      Map<String, NotificationAutoTaskDO> autoTaskDOMap,
                                                      Map<String, NotificationTask> manualTaskDOMap,
                                                      Map<String, NotificationTask> oldAutoTaskDOMap) {
        log.info("构建发送日志页面VO列表, historyList的数量：{}, autoTaskDOMap数量：{}, manualTaskDOMap的数量:{}, oldAutoTaskDOMap的数量：{}",
                historyList.size(), autoTaskDOMap.size(), manualTaskDOMap.size(), oldAutoTaskDOMap.size());
        List<SendLogPageVo> resp = new ArrayList<>();
        if (CollectionUtils.isEmpty(historyList)) {
            log.info("构建发送日志页面VO列表, 发送历史数据为空");
            return resp;
        }
        if (CollUtil.isEmpty(autoTaskDOMap) && CollUtil.isEmpty(manualTaskDOMap) && CollUtil.isEmpty(oldAutoTaskDOMap)) {
            log.info("构建发送日志页面VO列表, 自动任务数量, 手动任务都为空");
            return resp;
        }
        int filterCount = 0;
        for (NotificationHistory history : historyList) {
            if (!autoTaskDOMap.containsKey(history.getTaskCode())
                    && !manualTaskDOMap.containsKey(history.getTaskCode())
                    && !oldAutoTaskDOMap.containsKey(history.getTaskCode())) {
                filterCount++;
                continue;
            }
            resp.add(buildSendLogPageVo(history, autoTaskDOMap, manualTaskDOMap, oldAutoTaskDOMap));
        }
        log.info("构建发送日志页面VO列表, 过滤历史记录的数量：{}", filterCount);
        return resp;
    }

    /**
     *  构建通知记录Vo
     * @param history 单个历史记录
     * @param autoTaskDOMap 自动任务Map
     * @param manualTaskDOMap 手动任务Map
     * @param oldAutoTaskDOMap 旧自动任务Map
     * @return SendLogPageVo
     * */
    public SendLogPageVo buildSendLogPageVo(NotificationHistory history,
                                            Map<String, NotificationAutoTaskDO> autoTaskDOMap,
                                            Map<String, NotificationTask> manualTaskDOMap,
                                            Map<String, NotificationTask> oldAutoTaskDOMap) {
        log.info("构建发送日志页面VO列表, history：{}, autoTaskDOMap数量：{}, manualTaskDOMap的数量:{}, oldAutoTaskDOMap的数量：{}",
                history, autoTaskDOMap.size(), manualTaskDOMap.size(), oldAutoTaskDOMap.size());
        if (Objects.isNull(history)) {
            return SendLogPageVo.builder().build();
        }
        SendLogPageVo sendLogPageVo = SendLogPageVo.builder()
                .instanceCode(history.getTaskInstanceCode())
                .taskSendTime(getTaskSendTime(history.getTaskSendTime()))
                .sendTotalCount(history.getSendTotalCount())
                .sendSuccessCount(history.getReachUserSuccessCount())
                .sendFailCount(getFailCount(history))
                .openShortLinkCount(history.getOpenShortLinkCount())
                .openShortLinkRate(calculateOpenShortLinkRate(history.getOpenShortLinkCount(),history.getReachUserSuccessCount()))
                .build();
        if (autoTaskDOMap.containsKey(history.getTaskCode())) {
            sendLogPageVo.setTaskName(autoTaskDOMap.getOrDefault(history.getTaskCode(), new NotificationAutoTaskDO()).getTaskName());
            sendLogPageVo.setTaskType(TaskTypeEnum.AUTO_TASK.getDesc());
        } else if (manualTaskDOMap.containsKey(history.getTaskCode())) {
            sendLogPageVo.setTaskName(manualTaskDOMap.getOrDefault(history.getTaskCode(), new NotificationTask()).getTaskName());
            sendLogPageVo.setTaskType(TaskTypeEnum.MANUAL.getDesc());
        } else if (oldAutoTaskDOMap.containsKey(history.getTaskCode())) {
            sendLogPageVo.setTaskName(oldAutoTaskDOMap.getOrDefault(history.getTaskCode(), new NotificationTask()).getTaskName());
            sendLogPageVo.setTaskType(TaskTypeEnum.AUTO_TASK.getDesc());
        }
        return sendLogPageVo;
    }

    /**
     * 计算短链打开成功率
     *
     * @param openShortLinkCount 打开短链的次数
     * @param reachUserSuccessCount 成功到达用户的次数
     * @return 返回格式化后的成功率，格式为百分比字符串，如 "50.00%"
     */
    private String calculateOpenShortLinkRate(Integer openShortLinkCount, Integer reachUserSuccessCount) {
        log.info("计算短链打开成功率, openShortLinkCount:{}, reachUserSuccessCount:{}",
                openShortLinkCount, reachUserSuccessCount);
        if (Objects.isNull(openShortLinkCount) || Objects.isNull(reachUserSuccessCount)
                || ZERO.equals(openShortLinkCount) || ZERO.equals(reachUserSuccessCount)) {
            return "";
        }
        BigDecimal rate = new BigDecimal(openShortLinkCount)
                .divide(new BigDecimal(reachUserSuccessCount), 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(100));
        return rate.setScale(2, RoundingMode.HALF_UP) + "%";
    }

    /**
     * 计算失败次数的总数。
     *
     * @param history SendLogPageDto对象，包含发送失败和到达用户失败的计数。
     * @return 返回失败次数的总数，如果dto或其任一部分为null，则返回0。
     */
    private Integer getFailCount(NotificationHistory history) {
        if (Objects.isNull(history)) {
            return 0;
        }
        int failCount = 0;
        if (Objects.nonNull(history.getSendFailCount())) {
            failCount += history.getSendFailCount();
        }
        if (Objects.nonNull(history.getReachUserFailCount())) {
            failCount += history.getReachUserFailCount();
        }
        return failCount;
    }

    /**
     *  获取发送时间，只包含年月日
     * @param sendTime  全格式年月日，时分秒
     * @return String
     * */
    public String getTaskSendTime(LocalDateTime sendTime) {
        if (Objects.isNull(sendTime)) {
            return "";
        }
        return TimeFormatUtil.timeToStringByFormat(sendTime, TimeFormatUtil.formatter_4);
    }



    /**
     * 返回让任务类型对应的描述
     * @param taskCode 任务code
     * @param oldTaskMap 旧任务Map
     * @param autoTaskDOMap 自动任务Map
     * @return String
     * */
    public String getTaskTypeDesc(String taskCode, Map<String, NotificationTask> oldTaskMap,
                                  Map<String, NotificationAutoTaskDO> autoTaskDOMap) {
        if (oldTaskMap.containsKey(taskCode)) {
            NotificationTask notificationTask = oldTaskMap.get(taskCode);
            TaskTypeEnum taskTypeEnum = TaskTypeEnum.taskTypeEnumByCode(notificationTask.getTaskType());
            if (Objects.nonNull(taskTypeEnum)) {
                return taskTypeEnum.getDesc();
            }
        }
        if (autoTaskDOMap.containsKey(taskCode)) {
            return TaskTypeEnum.AUTO_TASK.getDesc();
        }
        return "";
    }
}
