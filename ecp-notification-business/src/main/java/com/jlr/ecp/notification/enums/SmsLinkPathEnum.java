package com.jlr.ecp.notification.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum SmsLinkPathEnum {
    // RENEWAL_REMINDER("%2Fpages%2FsubscribeTo%2FshopDetails%2Fshopdetails", "续费提醒路由"),
    RENEWAL_REMINDER("ecp/pages/subscribeTo/shopDetails/shopDetails", "续费提醒路由"),
    PATH_AVAILABLE("ecp/pages/subscribeTo/subscribeTo", "开发环境可用的地址");


    private final String path;

    private final String desc;
}
