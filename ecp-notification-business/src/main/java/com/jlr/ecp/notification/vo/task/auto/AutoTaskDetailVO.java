package com.jlr.ecp.notification.vo.task.auto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "自动任务详情vo")
@Data
public class AutoTaskDetailVO {
    @Schema(description = "任务code")
    private String taskCode;

    @Schema(description = "任务名称")
    private String taskName;

    @Schema(description = "开始日期")
    private String rangeBeginDate;

    @Schema(description = "结束日期")
    private String rangeEndDate;

    @Schema(description = "通知发送时间")
    private String dailySendTime;

    @Schema(description = "触发逻辑描述")
    private String triggerActionString;

    @Schema(description = "触发逻辑")
    private List<ConditionAttributeVO> triggerAction;

    @Schema(description = "模板类型")
    private String templateType;

    @Schema(description = "发送时间类型 1：永久循环发送 2：限定时间发送")
    private Integer taskTimeType;

    @Schema(description = "模板code")
    private String messageTemplateCode;

    @Schema(description = "模板名称")
    private String templateName;

    @Schema(description = "模板内容")
    private String messageTemplateContent;

    @Schema(description = "商品code")
    private String notifySpuCode;

    @Schema(description = "商品名称")
    private String notifySpuName;

    @Schema(description = "任务状态, 0停用、1启用、2待启用、3已发送")
    private Integer status;
}
