package com.jlr.ecp.notification.util.mp;

import com.jlr.ecp.notification.properties.SnsProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.core.internal.http.loader.DefaultSdkHttpClientBuilder;
import software.amazon.awssdk.http.SdkHttpConfigurationOption;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sns.SnsClient;
import software.amazon.awssdk.services.sns.model.MessageAttributeValue;
import software.amazon.awssdk.services.sns.model.PublishRequest;
import software.amazon.awssdk.services.sns.model.PublishResponse;
import software.amazon.awssdk.services.sns.model.SnsException;
import software.amazon.awssdk.utils.AttributeMap;

import javax.annotation.Resource;
import java.net.URI;
import java.util.Map;

@Component
@Slf4j
public class SnsUtil {

    @Resource
    private SnsProperties snsProperties;


    /**
     * 获取用于私有访问的SNS客户端。
     * 此方法配置并返回一个SnsClient实例，用于与Amazon Simple Notification Service (SNS)进行交互。
     * 客户端配置包括区域设置、网络端点覆盖、HTTP客户端配置和凭证提供。
     *
     * @return 配置好的SnsClient实例
     */
    public SnsClient getSnsClient() {
        return SnsClient.builder()
                .region(Region.CN_NORTHWEST_1)
                // 私有网络在Landing Zone中的配置（当前被注释掉）
                .endpointOverride(URI.create(snsProperties.getPrivateEndpoint()))
                // 公共网络配置
                //.endpointOverride(URI.create(snsProperties.getPublicEndpoint()))
                // 配置HTTP客户端，信任所有证书
                .httpClient(new DefaultSdkHttpClientBuilder().buildWithDefaults(AttributeMap.builder()
                        .put(SdkHttpConfigurationOption.TRUST_ALL_CERTIFICATES, true).build()))
                // 单一角色假设配置（当前被注释掉）
                //.credentialsProvider(loadCredentials(snsProperties.getAssumeRoleArn2()))
                // 双重角色假设配置
                .credentialsProvider(StsUtil.loadCredentialsWithDoubleAssumeRole(snsProperties.getAssumeRoleArn(), snsProperties.getAssumeRoleArn2()))
                .build();
    }

    /**
     * 获取配置好的SNS客户端。
     * 此方法配置并返回一个SnsClient实例，用于与Amazon Simple Notification Service (SNS)进行交互。
     * 客户端配置包括区域设置、网络端点覆盖、HTTP客户端配置和凭证提供。
     *
     * @param endpoint            SNS DNS
     * @param credentialsProvider 用于认证的凭证提供者
     * @return 配置好的SnsClient实例
     */
    public SnsClient getSnsClient(String endpoint, AwsCredentialsProvider credentialsProvider) {
        return SnsClient.builder()
                .region(Region.AP_NORTHEAST_1)
                // 配置网络端点
                .endpointOverride(URI.create(endpoint))
                // 配置HTTP客户端，信任所有证书
                .httpClient(new DefaultSdkHttpClientBuilder().buildWithDefaults(AttributeMap.builder()
                        .put(SdkHttpConfigurationOption.TRUST_ALL_CERTIFICATES, true).build()))
                // 配置凭证提供者
                .credentialsProvider(credentialsProvider)
                .build();
    }


    /**
     * 发布标准主题消息
     *
     * @param message 消息正文，即要发送的内容
     * @param topicArn SNS主题的ARN，标识消息发送到哪个主题
     * @param client SNS客户端，用于与AWS SNS服务进行交互
     */
    public String publishStandardTopic(String message, String topicArn, SnsClient client) {
        try {

            PublishRequest request = PublishRequest.builder().message(message)
                    .subject("Subject").messageAttributes(Map.of("clientId", MessageAttributeValue.builder().
                                    dataType("String.Array").stringValue("[\"MPJAG\", \"MPLDR\"]").build(),
                            "key1", MessageAttributeValue.builder().dataType("String").stringValue("value1").build(),
                            "key2", MessageAttributeValue.builder().dataType("String").stringValue("value2").build()))
                    .topicArn(topicArn).build();

            PublishResponse result = client.publish(request);
            log.info("publishStandardTopic send success, Message Id: " + result.messageId() + ". Message sent. Status is " + result.sdkHttpResponse().statusCode());
            return result.messageId();
        } catch (SnsException e) {
            log.info("publishStandardTopic message exception：{}", e.awsErrorDetails().errorMessage());
        }
        return "";
    }

    /**
     *1.同一消息组内的消息： 是有时序的，意味着它们将按照发布的顺序被处理。这是 FIFO（先进先出）原则的一部分，确保了消息的顺序性。
     * 2.不同消息组之间的消息： 在放入 FIFO 队列时，各组之间的消息是没有固定时序的。这些消息可以并行处理，也就是说，不同组之间的消息顺序不受保证。
     *     每个消息组独立维护其内部消息的顺序，但不同组之间的消息可以以任何顺序被处理。
     * */
    public String publishFIFOTopic(String message, String topicArn, SnsClient client, String messageGroupId, String messageDeduplicationId) {

        try {

            PublishRequest request = PublishRequest.builder().message(message).messageAttributes(
                            Map.of("clientId", MessageAttributeValue.builder().dataType("String.Array").stringValue("[\"MPLDR\"]").build()))
                    .topicArn(topicArn).messageGroupId(messageGroupId).messageDeduplicationId(messageDeduplicationId).build();

            PublishResponse result = client.publish(request);
            log.info("publishFIFOTopic send success, Message Id: " + result.messageId() + " Message sent. Status is " + result.sdkHttpResponse().statusCode());
            return result.messageId();
        } catch (SnsException e) {
            log.info("publishFIFOTopic message exception：{}", e.awsErrorDetails().errorMessage());
        }
        return "";
    }

}
