<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jlr.ecp.notification.dal.mysql.history.SendHistoryMapper">

    <select id="pageQuerySendHistoryList" resultType="com.jlr.ecp.notification.dto.SendLogPageDto">
        select
        log.task_instance_code,
        task.task_name,
        task.task_type,
        log.task_send_time,
        log.send_total_count,
        log.send_success_count,
        log.send_fail_count,
        log.reach_user_success_count,
        log.reach_user_fail_count,
        log.open_short_link_count
        from
        t_notification_task as task
        left join t_notification_history as log on task.task_code = log.task_code
        where
        log.is_deleted = 0
        <!-- 使用<choose>/<when>结构处理不同的 taskType 值 -->
        <choose>
            <when test="logPage.taskType == null">
                AND (task.task_code in ('remote-service-expire-code-three-day', 'remote-service-expire-code-thirty-day', 'pivi-service-expire-code-three-day', 'pivi-service-expire-code-thirty-day', 'pivi-service-expire-code-ninety-two-day', 'remote-service-expire-code-thirty-day-after', 'pivi-service-expire-code-thirty-day-after', 'pivi-service-expire-code-three-day-unnamed', 'pivi-service-expire-code-thirty-day-unnamed', 'pivi-service-expire-code-ninety-two-day-unnamed') OR task.task_type = 2)
            </when>
            <when test="logPage.taskType == 1">
                AND task.task_code in  ('remote-service-expire-code-three-day', 'remote-service-expire-code-thirty-day', 'pivi-service-expire-code-three-day', 'pivi-service-expire-code-thirty-day', 'pivi-service-expire-code-ninety-two-day', 'remote-service-expire-code-thirty-day-after', 'pivi-service-expire-code-thirty-day-after', 'pivi-service-expire-code-three-day-unnamed', 'pivi-service-expire-code-thirty-day-unnamed', 'pivi-service-expire-code-ninety-two-day-unnamed')
            </when>
            <when test="logPage.taskType == 2">
                AND task.task_type = 2
            </when>
        </choose>
        <!-- 使用<if>结构处理非空的 taskName 参数 -->
        <if test="logPage.taskName != null and logPage.taskName != ''">
            and task.task_name LIKE CONCAT('%', #{logPage.taskName}, '%')
        </if>
        <!-- 使用<if>结构处理非空的 startTime 参数 -->
        <if test="logPage.startTime != null and logPage.startTime != ''">
            and log.task_send_time >= #{logPage.startTime}
        </if>
        <!-- 使用<if>结构处理非空的 endTime 参数 -->
        <if test="logPage.endTime != null and logPage.endTime != ''">
            and log.task_send_time &lt;= #{logPage.endTime}
        </if>
        <!-- 使用<if>结构处理非空的 instanceCodeList 参数 -->
        <if test="logPage.instanceCodeList != null and logPage.instanceCodeList.size() > 0">
            and log.task_instance_code IN
            <foreach item="instanceCode" index="index" collection="logPage.instanceCodeList" open="(" separator="," close=")">
                #{instanceCode}
            </foreach>
        </if>
        ORDER BY
        <!-- 使用<choose>/<when>结构处理 timeSortType 参数决定排序方式 -->
        <choose>
            <when test="logPage.timeSortType == 0">
                log.task_send_time ASC, log.id ASC
            </when>
            <otherwise>
                log.task_send_time DESC, log.id DESC
            </otherwise>
        </choose>
    </select>


    <update id="updateByInstanceCode" parameterType="com.jlr.ecp.notification.dal.dataobject.history.NotificationHistory">
        UPDATE t_notification_history
        <set>
            <if test="notificationHistory.taskCode != null">task_code = #{notificationHistory.taskCode},</if>
            <if test="notificationHistory.taskSendTime != null">task_send_time = #{notificationHistory.taskSendTime},</if>
            <if test="notificationHistory.sendTotalCount != null">send_total_count = #{notificationHistory.sendTotalCount},</if>
            <if test="notificationHistory.sendSuccessCount != null">send_success_count = #{notificationHistory.sendSuccessCount},</if>
            <if test="notificationHistory.sendFailCount != null">send_fail_count = #{notificationHistory.sendFailCount},</if>
            <if test="notificationHistory.tenantId != null">tenant_id = #{notificationHistory.tenantId}</if>
        </set>
        WHERE task_instance_code = #{notificationHistory.taskInstanceCode}
    </update>

</mapper>