package com.jlr.ecp.notification.excel.listener;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.util.ListUtils;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.constant.Constants;
import com.jlr.ecp.notification.excel.pojo.VinAndPhoneErrorDetail;
import com.jlr.ecp.notification.excel.pojo.VinAndPhoneNumber;
import com.jlr.ecp.notification.excel.utils.NotificationExcelUtil;
import com.jlr.ecp.notification.util.PhoneNumberValidatorUtil;
import com.jlr.ecp.subscription.api.subscripiton.SubscriptionServiceApi;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Getter
public class VinAndPhoneListenerCheck extends AnalysisEventListener<VinAndPhoneNumber> {

    @Resource
    private SubscriptionServiceApi subscriptionServiceApi;

    private Boolean formatError = false;

    private Boolean checkResult = true;

    private File vinAndPhoneErrorDetailFile = null;

    private File vinAndPhoneCorrectDetailFile = null;

    private static final int BATCH_COUNT = 500;

    private List<VinAndPhoneNumber> dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private List<List<VinAndPhoneErrorDetail>> errorDetailList = new ArrayList<>();

    private List<List<VinAndPhoneNumber>> correctList = new ArrayList<>();

    private List<VinAndPhoneNumber> allDataList = new ArrayList<>();

    private List<Map<Integer, String>> headers = new ArrayList<>();

    private static final String VIN_TITLE = "VIN";

    private static final String PHONE_NUMBER_TITLE = "Phone Number";

    private Set<String> vinSet = new HashSet<>();

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 当遇到表头行时，将表头数据添加到headers列表中
        headers.add(headMap);
    }

    /**
     * 调用给定的VinAndPhoneNumber对象和AnalysisContext对象执行某些操作。
     * 该方法会根据传入的车辆识别号码（VIN）和电话号码进行特定的分析处理。
     *
     * @param vinAndPhoneNumber 包含车辆识别号码和电话号码的对象，用于分析处理。
     * @param analysisContext 分析上下文，提供额外的上下文信息以支持分析过程。
     */
    @Override
    public void invoke(VinAndPhoneNumber vinAndPhoneNumber, AnalysisContext analysisContext) {
        try {
            dataList.add(vinAndPhoneNumber);
            if (dataList.size() >= BATCH_COUNT) {
                allDataList.addAll(dataList);
                List<VinAndPhoneErrorDetail> errorData = processData(dataList);
                errorDetailList.add(errorData);
                if (Boolean.TRUE.equals(checkResult)) {
                    correctList.add(dataList);
                } else {
                    correctList.clear();
                }
                dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            }
        } catch (Exception e) {
            log.error("解析vin+phone异常：", e);
            checkResult = true;
            dataList.clear();
            errorDetailList.clear();
            correctList.clear();
            headers.clear();
            vinSet.clear();
        }
    }

    /**
     * 在所有分析完成后执行的操作。
     * 这个方法是分析上下文生命周期的一部分，可以在整个分析过程结束后执行一些额外的逻辑。
     *
     * @param analysisContext 分析上下文，提供了关于当前分析会话的上下文信息，例如分析的文件列表等。
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("vin+phone处理最后剩余的数据");
        try {
            allDataList.addAll(dataList);
            List<VinAndPhoneErrorDetail> data = finalProcessData(dataList);
            errorDetailList.add(data);
            if (Boolean.TRUE.equals(checkResult)) {
                correctList.add(dataList);
            } else {
                correctList.clear();
            }
            uploadFileDetail(errorDetailList, correctList);
            log.info("vin+phone所有数据解析完成！");
        } catch (Exception e) {
            log.error("vin+phone处理最后剩余的数据异常：", e);
            checkResult = true;
            dataList.clear();
            errorDetailList.clear();
            correctList.clear();
            headers.clear();
            vinSet.clear();
        }
    }



    /**
     * 处理数据的方法。
     * 这是一个抽象方法，需要在具体实现类中进行具体实现。
     * 它接受一个VinAndPhoneNumber对象的列表作为输入，处理这些数据，并返回处理后的结果。
     *
     * @param vinAndPhoneNumbers 包含车辆识别号（VIN）和电话号码的列表。列表中的每个元素都包含一个VIN和一个对应的电话号码。
     * @return 返回处理后的数据。返回类型为泛型T，具体类型取决于实现类。
     */
    public List<VinAndPhoneErrorDetail> processData(List<VinAndPhoneNumber> vinAndPhoneNumbers) {
        return getVinAndPhoneErrorDetailList(vinAndPhoneNumbers);
    }


    /**
     * 对收集到的车辆识别码（VIN）和电话号码列表进行最终处理。
     * 这是一个抽象方法，需要在子类中具体实现处理逻辑。
     *
     * @param vinAndPhoneNumbers 包含车辆识别码和电话号码的列表，列表中每一项都是一个VinAndPhoneNumber对象。
     *                           车辆识别码（VIN）是世界范围内通用的用于识别汽车的一组独特的字母和数字，
     *                           电话号码则是与车辆相关联的联系电话。
     * @return 返回处理后的数据，具体类型由泛型T决定，取决于具体实现。
     */
    public List<VinAndPhoneErrorDetail> finalProcessData(List<VinAndPhoneNumber> vinAndPhoneNumbers) {
        return getVinAndPhoneErrorDetailList(vinAndPhoneNumbers);
    }

    /**
     * 上传错误详情的方法。
     * 这是一个抽象方法，需要在具体实现类中进行具体实现。
     * 它接受一个包含处理后的数据的列表作为输入，并将这些数据上传到相应的错误详情表中。
     *
     * @param errorList 包含处理后的数据的列表，列表中的每个元素都包含一个处理后的数据。
     * @param correctList 正确数据列表。
     */
    public void uploadFileDetail(List<List<VinAndPhoneErrorDetail>> errorList,
                                 List<List<VinAndPhoneNumber>> correctList) {
        try {
            if (Boolean.FALSE.equals(checkResult)) {
                vinAndPhoneErrorDetailFile = NotificationExcelUtil.batchWriteVinAadPhoneError(errorList);
            } else {
                vinAndPhoneCorrectDetailFile = NotificationExcelUtil.batchWriteVinAadPhoneCorrect(correctList);
            }
        } catch (Exception e) {
            log.error("vin+phone写入错误详情异常:", e);
        } finally {
            checkResult = true;
            dataList.clear();
            errorDetailList.clear();
            correctList.clear();
            headers.clear();
            vinSet.clear();
        }
    }

    /**
     * 获取VIN码和手机号错误详情列表。
     *
     * @param vinAndPhoneNumbers VIN码和手机号的列表。可能会包含不合法或不完整的VIN码和手机号。
     * @return 返回一个包含所有输入项错误详情的列表。每个错误详情包括原始的VIN码、手机号以及对应的错误信息。
     */
    private List<VinAndPhoneErrorDetail> getVinAndPhoneErrorDetailList(List<VinAndPhoneNumber> vinAndPhoneNumbers) {
        List<VinAndPhoneErrorDetail> vinAndPhoneErrorDetails = new ArrayList<>();
        if (headers.isEmpty() ||  headers.get(0).size() < 2 || !VIN_TITLE.equals(headers.get(0).get(0)) ||
                !PHONE_NUMBER_TITLE.equals(headers.get(0).get(1))) {
            formatError = true;
            return vinAndPhoneErrorDetails;
        }
        //carVin有，phone为空，分组获取所有carVin对应的phone
        Map<String, String> carVinToPhoneMap = getAllCarVinToPhoneMapByGroup(vinAndPhoneNumbers);
        for (VinAndPhoneNumber vinAndPhone : vinAndPhoneNumbers) {
            VinAndPhoneErrorDetail vinAndPhoneErrorDetail = getVinAndPhoneErrorDetail(vinAndPhone, carVinToPhoneMap);
            vinAndPhoneErrorDetails.add(vinAndPhoneErrorDetail);
        }
        if (dataList.isEmpty() && errorDetailList.isEmpty()) {
            checkResult = false;
            VinAndPhoneErrorDetail vinAndPhoneErrorDetail = VinAndPhoneErrorDetail.builder()
                    .errorMessage(Constants.VIN_MISS)
                    .build();
            vinAndPhoneErrorDetails.add(vinAndPhoneErrorDetail);
        }
        return vinAndPhoneErrorDetails;
    }

    /**
     * 分组获取所有车辆VIN码和电话号码的映射关系
     *
     * @param vinAndPhoneNumbers 包含VIN码和电话号码信息的列表
     * @return 一个映射，其中键为VIN码，值为相应的电话号码
     */
    private Map<String, String> getAllCarVinToPhoneMapByGroup(List<VinAndPhoneNumber> vinAndPhoneNumbers) {
        log.info("获取所有车辆VIN码和电话号码的映射关系, vinAndPhoneNumbers的数量:{}", vinAndPhoneNumbers.size());
        Map<String, String> resp = new HashMap<>();
        if (CollUtil.isEmpty(vinAndPhoneNumbers)) {
            return resp;
        }
        List<VinAndPhoneNumber> vinList = new ArrayList<>();
        List<VinAndPhoneNumber> vinAndPhoneList = new ArrayList<>();
        for (VinAndPhoneNumber vinAndPhone : vinAndPhoneNumbers) {
            if (StringUtils.isNotBlank(vinAndPhone.getVin())
                    && (StringUtils.isBlank(vinAndPhone.getPhoneNumber()) || !PhoneNumberValidatorUtil.isValidChinaMobileNumber(vinAndPhone.getPhoneNumber()))) {
                vinList.add(vinAndPhone);
            } else if (StringUtils.isNotBlank(vinAndPhone.getVin()) && StringUtils.isNotBlank(vinAndPhone.getPhoneNumber())) {
                vinAndPhoneList.add(vinAndPhone);
            }
        }
        Map<String, String> vinToPhoneMap = getCarVinToPhoneMap(vinList);
        Map<String, String> vinAndPhoneToPhoneMap = getVinAndPhoneToPhoneMap(vinAndPhoneList);
        resp.putAll(vinToPhoneMap);
        resp.putAll(vinAndPhoneToPhoneMap);
        log.info("获取所有车辆VIN码和电话号码的映射关系, vinList的数量：{}, vinToPhoneMap的数量：{}，vinAndPhoneList的数量：{}，" +
                "vinAndPhoneToPhoneMap的数量：{}，resp的数量：{}", vinList.size(), vinToPhoneMap.size(),
                vinAndPhoneList, vinAndPhoneToPhoneMap.size(), resp.size());
        return resp;
    }

    /**
     * 将VIN码和电话号码列表转换为以VIN码为键、电话号码为值的映射
     *
     * @param vinAndPhoneList VIN码和电话号码的列表，不能为空
     * @return 一个Map对象，其中VIN码作为键，电话号码作为值
     */
    private Map<String, String> getVinAndPhoneToPhoneMap(List<VinAndPhoneNumber> vinAndPhoneList) {
        log.info("将VIN码和电话号码列表转换为以VIN码为键、电话号码为值的映射, vinAndPhoneList的数量:{}", vinAndPhoneList.size());
        Map<String, String> map = new HashMap<>();
        if (CollUtil.isEmpty(vinAndPhoneList)) {
            log.info("将VIN码和电话号码列表转换为以VIN码为键、电话号码为值的映射, vinAndPhoneList为空");
            return map;
        }
        for (VinAndPhoneNumber vinAndPhone : vinAndPhoneList) {
            if (StringUtils.isBlank(vinAndPhone.getVin())) {
                log.info("将VIN码和电话号码列表转换为以VIN码为键、电话号码为值的映射, key为空vinAndPhone：{}", vinAndPhone);
                continue;
            }
            map.put(vinAndPhone.getVin(), vinAndPhone.getPhoneNumber());
        }
        return map;
    }

    /**
     * 根据提供的车辆VIN码和电话号码列表，获取车辆VIN码到电话号码的映射。
     *
     * @param vinAndPhoneNumbers 包含VIN码和电话号码的列表，可能为空。
     * @return 返回一个映射，其中包含车辆的VIN码作为键，相应的电话号码作为值。如果查询失败或没有找到对应关系，则返回空映射。
     */
    private Map<String, String> getCarVinToPhoneMap(List<VinAndPhoneNumber> vinAndPhoneNumbers) {
        Map<String, String> carVinToPhoneMap = new HashMap<>();
        if (CollectionUtils.isEmpty(vinAndPhoneNumbers)) {
            return carVinToPhoneMap;
        }
        List<VinAndPhoneNumber> phoneEmptyList = vinAndPhoneNumbers.stream()
                .filter(vinAndPhone -> StringUtils.isNotBlank(vinAndPhone.getVin()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(phoneEmptyList)) {
            return carVinToPhoneMap;
        }
        List<String> carVinList = phoneEmptyList.stream()
                .map(VinAndPhoneNumber::getVin)
                .distinct()
                .collect(Collectors.toList());
        try {
            CommonResult<Map<String, String>> resp = subscriptionServiceApi.queryCarVinToPhoneMap(carVinList);
            carVinToPhoneMap = resp.getData();
        } catch (Exception e) {
            log.error("使用carVin查询手机号异常:", e);
        }
        log.info("使用carvin从tsdp获取手机号, carVinList:{}, carVinToPhoneMap:{}", carVinList, carVinToPhoneMap);
        return carVinToPhoneMap;
    }

    /**
     * 根据提供的VIN和电话号码信息，生成并返回一个包含错误详情的VinAndPhoneErrorDetail对象。
     * 如果VIN或电话号码有误，将设置相应的错误信息。
     *
     * @param vinAndPhone 包含VIN和电话号码信息的对象。
     * @param carVinToPhoneMap 存储carVin到phone的映射关系。
     * @return 一个包含VIN和电话号码错误详情的VinAndPhoneErrorDetail对象。
     */
    private VinAndPhoneErrorDetail getVinAndPhoneErrorDetail(VinAndPhoneNumber vinAndPhone,
                                                             Map<String, String> carVinToPhoneMap) {
        VinAndPhoneErrorDetail vinAndPhoneErrorDetail = VinAndPhoneErrorDetail.builder()
                .vin(vinAndPhone.getVin())
                .phoneNumber(vinAndPhone.getPhoneNumber())
                .build();
        if (StringUtils.isBlank(vinAndPhone.getVin())) {
            checkResult = false;
            vinAndPhoneErrorDetail.setErrorMessage("VIN缺失");
        } else if (vinAndPhone.getVin().length() != 17) {
            checkResult = false;
            vinAndPhoneErrorDetail.setErrorMessage("VIN格式错误");
        } else if (vinSet.contains(vinAndPhone.getVin())) {
            checkResult = false;
            vinAndPhoneErrorDetail.setErrorMessage("VIN重复");
        }
        vinSet.add(vinAndPhone.getVin());
        String errorMessage = vinAndPhoneErrorDetail.getErrorMessage();
        if (StringUtils.isBlank(vinAndPhone.getPhoneNumber())) {
            updatePhoneMiss(vinAndPhone, vinAndPhoneErrorDetail, errorMessage, carVinToPhoneMap);
        } else if (!PhoneNumberValidatorUtil.isValidChinaMobileNumber(vinAndPhone.getPhoneNumber())) {
            updatePhoneFormatError(vinAndPhone, vinAndPhoneErrorDetail, errorMessage, carVinToPhoneMap);
        }
        return vinAndPhoneErrorDetail;
    }

    /**
     * 更新车辆VIN码至电话号码的映射信息。
     * @param vinAndPhone vin+phone
     * @param vinAndPhoneErrorDetail 包含VIN码和错误信息的对象。
     * @param errorMessage 错误信息，如果为空则默认设置为“手机号缺失”。
     * @param carVinToPhoneMap 车辆VIN码到电话号码的映射表。
     * 此方法主要用于检查给定的VIN码是否在映射表中存在，如果存在则更新对应的电话号码，如果不存在则根据情况更新错误信息。
     */
    private void updatePhoneMiss(VinAndPhoneNumber vinAndPhone, VinAndPhoneErrorDetail vinAndPhoneErrorDetail,
                                     String errorMessage, Map<String, String> carVinToPhoneMap) {
        // 验证输入参数非空
        if (Objects.isNull(vinAndPhoneErrorDetail)) {
            log.error("updatePhoneMiss, vinAndPhoneErrorDetail must not be null.");
            return ;
        }
        log.info("updatePhoneMiss, 入参，vinAndPhone:{}, vinAndPhoneErrorDetail：{}, errorMessage：{}," +
                        " carVinToPhoneMap:{}", vinAndPhone, vinAndPhoneErrorDetail, errorMessage, carVinToPhoneMap);
        String effectiveErrorMessage = errorMessage; // 用于简化错误消息更新的变量
        if (CollectionUtils.isEmpty(carVinToPhoneMap)
                || !carVinToPhoneMap.containsKey(vinAndPhoneErrorDetail.getVin())
                || StringUtils.isBlank(carVinToPhoneMap.get(vinAndPhoneErrorDetail.getVin()))) {
            effectiveErrorMessage = addErrorMessage(effectiveErrorMessage, Constants.PHONE_MISS);
            checkResult = false;
        } else if (!PhoneNumberValidatorUtil.isValidChinaMobileNumber(carVinToPhoneMap.get(vinAndPhoneErrorDetail.getVin()))) {
            vinAndPhoneErrorDetail.setPhoneNumber(carVinToPhoneMap.get(vinAndPhoneErrorDetail.getVin()));
            effectiveErrorMessage = addErrorMessage(effectiveErrorMessage, Constants.PHONE_FORMAT);
            checkResult = false;
        } else {
            vinAndPhone.setPhoneNumber(carVinToPhoneMap.get(vinAndPhoneErrorDetail.getVin()));
            vinAndPhoneErrorDetail.setPhoneNumber(carVinToPhoneMap.get(vinAndPhoneErrorDetail.getVin()));
        }
        vinAndPhoneErrorDetail.setErrorMessage(effectiveErrorMessage);
        log.info("updatePhoneMiss, 修改后结果，vinAndPhoneErrorDetail：{}", vinAndPhoneErrorDetail);
    }

    /**
     * 更新电话号码格式错误信息。
     *
     * @param vinAndPhone 车辆VIN和电话号码信息，用于更新电话号码。
     * @param vinAndPhoneErrorDetail 车辆VIN和电话号码的错误详情，用于记录错误信息。
     * @param errorMessage 错误消息，如果非空，会与电话格式错误消息合并。
     * @param carVinToPhoneMap VIN到电话号码的映射，用于验证或更新电话号码。
     */
    private void updatePhoneFormatError(VinAndPhoneNumber vinAndPhone, VinAndPhoneErrorDetail vinAndPhoneErrorDetail,
                                 String errorMessage, Map<String, String> carVinToPhoneMap) {
        // 验证输入参数非空
        if (Objects.isNull(vinAndPhoneErrorDetail)) {
            log.error("updatePhoneFormatError, vinAndPhoneErrorDetail must not be null.");
            return ;
        }
        log.info("updatePhoneFormatError, 入参，vinAndPhone:{}, vinAndPhoneErrorDetail：{}, errorMessage：{}," +
                " carVinToPhoneMap:{}", vinAndPhone, vinAndPhoneErrorDetail, errorMessage, carVinToPhoneMap);
        String effectiveErrorMessage = errorMessage;
        if (!CollectionUtils.isEmpty(carVinToPhoneMap) && StringUtils.isNotBlank(carVinToPhoneMap.get(vinAndPhone.getVin()))
                && PhoneNumberValidatorUtil.isValidChinaMobileNumber(carVinToPhoneMap.get(vinAndPhone.getVin()))) {
            vinAndPhone.setPhoneNumber(carVinToPhoneMap.get(vinAndPhoneErrorDetail.getVin()));
            vinAndPhoneErrorDetail.setPhoneNumber(carVinToPhoneMap.get(vinAndPhoneErrorDetail.getVin()));
        } else {
            effectiveErrorMessage = addErrorMessage(effectiveErrorMessage, Constants.PHONE_FORMAT);
            checkResult = false;
        }
        vinAndPhoneErrorDetail.setErrorMessage(effectiveErrorMessage);
        log.info("updatePhoneFormatError, 修改后结果，vinAndPhoneErrorDetail：{}", vinAndPhoneErrorDetail);
    }

    /**
     * 如果传入的消息非空，追加新的错误消息。
     *
     * @param errorMessage 当前错误消息
     * @param newErrorMessage 新的错误消息部分
     * @return 更新后的错误消息
     */
    private String addErrorMessage(String errorMessage, String newErrorMessage) {
        return StringUtils.isBlank(errorMessage) ? newErrorMessage : errorMessage + "，" + newErrorMessage;
    }

}
