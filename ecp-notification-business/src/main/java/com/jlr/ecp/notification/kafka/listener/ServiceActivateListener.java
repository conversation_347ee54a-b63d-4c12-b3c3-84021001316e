package com.jlr.ecp.notification.kafka.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import com.alibaba.fastjson.JSON;
import com.jlr.ecp.notification.constant.MpEventTypeConstants;
import com.jlr.ecp.notification.dal.dataobject.mp.MpNoticeOrderSubscriptionDO;
import com.jlr.ecp.notification.dal.dataobject.mp.MpTemplateNoticeRecordDO;
import com.jlr.ecp.notification.dal.repository.mp.MpNoticeOrderSubscriptionRepository;
import com.jlr.ecp.notification.dal.repository.mp.MpTemplateNoticeRecordRepository;
import com.jlr.ecp.notification.dto.mp.MpOrderChangeMsgDTO;
import com.jlr.ecp.notification.dto.mp.MpOrderParametersMsgDTO;
import com.jlr.ecp.notification.enums.BrandCodeEnum;
import com.jlr.ecp.notification.enums.mp.MpMsgTypeEnum;
import com.jlr.ecp.notification.kafka.message.ActivateMessage;
import com.jlr.ecp.notification.kafka.send.template.SmsSendTemplate;
import com.jlr.ecp.notification.properties.MpMsgProperties;
import com.jlr.ecp.notification.properties.SnsProperties;
import com.jlr.ecp.notification.util.mp.SnsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.sns.SnsClient;

import javax.annotation.Resource;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ServiceActivateListener {
    @Resource
    private SmsSendTemplate smsSendTemplate;

    @Resource
    private MpNoticeOrderSubscriptionRepository orderChangeRepository;

    @Resource
    private MpMsgProperties mpMsgProperties;

    @Resource
    private SnsProperties snsProperties;

    @Resource
    private SnsUtil snsUtil;

    @Resource
    private MpTemplateNoticeRecordRepository mpRecordRepository;

    @Resource
    private Snowflake snowflake;

    /**
     * ‘移除在黑名单的vin’ 消息发送的taskCode
     */
    private static final String REMOVE_BLACKLIST_VIN = "remove-blacklist-vin";

    /**
     * 服务激活通知
     *
     * @param records 消息体
     */
    @KafkaListener(topics = "service-activate-topic", groupId = "service-activate-group", batch = "true",
            properties = "max.poll.records:100")
    public void listen(List<ConsumerRecord<String, String>> records) {
        log.info("服务激活通知消息，records:{}", records);
        List<ActivateMessage> activateMessageList = buildActivateMessageList(records);
        if (CollUtil.isEmpty(activateMessageList)) {
            log.info("服务激活通知, 构建激活息体为空");
            return;
        }
        for (ActivateMessage activateMessage : activateMessageList) {
            //taskCode为‘remove-blacklist-vin’
            if (REMOVE_BLACKLIST_VIN.equals(activateMessage.getTaskCode())) {
                smsSendTemplate.sendRemoveVinToBlackListMsg(activateMessage);
            } else {
                smsSendTemplate.sendSmsMsg(activateMessage);
            }
        }
        //发送小程序模板消息-订单状态变化通知
        sendOrderChangeMsgToSns(activateMessageList);
    }

    /**
     * 向小程序发送订单变更消息
     *
     * @param activateMessageList 一个包含多个ActivateMessage对象的列表，用于发送订单变更通知
     */
    public void sendOrderChangeMsgToSns(List<ActivateMessage> activateMessageList) {
        log.info("向小程序发送订单变更消息, 数量：{}", activateMessageList.size());
        List<String> parentOrderCodeList = activateMessageList.stream()
                .map(ActivateMessage::getParentOrderCode)
                .collect(Collectors.toList());
        Map<String, MpNoticeOrderSubscriptionDO> orderChangeMap = orderChangeRepository.queryOrderSubscribeMapByList(parentOrderCodeList,
                MpMsgTypeEnum.ORDER_CHANGE.getCode());
        if (CollUtil.isEmpty(orderChangeMap)) {
            log.info("根据订单号列表查询订单订阅信息Map为空, 不发送MP订单变化通知, parentOrderCodeList:{}", parentOrderCodeList);
            return ;
        }
        List<MpTemplateNoticeRecordDO> mpRecordDOList = new ArrayList<>();
        List<MpNoticeOrderSubscriptionDO> orderChangeList = new ArrayList<>();
        //发送小程序模板消息-订单状态变化通知
        for (ActivateMessage activateMessage : activateMessageList) {
            if (!Boolean.TRUE.equals(activateMessage.getCompleted())) {
                log.info("状态为不发送-订单变更通知, activateMessage：{}", activateMessage);
                continue;
            }
            MpNoticeOrderSubscriptionDO orderChange = orderChangeMap.get(activateMessage.getParentOrderCode());
            MpOrderChangeMsgDTO orderChangeMsgDTO = buildMpOrderChangeMsgDTO(activateMessage, orderChange);
            if (Objects.isNull(orderChangeMsgDTO)) {
                log.info("MpOrderChangeMsgDTO为空, 不发送MP订单变化通知, activateMessage:{}", activateMessage);
                continue;
            }
            String messageGroupId = getOrderCancelEventType(activateMessage.getBrandCode());
            // 发送到sns
            String messageId = sendMpMsgToSns(orderChangeMsgDTO, messageGroupId);
            if (StringUtils.isBlank(messageId)) {
                log.info("发送小程序模板消息-订单变更通知失败, orderChangeMsgDTO:{}", orderChangeMsgDTO);
            }

            mpRecordDOList.add(buildMpTemplateNoticeRecordDO(orderChangeMsgDTO, messageId));

            orderChange.setOrderSuccessFlag(calculateOrderSuccessFlag(orderChange, messageId));
            orderChange.setUpdatedTime(LocalDateTime.now());
            orderChangeList.add(orderChange);
        }
        // 保存发送记录
        mpRecordRepository.saveBatch(mpRecordDOList);
        //更新订阅记录
        orderChangeRepository.updateBatchById(orderChangeList);
    }

    /**
     * 计算订单状态变化标志位。
     *
     * @param orderChange 订单变更信息对象，包含订单成功标志位等信息
     * @param messageId 消息ID，用于判断是否需要返回订单成功标志位
     * @return 订单成功标志位，0 或 `orderChange` 中的 `orderSuccessFlag`
     */
    private Integer calculateOrderSuccessFlag(MpNoticeOrderSubscriptionDO orderChange, String messageId) {
        if (Objects.isNull(orderChange) || Objects.isNull(orderChange.getOrderSuccessFlag())) {
            return 0;
        }
        if (StringUtils.isBlank(messageId)) {
            return orderChange.getOrderSuccessFlag();
        }
        return 0;
    }

    /**
     * 发送订单状态变化消息到SNS（Amazon Simple Notification Service）主题
     *
     * @param orderChangeMsgDTO 订单变更消息数据传输对象，包含需要发送到SNS的订单变更信息
     */
    public String sendMpMsgToSns(MpOrderChangeMsgDTO orderChangeMsgDTO, String messageGroupId) {
        log.info("发送订单状态变化消息到SNS, orderChangeMsgDTO:{}", orderChangeMsgDTO);
        try {
            SnsClient snsClient = snsUtil.getSnsClient();
            String message = JSON.toJSONString(orderChangeMsgDTO);
            return snsUtil.publishFIFOTopic(message, snsProperties.getTopicArn(), snsClient, messageGroupId, snowflake.nextIdStr());
        } catch (Exception e) {
            // 使用StringWriter和PrintWriter来获取堆栈跟踪的字符串表示
            StringWriter sw = new StringWriter();
            e.printStackTrace(new PrintWriter(sw));
            String exceptionAsString = sw.toString();
            // 打印异常信息和堆栈跟踪
            log.info("发送订单状态变化消息到SNS，异常堆栈跟踪: {}", exceptionAsString);
        }
        return "";
    }

    /**
     * 根据品牌代码获取订单取消事件类型。
     *
     * @param brandCode 品牌代码，用于标识不同的品牌。
     * @return String 返回对应品牌的订单取消事件类型。
     */
    private String getOrderCancelEventType(Integer brandCode) {
        String eventType = "";
        if (BrandCodeEnum.LAND_ROVER.getCode().equals(brandCode)) {
            eventType = MpEventTypeConstants.VCS_LAN_ORDER_CHANGE_EVENT_TYPE;
        } else if (BrandCodeEnum.JAGUAR.getCode().equals(brandCode)) {
            eventType = MpEventTypeConstants.VCS_JAG_ORDER_CHANGE_EVENT_TYPE;
        }
        return eventType;
    }

    /**
     * 构建小程序模板消息记录实体对象
     *
     * @param orderChangeMsgDTO 订单变更消息数据传输对象，包含消息原始数据
     * @param messageId 消息ID，用于标识消息的唯一性
     * @return MpTemplateNoticeRecordDO 构建完成的模板消息记录实体对象
     */
    private MpTemplateNoticeRecordDO buildMpTemplateNoticeRecordDO(MpOrderChangeMsgDTO orderChangeMsgDTO, String messageId) {
        MpTemplateNoticeRecordDO mpRecordDO = new MpTemplateNoticeRecordDO();
        mpRecordDO.setMessageId(messageId);
        mpRecordDO.setJlrId(orderChangeMsgDTO.getJlrId());
        mpRecordDO.setOpenId(orderChangeMsgDTO.getOpenId());
        mpRecordDO.setSnsTopic("ECP_VCS_TEMPLATE_MESSAGE");
        mpRecordDO.setEventType(orderChangeMsgDTO.getEventType());
        mpRecordDO.setOrderNo(orderChangeMsgDTO.getBusinessNo());
        mpRecordDO.setSendMessage(JSON.toJSONString(orderChangeMsgDTO));
        return mpRecordDO;
    }

    /**
     * 构建MpOrderChangeMsgDTO对象
     *
     * @param activateMessage 激活消息对象，包含激活相关信息
     * @param orderChange 订单变更通知对象，包含订单变更细节
     * @return 返回构建好的MpOrderChangeMsgDTO对象，如果输入参数为空则返回null
     */
    private MpOrderChangeMsgDTO buildMpOrderChangeMsgDTO(ActivateMessage activateMessage,
                                                         MpNoticeOrderSubscriptionDO orderChange) {
        log.info("构建MpOrderChangeMsgDTO对象, activateMessage:{}, orderChange:{}", activateMessage, orderChange);
        if (Objects.isNull(activateMessage) || Objects.isNull(orderChange)) {
            return null;
        }
        MpOrderChangeMsgDTO orderChangeMsgDTO = new MpOrderChangeMsgDTO();
        orderChangeMsgDTO.setClientId("VCS_ECP");
        orderChangeMsgDTO.setJlrId(orderChange.getJlrId());
        String eventType = getOrderCancelEventType(activateMessage.getBrandCode());
        orderChangeMsgDTO.setEventType(eventType);
        orderChangeMsgDTO.setBusinessNo(activateMessage.getOrderNumber());
        orderChangeMsgDTO.setApp(mpMsgProperties.getApp());
        orderChangeMsgDTO.setOpenId(orderChange.getOpenId());
        MpOrderParametersMsgDTO orderParametersMsgDTO = MpOrderParametersMsgDTO.builder()
                .orderNo(activateMessage.getOrderNumber())
                .orderContent("智能驭领服务")
                .orderStatus("订单完成")
                .reminder("订单已完成，可点击查看订单详情。")
                .build();
        orderChangeMsgDTO.setParameters(orderParametersMsgDTO);
        return orderChangeMsgDTO;
    }

    /**
     * 构建ActivateMessage消息体
     *
     * @param records 发送消息
     * @return List<ActivateMessage>
     */
    public List<ActivateMessage> buildActivateMessageList(List<ConsumerRecord<String, String>> records) {
        List<ActivateMessage> activateMessageList = new ArrayList<>();
        for (ConsumerRecord<String, String> record : records) {
            ActivateMessage activateMessage = JSON.parseObject(record.value(), ActivateMessage.class);
            activateMessageList.add(activateMessage);
        }
        return activateMessageList;
    }

}
