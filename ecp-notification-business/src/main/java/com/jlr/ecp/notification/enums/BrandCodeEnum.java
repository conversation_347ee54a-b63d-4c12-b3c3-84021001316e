package com.jlr.ecp.notification.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 品牌code枚举
 * 1路虎, 2捷豹
 * */
@AllArgsConstructor
@Getter
public enum BrandCodeEnum {
    LAND_ROVER(1, "LAN", "路虎"),
    JAGUAR(2, "JAG", "捷豹");

    private final Integer code;

    private final String brandCode;

    private final String desc;

    /**
     * 根据品牌代码获取品牌描述
     *
     * @param brandCode 品牌代码，用于查找对应的品牌描述
     * @return 返回与品牌代码对应的描述信息，如果没有找到则返回空字符串
     */
    public static String getDescByCode(String brandCode) {
        for (BrandCodeEnum brandCodeEnum : BrandCodeEnum.values()) {
            if (brandCodeEnum.getBrandCode().equals(brandCode)) {
                return brandCodeEnum.getDesc();
            }
        }
        return "";
    }

    /**
     * 根据代码获取品牌代码
     *
     * @param code 品牌对应的代码
     * @return 品牌代码，如果找不到对应的品牌则返回空字符串
     */
    public static String getBrandByCode(Integer code) {
        for (BrandCodeEnum brandCodeEnum : BrandCodeEnum.values()) {
            if (brandCodeEnum.getCode().equals(code)) {
                return brandCodeEnum.getBrandCode();
            }
        }
        return "";
    }
}
