package com.jlr.ecp.notification.dal.mysql.history;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.notification.dal.dataobject.history.NotificationHistory;
import com.jlr.ecp.notification.dto.SendLogPageDto;
import com.jlr.ecp.notification.req.history.SendLogPageReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SendHistoryMapper extends BaseMapperX<NotificationHistory> {

    /**
     * 分页查询通知日志列表
     * @param page 分页参数
     * @param logPage 查询条件
     * @return Page<SendLogPageDto>
     * */
    Page<SendLogPageDto> pageQuerySendHistoryList(Page<SendLogPageDto> page, @Param("logPage") SendLogPageReq logPage);

    Boolean updateByInstanceCode(@Param("notificationHistory") NotificationHistory notificationHistory);

}
