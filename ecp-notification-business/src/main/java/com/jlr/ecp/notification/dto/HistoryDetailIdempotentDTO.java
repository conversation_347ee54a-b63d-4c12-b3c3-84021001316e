package com.jlr.ecp.notification.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class HistoryDetailIdempotentDTO {
    /**
     *  任务code
     * */
    private String taskCode;

    /**
     *  创建开始时间
     * */
    private LocalDateTime startTime;

    /**
     *  创建结束时间
     * */
    private LocalDateTime endTime;

    /**
     *  发送状态
     * */
    private Integer sendStatus;
}
