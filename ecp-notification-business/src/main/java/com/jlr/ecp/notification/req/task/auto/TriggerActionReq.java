package com.jlr.ecp.notification.req.task.auto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Schema(description = "触发逻辑请求")
@AllArgsConstructor
@NoArgsConstructor
public class TriggerActionReq {
    @Schema(description = "品牌入参")
    private TriggerActionFieldReq brandReq;

    @Schema(description = "车辆产地入参")
    private TriggerActionFieldReq vehicleOriginReq;

    @Schema(description = "到期服务入参")
    private TriggerActionFieldReq expirationServiceReq;

    @Schema(description = "实名状态入参")
    private TriggerActionFieldReq realNameStatusReq;

    @Schema(description = "到期区间入参")
    private TriggerActionFieldReq expirationIntervalReq;

}
