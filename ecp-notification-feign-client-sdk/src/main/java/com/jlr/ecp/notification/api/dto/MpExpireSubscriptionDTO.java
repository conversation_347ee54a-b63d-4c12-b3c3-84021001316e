package com.jlr.ecp.notification.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "小程序到期通知")
public class MpExpireSubscriptionDTO {

    /**
     * 自增主键id
     */
    private Long id;

    /**
     * 用户全局唯一ID
     */
    private String jlrId;

    /**
     * 小程序的openId
     */
    private String openId;

    /**
     * 服务到期的consent
     */
    private Integer serviceExpirationConsent;
}
