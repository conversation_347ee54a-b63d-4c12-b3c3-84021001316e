package com.jlr.ecp.notification.dal.repository.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.notification.dal.dataobject.task.auto.NotificationAutoTaskDO;
import com.jlr.ecp.notification.dal.mysql.task.auto.NotificationAutoTaskDOMapper;
import com.jlr.ecp.notification.dal.repository.MessageTemplateRepository;
import com.jlr.ecp.notification.dal.repository.NotificationAutoTaskRepository;
import com.jlr.ecp.notification.enums.IsDeleteEnum;
import com.jlr.ecp.notification.enums.SortEnum;
import com.jlr.ecp.notification.enums.task.AutoTaskTimeTypeEnum;
import com.jlr.ecp.notification.enums.task.TaskSendTypeEnum;
import com.jlr.ecp.notification.enums.task.TaskStatusEnum;
import com.jlr.ecp.notification.req.task.TaskPageListReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class NotificationAutoTaskRepositoryImpl extends ServiceImpl<NotificationAutoTaskDOMapper, NotificationAutoTaskDO>
        implements NotificationAutoTaskRepository {

    @Resource
    private NotificationAutoTaskDOMapper autoTaskDOMapper;

    @Resource
    private MessageTemplateRepository messageTemplateRepository;

    /**
     *  分页查询自动通知任务列表页
     * @param taskPageListReq 分页查询自动通知任务列表页入参
     * @return Page<NotificationAutoTaskDO>
     * */
    @Override
    public Page<NotificationAutoTaskDO> queryAutoTaskPageList(TaskPageListReq taskPageListReq) {
        Page<NotificationAutoTaskDO> page = new Page<>(taskPageListReq.getPageNo(), taskPageListReq.getPageSize());
        LambdaQueryWrapper<NotificationAutoTaskDO> wrapper = new LambdaQueryWrapper<>();

        // 业务码过滤逻辑
        if (StringUtils.isNotBlank(taskPageListReq.getBusinessCode())) {
            List<String> templateCodes = messageTemplateRepository.listTemplateCodesByBusinessCode(taskPageListReq.getBusinessCode());
            if (CollUtil.isEmpty(templateCodes)) {
                return page; // 保持原有空结果返回逻辑
            }
            wrapper.in(NotificationAutoTaskDO::getMessageTemplateCode, templateCodes);
        }

        if (CollUtil.isNotEmpty(taskPageListReq.getStatusList())) {
            wrapper.in(NotificationAutoTaskDO::getStatus, taskPageListReq.getStatusList());
        }

        // 基础查询条件
        buildBaseQueryConditions(wrapper, taskPageListReq);

        // 处理排序逻辑
        applySorting(wrapper, taskPageListReq);

        // 保持原有的默认排序
        wrapper.orderByDesc(NotificationAutoTaskDO::getUpdatedTime)
                .orderByDesc(NotificationAutoTaskDO::getId);

        return autoTaskDOMapper.selectPage(page, wrapper);
    }

    // 抽取基础查询条件
    private void buildBaseQueryConditions(LambdaQueryWrapper<NotificationAutoTaskDO> wrapper, TaskPageListReq req) {
        wrapper.eq(StringUtils.isNotBlank(req.getTaskCode()),
                        NotificationAutoTaskDO::getTaskCode, req.getTaskCode())
                .eq(NotificationAutoTaskDO::getIsDeleted, IsDeleteEnum.NO.getStatus())
                .eq(NotificationAutoTaskDO::getBusinessCode, req.getBusinessCode());

        String taskName = processTaskName(req.getTaskName());
        if (StringUtils.isNotBlank(taskName)) {
            wrapper.like(NotificationAutoTaskDO::getTaskName, taskName);
        }
    }

    // 抽取排序逻辑
    private void applySorting(LambdaQueryWrapper<NotificationAutoTaskDO> wrapper, TaskPageListReq req) {
        // 处理启用时间排序
        if (handleSingleSort(wrapper, req.getStartTimeSortType(), NotificationAutoTaskDO::getActivateTime)) {
            return ;
        }
        // 处理停用时间排序
        if (handleSingleSort(wrapper, req.getEndTimeSortType(), NotificationAutoTaskDO::getDeactivateTime)) {
            return ;
        }
        // 处理状态排序
        handleSingleSort(wrapper, req.getStatusSortType(), NotificationAutoTaskDO::getStatus);
    }

    // 通用排序处理方法
    private boolean handleSingleSort(LambdaQueryWrapper<NotificationAutoTaskDO> wrapper,
                                     Integer sortType,
                                     SFunction<NotificationAutoTaskDO, ?> column) {
        if (Objects.isNull(sortType)) {
            return false;
        }
        if (SortEnum.ASC.getSortType().equals(sortType)) {
            wrapper.orderByAsc(column);
        } else {
            wrapper.orderByDesc(column);
        }
        return true;
    }

    /**
     * 根据任务代码查询自动任务信息
     *
     * @param taskCode 任务代码，用于唯一标识一个自动任务
     * @return 返回一个NotificationAutoTaskDO对象，包含任务详细信息如果找不到匹配的任务，则返回null
     */
    @Override
    public NotificationAutoTaskDO queryAutoTaskByCode(String taskCode) {
        log.info("根据任务代码查询自动任务信息, taskCode:{}", taskCode);
        LambdaQueryWrapper<NotificationAutoTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NotificationAutoTaskDO::getTaskCode, taskCode)
                .eq(NotificationAutoTaskDO::getIsDeleted, IsDeleteEnum.NO.getStatus());
        return getOne(queryWrapper);
    }

    /**
     * 根据ID更新自动任务信息
     *
     * @param autoTaskDO 包含自动任务新信息的对象，用于更新数据库中的记录
     * @return boolean 表示更新操作是否成功true表示成功，false表示失败
     */
    @Override
    public boolean updateAutoTaskById(NotificationAutoTaskDO autoTaskDO) {
        return updateById(autoTaskDO);
    }


    /**
     * 查询自动任务名称列表
     *
     * @return 返回自动任务名称列表，如果查询结果为空，则返回空列表
     */
    @Override
    public List<String> querytAutoTaskNameList() {
        LambdaQueryWrapper<NotificationAutoTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NotificationAutoTaskDO::getIsDeleted, false)
                .select(NotificationAutoTaskDO::getTaskName);
        List<NotificationAutoTaskDO> resp = list(queryWrapper);
        if (CollUtil.isEmpty(resp)) {
            return new ArrayList<>();
        }
        return resp.stream().map(NotificationAutoTaskDO::getTaskName).collect(Collectors.toList());
    }

    /**
     * 插入自动任务通知记录
     *
     * @param autoTaskDO 自动任务通知对象，包含需要插入的自动任务通知的相关信息
     * @return 返回一个布尔值，表示自动任务通知记录是否成功插入
     *         - true：表示记录成功插入
     *         - false：表示记录插入失败
     */
    @Override
    public boolean insertAutoTask(NotificationAutoTaskDO autoTaskDO) {
        return save(autoTaskDO);
    }

    /**
     * 根据模板代码查询自动任务
     *
     * @param templateCode 消息模板代码，用于查询自动任务
     * @return 返回符合查询条件的自动任务列表
     */
    @Override
    public List<NotificationAutoTaskDO> queryAutoTaskByTemplateCode(String templateCode) {
        LambdaQueryWrapper<NotificationAutoTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NotificationAutoTaskDO::getMessageTemplateCode, templateCode)
                .eq(NotificationAutoTaskDO::getIsDeleted, false);
        return list(queryWrapper);
    }

    /**
     * 获取自动任务到期通知列表
     *
     * @param expireDate 到期日
     * @return 自动任务到期通知列表
     */
    @Override
    public List<NotificationAutoTaskDO> getExpireNotifyAutoTask(LocalDateTime expireDate) {
        log.info("获取自动任务到期通知列表, expireDay:{}", expireDate);
        if (Objects.isNull(expireDate)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<NotificationAutoTaskDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();

        lambdaQueryWrapper.eq(NotificationAutoTaskDO::getTaskSendType, TaskSendTypeEnum.SCHEDULED.getCode()) // 发送类型为定时发送
                .eq(NotificationAutoTaskDO::getStatus, TaskStatusEnum.START.getStatus())       // 状态为启用
                .and(wrapper -> wrapper.eq(NotificationAutoTaskDO::getTaskTimeType, AutoTaskTimeTypeEnum.CYCLIC.getCode()) // 永久循环发送
                        .or(subWrapper -> subWrapper.eq(NotificationAutoTaskDO::getTaskTimeType, AutoTaskTimeTypeEnum.RANGE.getCode()) // 或者限定时间发送
                                .le(NotificationAutoTaskDO::getRangeBeginDate, expireDate) // 开始时间小于等于当前时间
                                .ge(NotificationAutoTaskDO::getRangeEndDate, expireDate))) // 结束时间大于等于当前时间
                .eq(NotificationAutoTaskDO::getIsDeleted, false); // 未删除
        log.info("获取自动任务到期通知列表, 查询条件:{}", lambdaQueryWrapper.getSqlSegment());
        List<NotificationAutoTaskDO> autoTaskDOList = autoTaskDOMapper.selectList(lambdaQueryWrapper);
        log.info("获取自动任务到期通知列表, autoTaskDOList:{}", autoTaskDOList);
        return autoTaskDOList;
    }

    /**
     * 更新指定任务的开始和结束日期为null
     *
     * @param taskCode 任务代码，用于标识特定的任务
     * @return 返回一个Boolean值，表示更新操作是否成功
     */
    @Override
    public Boolean updateBeginEndDateNull(String taskCode) {
        return autoTaskDOMapper.updateBeginAndEndDateNull(taskCode) > 0;
    }

    /**
     * 根据发送类型和任务名称获取自动任务列表
     *
     * @param taskSendType 任务发送类型，用于筛选特定发送类型的任务
     * @param taskName 任务名称，用于模糊匹配任务名称
     * @return 返回筛选后的自动任务列表如果taskSendType和taskName都为空，则返回空列表
     */
    @Override
    public List<NotificationAutoTaskDO> getAutoTaskBySendTypeAndName(Integer taskSendType,
                                                                     String taskName) {
        log.info("根据发送类型获取自动任务列表,  taskSendType:{}, taskName:{}", taskSendType, taskName);
        if (Objects.isNull(taskSendType) && StringUtils.isBlank(taskName)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<NotificationAutoTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(taskSendType), NotificationAutoTaskDO::getTaskSendType, taskSendType)
                .like(StringUtils.isNotBlank(taskName), NotificationAutoTaskDO::getTaskName, "%" + taskName + "%")
                .eq(NotificationAutoTaskDO::getIsDeleted, false);
        return list(queryWrapper);
    }

    /**
     * 根据任务代码列表获取自动任务Map
     *
     * @param taskCodeList 任务代码列表，用于查询自动任务信息
     * @return 返回一个映射，键是任务代码，值是对应的NotificationAutoTaskDO实例
     */
    @Override
    public Map<String, NotificationAutoTaskDO> getAutoTaskMapByCodes(List<String> taskCodeList) {
        List<NotificationAutoTaskDO> autoTaskDOList = queryAutoTaskByCodes(taskCodeList);
        log.info("根据任务代码列表获取自动任务Map, 查询的数量:{}", taskCodeList.size());
        if (CollUtil.isEmpty(autoTaskDOList)) {
            return new HashMap<>();
        }
        return autoTaskDOList.stream()
                .collect(Collectors.toMap(NotificationAutoTaskDO::getTaskCode, Function.identity(), (v1,v2) -> v1));
    }

    /**
     * 根据任务代码列表查询自动任务信息
     *
     * @param taskCodeList 任务代码列表，用于查询自动任务信息
     * @return 返回查询到的自动任务信息列表如果无数据，返回空列表
     */
    @Override
    public List<NotificationAutoTaskDO> queryAutoTaskByCodes(List<String> taskCodeList) {
        if (CollUtil.isEmpty(taskCodeList)) {
            return new ArrayList<>();
        }
        log.info("根据任务代码列表查询自动任务信息, taskCodeList的数量:{}", taskCodeList.size());
        LambdaQueryWrapper<NotificationAutoTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(NotificationAutoTaskDO::getTaskCode, taskCodeList)
                .eq(NotificationAutoTaskDO::getIsDeleted, false);
        return list(queryWrapper);
    }

    /**
     * 处理任务名称，确保特定字符被转义
     *
     * @param taskName 原始任务名称，可能包含需要转义的特殊字符
     * @return 转义后的任务名称如果输入的任务名称为空或只包含空白字符，则返回原始任务名称
     */
    private String processTaskName(String taskName) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(taskName)) {
            // 正确地替换字符串并返回新字符串
            return taskName.replace("%", "\\%").replace("_", "\\_");
        }
        return taskName;
    }

}
