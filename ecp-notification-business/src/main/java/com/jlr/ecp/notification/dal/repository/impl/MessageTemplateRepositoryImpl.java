package com.jlr.ecp.notification.dal.repository.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.notification.dal.dataobject.template.MessageTemplate;
import com.jlr.ecp.notification.dal.mysql.template.SmsTemplateMapper;
import com.jlr.ecp.notification.dal.repository.MessageTemplateRepository;
import com.jlr.ecp.notification.enums.IsDeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class MessageTemplateRepositoryImpl implements MessageTemplateRepository {

    @Resource
    private SmsTemplateMapper templateMapper;

    /**
     * 手动添加短信模板
     * @param messageTemplate 手动模板信息的数据
     * @return 返回操作结果，成功返回true，失败返回false
     */
    @Override
    public Boolean addMessageTemplate(MessageTemplate messageTemplate) {
        try {
            log.error("添加手动短信模板, 数据为空");
            templateMapper.insert(messageTemplate);
        } catch (Exception e) {
            log.error("添加短信手动模板异常：", e);
            return false;
        }
        return true;
    }

    /**
     *  修改手动短信模板
     * @param messageTemplate 包含手动模板信息的数据
     * @return 返回操作结果，成功返回true，失败返回false
     */
    @Override
    public Boolean updateMessageManualTemplate(MessageTemplate messageTemplate) {
        if (Objects.isNull(messageTemplate)) {
            log.error("修改手动短信模板, 数据为空");
            return false;
        }
        try {
            templateMapper.updateByTemplateCode(messageTemplate);
        } catch (Exception e) {
            log.error("修改短信手动模板异常：", e);
            return false;
        }
        return true;
    }

    /**
     *  获取手动模板详情
     *
     * @param templateCode 模板code
     * @return MessageTemplate
     * */
    @Override
    public MessageTemplate getManualTemplateDetail(String templateCode) {
        if (StringUtils.isBlank(templateCode)) {
            log.error("获取手动模板详情, 模板code为空");
            return null;
        }
        try {
            LambdaQueryWrapper<MessageTemplate> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MessageTemplate::getTemplateCode, templateCode)
                    .eq(MessageTemplate::getIsDeleted, false);
            List<MessageTemplate> messageTemplateList = templateMapper.selectList(wrapper);
            if (CollectionUtils.isEmpty(messageTemplateList)) {
                log.warn("模板code获取模板信息为空, templateCode:{}", templateCode);
                return null;
            }
            if (messageTemplateList.size() > 1) {
                log.error("模板code获取模板信息大于1条, templateCode:{}", templateCode);
            }
            return messageTemplateList.get(0);
        } catch (Exception e) {
            log.error("获取手动模板详情异常：", e);
        }
        return null;
    }

    /**
     * 通过模板code删除模板（逻辑删除）
     *
     * @param templateCode 模板code
     * @return Boolean
     * */
    @Override
    public Boolean deleteTemplateByCode(String templateCode) {
        if (StringUtils.isBlank(templateCode)) {
            log.error("删除手动模板, 模板code为空");
            return false;
        }
        try {
            templateMapper.deleteByTemplateCode(templateCode);
        } catch (Exception e) {
            log.error("删除手动模板异常：", e);
            return false;
        }
        return true;
    }


    /**
     *  按照模板Code查询模板数据
     * @param templateCode 模板code
     * @return MessageTemplate
     * */
    public MessageTemplate getMessageTemplateByCode(String templateCode) {
        if (StringUtils.isBlank(templateCode)) {
            log.info("按照模板code查询模板数据, 模板code为空");
            return null;
        }
        LambdaQueryWrapper<MessageTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MessageTemplate::getTemplateCode, templateCode)
                .eq(MessageTemplate::getIsDeleted, IsDeleteEnum.NO.getStatus());
        List<MessageTemplate> templates = templateMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(templates)) {
            log.info("当前模板不存在, templateCode:{}", templateCode);
            return null;
        }
        if (templates.size() > 1) {
            log.warn("当前的模板code存在多个模板, templateCode:{}", templateCode);
        }
        return templates.get(0);
    }



    /**
     * 根据模板名称获取消息模板列表。
     *
     * @param templateName 模板名称，不可为空或仅包含空白字符。
     * @param businessCode 业务线code
     * @return 如果找到匹配的且未被删除的消息模板，则返回其列表；如果名称为空或仅包含空白字符，则返回null。
     */
    @Override
    public List<MessageTemplate> getMessageTemplateByName(String templateName, String businessCode) {
        log.info("根据模板名称获取消息模板列表, templateName:{}, businessCode:{}",
                templateName, businessCode);
        if (StringUtils.isBlank(templateName) || StringUtils.isBlank(businessCode)) {
            log.info("根据模板名称获取消息模板列表templateName或businessCode为空, templateName:{}, businessCode:{}",
                    templateName, businessCode);
            return new ArrayList<>();
        }
        LambdaQueryWrapper<MessageTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MessageTemplate::getTemplateName, templateName)
                .eq(MessageTemplate::getBusinessCode, businessCode)
                .eq(MessageTemplate::getIsDeleted, false);
        return templateMapper.selectList(wrapper);
    }

    /**
     * 根据模板名称和类型获取消息模板列表。
     *
     * @param templateName 模板名称，不可为空或仅包含空白字符。
     * @param businessCode 业务线code
     * @param templateType 模板类型
     * @return 如果找到匹配的且未被删除的消息模板，则返回其列表；如果名称为空或仅包含空白字符，则返回null。
     */
    @Override
    public List<MessageTemplate> getTemplateByTypeName(String templateName, String businessCode, Integer templateType) {
        log.info("根据模板名称和类型获取消息模板列表, templateName:{}, businessCode:{}, templateType:{}",
                templateName, businessCode, templateType);
        if (StringUtils.isBlank(templateName) || StringUtils.isBlank(businessCode)) {
            log.info("根据模板名称和类型获取消息模板列表结果为空");
            return new ArrayList<>();
        }
        LambdaQueryWrapper<MessageTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MessageTemplate::getTemplateName, templateName)
                .eq(MessageTemplate::getBusinessCode, businessCode)
                .eq(MessageTemplate::getTemplateType, templateType)
                .eq(MessageTemplate::getIsDeleted, false);
        return templateMapper.selectList(wrapper);
    }

    /**
     * 自动模板分页
     *
     * @param pageReq 分页请求对象，包含分页查询所需的信息，如当前页码、每页记录数等
     * @param wrapper 查询包装器，用于指定查询条件
     * @return 返回一个分页对象，其中包含根据查询条件筛选出的记录
     */
    @Override
    public Page<MessageTemplate> selectAutoTemplatePage(Page<MessageTemplate> pageReq,
                                                        LambdaQueryWrapper<MessageTemplate> wrapper) {
        return templateMapper.selectPage(pageReq, wrapper);
    }

    /**
     * 根据ID更新短信模板信息
     *
     * @param messageTemplate 包含更新信息的短信模板对象
     * @return 更新操作的结果，通常为受影响的行数
     */
    @Override
    public int updateById(MessageTemplate messageTemplate) {
        return templateMapper.updateById(messageTemplate);
    }

    /**
     *   按照模板code返回模板内容
     * @param templateCode  模板code
     * @return String
     * */
    public String getTemplateContentByCode(String templateCode) {
        MessageTemplate messageTemplate = getMessageTemplateByCode(templateCode);
        if (Objects.isNull(messageTemplate)) {
            log.info("按照模板code返回模板内容， 模板数据为空");
            return null;
        }
        return messageTemplate.getTemplateContent();
    }

    /**
     * 根据模板代码列表查询消息模板列表
     *
     * @param templateCodeList 模板代码列表，用于查询消息模板
     * @return 返回一个消息模板列表，如果输入为空，则返回空列表
     */
    @Override
    public List<MessageTemplate> queryTemplateListByCodes(Collection<String> templateCodeList) {
        log.info("根据模板代码列表查询消息模板列表, templateCodeList:{}", templateCodeList);
        if (CollUtil.isEmpty(templateCodeList)) {
            return new ArrayList<>();
        }
        return templateMapper.selectList(new LambdaQueryWrapper<MessageTemplate>()
                .select(MessageTemplate::getTemplateContent, MessageTemplate::getTemplateCode)
                .in(MessageTemplate::getTemplateCode, templateCodeList));
    }

    /**
     * 检查指定的模板代码是否已存在于数据库中
     *
     * @param templateCode 需要检查的模板代码
     * @return 如果模板代码存在，则返回true；否则返回false
     */
    @Override
    public boolean existsTemplateCode(String templateCode) {
        log.info("检查指定的模板代码是否已存在于数据库中, templateCode:{}", templateCode);
        if (StringUtils.isBlank(templateCode)) {
            return false;
        }
        return templateMapper.exists(new LambdaQueryWrapperX<MessageTemplate>()
                .eq(MessageTemplate::getTemplateCode, templateCode));
    }

    /**
     * 检查手动模板代码是否已存在于数据库中
     *
     * @param templateCode 需要检查的模板代码
     * @param templateType 模板类型
     * @return 如果模板代码存在，则返回true；否则返回false
     */
    @Override
    public  boolean existsTemplateCodeByType(String templateCode, Integer templateType) {
        log.info("检查手动模板代码是否已存在于数据库中, templateCode:{}, templateType:{}", templateCode, templateType);
        if (StringUtils.isBlank(templateCode) || Objects.isNull(templateType)) {
            return false;
        }
        return templateMapper.exists(new LambdaQueryWrapperX<MessageTemplate>()
                .eq(MessageTemplate::getTemplateCode, templateCode)
                .eq(MessageTemplate::getTemplateType, templateType)
        );
    }

    /**
     * 根据模板类型获取模板名称列表
     *
     * @param templateType 模板类型，用于筛选具有特定类型的邮件模板
     * @param  templateType，用于筛选具有特定时间类型的模板
     * @param businessCode 业务代码
     * @return 包含所有指定类型的模板的名称
     */
    @Override
    public List<MessageTemplate> getTemplateListByType(Integer templateType, Integer autoType, String businessCode) {
        log.info("根据模板类型获取模板名称列表, templateType:{}, autoType:{}", templateType, autoType);
        if (Objects.isNull(templateType)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<MessageTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MessageTemplate::getTemplateType, templateType)
                .eq(Objects.nonNull(autoType), MessageTemplate::getAutoType, autoType)
                .eq(MessageTemplate::getIsDeleted, false);
        if (StringUtils.isNotBlank(businessCode)) {
            queryWrapper.eq(MessageTemplate::getBusinessCode, businessCode);
        }
        return templateMapper.selectList(queryWrapper);
    }

    /**
     * 根据业务线获取模板代码列表
     *
     * @param businessCode 业务线代码
     * @return 模板代码列表
     */
    @Override
    public List<String> listTemplateCodesByBusinessCode(String businessCode) {
        if (StringUtils.isBlank(businessCode)) {
            return new ArrayList<>();
        }
        
        LambdaQueryWrapper<MessageTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MessageTemplate::getBusinessCode, businessCode)
                    .select(MessageTemplate::getTemplateCode);
        
        List<MessageTemplate> templates = templateMapper.selectList(queryWrapper);
        
        return templates.stream()
                    .map(MessageTemplate::getTemplateCode)
                    .collect(Collectors.toList());
    }
}
