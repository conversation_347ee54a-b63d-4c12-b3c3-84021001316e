package com.jlr.ecp.notification.dal.dataobject.template;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

@TableName("t_notification_template_modify_log")
@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class TemplateModifyLog extends BaseDO {
    /**
     * 主键id, 自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 模板编码
     */
    @TableField(value = "template_code")
    private String templateCode;

    /**
     * 修改模块
     */
    @TableField(value = "modify_module")
    private String modifyModule;

    /**
     * 修改字段数量
     */
    @TableField(value = "modify_field_count")
    private Integer modifyFieldCount;

    /**
     * 修改前字段值（JSON格式）
     */
    @TableField(value = "modify_field_old_value")
    private String modifyFieldOldValue;

    /**
     * 修改后字段值（JSON格式）
     */
    @TableField(value = "modify_field_new_value")
    private String modifyFieldNewValue;

    /**
     * 修改人账号
     */
    @TableField(value = "operate_user")
    private String operateUser;

    /**
     * 修改时间
     */
    @TableField(value = "operate_time")
    private LocalDateTime operateTime;

    /**
     * 租户号
     */
    @TableField(value = "tenant_id")
    private Integer tenantId;

    // BaseDO已包含以下字段，无需重复定义：
    // createdTime, updatedTime, createdBy, updatedBy, isDeleted, revision
}
