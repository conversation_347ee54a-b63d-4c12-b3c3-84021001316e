package com.jlr.ecp.notification.dto.mp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Schema(description = "小程序-订单取消消息DTO")
public class MpOrderCancelMsgDTO {
    @JsonProperty("orderNo")
    private String orderNo;

    @JsonProperty("reminder")
    private String reminder;
}
