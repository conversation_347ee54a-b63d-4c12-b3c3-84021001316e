package com.jlr.ecp.notification.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "发送小程序模板消息")
public class SendMpMsgDTO {
    /**
     * 自增主键id
     */
    private Long id;

    /**
     * 用户全局唯一ID
     */
    private String jlrId;

    /**
     * 小程序的openId
     */
    private String openId;

    /**
     * 服务到期的consent
     */
    private Integer serviceExpirationConsent;

    /**
     *  incontrolId
     * */
    private String incontrolId;

    /**
     *  车辆编码
     * */
    private String carVin;

    /**
     * 车辆编码
     * */
    private String brandCode;

    /**
     *  服务类型
     * */
    private Integer serviceType;


    /**
     *  到期时间
     * */
    private String expiryDate;
}
