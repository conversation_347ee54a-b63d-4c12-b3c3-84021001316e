package com.jlr.ecp.notification.kafka.listener;

import com.alibaba.fastjson.JSON;
import com.jlr.ecp.notification.kafka.message.OrderMessage;
import com.jlr.ecp.notification.kafka.send.template.SmsSendTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

@Component
@Slf4j
public class OrderSuccessListener {
    @Resource
    private SmsSendTemplate smsSendTemplate;

    @KafkaListener(topics = "order-successful-topic", groupId = "order-successful-group", batch = "true",
            properties = "max.poll.records:100")
    public void listen(List<ConsumerRecord<String, String >> records) {
        log.info("下单成功消息，records:{}", records);
        List<OrderMessage> orderMessageList = buildOrderMessageList(records);
        for (OrderMessage orderMessage : orderMessageList) {
            smsSendTemplate.sendSmsMsg(orderMessage);
        }
    }

    /**
     *  构建订单消息
     *  @param records 消息体
     *  @return  List<OrderMessage>
     * */
    public List<OrderMessage> buildOrderMessageList(List<ConsumerRecord<String, String >> records) {
        List<OrderMessage> orderMessageList = new ArrayList<>();
        if (CollectionUtils.isEmpty(records)) {
            return orderMessageList;
        }
        for (ConsumerRecord<String, String> record : records) {
            OrderMessage orderMessage = JSON.parseObject(record.value(), OrderMessage.class);
            orderMessageList.add(orderMessage);
        }
        return orderMessageList;
    }

}
