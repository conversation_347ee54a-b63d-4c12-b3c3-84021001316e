package com.jlr.ecp.notification.kafka.message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExpireMessage extends SmsMessage{
    /**
     * 服务到期时间
     * */
    private String expireDate;

    /**
     *  品牌+车型    这里不需要用到brandCode,在传入的数据PhoneByCarVinDTO包含brandCode
     * */
    private String brandAndModel;

    /**
     *   微信小程序链接
     * */
    private String wxUrl;
}
