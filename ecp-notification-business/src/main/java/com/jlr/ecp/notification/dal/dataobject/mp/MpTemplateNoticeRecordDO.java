package com.jlr.ecp.notification.dal.dataobject.mp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 小程序模板通知发送记录DO
 *
 */
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("t_mp_template_notice_record")
public class MpTemplateNoticeRecordDO extends BaseDO {

    /**
     * 自增主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     *  发送成功的消息Id
     * */
    @TableField("message_id")
    private String messageId;

    /**
     * 用户全局唯一ID
     */
    @TableField("jlr_id")
    private String jlrId;

    /**
     * 小程序的openId
     */
    @TableField("open_id")
    private String openId;

    /**
     * sns的topic
     */
    @TableField("sns_topic")
    private String snsTopic;

    /**
     * 事件类型
     */
    @TableField("event_type")
    private String eventType;

    /**
     * 订单编号
     */
    @TableField(value = "order_no")
    private String orderNo;

    /**
     * 车辆编号
     */
    @TableField(value = "car_vin")
    private String carVin;

    /**
     * 发送消息，包含了发送的参数
     */
    @TableField("send_message")
    private String sendMessage;

    /**
     * 租户号
     */
    @TableField("tenant_id")
    private Integer tenantId;

}
