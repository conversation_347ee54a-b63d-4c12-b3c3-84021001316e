<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />
    <springProperty scope="context" name="BASE_PACKAGE" source="lc.info.base-package"/>
    <property name="PATTERN_DEFAULT" value="%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%thread] [%X{traceId:-N/A}] %-40.40logger{39} : %m%n%singleLineEx"/>
    <conversionRule conversionWord="singleLineEx"
                    converterClass="com.jlr.ecp.notification.config.SingleLineThrowableProxyConverter"/>
    <!-- 控制台 Appender -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>${PATTERN_DEFAULT}</pattern>
            </layout>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 文件 Appender，改为 JSON 格式，便于 Fluent Bit 采集 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <pattern>
                    <pattern>
                        {
                        "appName":"${APP_NAME:-default}",
                        "time":"%date{ISO8601}",
                        "level":"%level",
                        "thread":"%thread",
                        "sourceLocation":"%c{36}-%M-%L",
                        "message":"%msg",
                        "exception":"%ex{full}"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
        <file>${LOG_FILE}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOGBACK_ROLLINGPOLICY_FILE_NAME_PATTERN:-${LOG_FILE}.%d{yyyy-MM-dd}.%i.gz}</fileNamePattern>
            <cleanHistoryOnStart>${LOGBACK_ROLLINGPOLICY_CLEAN_HISTORY_ON_START:-false}</cleanHistoryOnStart>
            <maxFileSize>${LOGBACK_ROLLINGPOLICY_MAX_FILE_SIZE:-10MB}</maxFileSize>
            <totalSizeCap>${LOGBACK_ROLLINGPOLICY_TOTAL_SIZE_CAP:-0}</totalSizeCap>
            <maxHistory>${LOGBACK_ROLLINGPOLICY_MAX_HISTORY:-30}</maxHistory>
        </rollingPolicy>
    </appender>

    <!-- 异步写入日志，提升性能 -->
    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>256</queueSize>
        <appender-ref ref="FILE"/>
    </appender>

    <!-- 本地环境 -->
    <springProfile name="local,localDev">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="ASYNC"/>
        </root>
        <logger name="com.jlr.ecp.notification.dal.mysql" level="debug" />
        <logger name="com.jlr.ecp.framework.kafka" level="error"/>
        <logger name="org.springframework.cache" level="trace"/>
        <logger name="org.springframework.kafka" level="error"/>
        <logger name="org.apache.kafka" level="error"/>
    </springProfile>
    <!-- 其它环境 -->
    <springProfile name="dev,test,uat,stage,prod,default">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="ASYNC"/>
        </root>
    </springProfile>
</configuration>