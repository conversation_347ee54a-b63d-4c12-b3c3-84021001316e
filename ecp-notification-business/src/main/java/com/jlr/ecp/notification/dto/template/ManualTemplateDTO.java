package com.jlr.ecp.notification.dto.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "手动模板dto")
public class ManualTemplateDTO {

    @Schema(description = "业务线code，必填")
    @Size(max = 90, message = "业务线code长度不能超过90个字符")
    private String businessCode;

    @Schema(description = "模板code, 修改时必填, 添加时不填")
    private String templateCode;

    @Schema(description = "模板名称，必填")
    @Size(max = 30, message = "模板名称长度不能超过30个字符")
    private String templateName;

    @Schema(description = "模板类型，必填, 1-自动配置, 2-手动配置")
    private Integer templateType;

    @Schema(description = "模板内容，必填")
    @Size(max = 280, message = "模板内容长度不能超过280个字符")
    private String templateContent;

    @Schema(description = "场景说明，必填")
    @Size(max = 900, message = "场景说明长度不能超过900个字符")
    private String templateRemarks;
}
