package com.jlr.ecp.notification.vo.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 *  sms模板返回的数据
 *
 * <AUTHOR>
 * */
@Data
@Builder
@Schema(description = "短信模板列表")
public class SmsTemplateVo {

    @Schema(description = "业务线")
    private String businessCode;

    @Schema(description = "业务线名称")
    private String businessName;

    @Schema(description = "模板编码")
    private String templateCode;

    @Schema(description = "模板名称")
    private String templateName;

    /**
     * 模板类型, @see com.jlr.ecp.notification.enums.template.SmsTemplateTypeEnum
     * */
    @Schema(description = "模板类型")
    private String templateType;

    @Schema(description = "模板内容")
    private String templateContent;

    @Schema(description = "修改时间")
    private String modifyTime;

    @Schema(description = "修改人")
    private String modifyBy;

    @Schema(description = "模板中的变量")
    private String templateVariables;
}
