package com.jlr.ecp.notification.enums.task;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum TaskSendTypeEnum {
    REALTIME(1, "实时"),
    SCHEDULED(2, "定时");
    /**
     * 发送类型code
     * */
    private final Integer code;

    /**
     * 发送类型描述
     * */
    private final String desc;

    /**
     * 按照类型code返回发送类型枚举
     * @param typeCode 类型枚举
     * @return TaskTypeEnum
     * */
    public static TaskSendTypeEnum getTaskEnumByCode(Integer typeCode) {
        TaskSendTypeEnum[] typeEnums = TaskSendTypeEnum.values();
        for (TaskSendTypeEnum typeEnum : typeEnums) {
            if (typeEnum.getCode().equals(typeCode)) {
                return typeEnum;
            }
        }
        return null;
    }
}
