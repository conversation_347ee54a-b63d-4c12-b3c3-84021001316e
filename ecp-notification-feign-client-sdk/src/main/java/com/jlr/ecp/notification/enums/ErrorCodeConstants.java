package com.jlr.ecp.notification.enums;

import com.jlr.ecp.framework.common.exception.ErrorCode;


/**
 * System 错误码枚举类
 * <p>
 * system 系统，使用 1-002-000-000 段
 */
public class ErrorCodeConstants {
    private static final String TEMPLATE_ARGUMENT_ABSENT = "模板参数({})缺失";

    // ========== AUTH 模块 1002000000 ==========
    public static final ErrorCode AUTH_LOGIN_BAD_CREDENTIALS = new ErrorCode(1002000000, "登录失败，账号密码不正确");
    public static final ErrorCode AUTH_LOGIN_USER_DISABLED = new ErrorCode(1002000001, "登录失败，账号被禁用");
    public static final ErrorCode AUTH_LOGIN_CAPTCHA_CODE_ERROR = new ErrorCode(1002000004, "验证码不正确，原因：{}");
    public static final ErrorCode AUTH_THIRD_LOGIN_NOT_BIND = new ErrorCode(1002000005, "未绑定账号，需要进行绑定");
    public static final ErrorCode AUTH_TOKEN_EXPIRED = new ErrorCode(1002000006, "Token 已经过期");
    public static final ErrorCode AUTH_MOBILE_NOT_EXISTS = new ErrorCode(1002000007, "手机号不存在");

    // ========== 菜单模块 1002001000 ==========
    public static final ErrorCode MENU_NAME_DUPLICATE = new ErrorCode(1002001000, "已经存在该名字的菜单");
    public static final ErrorCode MENU_PARENT_NOT_EXISTS = new ErrorCode(1002001001, "父菜单不存在");
    public static final ErrorCode MENU_PARENT_ERROR = new ErrorCode(1002001002, "不能设置自己为父菜单");
    public static final ErrorCode MENU_NOT_EXISTS = new ErrorCode(1002001003, "菜单不存在");
    public static final ErrorCode MENU_EXISTS_CHILDREN = new ErrorCode(1002001004, "存在子菜单，无法删除");
    public static final ErrorCode MENU_PARENT_NOT_DIR_OR_MENU = new ErrorCode(1002001005, "父菜单的类型必须是目录或者菜单");

    // ========== 角色模块 1002002000 ==========
    public static final ErrorCode ROLE_NOT_EXISTS = new ErrorCode(1002002000, "角色不存在");
    public static final ErrorCode ROLE_NAME_DUPLICATE = new ErrorCode(1002002001, "已经存在名为【{}】的角色");
    public static final ErrorCode ROLE_CODE_DUPLICATE = new ErrorCode(1002002002, "已经存在编码为【{}】的角色");
    public static final ErrorCode ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE = new ErrorCode(1002002003, "不能操作类型为系统内置的角色");
    public static final ErrorCode ROLE_IS_DISABLE = new ErrorCode(1002002004, "名字为【{}】的角色已被禁用");
    public static final ErrorCode ROLE_ADMIN_CODE_ERROR = new ErrorCode(1002002005, "编码【{}】不能使用");

    // ========== 用户模块 1002003000 ==========
    public static final ErrorCode USER_USERNAME_EXISTS = new ErrorCode(1002003000, "用户账号已经存在");
    public static final ErrorCode USER_MOBILE_EXISTS = new ErrorCode(1002003001, "手机号已经存在");
    public static final ErrorCode USER_EMAIL_EXISTS = new ErrorCode(1002003002, "邮箱已经存在");
    public static final ErrorCode USER_NOT_EXISTS = new ErrorCode(1002003003, "用户不存在");
    public static final ErrorCode USER_IMPORT_LIST_IS_EMPTY = new ErrorCode(1002003004, "导入用户数据不能为空！");
    public static final ErrorCode USER_PASSWORD_FAILED = new ErrorCode(1002003005, "用户密码校验失败");
    public static final ErrorCode USER_IS_DISABLE = new ErrorCode(1002003006, "名字为【{}】的用户已被禁用");
    public static final ErrorCode USER_COUNT_MAX = new ErrorCode(1002003008, "创建用户失败，原因：超过租户最大租户配额({})！");

    // ========== 部门模块 1002004000 ==========
    public static final ErrorCode DEPT_NAME_DUPLICATE = new ErrorCode(1002004000, "已经存在该名字的部门");
    public static final ErrorCode DEPT_PARENT_NOT_EXITS = new ErrorCode(1002004001, "父级部门不存在");
    public static final ErrorCode DEPT_NOT_FOUND = new ErrorCode(1002004002, "当前部门不存在");
    public static final ErrorCode DEPT_EXITS_CHILDREN = new ErrorCode(1002004003, "存在子部门，无法删除");
    public static final ErrorCode DEPT_PARENT_ERROR = new ErrorCode(1002004004, "不能设置自己为父部门");
    public static final ErrorCode DEPT_EXISTS_USER = new ErrorCode(1002004005, "部门中存在员工，无法删除");
    public static final ErrorCode DEPT_NOT_ENABLE = new ErrorCode(1002004006, "部门({})不处于开启状态，不允许选择");
    public static final ErrorCode DEPT_PARENT_IS_CHILD = new ErrorCode(1002004007, "不能设置自己的子部门为父部门");

    // ========== 岗位模块 1002005000 ==========
    public static final ErrorCode POST_NOT_FOUND = new ErrorCode(1002005000, "当前岗位不存在");
    public static final ErrorCode POST_NOT_ENABLE = new ErrorCode(1002005001, "岗位({}) 不处于开启状态，不允许选择");
    public static final ErrorCode POST_NAME_DUPLICATE = new ErrorCode(1002005002, "已经存在该名字的岗位");
    public static final ErrorCode POST_CODE_DUPLICATE = new ErrorCode(1002005003, "已经存在该标识的岗位");

    // ========== 字典类型 1002006000 ==========
    public static final ErrorCode DICT_TYPE_NOT_EXISTS = new ErrorCode(1002006001, "当前字典类型不存在");
    public static final ErrorCode DICT_TYPE_NOT_ENABLE = new ErrorCode(1002006002, "字典类型不处于开启状态，不允许选择");
    public static final ErrorCode DICT_TYPE_NAME_DUPLICATE = new ErrorCode(1002006003, "已经存在该名字的字典类型");
    public static final ErrorCode DICT_TYPE_TYPE_DUPLICATE = new ErrorCode(1002006004, "已经存在该类型的字典类型");
    public static final ErrorCode DICT_TYPE_HAS_CHILDREN = new ErrorCode(1002006005, "无法删除，该字典类型还有字典数据");

    // ========== 字典数据 1002007000 ==========
    public static final ErrorCode DICT_DATA_NOT_EXISTS = new ErrorCode(1002007001, "当前字典数据不存在");
    public static final ErrorCode DICT_DATA_NOT_ENABLE = new ErrorCode(1002007002, "字典数据({})不处于开启状态，不允许选择");
    public static final ErrorCode DICT_DATA_VALUE_DUPLICATE = new ErrorCode(1002007003, "已经存在该值的字典数据");

    // ========== 通知公告 1002008000 ==========
    public static final ErrorCode NOTICE_NOT_FOUND = new ErrorCode(1002008001, "当前通知公告不存在");

    // ========== 短信渠道 1002011000 ==========
    public static final ErrorCode SMS_CHANNEL_NOT_EXISTS = new ErrorCode(1002011000, "短信渠道不存在");
    public static final ErrorCode SMS_CHANNEL_DISABLE = new ErrorCode(1002011001, "短信渠道不处于开启状态，不允许选择");
    public static final ErrorCode SMS_CHANNEL_HAS_CHILDREN = new ErrorCode(1002011002, "无法删除，该短信渠道还有短信模板");

    // ========== 短信模板 1002012000 ==========
    public static final ErrorCode SMS_TEMPLATE_NOT_EXISTS = new ErrorCode(1002012000, "短信模板不存在");
    public static final ErrorCode SMS_TEMPLATE_CODE_DUPLICATE = new ErrorCode(1002012001, "已经存在编码为【{}】的短信模板");

    // ========== 短信发送 1002013000 ==========
    public static final ErrorCode SMS_SEND_MOBILE_NOT_EXISTS = new ErrorCode(1002013000, "手机号不存在");
    public static final ErrorCode SMS_SEND_MOBILE_TEMPLATE_PARAM_MISS = new ErrorCode(1002013001, TEMPLATE_ARGUMENT_ABSENT);
    public static final ErrorCode SMS_SEND_TEMPLATE_NOT_EXISTS = new ErrorCode(1002013002, "短信模板不存在");

    // ========== 短信验证码 1002014000 ==========
    public static final ErrorCode SMS_CODE_NOT_FOUND = new ErrorCode(1002014000, "验证码不存在");
    public static final ErrorCode SMS_CODE_EXPIRED = new ErrorCode(1002014001, "验证码已过期");
    public static final ErrorCode SMS_CODE_USED = new ErrorCode(1002014002, "验证码已使用");
    public static final ErrorCode SMS_CODE_NOT_CORRECT = new ErrorCode(1002014003, "验证码不正确");
    public static final ErrorCode SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY = new ErrorCode(1002014004, "超过每日短信发送数量");
    public static final ErrorCode SMS_CODE_SEND_TOO_FAST = new ErrorCode(1002014005, "短信发送过于频率");
    public static final ErrorCode SMS_CODE_IS_EXISTS = new ErrorCode(1002014006, "手机号已被使用");
    public static final ErrorCode SMS_CODE_IS_UNUSED = new ErrorCode(1002014007, "验证码未被使用");

    // ========== 租户信息 1002015000 ==========
    public static final ErrorCode TENANT_NOT_EXISTS = new ErrorCode(1002015000, "租户不存在");
    public static final ErrorCode TENANT_DISABLE = new ErrorCode(1002015001, "名字为【{}】的租户已被禁用");
    public static final ErrorCode TENANT_EXPIRE = new ErrorCode(1002015002, "名字为【{}】的租户已过期");
    public static final ErrorCode TENANT_CAN_NOT_UPDATE_SYSTEM = new ErrorCode(1002015003, "系统租户不能进行修改、删除等操作！");
    public static final ErrorCode TENANT_NAME_DUPLICATE = new ErrorCode(1002015004, "名字为【{}】的租户已存在");

    // ========== 租户套餐 1002016000 ==========
    public static final ErrorCode TENANT_PACKAGE_NOT_EXISTS = new ErrorCode(1002016000, "租户套餐不存在");
    public static final ErrorCode TENANT_PACKAGE_USED = new ErrorCode(1002016001, "租户正在使用该套餐，请给租户重新设置套餐后再尝试删除");
    public static final ErrorCode TENANT_PACKAGE_DISABLE = new ErrorCode(1002016002, "名字为【{}】的租户套餐已被禁用");

    // ========== 错误码模块 1002017000 ==========
    public static final ErrorCode ERROR_CODE_NOT_EXISTS = new ErrorCode(1002017000, "错误码不存在");
    public static final ErrorCode ERROR_CODE_DUPLICATE = new ErrorCode(1002017001, "已经存在编码为【{}】的错误码");

    // ========== 社交用户 1002018000 ==========
    public static final ErrorCode SOCIAL_USER_AUTH_FAILURE = new ErrorCode(1002018000, "社交授权失败，原因是：{}");
    public static final ErrorCode SOCIAL_USER_UNBIND_NOT_SELF = new ErrorCode(1002018001, "社交解绑失败，非当前用户绑定");
    public static final ErrorCode SOCIAL_USER_NOT_FOUND = new ErrorCode(1002018002, "社交授权失败，找不到对应的用户");

    // ========== 系统敏感词 1002019000 =========
    public static final ErrorCode SENSITIVE_WORD_NOT_EXISTS = new ErrorCode(1002019000, "系统敏感词在所有标签中都不存在");
    public static final ErrorCode SENSITIVE_WORD_EXISTS = new ErrorCode(1002019001, "系统敏感词已在标签中存在");

    // ========== OAuth2 客户端 1002020000 =========
    public static final ErrorCode OAUTH2_CLIENT_NOT_EXISTS = new ErrorCode(1002020000, "OAuth2 客户端不存在");
    public static final ErrorCode OAUTH2_CLIENT_EXISTS = new ErrorCode(1002020001, "OAuth2 客户端编号已存在");
    public static final ErrorCode OAUTH2_CLIENT_DISABLE = new ErrorCode(1002020002, "OAuth2 客户端已禁用");
    public static final ErrorCode OAUTH2_CLIENT_AUTHORIZED_GRANT_TYPE_NOT_EXISTS = new ErrorCode(1002020003, "不支持该授权类型");
    public static final ErrorCode OAUTH2_CLIENT_SCOPE_OVER = new ErrorCode(1002020004, "授权范围过大");
    public static final ErrorCode OAUTH2_CLIENT_REDIRECT_URI_NOT_MATCH = new ErrorCode(1002020005, "无效 redirect_uri: {}");
    public static final ErrorCode OAUTH2_CLIENT_CLIENT_SECRET_ERROR = new ErrorCode(1002020006, "无效 client_secret: {}");

    // ========== OAuth2 授权 1002021000 =========
    public static final ErrorCode OAUTH2_GRANT_CLIENT_ID_MISMATCH = new ErrorCode(1002021000, "client_id 不匹配");
    public static final ErrorCode OAUTH2_GRANT_REDIRECT_URI_MISMATCH = new ErrorCode(1002021001, "redirect_uri 不匹配");
    public static final ErrorCode OAUTH2_GRANT_STATE_MISMATCH = new ErrorCode(**********, "state 不匹配");
    public static final ErrorCode OAUTH2_GRANT_CODE_NOT_EXISTS = new ErrorCode(**********, "code 不存在");

    // ========== OAuth2 授权 ********** =========
    public static final ErrorCode OAUTH2_CODE_NOT_EXISTS = new ErrorCode(**********, "code 不存在");
    public static final ErrorCode OAUTH2_CODE_EXPIRE = new ErrorCode(**********, "code 已过期");

    // ========== 邮箱账号 ********** ==========
    public static final ErrorCode MAIL_ACCOUNT_NOT_EXISTS = new ErrorCode(**********, "邮箱账号不存在");
    public static final ErrorCode MAIL_ACCOUNT_RELATE_TEMPLATE_EXISTS = new ErrorCode(**********, "无法删除，该邮箱账号还有邮件模板");

    // ========== 邮件模版 ********** ==========
    public static final ErrorCode MAIL_TEMPLATE_NOT_EXISTS = new ErrorCode(**********, "邮件模版不存在");
    public static final ErrorCode MAIL_TEMPLATE_CODE_EXISTS = new ErrorCode(**********, "邮件模版 code({}) 已存在");

    // ========== 邮件发送 ********** ==========
    public static final ErrorCode MAIL_SEND_TEMPLATE_PARAM_MISS = new ErrorCode(**********, TEMPLATE_ARGUMENT_ABSENT);
    public static final ErrorCode MAIL_SEND_MAIL_NOT_EXISTS = new ErrorCode(**********, "邮箱不存在");

    // ========== 站内信模版 ********** ==========
    public static final ErrorCode NOTIFY_TEMPLATE_NOT_EXISTS = new ErrorCode(**********, "站内信模版不存在");
    public static final ErrorCode NOTIFY_TEMPLATE_CODE_DUPLICATE = new ErrorCode(**********, "已经存在编码为【{}】的站内信模板");

    // ========== 站内信模版 1002027000 ==========

    // ========== 站内信发送 1002028000 ==========
    public static final ErrorCode NOTIFY_SEND_TEMPLATE_PARAM_MISS = new ErrorCode(1002028000, TEMPLATE_ARGUMENT_ABSENT);
    // ========== bpm Http接口调用 1002029000 ==========
    public static final ErrorCode REST_TEMPLATE_ERROR = new ErrorCode(1002029000, "Http 接口调用失败");
    // ========== 参数配置 1001000000 ==========
    public static final ErrorCode CONFIG_NOT_EXISTS = new ErrorCode(1001000001, "参数配置不存在");
    public static final ErrorCode CONFIG_KEY_DUPLICATE = new ErrorCode(1001000002, "参数配置 key 重复");
    public static final ErrorCode CONFIG_CAN_NOT_DELETE_SYSTEM_TYPE = new ErrorCode(1001000003, "不能删除类型为系统内置的参数配置");
    public static final ErrorCode CONFIG_GET_VALUE_ERROR_IF_VISIBLE = new ErrorCode(1001000004, "获取参数配置失败，原因：不允许获取不可见配置");

    // ========== 定时任务 1001001000 ==========
    public static final ErrorCode JOB_NOT_EXISTS = new ErrorCode(1001001000, "定时任务不存在");
    public static final ErrorCode JOB_HANDLER_EXISTS = new ErrorCode(1001001001, "定时任务的处理器已经存在");
    public static final ErrorCode JOB_CHANGE_STATUS_INVALID = new ErrorCode(1001001002, "只允许修改为开启或者关闭状态");
    public static final ErrorCode JOB_CHANGE_STATUS_EQUALS = new ErrorCode(1001001003, "定时任务已经处于该状态，无需修改");
    public static final ErrorCode JOB_UPDATE_ONLY_NORMAL_STATUS = new ErrorCode(1001001004, "只有开启状态的任务，才可以修改");
    public static final ErrorCode JOB_CRON_EXPRESSION_VALID = new ErrorCode(1001001005, "CRON 表达式不正确");

    // ========== API 错误日志 1001002000 ==========
    public static final ErrorCode API_ERROR_LOG_NOT_FOUND = new ErrorCode(1001002000, "API 错误日志不存在");
    public static final ErrorCode API_ERROR_LOG_PROCESSED = new ErrorCode(1001002001, "API 错误日志已处理");

    // ========= 文件相关 1001003000=================
    public static final ErrorCode FILE_PATH_EXISTS = new ErrorCode(1001003000, "文件路径已存在");
    public static final ErrorCode FILE_NOT_EXISTS = new ErrorCode(1001003001, "文件不存在");
    public static final ErrorCode FILE_IS_EMPTY = new ErrorCode(1001003002, "文件为空");

    // ========== 代码生成器 1001004000 ==========
    public static final ErrorCode CODEGEN_TABLE_EXISTS = new ErrorCode(1003001000, "表定义已经存在");
    public static final ErrorCode CODEGEN_IMPORT_TABLE_NULL = new ErrorCode(1003001001, "导入的表不存在");
    public static final ErrorCode CODEGEN_IMPORT_COLUMNS_NULL = new ErrorCode(1003001002, "导入的字段不存在");
    public static final ErrorCode CODEGEN_TABLE_NOT_EXISTS = new ErrorCode(1003001004, "表定义不存在");
    public static final ErrorCode CODEGEN_COLUMN_NOT_EXISTS = new ErrorCode(1003001005, "字段义不存在");
    public static final ErrorCode CODEGEN_SYNC_COLUMNS_NULL = new ErrorCode(1003001006, "同步的字段不存在");
    public static final ErrorCode CODEGEN_SYNC_NONE_CHANGE = new ErrorCode(1003001007, "同步失败，不存在改变");
    public static final ErrorCode CODEGEN_TABLE_INFO_TABLE_COMMENT_IS_NULL = new ErrorCode(1003001008, "数据库的表注释未填写");
    public static final ErrorCode CODEGEN_TABLE_INFO_COLUMN_COMMENT_IS_NULL = new ErrorCode(1003001009, "数据库的表字段({})注释未填写");

    // ========== 字典类型（测试）1001005000 ==========
    public static final ErrorCode TEST_DEMO_NOT_EXISTS = new ErrorCode(1001005000, "测试示例不存在");

    // ========== 文件配置 1001006000 ==========
    public static final ErrorCode FILE_CONFIG_NOT_EXISTS = new ErrorCode(1001006000, "文件配置不存在");
    public static final ErrorCode FILE_CONFIG_DELETE_FAIL_MASTER = new ErrorCode(1001006001, "该文件配置不允许删除，原因：它是主配置，删除会导致无法上传文件");

    // ========== 数据源配置 1001007000 ==========
    public static final ErrorCode DATA_SOURCE_CONFIG_NOT_EXISTS = new ErrorCode(1001007000, "数据源配置不存在");
    public static final ErrorCode DATA_SOURCE_CONFIG_NOT_OK = new ErrorCode(1001007001, "数据源配置不正确，无法进行连接");

    // ========== ProductService标准接口-商品配置接口 101001 ==========

    public static final ErrorCode SERIES_CREATE_FAIL = new ErrorCode(105001, "车型年款配置创建失败");
    public static final ErrorCode SERIES_VALUE_EXIST_ERROR = new ErrorCode(105002, "该车型已存在，请重新输入");
    public static final ErrorCode SERIES_UPDATE_FAIL = new ErrorCode(105003, "车型年款配置修改失败");
    public static final ErrorCode SERIES_UPDATE_REVISION_ERROR = new ErrorCode(105004, "车型年款配置已被其他人修改");
    public static final ErrorCode SERIES_DELETE_FAIL = new ErrorCode(105006, "该车型年款配置刪除失败");
    public static final ErrorCode SERIES_MODEL_YEAR_EXISTS_ERROR = new ErrorCode(105007, "车型年款有重复");

    // ========== ProductService标准接口-服务包配置接口 105020 ==========
    public static final ErrorCode PACKAGE_CREATE_FAIL = new ErrorCode(105020, "服务包配置创建失败");
    public static final ErrorCode PACKAGE_CODE_ALREADY_EXIST = new ErrorCode(105021, "该服务包编号已存在，请重新输入");
    public static final ErrorCode PACKAGE_DELETE_FAIL = new ErrorCode(105022, "服务包配置删除失败");
    public static final ErrorCode PACKAGE_NOT_FOUND = new ErrorCode(105023, "服务包配置不存在");

    public static final ErrorCode FILE_SIZE_EXCEED_LIMIT = new ErrorCode(105030, "此文件大小超过2MB");
    public static final ErrorCode PACKAGE_BATCH_UPLOAD_FAIL = new ErrorCode(105031, "批量上传服务包编号失败");
    public static final ErrorCode SERVER_ERROR= new ErrorCode(105032, "无法获取模板文件的URL");

    // ======= notification服务的错误码 303001 ======================
    public static final ErrorCode TEMPLATE_NO_EXIST = new ErrorCode(303001, "模板不存在或模板已经删除");
    public static final ErrorCode TEMPLATE_VAR_ERROR = new ErrorCode(303002, "模板变量有误");
    public static final ErrorCode TASK_NO_EXIST = new ErrorCode(303003, "发送任务不存在或被删除");
    public static final ErrorCode TASK_NOT_TIMED = new ErrorCode(303004, "当前任务不是定时任务，不能编辑发每日定时发送时间");
    public static final ErrorCode TEMPLATE_REQ_NULL = new ErrorCode(303005, "模板编辑整个请求为空");
    public static final ErrorCode TEMPLATE_CODE_NULL = new ErrorCode(303006, "模板code不能为空");
    public static final ErrorCode TEMPLATE_CONTENT_NULL = new ErrorCode(303007, "模板内容不能为空");
    public static final ErrorCode TEMPLATE_REMARKS_NULL = new ErrorCode(303008, "模板场景说明不能为空");
    public static final ErrorCode TEMPLATE_UPDATE_BY_NULL = new ErrorCode(303009, "模板修改人不能为空");
    public static final ErrorCode TEMPLATE_CONTENT_OUT_LIMIT = new ErrorCode(303010, "模板内容不能超过900个字符");
    public static final ErrorCode TEMPLATE_REMARK_OUT_LIMIT = new ErrorCode(303011, "模板场景说明不能超过900个字符");
    public static final ErrorCode TASK_REQ_NULL = new ErrorCode(303012, "任务入参整个请求为空");
    public static final ErrorCode TASK_CODE_NULL = new ErrorCode(303013, "任务code不能为空");
    public static final ErrorCode TASK_MODIFY_USER_NULL = new ErrorCode(303014, "任务修改人不能为空");
    public static final ErrorCode TASK_STATUS_NULL = new ErrorCode(303015, "任务状态不能为空");
    public static final ErrorCode TASK_STATUS_ERROR = new ErrorCode(303016, "任务状态的值错误");
    public static final ErrorCode TASK_SEND_TIME_NULL = new ErrorCode(303017, "任务的发送时间为空");
    public static final ErrorCode TASK_SEND_TIME_FORMAT_ERROR = new ErrorCode(303018, "任务发送时间格式错误");
    public static final ErrorCode TASK_STATUS_NOT_STOP = new ErrorCode(303019, "保存失败，任务已启用");
    public static final ErrorCode TASK_SEND_TIME_ERROR = new ErrorCode(303020, "无法选择00:00，最小选择00:01");
    public static final ErrorCode TASK_MODIFY_NOT_ALLOW = new ErrorCode(303021, "已启用或已发送的任务禁止更新");
    public static final ErrorCode TASK_SELECTED_ERROR = new ErrorCode(303022, "选择的手动通知模板无效或不存在");
    public static final ErrorCode TASK_CANNOT_SUBMIT = new ErrorCode(303023, "请上传校验通过的手机号excel再操作");
    public static final ErrorCode TASK_CANNOT_OPEN = new ErrorCode(303023, "接收人手机号缺失，启用前请上传");

    public static final ErrorCode TEMPLATE_DELETE_ERROR = new ErrorCode(303024, "该通知模板正在使用中，不能删除");

    public static final ErrorCode MANUAL_TEMPLATE_ARGS_NULL = new ErrorCode(303025, "必填项未填写，请检查必填项是否全部填写。");

    public static final ErrorCode UPLOAD_EXCEL_ERROR = new ErrorCode(303026, "上传excel文件失败");

    public static final ErrorCode UPLOAD_EXCEL_TYPE_ERROR = new ErrorCode(303027, "上传excel文件类型不存在");
    public static final ErrorCode TASK_EXPIRED = new ErrorCode(303028, "启用失败，任务已超时，请进入编辑页面重新设置通知发送时间。");
    public static final ErrorCode TASK_EXPIRED_REMAIN_THREE_MINUTES = new ErrorCode(303029, "启用失败，手动定时任务需要在通知发送时间前3分钟进行启用。");

    public static final ErrorCode TEMPLATE_NAME_REPEATABLE = new ErrorCode(303030, "该通知模板已经存在，请重新输入");
    public static final ErrorCode MANUAL_TASK_STATUS_NOT_STOP = new ErrorCode(303031, "删除失败，任务已启用");
    public static final ErrorCode MANUAL_TASK_NOT_EXIST = new ErrorCode(303032, "任务不存在");

    public static final ErrorCode MANUAL_TASK_SEND_TIME_ERROR = new ErrorCode(303033, "通知发送时间需大于当前时间");

    public static final ErrorCode MANUAL_TASK_SIGN_EMPTY = new ErrorCode(303034, "签名不能为空");

    public static final ErrorCode MANUAL_TASK_CHANNEL_EMPTY = new ErrorCode(303035, "发送通道类型不能为空");

    public static final ErrorCode NOTIFICATION_LOG_ALL_EMPTY = new ErrorCode(303036, "请填写推送时间或通知任务批次编号");

    public static final ErrorCode NOTIFICATION_LOG_TIME_EMPTY = new ErrorCode(303037, "请选择推送时间");

    public static final ErrorCode TASK_STATUS_FINISH_SEND = new ErrorCode(303038, "任务已发送,无法修改状态");

    public static final ErrorCode UPLOAD_FILE_FORMAT_ERROR = new ErrorCode(303039, "上传文件填写格式错误");

    public static final ErrorCode UPLOAD_FILE_CONTENT_EMPTY = new ErrorCode(303040, "上传文件内容为空");

    public static final ErrorCode AUTO_TASK_EXPIRE = new ErrorCode(303041, "当前时间不在启用时间段内，请重新编辑该任务的限时发送时间");

    // ======= notification短信服务的错误码 304001 ======================
    public static final ErrorCode SMS_SHORT_LINK_FETCH_FAIL = new ErrorCode(304001, "生成短链失败");
}
