package com.jlr.ecp.notification.dal.dataobject.shortlink;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_short_link_click_total")
public class ShortLinkClickTotalDO extends BaseDO {

    // 自增主键
    @TableId(value = "id")
    private Long id;

    @TableField(value = "url_code")
    private String urlCode;

    @TableField(value = "click_total")
    private Long clickTotal;

    @TableField(value = "tenant_id")
    private Integer tenantId;
}
