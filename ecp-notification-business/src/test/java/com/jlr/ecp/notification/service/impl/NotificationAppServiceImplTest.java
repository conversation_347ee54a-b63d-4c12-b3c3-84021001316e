package com.jlr.ecp.notification.service.impl;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.dal.repository.NotificationHistoryRepository;
import com.jlr.ecp.notification.dal.repository.SendHistoryDetailRepository;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class NotificationAppServiceImplTest {

    @Mock
    private SendHistoryDetailRepository mockDetailRepository;
    @Mock
    private NotificationHistoryRepository mockHistoryRepository;

    @InjectMocks
    private NotificationAppServiceImpl notificationAppServiceImplUnderTest;

    @Test
    public void testUpdateSmsShortLink() {
        // Setup
        final ShortLinkDTO shortLinkDTO = ShortLinkDTO.builder()
                .instanceCode("instanceCode")
                .phoneNumber("phoneNumber")
                .build();
        final CommonResult<Boolean> expectedResult = CommonResult.success(false);
        when(mockDetailRepository.updateShortLinkStatus(shortLinkDTO)).thenReturn(false);
        when(mockDetailRepository.queryOpenCountByInstanceCode("instanceCode")).thenReturn(0L);

        // Run the test
        final CommonResult<Boolean> result = notificationAppServiceImplUnderTest.updateSmsShortLink(shortLinkDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockHistoryRepository).updateHistoryOpenShortLinkCount("instanceCode", 0);
    }
}
