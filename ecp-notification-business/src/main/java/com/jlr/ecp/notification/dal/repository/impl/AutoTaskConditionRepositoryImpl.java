package com.jlr.ecp.notification.dal.repository.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.notification.dal.dataobject.task.auto.AutoTaskConditionDO;
import com.jlr.ecp.notification.dal.mysql.task.auto.AutoTaskConditionDOMapper;
import com.jlr.ecp.notification.dal.repository.AutoTaskConditionRepository;
import io.swagger.v3.oas.models.security.SecurityScheme;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AutoTaskConditionRepositoryImpl extends ServiceImpl<AutoTaskConditionDOMapper, AutoTaskConditionDO>
        implements AutoTaskConditionRepository {
    @Resource
    private AutoTaskConditionDOMapper conditionMapper;

    /**
     * 根据条件类型查询任务条件
     *
     * @param type 条件类型，用于筛选特定类型的自动任务条件
     * @return 返回符合条件类型且未被删除的任务条件列表
     */
    @Override
    public List<AutoTaskConditionDO> queryConditionByType(Integer type) {
        LambdaQueryWrapper<AutoTaskConditionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AutoTaskConditionDO::getConditionType, type)
                .eq(AutoTaskConditionDO::getIsDeleted, false);
        return conditionMapper.selectList(queryWrapper);
    }


    /**
     * 根据条件ID列表获取未删除的条件信息, key：conditionType
     *
     * @param conditionIdList 条件ID列表，用于指定需要查询的条件的ID
     * @return 返回一个自动任务条件对象列表，包含未被删除的条件信息
     */
    @Override
    public Map<Integer, AutoTaskConditionDO> getConditionTypeMapByIds(List<String> conditionIdList) {
        log.info("根据条件ID列表获取未删除的条件信息, conditionIdList:{}", conditionIdList);
        List<AutoTaskConditionDO> conditionDOList = getConditionByIdList(conditionIdList);
        log.info("根据条件ID列表获取未删除的条件信息, 获取的数量：{}", conditionDOList.size());
        if (CollUtil.isEmpty(conditionIdList)) {
            return new HashMap<>();
        }
        return conditionDOList.stream()
                .collect(Collectors.toMap(
                        AutoTaskConditionDO::getConditionType,
                        Function.identity(),
                        (v1,v2) -> v1));
    }

    /**
     * 根据条件ID列表获取条件信息列表
     *
     * @param conditionIdList 条件ID列表，用于查询条件信息
     * @return 返回一个AutoTaskConditionDO对象列表，包含符合条件ID列表的条件信息
     */
    @Override
    public List<AutoTaskConditionDO> getConditionByIdList(List<String> conditionIdList) {
        LambdaQueryWrapper<AutoTaskConditionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AutoTaskConditionDO::getConditionId, conditionIdList)
                .eq(AutoTaskConditionDO::getIsDeleted, false);
        return list(queryWrapper);
    }
}
