package com.jlr.ecp.notification.dto.shortlink;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Schema(description = "太阳码生成DTO")
@Data
public class QrCodeCreateDTO {
    @NotBlank(message = "品牌标识不能为空")
    @Schema(description = "品牌表示", requiredMode = Schema.RequiredMode.REQUIRED,example = "LAN:路虎，JAG：捷豹")
    private String brandCode;

    @NotBlank(message = "url类型不能为空")
    @Schema(description = "url类型", requiredMode = Schema.RequiredMode.REQUIRED,example = "PDP:商品详情页，PLP：商品列表页")
    private String urlType;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED,example = "Remote远程车控")
    private String productName;

    @Schema(description = "商品Code", requiredMode = Schema.RequiredMode.REQUIRED,example = "BS0015669A63A3E4B04514BB5186EF")
    private String productCode;

    @Schema(description = "埋点参数1", requiredMode = Schema.RequiredMode.REQUIRED,example = "ICRAPP")
    private String param1;

    @Schema(description = "埋点参数2", requiredMode = Schema.RequiredMode.REQUIRED,example = "ICRAPP")
    private String param2;

    @Schema(description = "埋点参数3", requiredMode = Schema.RequiredMode.REQUIRED,example = "ICRAPP")
    private String param3;

    @Schema(description = "埋点参数4", requiredMode = Schema.RequiredMode.REQUIRED,example = "ICRAPP")
    private String param4;

    @Schema(description = "埋点参数5", requiredMode = Schema.RequiredMode.REQUIRED,example = "ICRAPP")
    private String param5;

}
