package com.jlr.ecp.notification.service.crc;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.dto.crc.ReadCrcMessageDTO;
import com.jlr.ecp.notification.dto.crc.SaveCrcUnreadMsgDTO;
import com.jlr.ecp.notification.vo.crc.SaveCrcUnreadMsgVO;

public interface CrcUnreadMessageService {

    /**
     * 添加CRC未读消息
     *
     * @param saveCrcUnreadMsgDTO 包含未读消息信息的DTO对象
     * @return 返回一个通用结果对象，表示消息是否成功发送
     */
    CommonResult<SaveCrcUnreadMsgVO> addCrcUnreadMessage(SaveCrcUnreadMsgDTO saveCrcUnreadMsgDTO);

    /**
     * 查询crc未读消息的数量
     *
     * @param jilId 用于标识消息接收者的ID
     * @param messageStatus 消息的状态，用于过滤消息（如未读状态）
     * @return 返回一个CommonResult对象，其中包含未读消息的数量
     */
    CommonResult<Integer> findCrcUnreadMessage(String jilId, Integer messageStatus, String businessCode);

    /**
     * 阅读并更新CRC未读消息的状态
     *
     * @param readCrcMsgDTO 包含更新消息状态所需信息的DTO对象
     * @return 返回一个CommonResult对象，包含更新操作的结果信息
     */
    CommonResult<String> readCrcAllMessage(ReadCrcMessageDTO readCrcMsgDTO);
}
