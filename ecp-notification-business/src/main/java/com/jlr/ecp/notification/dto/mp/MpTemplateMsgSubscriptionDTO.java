package com.jlr.ecp.notification.dto.mp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import javax.validation.constraints.NotBlank;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Schema(description = "小程序模板消息订阅DTO")
public class MpTemplateMsgSubscriptionDTO {

    @Schema(description = "用户全局唯一JLRID")
    @NotBlank
    private String jlrId;

    @Schema(description = "小程序openId")
    @NotBlank
    private String openId;

    @Schema(description = "订单状态变化模板订阅, 0:没有订阅，1：订阅")
    @NonNull
    private Integer orderSuccessFlag;

    @Schema(description = "订单取消模板订阅, 0:没有订阅，1：订阅")
    @NonNull
    private Integer orderCancelFlag;

    @Schema(description = "订单号")
    @NotBlank
    private String orderNo;

    @Schema(description = "服务到期提醒模板订阅, 0:没有订阅，1：订阅")
    @NonNull
    private Integer serviceExpirationConsent;
}
