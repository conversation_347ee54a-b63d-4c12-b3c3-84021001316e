package com.jlr.ecp.notification.req.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Schema(description = "发送任务状态")
@Data
public class TaskModifyStatusReq {

    @Schema(description = "任务code")
    @NotBlank(message = "任务code不能为空")
    private String taskCode;

    @Schema(description = "状态：0-禁用、1-启用")
    @NotNull(message = "状态不能为空")
    private Integer modifyStatus;
}
