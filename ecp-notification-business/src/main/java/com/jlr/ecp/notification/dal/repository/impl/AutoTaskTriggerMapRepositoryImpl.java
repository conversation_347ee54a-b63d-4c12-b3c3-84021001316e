package com.jlr.ecp.notification.dal.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.notification.dal.dataobject.task.auto.AutoTaskTriggerMapDO;
import com.jlr.ecp.notification.dal.mysql.task.auto.AutoTaskTriggerMapDOMapper;
import com.jlr.ecp.notification.dal.repository.AutoTaskTriggerMapRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class AutoTaskTriggerMapRepositoryImpl extends ServiceImpl<AutoTaskTriggerMapDOMapper, AutoTaskTriggerMapDO>
        implements AutoTaskTriggerMapRepository {

    /**
     * 批量插入自动任务触发器映射
     *
     * @param taskTriggerMapDOList 一组自动任务触发器映射对象
     * @return 如果批量保存成功，返回true；否则返回false
     */
    @Override
    public boolean batchInsertAutoTaskTrigger(List<AutoTaskTriggerMapDO> taskTriggerMapDOList) {
        return saveBatch(taskTriggerMapDOList);
    }

    /**
     * 根据任务编码获取自动触发器列表
     *
     * @param taskCode 任务编码，用于查询自动触发器映射表中对应的任务
     * @return 返回一个自动任务触发器映射对象（AutoTaskTriggerMapDO）的列表，这些对象与给定任务编码相关联
     */
    @Override
    public List<AutoTaskTriggerMapDO> getAutoTriggerByTaskCode(String taskCode) {
        log.info("根据任务编码获取自动触发器列表, taskCode:{}", taskCode);
        LambdaQueryWrapper<AutoTaskTriggerMapDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AutoTaskTriggerMapDO::getTaskCode, taskCode)
                .eq(AutoTaskTriggerMapDO::getIsDeleted, false);
        return list(queryWrapper);
    }
}
