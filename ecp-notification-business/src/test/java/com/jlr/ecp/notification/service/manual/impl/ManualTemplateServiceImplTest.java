package com.jlr.ecp.notification.service.manual.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.notification.config.RedisService;
import com.jlr.ecp.notification.dal.dataobject.task.NotificationTask;
import com.jlr.ecp.notification.dal.dataobject.template.MessageTemplate;
import com.jlr.ecp.notification.dal.dataobject.template.TemplateModifyLog;
import com.jlr.ecp.notification.dal.mysql.template.TemplateModifyLogMapper;
import com.jlr.ecp.notification.dal.repository.MessageTaskRepository;
import com.jlr.ecp.notification.dal.repository.MessageTemplateRepository;
import com.jlr.ecp.notification.dal.repository.TemplateModifyLogRepository;
import com.jlr.ecp.notification.dto.template.ManualTemplateDTO;
import com.jlr.ecp.notification.enums.ErrorCodeConstants;
import com.jlr.ecp.notification.enums.SortEnum;
import com.jlr.ecp.notification.enums.template.SmsTemplateTypeEnum;
import com.jlr.ecp.notification.enums.template.TemplateModifyTypeEnum;
import com.jlr.ecp.notification.req.template.SmsTemplatePageReq;
import com.jlr.ecp.notification.req.template.TemplateHistoryPageReq;
import com.jlr.ecp.notification.util.sms.TimeFormatUtil;
import com.jlr.ecp.notification.vo.template.ManualTemplateDetailVo;
import com.jlr.ecp.notification.vo.template.SmsTemplateHistoryVo;
import com.jlr.ecp.notification.vo.template.SmsTemplateVo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import reactor.retry.Repeat;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;
import static reactor.retry.Repeat.times;

@RunWith(MockitoJUnitRunner.class)
public class ManualTemplateServiceImplTest {

    @Mock
    private MessageTemplateRepository mockMessageTemplateRepository;
    @Mock
    private TemplateModifyLogMapper mockTemplateModifyLogMapper;
    @Mock
    private MessageTaskRepository mockMessageTaskRepository;

    @InjectMocks
    @Spy
    private ManualTemplateServiceImpl manualTemplateService;

    @Test
    public void testAddSmsManualTemplate() {
        // Setup
        final ManualTemplateDTO manualTemplateDTO = ManualTemplateDTO.builder()
                .businessCode("businessCode")
                .templateCode("templateCode")
                .templateName("templateName")
                .templateType(0)
                .templateContent("templateContent")
                .templateRemarks("templateRemarks")
                .build();
        final CommonResult<Boolean> expectedResult = CommonResult.success(false);

        // Configure MessageTemplateRepository.getMessageTemplateByName(...).
        final List<MessageTemplate> messageTemplates = List.of(MessageTemplate.builder()
                .businessCode("businessCode")
                .templateCode("templateCode")
                .templateName("templateName")
                .templateType(0)
                .templateContent("templateContent")
                .templateRemarks("templateRemarks")
                .tenantId(0)
                .build());

        // Run the test
        final CommonResult<Boolean> result = manualTemplateService.addSmsManualTemplate(manualTemplateDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testAddSmsManualTemplate_MessageTemplateRepositoryGetMessageTemplateByNameReturnsNoItems() {
        // Setup
        final ManualTemplateDTO manualTemplateDTO = ManualTemplateDTO.builder()
                .businessCode("businessCode")
                .templateCode("templateCode")
                .templateName("templateName")
                .templateType(0)
                .templateContent("templateContent")
                .templateRemarks("templateRemarks")
                .build();
        final CommonResult<Boolean> expectedResult = CommonResult.success(false);

        // Run the test
        final CommonResult<Boolean> result = manualTemplateService.addSmsManualTemplate(manualTemplateDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testDeleteSmsManualTemplate() {
        // Setup
        final CommonResult<Boolean> expectedResult = CommonResult.success(false);

        // Configure MessageTaskRepository.queryTasksByTemplateCode(...).
        final List<NotificationTask> notificationTasks = List.of(NotificationTask.builder().id(1L).build());

        when(mockMessageTaskRepository.queryTasksByTemplateCode("templateCode")).thenReturn(null)
                .thenReturn(notificationTasks);

        // Run the test
        final CommonResult<Boolean> result = manualTemplateService.deleteSmsManualTemplate("templateCode");
        final CommonResult<Boolean> result1 = manualTemplateService.deleteSmsManualTemplate("templateCode");
        final CommonResult<Boolean> result2 = manualTemplateService.deleteSmsManualTemplate("");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        assertThat(result1).isNotNull();
        assertThat(result2).isNotNull();
    }


    @Test
    public void testDeleteSmsManualTemplate_MessageTaskRepositoryReturnsNoItems() {
        // Setup
        final CommonResult<Boolean> expectedResult = CommonResult.success(false);
        when(mockMessageTaskRepository.queryTasksByTemplateCode("templateCode")).thenReturn(Collections.emptyList());

        // Run the test
        final CommonResult<Boolean> result = manualTemplateService.deleteSmsManualTemplate("templateCode");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCheckManualTemplateDTO() {
        final CommonResult<Boolean> expect1 = CommonResult.error(ErrorCodeConstants.MANUAL_TEMPLATE_ARGS_NULL);
        final CommonResult<Boolean> result1 = manualTemplateService.checkManualTemplateDTO(null);

        final ManualTemplateDTO dto2 = ManualTemplateDTO.builder()
                .templateCode("templateCode")
                .build();
        final CommonResult<Boolean> expect2 = CommonResult.error(ErrorCodeConstants.MANUAL_TEMPLATE_ARGS_NULL);
        final CommonResult<Boolean> result2 = manualTemplateService.checkManualTemplateDTO(dto2);

        final ManualTemplateDTO dto3 = ManualTemplateDTO.builder()
                .businessCode("businessCode")
                .build();
        final CommonResult<Boolean> expect3 = CommonResult.error(ErrorCodeConstants.MANUAL_TEMPLATE_ARGS_NULL);
        final CommonResult<Boolean> result3 = manualTemplateService.checkManualTemplateDTO(dto3);

        final ManualTemplateDTO dto4 = ManualTemplateDTO.builder()
                .businessCode("businessCode")
                .templateName("templateName")
                .build();
        final CommonResult<Boolean> expect4 = CommonResult.error(ErrorCodeConstants.MANUAL_TEMPLATE_ARGS_NULL);
        final CommonResult<Boolean> result4 = manualTemplateService.checkManualTemplateDTO(dto4);

        final ManualTemplateDTO dto5 = ManualTemplateDTO.builder()
                .businessCode("businessCode")
                .templateName("templateName")
                .templateType(1)
                .build();
        final CommonResult<Boolean> expect5 = CommonResult.error(ErrorCodeConstants.MANUAL_TEMPLATE_ARGS_NULL);
        final CommonResult<Boolean> result5 = manualTemplateService.checkManualTemplateDTO(dto5);

        final ManualTemplateDTO dto6 = ManualTemplateDTO.builder()
                .businessCode("businessCode")
                .templateName("templateName")
                .templateType(1)
                .templateContent("templateContent")
                .build();
        final CommonResult<Boolean> expect6 = CommonResult.error(ErrorCodeConstants.MANUAL_TEMPLATE_ARGS_NULL);
        final CommonResult<Boolean> result6 = manualTemplateService.checkManualTemplateDTO(dto6);

        final ManualTemplateDTO dto7 = ManualTemplateDTO.builder()
                .businessCode("businessCode")
                .templateName("templateName")
                .templateType(1)
                .templateContent("到南京时，有朋友约去游逛，勾留了一日；第二日上午便须渡江到浦口，下午上车北去。父亲因为事忙，本已说定不送我，叫旅馆里一个熟识的茶房陪我同去。他再三嘱咐茶房，甚是仔细。但他终于不放心，怕茶房不妥帖；颇踌躇了一会。其实我那年已二十岁，北京已来往过两三次，是没有甚么要紧的了。他踌躇了一会，终于决定还是自己送我去。我两三回劝他不必去；他只说，“不要紧，他们去不好！”\n" +
                        "\n" +
                        "我们过了江，进了车站。我买票，他忙着照看行李。行李太多了，得向脚夫行些小费，才可过去。他便又忙着和他们讲价钱。我那时真是聪明过分，总觉他说话不大漂亮，非自己插嘴不可，但他终于讲定了价钱；就送我上车。他给我拣定了靠车门的一张椅子；我将他给我做的紫毛大衣铺好坐位。他嘱我路上小心，夜里警醒些，不要受凉。又嘱托茶房好好照应我。我心里暗笑他的迂；他们只认得钱，托他们直是白托！而且我这样大年纪的人，难道还不能料理自己么？唉，我现在想想，那时真是太聪明了！\n" +
                        "\n" +
                        "我说道，“爸爸，你走吧。”他望车外看了看，说，“我买几个橘子去。你就在此地，不要走动。”我看那边月台的栅栏外有几个卖东西的等着顾客。走到那边月台，须穿过铁道，须跳下去又爬上去。父亲是一个胖子，走过去自然要费事些。我本来要去的，他不肯，只好让他去。我看见他戴着黑布小帽，穿着黑布大马褂，深青布棉袍，蹒跚地走到铁道边，慢慢探身下去，尚不大难。可是他穿过铁道，要爬上那边月台，就不容易了。他用两手攀着上面，两脚再向上缩；他肥胖的身子向左微倾，显出努力的样子。这时我看见他的背影，我的泪很快地流下来了。我赶紧拭干了泪，怕他看见，也怕别人看见。我再向外看时，他已抱了朱红的橘子往回走了。过铁道时，他先将橘子散放在地上，自己慢慢爬下，再抱起橘子走。到这边时，我赶紧去搀他。他和我走到车上，将橘子一股脑儿放在我的皮大衣上。于是扑扑衣上的泥土，心里很轻松似的，过一会说，“我走了；到那边来信！”我望着他走出去。他走了几步，回过头看见我，说，“进去吧，里边没人。”等他的背影混入来来往往的人里，再找不着了，我便进来坐下，我的眼泪又来了。\n" +
                        "\n" +
                        "近几年来，父亲和我都是东奔西走，家中光景是一日不如一日。他少年出外谋生，独力支持，做了许多大事。那知老境却如此颓唐！他触目伤怀，自然情不能自已。情郁于中，自然要发之于外；家庭琐屑便往往触他之怒。他待我渐渐不同往日。但最近两年的不见，他终于忘却我的不好，只是惦记着我，惦记着我的儿子。我北来后，他写了一信给我，信中说道，“我身体平安，惟膀子疼痛厉害，举箸提笔，诸多不便，大约大去之期不远矣。”我读到此处，在晶莹的泪光中，又看见那肥胖的、青布棉袍黑布马褂的背影。唉！我不知何时再能与他相见！”")
                .templateRemarks("templateRemarks")
                .build();
        final CommonResult<Boolean> expect7 = CommonResult.error(ErrorCodeConstants.TEMPLATE_CONTENT_OUT_LIMIT);
        final CommonResult<Boolean> result7 = manualTemplateService.checkManualTemplateDTO(dto7);

        final ManualTemplateDTO dto8 = ManualTemplateDTO.builder()
                .businessCode("businessCode")
                .templateName("templateName")
                .templateType(1)
                .templateContent("templateContent")
                .templateRemarks("鲁镇的酒店的格局，是和别处不同的：都是当街一个曲尺形的大柜台，柜里面预备着热水，可以随时温酒。做工的人，傍午傍晚散了工，每每花四文铜钱，买一碗酒，——这是二十多年前的事，现在每碗要涨到十文，——靠柜外站着，热热的喝了休息；倘肯多花一文，便可以买一碟盐煮笋，或者茴香豆，做下酒物了，如果出到十几文，那就能买一样荤菜，但这些顾客，多是短衣帮，大抵没有这样阔绰。只有穿长衫的，才踱进店面隔壁的房子里，要酒要菜，慢慢地坐喝。\n" +
                        "\n" +
                        "孔乙己是站着喝酒而穿长衫的唯一的人。他身材很高大；青白脸色，皱纹间时常夹些伤痕；一部乱蓬蓬的花白的胡子。穿的虽然是长衫，可是又脏又破，似乎十多年没有补，也没有洗。他对人说话，总是满口之乎者也，教人半懂不懂的。因为他姓孔，别人便从描红纸上的“上大人孔乙己”这半懂不懂的话里，替他取下一个绰号，叫作孔乙己。孔乙己一到店，所有喝酒的人便都看着他笑，有的叫道，“孔乙己，你脸上又添上新伤疤了！”他不回答，对柜里说，“温两碗酒，要一碟茴香豆。”便排出九文大钱。他们又故意的高声嚷道，“你一定又偷了人家的东西了！”孔乙己睁大眼睛说，“你怎么这样凭空污人清白……”“什么清白？我前天亲眼见你偷了何家的书，吊着打。”孔乙己便涨红了脸，额上的青筋条条绽出，争辩道，“窃书不能算偷……窃书！……读书人的事，能算偷么？”接连便是难懂的话，什么“君子固穷”，什么“者乎”之类，引得众人都哄笑起来：店内外充满了快活的空气。\n" +
                        "\n" +
                        "听人家背地里谈论，孔乙己原来也读过书，但终于没有进学，又不会营生；于是愈过愈穷，弄到将要讨饭了。幸而写得一笔好字，便替人家钞钞书，换一碗饭吃。可惜他又有一样坏脾气，便是好喝懒做。坐不到几天，便连人和书籍纸张笔砚，一齐失踪。如是几次，叫他钞书的人也没有了。孔乙己没有法，便免不了偶然做些偷窃的事。但他在我们店里，品行却比别人都好，就是从不拖欠；虽然间或没有现钱，暂时记在粉板上，但不出一月，定然还清，从粉板上拭去了孔乙己的名字。\n" +
                        "\n" +
                        "孔乙己喝过半碗酒，涨红的脸色渐渐复了原，旁人便又问道，“孔乙己，你当真认识字么？”孔乙己看着问他的人，显出不屑置辩的神气。他们便接着说道，“你怎的连半个秀才也捞不到呢？”孔乙己立刻显出颓唐不安模样，脸上笼上了一层灰色，嘴里说些话；这回可是全是之乎者也之类，一些不懂了。在这时候，众人也都哄笑起来：店内外充满了快活的空气。")
                .build();
        final CommonResult<Boolean> expect8 = CommonResult.error(ErrorCodeConstants.TEMPLATE_REMARK_OUT_LIMIT);
        final CommonResult<Boolean> result8 = manualTemplateService.checkManualTemplateDTO(dto8);

        // Verify the results
        assertThat(result1).isEqualTo(expect1);
        assertThat(result2).isEqualTo(expect2);
        assertThat(result3).isEqualTo(expect3);
        assertThat(result4).isEqualTo(expect4);
        assertThat(result5).isEqualTo(expect5);
        assertThat(result6).isEqualTo(expect6);
        assertThat(result7).isEqualTo(expect7);
        assertThat(result8).isEqualTo(expect8);
    }


    @Test
    public void testGetTemplateModifyContent() {
        assertThat(manualTemplateService.getTemplateModifyContent(1)).isEqualTo("编辑通知模板");
        assertThat(manualTemplateService.getTemplateModifyContent(0)).isEqualTo("");
    }



    @Mock
    private MessageTemplateRepository templateRepository;

    @Mock
    private RedisService redisService;

    /**
     * 测试正常情况下的分页查询
     */
    @Test
    public void testGetManualTemplateList_NormalCase() {
        // 准备测试数据
        SmsTemplatePageReq pageReq = new SmsTemplatePageReq();
        pageReq.setPageNo(1);
        pageReq.setPageSize(10);

        Page<MessageTemplate> mockPage = new Page<>();
        List<MessageTemplate> mockTemplates = new ArrayList<>();
        mockTemplates.add(MessageTemplate.builder()
                .templateCode("code1")
                .businessCode("business1")
                .templateName("name1")
                .templateType(1)
                .templateContent("content1")
                .templateVariables("vars1")
                .build());
        mockPage.setRecords(mockTemplates);
        mockPage.setTotal(1);

        when(templateRepository.selectAutoTemplatePage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        try (var mockedTimeFormatUtil = mockStatic(TimeFormatUtil.class)) {
            mockedTimeFormatUtil.when(() -> TimeFormatUtil.localDateToString(any()))
                    .thenReturn("2023-01-01 12:00:00");

            when(redisService.getCacheMapValue(anyString(), anyString()))
                    .thenReturn("Business Name");

            // 执行测试
            PageResult<SmsTemplateVo> result = manualTemplateService.getManualTemplateList(pageReq);

            // 验证结果
            assertThat(result).isNotNull();
            assertThat(result.getList()).hasSize(1);
            assertThat(result.getTotal()).isEqualTo(1);
            SmsTemplateVo vo = result.getList().get(0);
            assertThat(vo.getTemplateCode()).isEqualTo("code1");
            assertThat(vo.getBusinessName()).isEqualTo("Business Name");
            assertThat(vo.getModifyTime()).isEqualTo("2023-01-01 12:00:00");
        }
    }

    /**
     * 测试查询结果为空的情况
     */
    @Test
    public void testGetManualTemplateList_EmptyResult() {
        // 准备测试数据
        SmsTemplatePageReq pageReq = new SmsTemplatePageReq();
        pageReq.setPageNo(1);
        pageReq.setPageSize(10);

        Page<MessageTemplate> mockPage = new Page<>();
        mockPage.setRecords(Collections.emptyList());
        mockPage.setTotal(0);

        when(templateRepository.selectAutoTemplatePage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        // 执行测试
        PageResult<SmsTemplateVo> result = manualTemplateService.getManualTemplateList(pageReq);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getList()).isEmpty();
        assertThat(result.getTotal()).isEqualTo(0);
    }

    /**
     * 测试 Redis 服务异常的情况
     */
    @Test
    public void testGetManualTemplateList_RedisException() {
        // 准备测试数据
        SmsTemplatePageReq pageReq = new SmsTemplatePageReq();
        pageReq.setPageNo(1);
        pageReq.setPageSize(10);

        Page<MessageTemplate> mockPage = new Page<>();
        List<MessageTemplate> mockTemplates = new ArrayList<>();
        mockTemplates.add(MessageTemplate.builder()
                .templateCode("code1")
                .businessCode("business1")
                .templateName("name1")
                .templateType(1)
                .templateContent("content1")
                .templateVariables("vars1")
                .build());
        mockPage.setRecords(mockTemplates);
        mockPage.setTotal(1);

        when(templateRepository.selectAutoTemplatePage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(mockPage);

        when(redisService.getCacheMapValue(anyString(), anyString()))
                .thenThrow(new RuntimeException("Redis Exception"));

        try (var mockedTimeFormatUtil = mockStatic(TimeFormatUtil.class)) {
            mockedTimeFormatUtil.when(() -> TimeFormatUtil.localDateToString(any()))
                    .thenReturn("2023-01-01 12:00:00");

            // 执行测试
            PageResult<SmsTemplateVo> result = manualTemplateService.getManualTemplateList(pageReq);

            // 验证结果
            assertThat(result).isNotNull();
            assertThat(result.getList()).hasSize(1);
            assertThat(result.getTotal()).isEqualTo(1);
            SmsTemplateVo vo = result.getList().get(0);
            assertThat(vo.getBusinessName()).isEqualTo("business1"); // Redis 异常时使用 businessCode
        }
    }

    @Mock
    private TemplateModifyLogRepository templateModifyLogRepository;


    /**
     * 测试用例 2：无历史记录
     */
    @Test
    public void testGetManualTemplateHistory_NoHistory() {
        // 准备输入参数
        TemplateHistoryPageReq historyPageReq = new TemplateHistoryPageReq();
        historyPageReq.setTemplateCode("testTemplateCode");

        // Mock 数据
        when(templateModifyLogRepository.queryTemplateModifyLogPage(historyPageReq)).thenReturn(null);

        // 执行测试
        PageResult<SmsTemplateHistoryVo> result = manualTemplateService.getManualTemplateHistory(historyPageReq);

        // 验证结果
        assertThat(result).isNull();
    }


    /**
     * 测试用例 3：模板内容为空
     */
//    @Test
//    public void testGetManualTemplateHistory_TemplateContentEmpty() {
//        // 准备输入参数
//        TemplateHistoryPageReq historyPageReq = new TemplateHistoryPageReq();
//        historyPageReq.setTemplateCode("testTemplateCode");
//
//        // Mock 数据
//        Page<TemplateModifyLog> mockPage = new Page<>();
//        List<TemplateModifyLog> mockLogs = new ArrayList<>();
//        TemplateModifyLog log = new TemplateModifyLog();
//        log.setTemplateCode("testTemplateCode");
//        log.setModifyTime(LocalDateTime.now());
//        log.setModifyUser("testUser");
//        mockLogs.add(log);
//        mockPage.setRecords(mockLogs);
//        mockPage.setTotal(1L);
//
//        when(templateModifyLogRepository.queryTemplateModifyLogPage(historyPageReq)).thenReturn(mockPage);
//        when(templateRepository.getMessageTemplateByCode(anyString())).thenReturn(null);
//
//        // 执行测试
//        PageResult<SmsTemplateHistoryVo> result = manualTemplateService.getManualTemplateHistory(historyPageReq);
//
//        // 验证结果
//        assertThat(result).isNotNull();
//        assertThat(result.getList()).hasSize(1);
//        assertThat(result.getTotal()).isEqualTo(1L);
//        SmsTemplateHistoryVo vo = result.getList().get(0);
//        assertThat(vo.getModifyContent()).isEmpty();
//
//    }



    @Test
    public void updateSmsManualTemplate_ValidationFails_ReturnsError() {
        ManualTemplateDTO manualTemplateDTO = new ManualTemplateDTO();
        CommonResult<Boolean> result = manualTemplateService.updateSmsManualTemplate(manualTemplateDTO);
        assertFalse(result.isSuccess());
        assertEquals(ErrorCodeConstants.MANUAL_TEMPLATE_ARGS_NULL.getCode(), result.getCode());
    }

    @Test
    public void updateSmsManualTemplate_TemplateCodeBlank_ReturnsError() {
        ManualTemplateDTO manualTemplateDTO = new ManualTemplateDTO();
        manualTemplateDTO.setBusinessCode("businessCode");
        manualTemplateDTO.setTemplateName("templateName");
        manualTemplateDTO.setTemplateType(SmsTemplateTypeEnum.MANUAL.getCode());
        manualTemplateDTO.setTemplateContent("templateContent");
        manualTemplateDTO.setTemplateRemarks("templateRemarks");

        CommonResult<Boolean> result = manualTemplateService.updateSmsManualTemplate(manualTemplateDTO);
        assertFalse(result.isSuccess());
        assertEquals(ErrorCodeConstants.TEMPLATE_CODE_NULL.getCode(), result.getCode());
    }

    @Test
    public void updateSmsManualTemplate_TemplateNotFound_ReturnsError() {
        ManualTemplateDTO manualTemplateDTO = new ManualTemplateDTO();
        manualTemplateDTO.setTemplateCode("templateCode");
        manualTemplateDTO.setBusinessCode("businessCode");
        manualTemplateDTO.setTemplateName("templateName");
        manualTemplateDTO.setTemplateType(SmsTemplateTypeEnum.MANUAL.getCode());
        manualTemplateDTO.setTemplateContent("templateContent");
        manualTemplateDTO.setTemplateRemarks("templateRemarks");

        when(templateRepository.getMessageTemplateByCode("templateCode")).thenReturn(null);

        CommonResult<Boolean> result = manualTemplateService.updateSmsManualTemplate(manualTemplateDTO);
        assertFalse(result.isSuccess());
        assertEquals(ErrorCodeConstants.TEMPLATE_NO_EXIST.getCode(), result.getCode());
    }

    @Test
    public void updateSmsManualTemplate_DuplicateTemplateName_ReturnsError() {
        ManualTemplateDTO manualTemplateDTO = new ManualTemplateDTO();
        manualTemplateDTO.setTemplateCode("templateCode");
        manualTemplateDTO.setBusinessCode("businessCode");
        manualTemplateDTO.setTemplateName("templateName");
        manualTemplateDTO.setTemplateType(SmsTemplateTypeEnum.MANUAL.getCode());
        manualTemplateDTO.setTemplateContent("templateContent");
        manualTemplateDTO.setTemplateRemarks("templateRemarks");

        MessageTemplate existingTemplate = new MessageTemplate();
        existingTemplate.setTemplateCode("existingTemplateCode");
        when(templateRepository.getMessageTemplateByCode("templateCode")).thenReturn(existingTemplate);
        when(templateRepository.getTemplateByTypeName("templateName", "businessCode", SmsTemplateTypeEnum.MANUAL.getCode()))
                .thenReturn(Collections.singletonList(existingTemplate));

        CommonResult<Boolean> result = manualTemplateService.updateSmsManualTemplate(manualTemplateDTO);
        assertFalse(result.isSuccess());
        assertEquals(ErrorCodeConstants.TEMPLATE_NAME_REPEATABLE.getCode(), result.getCode());
    }

    @Test
    public void updateSmsManualTemplate_SuccessfulUpdate_ReturnsSuccess() {
        ManualTemplateDTO manualTemplateDTO = new ManualTemplateDTO();
        manualTemplateDTO.setTemplateCode("templateCode");
        manualTemplateDTO.setBusinessCode("businessCode");
        manualTemplateDTO.setTemplateName("templateName");
        manualTemplateDTO.setTemplateType(SmsTemplateTypeEnum.MANUAL.getCode());
        manualTemplateDTO.setTemplateContent("templateContent");
        manualTemplateDTO.setTemplateRemarks("templateRemarks");

        MessageTemplate existingTemplate = new MessageTemplate();
        existingTemplate.setTemplateCode("templateCode");
        when(templateRepository.getMessageTemplateByCode("templateCode")).thenReturn(existingTemplate);
        when(templateRepository.getTemplateByTypeName("templateName", "businessCode", SmsTemplateTypeEnum.MANUAL.getCode()))
                .thenReturn(Collections.emptyList());
        when(templateRepository.updateMessageManualTemplate(any(MessageTemplate.class))).thenReturn(true);

        CommonResult<Boolean> result = manualTemplateService.updateSmsManualTemplate(manualTemplateDTO);
        assertTrue(result.isSuccess());
        assertTrue(result.getData());
    }

    /**
     * 测试用例 1：templateCode 为空
     */
    @Test
    public void testSmsManualTemplateDetail_TemplateCodeIsNull() {
        // 执行测试
        CommonResult<ManualTemplateDetailVo> result = manualTemplateService.smsManualTemplateDetail("");

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getCode()).isEqualTo(ErrorCodeConstants.TEMPLATE_CODE_NULL.getCode());
        assertThat(result.getData()).isNull();
    }

    /**
     * 测试用例 2：templateCode 不为空，但查询结果为 null
     */
    @Test
    public void testSmsManualTemplateDetail_TemplateNotFound() {
        // Mock 数据
        when(templateRepository.getManualTemplateDetail(anyString())).thenReturn(null);

        // 执行测试
        CommonResult<ManualTemplateDetailVo> result = manualTemplateService.smsManualTemplateDetail("testCode");

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isNull();
    }

    /**
     * 测试用例 3：templateCode 不为空，且查询结果为有效数据
     */
    @Test
    public void testSmsManualTemplateDetail_TemplateFound() {
        // Mock 数据
        MessageTemplate mockTemplate = MessageTemplate.builder()
                .businessCode("businessCode")
                .templateCode("templateCode")
                .templateName("templateName")
                .templateType(2) // 假设为手动配置
                .templateContent("templateContent")
                .templateRemarks("templateRemarks")
                .build();
        when(templateRepository.getManualTemplateDetail(anyString())).thenReturn(mockTemplate);

        // 执行测试
        CommonResult<ManualTemplateDetailVo> result = manualTemplateService.smsManualTemplateDetail("testCode");

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isNotNull();

        ManualTemplateDetailVo detailVo = result.getData();
        assertThat(detailVo.getBusinessCode()).isEqualTo("businessCode");
        assertThat(detailVo.getTemplateCode()).isEqualTo("templateCode");
        assertThat(detailVo.getTemplateName()).isEqualTo("templateName");
        assertThat(detailVo.getTemplateType()).isEqualTo("手动配置");
        assertThat(detailVo.getTemplateContent()).isEqualTo("templateContent");
        assertThat(detailVo.getTemplateRemarks()).isEqualTo("templateRemarks");
    }

    /**
     * 测试用例1：正常情况
     * - businessCode 不为空
     * - templateType 不为空
     * - timeSortType 为升序
     * - templateTypeSort 为降序
     */
    @Test
    public void testQueryTemplatePage_NormalCase() {
        // 准备入参
        SmsTemplatePageReq smsTemplatePageReq = new SmsTemplatePageReq();
        smsTemplatePageReq.setPageNo(1);
        smsTemplatePageReq.setPageSize(10);
        smsTemplatePageReq.setBusinessCode(List.of("code1", "code2"));
        smsTemplatePageReq.setTemplateType(1);
        smsTemplatePageReq.setTimeSortType(SortEnum.ASC.getSortType());
        smsTemplatePageReq.setTemplateTypeSort(SortEnum.DESC.getSortType());

        // 模拟 Repository 返回值
        Page<MessageTemplate> mockPage = new Page<>();
        List<MessageTemplate> records = new ArrayList<>();
        records.add(new MessageTemplate(1L, "code1", "templateCode1", "templateName1", 1, 0, "content1", "vars1", "remarks1", 1));
        mockPage.setRecords(records);
        mockPage.setTotal(1);
        when(templateRepository.selectAutoTemplatePage(any(Page.class), any(LambdaQueryWrapper.class))).thenReturn(mockPage);

        // 执行测试
        Page<MessageTemplate> result = manualTemplateService.queryTemplatePage(smsTemplatePageReq);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getRecords()).hasSize(1);
        assertThat(result.getTotal()).isEqualTo(1);
    }

    /**
     * 测试用例2：边界情况
     * - businessCode 为空
     * - templateType 为空
     * - timeSortType 和 templateTypeSort 均为默认值（降序）
     */
    @Test
    public void testQueryTemplatePage_BoundaryCase() {
        // 准备入参
        SmsTemplatePageReq smsTemplatePageReq = new SmsTemplatePageReq();
        smsTemplatePageReq.setPageNo(1);
        smsTemplatePageReq.setPageSize(10);

        // 模拟 Repository 返回值
        Page<MessageTemplate> mockPage = new Page<>();
        mockPage.setRecords(new ArrayList<>());
        mockPage.setTotal(0);
        when(templateRepository.selectAutoTemplatePage(any(Page.class), any(LambdaQueryWrapper.class))).thenReturn(mockPage);

        // 执行测试
        Page<MessageTemplate> result = manualTemplateService.queryTemplatePage(smsTemplatePageReq);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getRecords()).isEmpty();
        assertThat(result.getTotal()).isEqualTo(0);
    }

    /**
     * 测试用例3：异常情况 - Repository 返回空结果
     */
    @Test
    public void testQueryTemplatePage_EmptyResult() {
        // 准备入参
        SmsTemplatePageReq smsTemplatePageReq = new SmsTemplatePageReq();
        smsTemplatePageReq.setPageNo(1);
        smsTemplatePageReq.setPageSize(10);

        // 模拟 Repository 返回空结果
        when(templateRepository.selectAutoTemplatePage(any(Page.class), any(LambdaQueryWrapper.class))).thenReturn(null);

        // 执行测试
        Page<MessageTemplate> result = manualTemplateService.queryTemplatePage(smsTemplatePageReq);

        // 验证结果
        assertThat(result).isNull();
    }

}
