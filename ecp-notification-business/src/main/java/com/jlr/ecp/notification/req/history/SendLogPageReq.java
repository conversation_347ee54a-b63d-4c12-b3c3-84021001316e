package com.jlr.ecp.notification.req.history;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
 
        

@Schema(description = "发送记录的入参")
@Data
public class SendLogPageReq extends PageParam {

    @Schema(description = "任务类型  1：自动任务 2：手动任务")
    private Integer taskType;

    @Schema(description = "排序: 0-正序 1-倒叙")
    private Integer timeSortType;

    @Schema(description = "任务名称")
    private String taskName;

    @Schema(description = "通知开始时间")
    private String startTime;

    @Schema(description = "通知结束时间")
    private String endTime;

    @Schema(description = "任务批次号列表")
    private List<String> instanceCodeList;
}
