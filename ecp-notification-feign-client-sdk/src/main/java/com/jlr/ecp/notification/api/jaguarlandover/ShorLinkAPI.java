package com.jlr.ecp.notification.api.jaguarlandover;


import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.api.dto.ShortLinkReqDto;
import com.jlr.ecp.notification.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 短链")
public interface ShorLinkAPI {

    String PREFIX = ApiConstants.PREFIX;

    /**
     * 生成路虎小程序的短链
     * @return
     */
    @PostMapping(PREFIX + "/genShortLink")
    @Operation(summary = "生成路虎小程序的短链")
    CommonResult<String> genShortLink(@RequestBody ShortLinkReqDto shortLinkReqDto);

    /**
     * 生成捷豹小程序的短链
     * @return
     */
    @PostMapping(PREFIX + "/genJaguarLink")
    @Operation(summary = "生成捷豹小程序的短链")
    CommonResult<String> genJaguarLink(@RequestBody ShortLinkReqDto shortLinkReqDto);
}
