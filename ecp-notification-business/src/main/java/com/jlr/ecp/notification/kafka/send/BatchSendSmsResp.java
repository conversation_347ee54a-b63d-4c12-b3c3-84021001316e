package com.jlr.ecp.notification.kafka.send;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchSendSmsResp {
    /**
     * 结果的code 0表示成功，其他都失败
     * */
    private String result;

    /**
     * 结果描述
     * */
    private String desc;

    /**
     * 结果数据
     * */
    private List<SendResp> data;
}
