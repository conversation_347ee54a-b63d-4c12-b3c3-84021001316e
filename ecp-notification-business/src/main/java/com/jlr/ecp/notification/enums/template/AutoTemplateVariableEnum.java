package com.jlr.ecp.notification.enums.template;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum AutoTemplateVariableEnum {
    BRAND(1, "品牌+车型", "brandAndModel"),
    SERVICE_NAME(2, "服务名称", "serviceName"),
    EXPIRE_DATE(3, "服务到期时间", "expireDate"),
    WX_URL(4, "微信小程序链接", "wxUrl"),
    BG_ORDER_NUMBER(5, "用户子订单号", "bgorderNumber"),
    LOGISTICS_NUMBER(6, "订单行物流编号", "logisticsNumber");

    private final Integer code;

    private final String desc;

    private final String name;
}
