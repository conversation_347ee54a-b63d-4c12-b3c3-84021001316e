package com.jlr.ecp.notification.service;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.notification.dto.NotificationLogSingleDTO;
import com.jlr.ecp.notification.req.history.SendLogPageReq;
import com.jlr.ecp.notification.vo.history.NotificationLogSingleVO;
import com.jlr.ecp.notification.vo.history.SendLogPageVo;

import java.util.List;

/**
 *  通知日志service
 *
 * <AUTHOR>
 * */
public interface SendHistoryService {

    /**
     * 通知日志分页接口
     * @param sendLogPageReq 通知日志入参
     * @return PageResult<SendLogPageVo>
     * */
    CommonResult<PageResult<SendLogPageVo>> sendHistoryPageList(SendLogPageReq sendLogPageReq);

    /**
     * 根据实例代码查询相关的任务代码。
     *
     * @param instanceCode 实例代码，用于查询特定的任务代码。
     * @return 如果找到相应的任务代码，则返回该任务代码；如果未找到或输入实例代码无效，则返回空字符串。
     */
    String queryByInstanceCode(String instanceCode);

    /**
     * 查询指定任务类型的任务名称列表。
     *
     * @param taskType 任务类型，可为空。如果提供，将过滤并返回该类型的任务名称列表。
     * @return 返回一个任务名称的列表。列表中的每个元素都是字符串类型，代表一个任务的名称。
     */
    List<String> queryTaskNameList(Integer taskType);

    /**
     * 根据单个条件查询短信发送记录列表
     *
     * @param singleDTO 包含查询条件（电话或车辆识别号）的通知日志单个DTO
     * @return 包含通知日志单个视图对象列表的公共结果
     */
    CommonResult<List<NotificationLogSingleVO>> querySmsRecordListBySingle(NotificationLogSingleDTO singleDTO);
}
