package com.jlr.ecp.notification.util.sms;

import org.apache.commons.lang.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * 时间格式工具类
 *
 * <AUTHOR>
 * */
public class TimeFormatUtil {
    public static final DateTimeFormatter formatter_1 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    // 创建格式器
    public static final DateTimeFormatter formatter_2 = DateTimeFormatter.ofPattern("yyyyMMddHHmm");

    public static final DateTimeFormatter formatter_3 = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public static final DateTimeFormatter formatter_4 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    public static final DateTimeFormatter formatter_5 = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");

    public static final DateTimeFormatter formatter_6 = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");

    public static final DateTimeFormatter formatter_7 = DateTimeFormatter.ofPattern("yyyy/MM/dd");


    /**
     *  localDate转化为String
     * @param  dateTime 时间
     * @return String
     * */
    public static String localDateToString(LocalDateTime dateTime) {
        if (Objects.isNull(dateTime)) {
            return "";
        }
        // 使用格式化器将 LocalDateTime 转换为字符串
        return dateTime.format(formatter_1);
    }

    /**
     *  localDate转化为String
     * @param  dateTime 时间
     * @return String
     * */
    public static String localDateToStringWithoutSecond(LocalDateTime dateTime) {
        if (Objects.isNull(dateTime)) {
            return "-";
        }
        // 使用格式化器将 LocalDateTime 转换为字符串
        return dateTime.format(formatter_4);
    }

    /**
     * 把string转化为LocalDateTime
     * @param dateString  时间
     * @return LocalDateTime
     * */
    public static LocalDateTime stringToLocalDate(String dateString) {
        if(StringUtils.isBlank(dateString)) {
            return null;
        }
        return LocalDateTime.parse(dateString, formatter_1);
    }

    /**
     *  转化为代理商需要的时间格式
     * @param  localDateTime 时间
     * @return String
     * */
    public static String changeToSendTime(LocalDateTime localDateTime) {
        if (Objects.isNull(localDateTime)) {
            return "";
        }
        return localDateTime.format(formatter_2);
    }

    /**
     * 转化时间格式
     * @param time 时间
     * @return LocalDateTime 时间
     * */
    public static LocalDateTime changeTimeFormat(LocalDateTime time) {
        String str = localDateToString(time);
        return stringToLocalDate(str);
    }

    /**
     * LocalDateTime按照指定格式转化为String
     * @param time 时间
     * @param formatter 格式
     * @return String
     * */
    public static String timeToStringByFormat(LocalDateTime time, DateTimeFormatter formatter) {
        if (Objects.isNull(time)) {
            return "";
        }
        return time.format(formatter);
    }

    /**
     * LocalDateTime按照指定格式转化为String
     * @param time 时间
     * @param formatter 格式
     * @return String
     * */
    public static LocalDateTime stringToTimeByFormat(String time, DateTimeFormatter formatter) {
        if (Objects.isNull(time)) {
            return null;
        }
        return LocalDateTime.parse(time, formatter);
    }
}
