package com.jlr.ecp.notification.dal.dataobject.mp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("t_mp_notice_order_subscription")
public class MpNoticeOrderSubscriptionDO extends BaseDO {

    /**
     * 自增主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户全局唯一ID
     */
    @TableField("jlr_id")
    private String jlrId;

    /**
     * 小程序的openId
     */
    @TableField("open_id")
    private String openId;

    /**
     * 订单成功模板标识，0:没订阅、1：订阅
     */
    @TableField("order_success_flag")
    private Integer orderSuccessFlag;

    /**
     * 订单取消模板标识，0:没订阅、1：订阅
     */
    @TableField("order_cancel_flag")
    private Integer orderCancelFlag;

    /**
     * 订单编号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 租户号
     */
    @TableField("tenant_id")
    private Integer tenantId;
}
