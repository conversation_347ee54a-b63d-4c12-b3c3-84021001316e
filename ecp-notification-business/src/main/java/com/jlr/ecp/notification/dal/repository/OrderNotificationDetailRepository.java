package com.jlr.ecp.notification.dal.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.notification.dal.dataobject.valet.OrderNotificationDetailDO;

import java.util.List;

public interface OrderNotificationDetailRepository extends IService<OrderNotificationDetailDO> {
    boolean insert(OrderNotificationDetailDO orderNotificationDetailDO);

    /**
     * 根据消息ID列表查询订单通知详情信息
     *
     * @param msgIdList 消息ID列表，用于查询对应的订单通知详情
     * @return 返回与消息ID列表匹配的未删除的订单通知详情列表
     */
    List<OrderNotificationDetailDO> selectByMsgIdList(List<String> msgIdList);

    /**
     * 根据电话号码获取礼宾订单详情
     *
     * @param phone 电话号码，用于查询订单通知详情
     * @return 返回一个列表，包含匹配的订单通知详情对象
     */
    List<OrderNotificationDetailDO> getValetOrderDetailByPhone(String phone);

    /**
     * 根据车辆识别号（VIN）获取代驾订单详情列表
     *
     * @param carVin 车辆识别号（VIN），用于查询订单详情
     * @return 返回一个代驾订单详情列表，如果查询不到或参数无效，则返回空列表
     */
    List<OrderNotificationDetailDO> getValetOrderDetailByVin(String carVin);
}
