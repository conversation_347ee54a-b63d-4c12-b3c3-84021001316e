package com.jlr.ecp.notification.vo.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;


/**
 * 短信通知任务列表页VO
 *
 * <AUTHOR>
 * */

@Data
@Schema(description = "短信通知任务列表页VO")
@Builder
public class SmsTaskPageListVo {

    @Schema(description = "任务编码")
    private String taskCode;

    @Schema(description = "任务名称")
    private String taskName;

    @Schema(description = "触发逻辑")
    private String triggerAction;

    @Schema(description = "通知发送时间: 实时发送、定时发送")
    private String taskSendTimeType;

    @Schema(description = "开始日期")
    private String rangeBeginDate;

    @Schema(description = "结束日期")
    private String rangeEndDate;

    @Schema(description = "通知发送时间")
    private String dailySendTime;

    @Schema(description = "通知模板内容")
    private String messageTemplateContent;

    @Schema(description = "通知状态 0停用 1启用 2待启用 3已发送")
    private Integer status;

    @Schema(description = "启用时间")
    private String activateTime;

    @Schema(description = "停用时间")
    private String deactivateTime;

    @Schema(description = "限定时间发送，过期标识, true已经过期，false未过期")
    private Boolean expirationFlag;
}
