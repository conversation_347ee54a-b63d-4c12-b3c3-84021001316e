package com.jlr.ecp.notification.enums.task;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

@AllArgsConstructor
@Getter
public enum ManualNotificationTaskFieldEnum {
    // 编辑任务概览模块
    EDIT_TASK_NAME(0, "通知任务名称", "编辑任务概览", true),
    EDIT_SEND_TIME(1, "通知发送时间", "编辑任务概览", true),

    // 编辑接收号码模块
    EDIT_RECEIVER_TYPE(2, "接收号码形式", "编辑接收号码", true),
    UPLOAD_FILE(3, "上传文件", "编辑接收号码", false),

    // 编辑模板信息模块
    EDIT_SIGNATURE(4, "通知签名", getTemplateModule(), true),
    EDIT_TEMPLATE_NAME(5, "模板名称", getTemplateModule(), true),
    EDIT_NOTIFICATION_TYPE(6, "通知性质", getTemplateModule(), true),

    // 编辑通知状态模块
    EDIT_STATUS(7, "通知状态", "编辑通知状态", true);

    private final Integer code;
    private final String fieldName;     // 字段名（用于日志、展示）
    private final String moduleName;    // 修改模块
    private final Boolean showDetail;  // 是否显示修改详情

    private static final String TEMPLATE_MODULE = "编辑模板信息";

    // 定义静态方法返回常量
    private static String getTemplateModule() {
        return TEMPLATE_MODULE;
    }

    /**
     * 根据 code 获取对应的枚举实例
     */
    public static ManualNotificationTaskFieldEnum fromCode(Integer code) {
        for (ManualNotificationTaskFieldEnum field : values()) {
            if (field.getCode().equals(code)) {
                return field;
            }
        }
        return null;
    }

    public static Set<String> getAllDetailFields() {
        return Arrays.stream(values())
                .filter(e -> e.showDetail)
                .map(e -> e.fieldName)
                .collect(Collectors.toSet());
    }
}