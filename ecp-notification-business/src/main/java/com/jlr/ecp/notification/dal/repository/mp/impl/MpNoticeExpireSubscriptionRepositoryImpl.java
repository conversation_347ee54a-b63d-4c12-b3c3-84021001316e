package com.jlr.ecp.notification.dal.repository.mp.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.notification.dal.dataobject.mp.MpNoticeExpireSubscriptionDO;
import com.jlr.ecp.notification.dal.mysql.mp.MpNoticeExpireSubscriptionMapper;
import com.jlr.ecp.notification.dal.repository.mp.MpNoticeExpireSubscriptionRepository;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class MpNoticeExpireSubscriptionRepositoryImpl extends
        ServiceImpl<MpNoticeExpireSubscriptionMapper, MpNoticeExpireSubscriptionDO>
        implements MpNoticeExpireSubscriptionRepository {

    @Resource
    private  MpNoticeExpireSubscriptionMapper expireSubscribeMapper;

    /**
     * 根据JLR ID查询到期订阅信息
     *
     * @param jlrId 经销商ID，用于查询特定的到期订阅记录
     * @param openId 微信公众号id
     * @return 返回查询到的到期订阅记录对象，如果没有找到则返回null
     */
    @Override
    public MpNoticeExpireSubscriptionDO queryExpireSubscribeByJlrAndOpenId(String jlrId, String openId) {
        LambdaQueryWrapper<MpNoticeExpireSubscriptionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MpNoticeExpireSubscriptionDO::getJlrId, jlrId)
                .eq(MpNoticeExpireSubscriptionDO::getOpenId, openId)
                .eq(MpNoticeExpireSubscriptionDO::getIsDeleted, false);
        return expireSubscribeMapper.selectOne(queryWrapper);
    }

    /**
     * 选择可以推送已过期的MP订阅信息
     *
     * @return List<MpNoticeExpireSubscriptionDO> 返回已过期的MP消息列表
     */
    @Override
    public List<MpNoticeExpireSubscriptionDO> selectExpiredMpMessageAllId() {
        LambdaQueryWrapper<MpNoticeExpireSubscriptionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(MpNoticeExpireSubscriptionDO::getServiceExpirationConsent, 0)
                .eq(MpNoticeExpireSubscriptionDO::getIsDeleted, 0)
                .select(MpNoticeExpireSubscriptionDO::getId);
        return list(queryWrapper);
    }

    /**
     * 根据ID列表查询已到期的会员通知订阅信息
     *
     * @param idList 会员ID列表，用于查询已到期的订阅信息
     * @return 返回已到期的会员通知订阅信息列表如果传入的ID列表为空，则返回空列表
     */
    @Override
    public List<MpNoticeExpireSubscriptionDO> queryExpiredMpByIdList(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<MpNoticeExpireSubscriptionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MpNoticeExpireSubscriptionDO::getId, idList)
                .eq(MpNoticeExpireSubscriptionDO::getIsDeleted, 0);
        return list(queryWrapper);
    }

    /**
     * 根据监理人ID列表查询已过期的公众号订阅通知
     *
     * @param jlrIdList ID列表，用于查询对应的公众号订阅通知
     * @return 返回符合条件的公众号订阅通知列表，如果输入列表为空，则返回空列表
     */
    @Override
    public List<MpNoticeExpireSubscriptionDO> queryMpExpiredListByJlrId(List<String> jlrIdList) {
        if(CollUtil.isEmpty(jlrIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<MpNoticeExpireSubscriptionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MpNoticeExpireSubscriptionDO::getJlrId, jlrIdList)
                .eq(MpNoticeExpireSubscriptionDO::getIsDeleted, 0);
        return list(queryWrapper);
    }
}
