package com.jlr.ecp.notification.service.manual.impl;

import com.alibaba.fastjson.JSON;
import com.jlr.ecp.framework.forgeRock.core.dto.AccessTokenResponse;
import com.jlr.ecp.framework.forgeRock.core.util.AccessTokenUtil;
import com.jlr.ecp.notification.dal.dataobject.history.NotificationHistory;
import com.jlr.ecp.notification.dal.dataobject.history.SendHistoryDetail;
import com.jlr.ecp.notification.dal.dataobject.task.NotificationTask;
import com.jlr.ecp.notification.dal.dataobject.template.MessageTemplate;
import com.jlr.ecp.notification.dal.repository.MessageTaskRepository;
import com.jlr.ecp.notification.dal.repository.MessageTemplateRepository;
import com.jlr.ecp.notification.dal.repository.NotificationHistoryRepository;
import com.jlr.ecp.notification.dal.repository.SendHistoryDetailRepository;
import com.jlr.ecp.notification.dto.VinAndPhoneDTO;
import com.jlr.ecp.notification.enums.ExcelTemplateTypeEnum;
import com.jlr.ecp.notification.enums.SmsResultErrorEnum;
import com.jlr.ecp.notification.enums.SmsSendResultEnum;
import com.jlr.ecp.notification.enums.task.InstanceCodeTypeEnum;
import com.jlr.ecp.notification.enums.task.SendChannelEnum;
import com.jlr.ecp.notification.enums.task.SignBrandTextEnum;
import com.jlr.ecp.notification.enums.task.TaskStatusEnum;
import com.jlr.ecp.notification.excel.service.NotificationExcelService;
import com.jlr.ecp.notification.kafka.send.BatchSendMessage;
import com.jlr.ecp.notification.kafka.send.BatchSendSmsResp;
import com.jlr.ecp.notification.kafka.send.SendMessage;
import com.jlr.ecp.notification.kafka.send.SendResp;
import com.jlr.ecp.notification.resp.ReportResp;
import com.jlr.ecp.notification.resp.UpdateSmsSendStatusResp;
import com.jlr.ecp.notification.service.UpdateSmsUserReachService;
import com.jlr.ecp.notification.util.InstanceCodeGenerator;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ManualTaskSendServiceImplTest {
    @Mock
    private UpdateSmsUserReachService mockUpdateSmsUserReachService;
    @Mock
    private ThreadPoolTaskScheduler mockTaskScheduler;

    @InjectMocks
    @Spy
    private ManualTaskSendServiceImpl manualTaskSendService;

    private NotificationHistory history;
    private List<SendHistoryDetail> sendHistoryDetails;
    private BatchSendSmsResp batchSendSmsResp;
    private NotificationTask notificationTask;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(manualTaskSendService, "smsBaseUrl", "smsBaseUrl");
        ReflectionTestUtils.setField(manualTaskSendService, "apiKey", "apiKey");
        ReflectionTestUtils.setField(manualTaskSendService, "apiSecret", "apiSecret");
        ReflectionTestUtils.setField(manualTaskSendService, "batchPath", "batchPath");
        ReflectionTestUtils.setField(manualTaskSendService, "smsNotificationAccount", "account");
        ReflectionTestUtils.setField(manualTaskSendService, "smsNotificationPassword", "password");
        ReflectionTestUtils.setField(manualTaskSendService, "smsMarketAccount", "account");
        ReflectionTestUtils.setField(manualTaskSendService, "smsMarketPassword", "password");
        ReflectionTestUtils.setField(manualTaskSendService, "updateSmsResponseFlag", false);

        notificationTask = new NotificationTask();
        notificationTask.setTaskCode("testTaskCode");
        notificationTask.setStatus(1); // 启用状态
        notificationTask.setMessageFile("testFile.xlsx");
        notificationTask.setSubmitFileType(1); // 假设1代表PHONE_EXCEL
        notificationTask.setMessageTemplateCode("testTemplateCode");

        history = new NotificationHistory();
        history.setTaskInstanceCode("instanceCode");
        history.setSendTotalCount(0);
        history.setSendSuccessCount(0);
        history.setSendFailCount(0);

        sendHistoryDetails = new ArrayList<>();
        SendHistoryDetail detail = new SendHistoryDetail();
        detail.setSendPhone("**********");
        sendHistoryDetails.add(detail);

        batchSendSmsResp = new BatchSendSmsResp();
        SendResp sendResp = new SendResp();
        sendResp.setResult(SmsResultErrorEnum.SUCCESS.getCode());
        batchSendSmsResp.setData(Arrays.asList(sendResp));


        notificationTask = new NotificationTask();
        notificationTask.setStatus(TaskStatusEnum.START.getStatus());
    }

    @Test
    public void testBatchInsertDetail() {
        // Setup
        final List<SendMessage> sendMessages = List.of(SendMessage.builder()
                .msgid("msgId")
                .phones("phones")
                .content("content")
                .sign("sign")
                .sendtime("sendtime")
                .build());

        // Run the test
        final List<SendHistoryDetail> result = manualTaskSendService.batchInsertPhoneDetail(List.of("value"),
                "instanceCode", "content", "taskCode", sendMessages, "brandCode");

        final List<SendHistoryDetail> expectedResult = result;
        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testBuildNotificationHistory() {
        // Setup
        //when(mockInstanceCodeGenerator.generateTaskBatchId("type")).thenReturn("instanceCode");

        // Run the test
        final NotificationHistory result = manualTaskSendService.buildNotificationHistory("taskCode");
        final NotificationHistory expectedResult = result;
        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testBuildBatchSendSmsMsg() {
        // Setup
        final NotificationTask notificationTask = NotificationTask.builder()
                .taskCode("taskCode")
                .messageTemplateCode("messageTemplateCode")
                .status(0)
                .messageFile("messageFile")
                .submitFileType(0)
                .signBrandText(0)
                .sendChannel(0)
                .build();
        // Run the test
        final BatchSendMessage result = manualTaskSendService.buildBatchSendSmsMsg(List.of("value"),
                "templateContent", notificationTask);
        final BatchSendMessage expectedResult = result;

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetSendData() {
        // Setup
        final NotificationTask task = NotificationTask.builder()
                .taskCode("taskCode")
                .messageTemplateCode("messageTemplateCode")
                .status(0)
                .messageFile("messageFile")
                .submitFileType(0)
                .signBrandText(0)
                .sendChannel(0)
                .build();

        // Run the test
        final List<SendMessage> result = manualTaskSendService.getSendData(List.of("value"),
                "templateContent", task);

        final List<SendMessage> expectedResult = result;

        // Verify the results
        assertEquals(expectedResult, result);
    }


    @Mock
    private MessageTaskRepository messageTaskRepository;

    @Mock
    private MessageTemplateRepository templateRepository;

    @Mock
    private AccessTokenUtil accessTokenUtil;

    @Mock
    private NotificationHistoryRepository historyRepository;

    @Mock
    private SendHistoryDetailRepository detailRepository;

    @Mock
    private InstanceCodeGenerator instanceCodeGenerator;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private RestTemplate restTemplate;


    @Test
    public void batchSendVinPhoneMsg_ExceptionThrown_LogsError() {
        // 准备
        List<List<VinAndPhoneDTO>> vinPhoneGroupList = new ArrayList<>();
        List<VinAndPhoneDTO> vinPhoneList = Arrays.asList(
                new VinAndPhoneDTO("**********", "VIN123"),
                new VinAndPhoneDTO("0987654321", "VIN456")
        );
        vinPhoneGroupList.add(vinPhoneList);

        MessageTemplate template = new MessageTemplate();
        template.setTemplateContent("Test Message");

        NotificationTask notificationTask = new NotificationTask();
        notificationTask.setTaskCode("TaskCode");
        notificationTask.setSendChannel(SendChannelEnum.NOTIFICATION.getChannelCode());
        notificationTask.setSignBrandText(SignBrandTextEnum.JAGUAR_LAND_ROVER.getSignCode());

        doThrow(new RuntimeException("Test Exception")).when(historyRepository).insertHistory(any(NotificationHistory.class));

        // 执行
        manualTaskSendService.batchSendVinPhoneMsg(vinPhoneGroupList, template, notificationTask);

        // 验证
        verify(historyRepository, times(1)).insertHistory(any(NotificationHistory.class));
        verify(detailRepository, never()).batchInsertDetail(anyList());
        verify(restTemplate, never()).postForEntity(anyString(), any(), eq(BatchSendSmsResp.class));
        verify(manualTaskSendService, never()).updateBatchSendResult(any(NotificationHistory.class), anyList(), any(BatchSendSmsResp.class), any(NotificationTask.class));
    }

    @Test
    public void batchSendPhoneMsg_ExceptionThrown_LogsError() throws Exception {
        // 准备
        List<List<String>> phoneList = new ArrayList<>();
        List<String> phoneNumbers = new ArrayList<>();
        phoneNumbers.add("**********");
        phoneList.add(phoneNumbers);

        MessageTemplate template = new MessageTemplate();
        template.setTemplateContent("Test Message");

        NotificationTask notificationTask = new NotificationTask();
        notificationTask.setTaskCode("TestTask");
        notificationTask.setSendChannel(1); // 假设1是NOTIFICATION

        doThrow(new RuntimeException("Test Exception")).when(historyRepository).insertHistory(any(NotificationHistory.class));

        // 执行
        manualTaskSendService.batchSendPhoneMsg(phoneList, template, notificationTask);

        // 验证
        verify(historyRepository, times(1)).insertHistory(any(NotificationHistory.class));
        verify(detailRepository, never()).batchInsertDetail(anyList());
        verify(manualTaskSendService, never()).batchSendSms(any(BatchSendMessage.class));
        verify(manualTaskSendService, never()).updateBatchSendResult(any(NotificationHistory.class), anyList(), any(BatchSendSmsResp.class), any(NotificationTask.class));
    }


    @Test
    public void updateBatchSendResult_NullInputs_ShouldReturn() {
        manualTaskSendService.updateBatchSendResult(null, null, null, null);
        verify(detailRepository, never()).batchQueryDetailByPhoneAndInstanceCode(any(), any());
    }

    @Test
    public void updateBatchSendResult_EmptySendRespList_ShouldUpdateTaskStatus() {
        batchSendSmsResp.setData(new ArrayList<>());
        manualTaskSendService.updateBatchSendResult(history, sendHistoryDetails, batchSendSmsResp, notificationTask);
        verify(messageTaskRepository, times(1)).updateTaskById(any(NotificationTask.class));
    }

    @Test
    public void updateBatchSendResult_EmptyPhoneNumbers_ShouldReturn() {
        sendHistoryDetails.clear();
        manualTaskSendService.updateBatchSendResult(history, sendHistoryDetails, batchSendSmsResp, notificationTask);
        verify(detailRepository, never()).batchQueryDetailByPhoneAndInstanceCode(any(), any());
    }

    @Test
    public void updateBatchSendResult_EmptyDetailList_ShouldLogAndReturn() {
        when(detailRepository.batchQueryDetailByPhoneAndInstanceCode(any(), any())).thenReturn(new ArrayList<>());
        manualTaskSendService.updateBatchSendResult(history, sendHistoryDetails, batchSendSmsResp, notificationTask);
        verify(detailRepository, times(1)).batchQueryDetailByPhoneAndInstanceCode(any(), any());
    }

    @Test
    public void updateBatchSendResult_SuccessfulSend_ShouldUpdateHistoryAndDetails() {
        when(detailRepository.batchQueryDetailByPhoneAndInstanceCode(any(), any())).thenReturn(sendHistoryDetails);
        manualTaskSendService.updateBatchSendResult(history, sendHistoryDetails, batchSendSmsResp, notificationTask);

        assertEquals(1, history.getSendTotalCount().intValue());
        assertEquals(1, history.getSendSuccessCount().intValue());
        assertEquals(0, history.getSendFailCount().intValue());

        verify(detailRepository, times(1)).batchUpdateByIds(any());
        verify(historyRepository, times(1)).updateByInstanceCode(any(NotificationHistory.class));
        verify(messageTaskRepository, times(1)).updateTaskById(any(NotificationTask.class));
    }

    @Test
    public void updateBatchSendResult_FailedSend_ShouldUpdateHistoryAndDetails() {
        batchSendSmsResp.getData().get(0).setResult(SmsResultErrorEnum.MESSAGE_CONTENT_ERROR.getCode());
        when(detailRepository.batchQueryDetailByPhoneAndInstanceCode(any(), any())).thenReturn(sendHistoryDetails);
        manualTaskSendService.updateBatchSendResult(history, sendHistoryDetails, batchSendSmsResp, notificationTask);

        assertEquals(1, history.getSendTotalCount().intValue());
        assertEquals(0, history.getSendSuccessCount().intValue());
        assertEquals(1, history.getSendFailCount().intValue());

        verify(detailRepository, times(1)).batchUpdateByIds(any());
        verify(historyRepository, times(1)).updateByInstanceCode(any(NotificationHistory.class));
        verify(messageTaskRepository, times(1)).updateTaskById(any(NotificationTask.class));
    }


    @Test
    public void buildVinPhoneHistoryDetail_ValidInputs_ShouldReturnCorrect() {
        // 准备
        String instanceCode = "instanceCode";
        String content = "content";
        VinAndPhoneDTO vinPhone = new VinAndPhoneDTO("**********", "carVin");
        String taskCode = "taskCode";
        String msgId = "msgId";
        String brandCode = "brandCode";

        // 运行测试
        SendHistoryDetail result = manualTaskSendService.buildVinPhoneHistoryDetail(instanceCode, content, vinPhone, taskCode, msgId, brandCode);

        // 验证结果
        assertNotNull(result);
        assertEquals(msgId, result.getMsgId());
        assertEquals(content, result.getSendMessage());
        assertEquals(vinPhone.getPhone(), result.getSendPhone());
        assertEquals(vinPhone.getCarVin(), result.getCarVin());
        assertEquals(taskCode, result.getTaskCode());
        assertEquals(instanceCode, result.getTaskInstanceCode());
        assertEquals(SmsSendResultEnum.WAITED.getCode(), result.getSendResult());
    }

    @Test
    public void buildVinPhoneHistoryDetail_SendHistoryDetailWithDefaults() {
        // 准备
        VinAndPhoneDTO vinPhone = new VinAndPhoneDTO(null, null);

        // 运行测试
        SendHistoryDetail result = manualTaskSendService.buildVinPhoneHistoryDetail(null, null, vinPhone, null, null, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(SmsSendResultEnum.WAITED.getCode(), result.getSendResult());
    }


    @Test
    public void buildVinPhoneHistoryDetail_ValidInputs_ShouldReturnCorrectSendHistoryDetail() {
        // 准备
        String instanceCode = "instanceCode";
        String content = "content";
        VinAndPhoneDTO vinPhone = new VinAndPhoneDTO("**********", "carVin");
        String taskCode = "taskCode";
        String msgId = "msgId";
        String brandCode = "brandCode";

        // 运行测试
        SendHistoryDetail result = manualTaskSendService.buildVinPhoneHistoryDetail(instanceCode, content, vinPhone, taskCode, msgId, brandCode);

        // 验证结果
        assertNotNull(result);
        assertEquals(msgId, result.getMsgId());
        assertEquals(content, result.getSendMessage());
        assertEquals(vinPhone.getPhone(), result.getSendPhone());
        assertEquals(vinPhone.getCarVin(), result.getCarVin());
        assertEquals(taskCode, result.getTaskCode());
        assertEquals(instanceCode, result.getTaskInstanceCode());
        assertEquals(SmsSendResultEnum.WAITED.getCode(), result.getSendResult());
    }

    @Test
    public void buildVinPhoneHistoryDetail_NullInputs_ShouldReturnSendHistoryDetailWithDefaults() {
        // 准备
        VinAndPhoneDTO vinPhone = new VinAndPhoneDTO(null, null);

        // 运行测试
        SendHistoryDetail result = manualTaskSendService.buildVinPhoneHistoryDetail(null, null, vinPhone, null, null, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(SmsSendResultEnum.WAITED.getCode(), result.getSendResult());
    }

    @Mock
    private RedisTemplate<String, String> redisTemplate;

    @Mock
    private ValueOperations<String, String> valueOperations;

    @Mock
    private NotificationExcelService notificationExcelService;

    /**
     * 测试幂等性检查失败的情况
     */
    @Test
    public void testSendTask_IdempotentCheckFailed() {
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);

        // Mock Redis 返回 false，表示任务已存在


        // 调用被测方法
        manualTaskSendService.sendTask("testTaskCode");

        // 验证未调用其他方法
        verify(messageTaskRepository, never()).queryTaskByCode(anyString());
    }

    /**
     * 测试任务状态不是启用状态的情况
     */
    @Test
    public void testSendTask_TaskNotStarted() {
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);

        // Mock 查询任务信息，返回任务状态为非启用状态
        NotificationTask task = new NotificationTask();
        task.setStatus(TaskStatusEnum.STOP.getStatus());


        // 调用被测方法
        manualTaskSendService.sendTask("testTaskCode");

        // 验证未调用模板查询和文件解析方法
        verify(templateRepository, never()).getMessageTemplateByCode(anyString());
        verify(notificationExcelService, never()).getPhoneList(anyString());
    }

    /**
     * 测试任务没有要发送的文件的情况
     */
    @Test
    public void testSendTask_NoMessageFile() {
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        // Mock Redis 返回 true，表示任务未执行


        // Mock 查询任务信息，返回任务状态为启用状态，但没有关联文件
        NotificationTask task = new NotificationTask();
        task.setStatus(TaskStatusEnum.START.getStatus());
        task.setMessageFile(null);


        // 调用被测方法
        manualTaskSendService.sendTask("testTaskCode");

        // 验证未调用模板查询和文件解析方法
        verify(templateRepository, never()).getMessageTemplateByCode(anyString());
        verify(notificationExcelService, never()).getPhoneList(anyString());
    }

    /**
     * 测试模板不存在或内容为空的情况
     */
    @Test
    public void testSendTask_TemplateNotFoundOrEmpty() throws InterruptedException {
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);

        // Mock 分布式锁操作 - 第一个 setIfAbsent 调用（分布式锁）


        // Mock 幂等性检查操作 - 第二个 setIfAbsent 调用（幂等性检查）
        when(redisTemplate.opsForValue().setIfAbsent(eq("testTaskCode"), eq("OK"), anyLong(), eq(TimeUnit.SECONDS)))
                .thenReturn(true);

        // Mock 查询任务信息，返回任务状态为启用状态，并有关联文件
        NotificationTask task = new NotificationTask();
        task.setStatus(TaskStatusEnum.START.getStatus());
        task.setMessageFile("testFilePath");
        task.setSubmitFileType(ExcelTemplateTypeEnum.PHONE_EXCEL.getType());
        task.setMessageTemplateCode("testTemplateCode"); // 设置模板代码
        when(messageTaskRepository.queryTaskByCode("testTaskCode")).thenReturn(task);

        // Mock 解析 Excel 文件，返回有效手机号码列表
        List<List<String>> phoneList = new ArrayList<>();
        phoneList.add(List.of("**********"));
        when(notificationExcelService.getPhoneList(anyString())).thenReturn(phoneList);

        // Mock 模板查询返回 null（模板不存在的情况）
        when(templateRepository.getMessageTemplateByCode("testTemplateCode")).thenReturn(null);

        // 直接调用 sendManualTask 方法，避免异步执行的复杂性
        manualTaskSendService.sendManualTask("testTaskCode");

        // 验证调用了文件解析方法
        verify(notificationExcelService, times(1)).getPhoneList(anyString());
        // 验证调用了模板查询方法
        verify(templateRepository, times(1)).getMessageTemplateByCode("testTemplateCode");
        // 验证未调用批量发送方法（因为模板不存在）
        verify(manualTaskSendService, never()).batchSendPhoneMsg(anyList(), any(MessageTemplate.class), any(NotificationTask.class));
    }

    /**
     * 测试 Excel 文件解析结果为空的情况
     */
    @Test
    public void testSendTask_EmptyPhoneList() {
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        // Mock Redis 返回 true，表示任务未执行
        // Mock 查询任务信息，返回任务状态为启用状态，并有关联文件
        NotificationTask task = new NotificationTask();
        task.setStatus(TaskStatusEnum.START.getStatus());
        task.setMessageFile("testFilePath");
        task.setSubmitFileType(ExcelTemplateTypeEnum.PHONE_EXCEL.getType());

        // Mock 查询模板信息，返回有效模板
        MessageTemplate template = new MessageTemplate();
        template.setTemplateContent("testContent");


        // 调用被测方法
        manualTaskSendService.sendTask("testTaskCode");

        // 验证未调用批量发送方法
        verify(manualTaskSendService, never()).batchSendPhoneMsg(anyList(), any(MessageTemplate.class), any(NotificationTask.class));
    }

    /**
     * 测试正常流程
     */
    @Test
    public void testSendTask_NormalFlow() {
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);


        // Mock 查询任务信息，返回任务状态为启用状态，并有关联文件
        NotificationTask task = new NotificationTask();
        task.setStatus(TaskStatusEnum.START.getStatus());
        task.setMessageFile("testFilePath");
        task.setSubmitFileType(ExcelTemplateTypeEnum.PHONE_EXCEL.getType());

        // Mock 查询模板信息，返回有效模板
        MessageTemplate template = new MessageTemplate();
        template.setTemplateContent("testContent");

        // Mock 解析 Excel 文件，返回有效手机号码列表
        List<List<String>> phoneList = new ArrayList<>();
        phoneList.add(List.of("**********"));

        // 调用被测方法
        manualTaskSendService.sendTask("testTaskCode");

        // 验证批量发送方法被调用
        verify(manualTaskSendService, never()).batchSendPhoneMsg(anyList(), any(MessageTemplate.class), any(NotificationTask.class));
    }



    @Mock
    private ManualTaskSendServiceImpl manualTaskSendServiceImpl;


    /**
     * 测试正常流程
     */
    @Test
    public void testBatchSendPhoneMsg_NormalFlow() {
        // 准备测试数据
        List<List<String>> phoneList = List.of(List.of("**********", "0987654321"));
        MessageTemplate template = new MessageTemplate();
        template.setTemplateContent("Test Content");
        NotificationTask notificationTask = new NotificationTask();
        notificationTask.setTaskCode("TASK123");
        notificationTask.setSignBrandText(1);

        // Mock依赖方法
        when(instanceCodeGenerator.generateTaskBatchId(InstanceCodeTypeEnum.MANUAL.getType()))
                .thenReturn("INSTANCE123");
        when(manualTaskSendService.buildBatchSendSmsMsg(anyList(), anyString(), any(NotificationTask.class)))
                .thenReturn(new BatchSendMessage());

        // 执行被测方法
        manualTaskSendService.batchSendPhoneMsg(phoneList, template, notificationTask);

        // 验证交互
        verify(historyRepository, times(1)).insertHistory(any(NotificationHistory.class));
    }

    /**
     * 测试异常分支
     */
    @Test
    public void testBatchSendPhoneMsg_ExceptionFlow() {
        // 准备测试数据
        List<List<String>> phoneList = List.of(List.of("**********", "0987654321"));
        MessageTemplate template = new MessageTemplate();
        template.setTemplateContent("Test Content");
        NotificationTask notificationTask = new NotificationTask();
        notificationTask.setTaskCode("TASK123");
        notificationTask.setSignBrandText(1);

        // Mock依赖方法抛出异常
        when(instanceCodeGenerator.generateTaskBatchId(InstanceCodeTypeEnum.MANUAL.getType()))
                .thenReturn("INSTANCE123");
        doThrow(new RuntimeException("Database Error")).when(historyRepository).insertHistory(any(NotificationHistory.class));

        // 执行被测方法
        manualTaskSendService.batchSendPhoneMsg(phoneList, template, notificationTask);

        // 验证异常被捕获
        verify(historyRepository, times(1)).insertHistory(any(NotificationHistory.class));
        verify(detailRepository, never()).batchInsertDetail(anyList());
        verify(manualTaskSendService, never()).batchSendSms(any(BatchSendMessage.class));
        verify(manualTaskSendServiceImpl, never()).updateBatchSendResult(any(), anyList(), any(), any());
    }




    /**
     * 测试正常路径
     */
    @Test
    public void testBatchSendVinPhoneMsg_NormalPath() throws Exception {
        // 准备测试数据
        List<List<VinAndPhoneDTO>> vinPhoneGroupList = Arrays.asList(
                Arrays.asList(new VinAndPhoneDTO("**********", "VIN123")),
                Arrays.asList(new VinAndPhoneDTO("0987654321", "VIN456"))
        );
        MessageTemplate template = new MessageTemplate();
        template.setTemplateContent("Test Content");
        NotificationTask notificationTask = new NotificationTask();
        notificationTask.setTaskCode("TASK123");
        notificationTask.setSignBrandText(1);

        // Mock依赖方法
        when(instanceCodeGenerator.generateTaskBatchId(anyString())).thenReturn("INSTANCE123");
        when(accessTokenUtil.fetchAccessToken()).thenReturn(new AccessTokenResponse());
        try (var mockedStatic = mockStatic(JSON.class)) {
            mockedStatic.when(() -> JSON.toJSONString(any())).thenReturn("{}");

            // 调用被测方法
            manualTaskSendService.batchSendVinPhoneMsg(vinPhoneGroupList, template, notificationTask);

            // 验证交互
            verify(historyRepository, times(1)).insertHistory(any(NotificationHistory.class));
            verify(detailRepository, times(2)).batchInsertDetail(anyList());
            verify(manualTaskSendServiceImpl, times(2)).updateBatchSendResult(any(), any(), any(), any());
        }
    }

    /**
     * 测试异常路径
     */
    @Test
    public void testBatchSendVinPhoneMsg_ExceptionPath() {
        // 准备测试数据
        List<List<VinAndPhoneDTO>> vinPhoneGroupList = Arrays.asList(
                Arrays.asList(new VinAndPhoneDTO("**********", "VIN123"))
        );
        MessageTemplate template = new MessageTemplate();
        template.setTemplateContent("Test Content");
        NotificationTask notificationTask = new NotificationTask();
        notificationTask.setTaskCode("TASK123");

        // Mock依赖方法抛出异常
        doThrow(new RuntimeException("Mock Exception")).when(historyRepository).insertHistory(any(NotificationHistory.class));

        // 调用被测方法
        manualTaskSendService.batchSendVinPhoneMsg(vinPhoneGroupList, template, notificationTask);

        // 验证日志记录
        verify(historyRepository, times(1)).insertHistory(any(NotificationHistory.class));
        verify(detailRepository, never()).batchInsertDetail(anyList());
        verify(manualTaskSendServiceImpl, never()).updateBatchSendResult(any(), any(), any(), any());
    }


}