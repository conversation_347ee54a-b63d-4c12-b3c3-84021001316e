package com.jlr.ecp.notification.vo.task.auto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Schema(description = "属性VO")
@Builder
public class ConditionAttributeVO {

    /**
     * 条件ID；条件ID，雪花算法ID
     * 对应数据库字段：condition_id
     */
    @Schema(description = "条件id")
    private String conditionId;

    /**
     * 条件编码；不同类型的条件编码如下：
     * 品牌：JAGUAR, LANDROVER, ALL
     * 产地：CHINA_MADE, IMPORTED, ALL
     * 服务：REMOTE,PIVI
     * 实名状态：RNR_TRUE,RNR_FALSE, ALL
     * 到期时间：BEFORE_EXPIRED,AFTER_EXPIRED
     * 对应数据库字段：condition_code
     */
    @Schema(description = "品牌：JAG, LAN, ALL" +
            "产地：CHINA_MADE, IMPORTED, ALL" +
            "服务：REMOTE, PIVI" +
            "实名状态：RNR_TRUE, RNR_FALSE, ALL" +
            "到期时间：BEFORE_EXPIRED, AFTER_EXPIRED")
    private String conditionCode;

    /**
     *  条件名称
     * */
    @Schema(description = "条件名称")
    private String conditionName;

    @Schema(description = "条件值")
    private String conditionValue;
}
