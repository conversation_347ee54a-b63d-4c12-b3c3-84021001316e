package com.jlr.ecp.notification.api.notification;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.api.dto.*;
import com.jlr.ecp.notification.api.vo.ShortLinkConfigVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import com.jlr.ecp.notification.enums.ApiConstants;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - notification相关服务")
public interface NotificationServiceApi {
    String PREFIX = ApiConstants.PREFIX;

    //String PREFIX = "/feign/v1/notification";

    @PostMapping(PREFIX + "/save/waited/send/message")
    @Operation(summary = "保存服务到期通知短信")
    CommonResult<Integer> saveWaitedSendMsg(@RequestBody List<PhoneByCarVinDTO> phoneByCarVinDTOList);

    @PostMapping(PREFIX + "/get/sms/expire/ids")
    @Operation(summary = "根据到期类型获取短信发送历史记录中待过期的短信ID列表")
    CommonResult<List<Long>> getSmsDetailIdsByExpireType(@RequestBody GetSmsExpireIdsDTO smsExpireIdsDTO);

    @PostMapping(PREFIX + "/scheduled/send/message")
    @Operation(summary = "发送定时通知短信")
    CommonResult<Integer> scheduledSendMsg(@RequestBody SendExpireSmsParam sendExpireSmsParam);

    @PostMapping(PREFIX + "/get/scheduled/time")
    @Operation(summary = "获取服务到期短信发送定时任务的时间")
    CommonResult<String> getScheduledTime(@RequestParam("expireType") Integer expireType);

    @PostMapping(PREFIX + "/retry/notification")
    @Operation(summary = "下单成功、取消订单成功、服务激活通知超时数据进行重试")
    CommonResult<Integer> retryNotification(@RequestParam("day") Integer day);

    @PostMapping(PREFIX + "/send/manual/message")
    @Operation(summary = "发送手动模板消息")
    void sendManualMsg(@RequestParam("taskCode") String taskCode);

    @PostMapping(PREFIX + "/update/sms/notification/status")
    @Operation(summary = "更新短信通知状态")
    void updateSmsNotificationStatus();

    @PostMapping(PREFIX + "/update/sms/market/status")
    @Operation(summary = "更新短信营销状态")
    void updateSmsMarketStatus();

    @PostMapping(PREFIX + "/check/carVin/blackList")
    @Operation(summary = "校验carVin是否在黑名单中")
    CommonResult<List<MsgBlackListDTO>> checkCarVinBlackList(@RequestBody List<String> carVinList);

    @PostMapping(PREFIX + "/get/shortLink/config")
    @Operation(summary = "获取短链配置")
    CommonResult<ShortLinkConfigVO> getShortLinkConfig(@RequestParam("brandCode") String brandCode);

    @GetMapping(PREFIX + "/expire/condition")
    @Operation(summary = "获取到期通知的待发送条件")
    CommonResult<List<ExpireNotifyConditionDTO>> getExpireNotifyCondition(@RequestParam(value = "expireDate")
                                                                          String expireDate);

    @PostMapping(PREFIX + "/get/expire/ids/taskCode")
    @Operation(summary = "根据taskCode获取短信发送历史记录中待过期的短信ID列表")
    CommonResult<List<Long>> getDetailIdsByTaskCode(@RequestBody GetDetailIdsByTaskCodeDTO detailIdsDTO);

    @PostMapping(PREFIX + "/send/message/taskCode")
    @Operation(summary = "发送定时通知短信通过taskCode")
    CommonResult<Integer> sendExpireMsgByTaskCode(@RequestBody SendExpireByTaskCodeDTO sendExpireDTO);
}
