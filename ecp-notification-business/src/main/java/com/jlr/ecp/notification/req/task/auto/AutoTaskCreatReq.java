package com.jlr.ecp.notification.req.task.auto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "创建自动任务请求")
@AllArgsConstructor
@NoArgsConstructor
public class AutoTaskCreatReq {

    @Schema(description = "任务名称")
    @NotBlank(message = "任务名称不能为空")
    private String taskName;

    @Schema(description = "发送时间类型 1：永久循环发送 2：限定时间发送")
    @NotNull(message = "发送时间类型不能为空")
    private Integer taskTimeType;

    @Schema(description = "范围开始时间")
    private String rangeBeginDate;

    @Schema(description = "范围结束时间")
    private String rangeEndDate;

    @Schema(description = "每日定时发送时间, 格式 xx:xx")
    @NotBlank(message = "每日定时发送时间不能为空")
    private String dailySendTime;

    @Schema(description = "任务触发字段")
    @NotBlank(message = "任务触发字段不能为空")
    private String triggerActionString;

    @Schema(description = "任务触发逻辑")
    @NotNull(message = "任务触发逻辑不能为空")
    private TriggerActionReq triggerAction;

    @Schema(description = "引用的通知模板code")
    @NotBlank(message = "模板code不能为空")
    private String messageTemplateCode;

    @Schema(description = "商品code")
    @NotBlank(message = "商品code不能为空")
    private String notifySpuCode;
}
