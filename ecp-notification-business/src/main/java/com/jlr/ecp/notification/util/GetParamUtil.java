package com.jlr.ecp.notification.util;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class GetParamUtil {

    /**
     * 根据基础URL和参数Map生成完整的带参URL路径。
     *
     * @param baseUrl 基础URL，不带任何参数。
     * @param paramMap 包含参数的Map，键为参数名，值为参数值。
     * @return 返回生成的完整URL路径，包含基础URL和拼接的参数。
     */
    public static String getParamPath(String baseUrl, Map<String, Object> paramMap) {
        log.info("根据基础URL和参数Map生成完整的带参URL路径, baseUrl:{}, paramMap:{}", baseUrl, paramMap);
        if (CollUtil.isEmpty(paramMap)) {
            return baseUrl;
        }
        StringBuilder path = new StringBuilder(baseUrl);
        int idx = 0;
        for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
            if (idx == 0) {
                path.append("?");
            } else {
                path.append("&");
            }
            path.append(entry.getKey()).append("=").append(entry.getValue());
            idx++;
        }
        log.info("根据基础URL和参数Map生成完整的带参URL路径, path:{}", path);
        return path.toString();
    }
}
