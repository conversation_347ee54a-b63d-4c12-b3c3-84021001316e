package com.jlr.ecp.notification.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "到期通知的条件")
public class ExpireNotifyConditionDTO {
    /**
     *  任务code
     * */
    @Schema(description = "任务code")
    private String taskCode;

    /**
     *  任务名称
     * */
    @Schema(description = "任务名称")
    private String taskName;

    /**
     *  触发逻辑
     * */
    @Schema(description = "触发逻辑")
    private String triggerAction;

    /**
     *  发送时间类型 1：永久循环发送 2：限定时间发送
     * */
    @Schema(description = "发送时间类型 1：永久循环发送 2：限定时间发送")
    private Integer taskTimeType;


    /**
     *  发送类型 1：实时发送 2：定时发送
     * */
    @Schema(description = "发送类型 1：实时发送 2：定时发送")
    private Integer taskSendType;

    /**
     * 范围开始时间
     * */
    @Schema(description = "范围开始时间")
    private LocalDateTime rangeBeginDate;

    /**
     * 范围结束时间
     * */
    @Schema(description = "范围结束时间")
    private LocalDateTime rangeEndDate;

    /**
     *  发送模板消息编码
     * */
    @Schema(description = "发送模板消息编码")
    private String messageTemplateCode;

    /**
     *  指定每天的发送时间时分，如10:00
     * */
    @Schema(description = "指定每天的发送时间时分")
    private String dailySendTime;

    /**
     *  通知短信商品编码
     * */
    @Schema(description = "通知短信商品编码")
    private String notifySpuCode;

    /**
     * 通知状态  0停用 1启用 2待启用 3已发送
     * */
    @Schema(description = "通知状态  0停用 1启用 2待启用 3已发送")
    private Integer status;

    @Schema(description = "品牌的ConditionCode  品牌：JAGUAR, LANDROVER, ALL")
    private String brand;

    @Schema(description = "车辆产地的ConditionCode 产地：CHINA_MADE, IMPORTED, ALL")
    private String vehicleOrigin;

    @Schema(description = "到期服务的ConditionCode 服务：REMOTE, PIVI")
    private String expirationService;

    @Schema(description = "实名状态的ConditionCode 实名状态：RNR_TRUE, RNR_FALSE, ALL")
    private String realNameStatus;

    @Schema(description = "到期区间的ConditionCode 到期时间：BEFORE_EXPIRED, AFTER_EXPIRED")
    private String expirationInterval;

    @Schema(description = "到期天数")
    private Integer expireDays;

    @Schema(description = "开始时间 格式:yyyy-MM-dd HH:mm:ss")
    private String startDate;

    @Schema(description = "结束时间 格式：yyyy-MM-dd HH:mm:ss")
    private String endDate;
}
