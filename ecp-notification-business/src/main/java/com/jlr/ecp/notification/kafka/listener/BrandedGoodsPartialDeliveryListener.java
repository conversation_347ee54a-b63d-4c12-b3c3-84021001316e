package com.jlr.ecp.notification.kafka.listener;

import com.jlr.ecp.notification.kafka.message.BrandedGoodsPartialDeliveryMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 精选好物部分发货通知监听器
 */
@Component
@Slf4j
public class BrandedGoodsPartialDeliveryListener extends BaseNotificationListener<BrandedGoodsPartialDeliveryMessage> {
    
    @KafkaListener(topics = "${branded-goods.kafka.topics.partial-delivery}", 
                  groupId = "${branded-goods.kafka.groups.partial-delivery}", 
                  batch = "true",
                  properties = "max.poll.records:100")
    public void listen(List<ConsumerRecord<String, String>> records) {
        log.info("精选好物商品部分发货通知消息，records:{}", records);
        processMessages(records, BrandedGoodsPartialDeliveryMessage.class);
    }
} 