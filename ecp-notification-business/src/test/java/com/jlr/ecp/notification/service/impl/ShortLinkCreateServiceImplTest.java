package com.jlr.ecp.notification.service.impl;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.notification.dal.dataobject.shortlink.MiniCodeConfigDO;
import com.jlr.ecp.notification.dal.dataobject.shortlink.MiniCodeGenerateRecordDO;
import com.jlr.ecp.notification.dal.dataobject.shortlink.ShortLinkClickTotalDO;
import com.jlr.ecp.notification.dal.dataobject.shortlink.ShortLinkCreateRecordDO;
import com.jlr.ecp.notification.dal.repository.ShortLinkCreateRepository;
import com.jlr.ecp.notification.dal.repository.shortlink.MiniCodeConfigRepository;
import com.jlr.ecp.notification.dal.repository.shortlink.MiniCodeGenerateRepository;
import com.jlr.ecp.notification.dal.repository.shortlink.ShortLinkClickTotalRepository;
import com.jlr.ecp.notification.dal.repository.shortlink.ShortLinkClickUserRepository;
import com.jlr.ecp.notification.dto.shortlink.QrCodeCreateDTO;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkCreateDTO;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkCreateListDTO;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkRemarkDTO;
import com.jlr.ecp.notification.enums.BrandCodeEnum;
import com.jlr.ecp.notification.enums.BrandToUrlEum;
import com.jlr.ecp.notification.resp.WeChatQrCodeResp;
import com.jlr.ecp.notification.service.ShortLinkService;
import com.jlr.ecp.notification.util.WeChatUtil;
import com.jlr.ecp.notification.util.sms.TimeFormatUtil;
import com.jlr.ecp.notification.vo.shortlink.QrCodeCreateVO;
import com.jlr.ecp.notification.vo.shortlink.ShortLinkClickVO;
import com.jlr.ecp.notification.vo.shortlink.ShortLinkCreateBaseUrlVO;
import com.jlr.ecp.notification.vo.shortlink.ShortLinkCreateListVO;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.*;

import static junit.framework.TestCase.assertNotNull;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ShortLinkCreateServiceImplTest {

    @Mock
    private Snowflake mockSnowflake;
    @Mock
    private ShortLinkCreateRepository mockShortLinkCreateRepository;
    @Mock
    private ShortLinkService mockShortLinkService;
    @Mock
    private ShortLinkClickTotalRepository mockClickTotalRepository;
    @Mock
    private ShortLinkClickUserRepository mockUserClickRepository;

    @InjectMocks
    private ShortLinkCreateServiceImpl shortLinkCreateService;

    @Mock
    private Snowflake snowflake;

    @Mock
    private MiniCodeConfigRepository miniCodeConfigRepository;

    @Mock
    private MiniCodeGenerateRepository miniCodeGenerateRepository;

    @Mock
    private WeChatUtil weChatUtil;

    @BeforeEach
    void setUp() {
        shortLinkCreateService = new ShortLinkCreateServiceImpl();
    }



    @Test
    public void testGetBaseUrlByBrandCode() {
        // Setup
        final List<ShortLinkCreateBaseUrlVO> expectedResult = List.of(ShortLinkCreateBaseUrlVO.builder()
                .urlName("urlName")
                .urlType("urlType")
                .url("brandToUrl")
                .brandId("brandId")
                .build());

        // Run the test
        final List<ShortLinkCreateBaseUrlVO> result = shortLinkCreateService.getBaseUrlByBrandCode(
                "brandCode");

        // Verify the results
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void testGetBaseUrlByBrandCode_WhenPathInfoNotEmpty_ReturnsPopulatedList() {
        // 单元测试设计: 验证当 BrandToUrlEum.getPathInfoByBrandCode 返回非空列表时，方法正确构建 VO 列表
        // 创建 BrandToUrlEum 的 Mock 实例
        BrandToUrlEum mockEnum = mock(BrandToUrlEum.class);
        when(mockEnum.getUrlName()).thenReturn("商品详情页");
        when(mockEnum.getUrlType()).thenReturn("PDP");
        when(mockEnum.getBrandToUrl()).thenReturn("ecp/pages/mine/order/detail/index");
        when(mockEnum.getBrandId()).thenReturn("BS0001");

        try (MockedStatic<BrandToUrlEum> mockedStatic = mockStatic(BrandToUrlEum.class)) {
            // 模拟静态方法返回包含 Mock 实例的列表
            mockedStatic.when(() -> BrandToUrlEum.getPathInfoByBrandCode(anyString()))
                    .thenReturn(Arrays.asList(mockEnum));

            // 调用被测方法
            List<ShortLinkCreateBaseUrlVO> result = shortLinkCreateService.getBaseUrlByBrandCode("validBrandCode");

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());

            ShortLinkCreateBaseUrlVO vo = result.get(0);
            assertEquals("商品详情页(PDP)", vo.getUrlName());
            assertEquals("PDP", vo.getUrlType());
            assertEquals("ecp/pages/mine/order/detail/index", vo.getUrl());
            assertEquals("BS0001", vo.getBrandId());
        }
    }


    @Test
    public void testCreateShortLink_Land() {
        // Setup
        final ShortLinkCreateDTO createDTO = new ShortLinkCreateDTO();
        createDTO.setBrandCode(BrandCodeEnum.LAND_ROVER.getBrandCode());
        createDTO.setUrlType("urlType");
        createDTO.setProductName("productName");
        createDTO.setParams(Map.ofEntries(Map.entry("value", "value")));


        // Run the test
        final String result = shortLinkCreateService.createShortLink(createDTO);

        // Verify the results
        assertEquals("", result);
    }

    @Test
    public void testCreateShortLink_Jag() {
        // Setup
        final ShortLinkCreateDTO createDTO = new ShortLinkCreateDTO();
        createDTO.setBrandCode(BrandCodeEnum.JAGUAR.getBrandCode());
        createDTO.setUrlType("urlType");
        createDTO.setProductName("productName");
        createDTO.setParams(Map.ofEntries(Map.entry("value", "value")));

        // Run the test
        final String result = shortLinkCreateService.createShortLink(createDTO);

        // Verify the results
        assertEquals("", result);
    }


    @Test
    public void testUpdateRemark() {
        // Setup
        final ShortLinkRemarkDTO remarkDTO = new ShortLinkRemarkDTO();
        remarkDTO.setConfigId(0L);
        remarkDTO.setRemarks("remark");

        final CommonResult<String> expectedResult = CommonResult.success("短链生成成功");

        // Configure ShortLinkCreateRepository.getRecordDoByConfigId(...).
        final ShortLinkCreateRecordDO createRecordDO = ShortLinkCreateRecordDO.builder()
                .configId(0L)
                .urlCode("urlCode")
                .operator("operator")
                .brandCode("brandCode")
                .baseUrl("baseUrl")
                .params("params")
                .configContent("configContent")
                .shortLink("shortLink")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .remark("remark")
                .build();
        when(mockShortLinkCreateRepository.getRecordDoByConfigId(0L)).thenReturn(createRecordDO);

        // Run the test
        final CommonResult<String> result = shortLinkCreateService.updateRemark(remarkDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockShortLinkCreateRepository).updateById(ShortLinkCreateRecordDO.builder()
                .configId(0L)
                .urlCode("urlCode")
                .operator("operator")
                .brandCode("brandCode")
                .baseUrl("baseUrl")
                .params("params")
                .configContent("configContent")
                .shortLink("shortLink")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .remark("remark")
                .build());
    }

    @Test
    public void testQueryShortLinkClick() {
        // Setup
        final ShortLinkClickVO shortLinkClickVO = new ShortLinkClickVO();
        shortLinkClickVO.setClickTotal(0L);
        shortLinkClickVO.setUserClickCount(0L);
        final CommonResult<ShortLinkClickVO> expectedResult = CommonResult.success(shortLinkClickVO);

        // Configure ShortLinkClickTotalRepository.selectTotalByUrlCode(...).
        final ShortLinkClickTotalDO clickTotalDO = ShortLinkClickTotalDO.builder()
                .clickTotal(0L)
                .build();
        when(mockClickTotalRepository.selectTotalByUrlCode("urlCode")).thenReturn(clickTotalDO);

        when(mockUserClickRepository.queryUserCountByUrlCode("urlCode")).thenReturn(0L);

        // Run the test
        final CommonResult<ShortLinkClickVO> result = shortLinkCreateService.queryShortLinkClick(
                "urlCode");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGenShortLink() {
        // Setup
        final ShortLinkCreateDTO createDTO = new ShortLinkCreateDTO();
        createDTO.setBrandCode("brandCode");
        createDTO.setUrlType("urlType");
        createDTO.setProductName("productName");
        createDTO.setParams(Map.ofEntries(Map.entry("value", "value")));

        // Run the test
        final String result = shortLinkCreateService.genShortLink(createDTO, "urlCode");

        // Verify the results
        assertEquals("", result);
    }


    /**
     * 测试用例1: 当 pageResp.getRecords() 返回空列表时，验证返回值是否为 PageResult.empty()
     */
    @Test
    public void testGetShortLinkList_EmptyRecords() {
        // 模拟输入参数
        ShortLinkCreateListDTO createListDTO = new ShortLinkCreateListDTO();
        createListDTO.setOperatorTimeSort("desc");

        // 模拟 repository 返回空分页数据
        Page<ShortLinkCreateRecordDO> mockPage = new Page<>();
        mockPage.setRecords(new ArrayList<>());
        mockPage.setTotal(0L);
        when(mockShortLinkCreateRepository.pageList(any(ShortLinkCreateListDTO.class))).thenReturn(mockPage);

        // 执行测试
        PageResult<ShortLinkCreateListVO> result = shortLinkCreateService.getShortLinkList(createListDTO);

        // 验证结果
        assertEquals(PageResult.empty(), result);
    }

    /**
     * 测试用例2: 当 pageResp.getRecords() 返回非空列表时，验证返回值是否正确包含转换后的视图对象和总记录数
     */
    @Test
    public void testGetShortLinkList_NonEmptyRecords() {
        // 模拟输入参数
        ShortLinkCreateListDTO createListDTO = new ShortLinkCreateListDTO();
        createListDTO.setOperatorTimeSort("asc");

        // 模拟 repository 返回非空分页数据
        List<ShortLinkCreateRecordDO> mockRecords = new ArrayList<>();
        ShortLinkCreateRecordDO record = new ShortLinkCreateRecordDO();
        record.setConfigId(1L);
        record.setUrlCode("testUrlCode");
        record.setOperator("testOperator");
        record.setCreatedTime(LocalDateTime.now());
        record.setBrandCode("LAN");
        record.setBaseUrl("http://example.com");
        record.setParams("param1=value1");
        record.setConfigContent("configContent");
        record.setShortLink("http://short.link");
        record.setExpiryDate(LocalDateTime.now().plusDays(1));
        record.setRemark("testRemark");
        mockRecords.add(record);

        Page<ShortLinkCreateRecordDO> mockPage = new Page<>();
        mockPage.setRecords(mockRecords);
        mockPage.setTotal(1L);
        when(mockShortLinkCreateRepository.pageList(any(ShortLinkCreateListDTO.class))).thenReturn(mockPage);

        // 执行测试
        PageResult<ShortLinkCreateListVO> result = shortLinkCreateService.getShortLinkList(createListDTO);

        // 验证结果
        assertEquals(1, result.getList().size());
        assertEquals(Long.valueOf(1), result.getTotal());

        ShortLinkCreateListVO vo = result.getList().get(0);
        assertEquals(Long.valueOf(1), vo.getConfigId());
        assertEquals("testUrlCode", vo.getUrlCode());
        assertEquals("testOperator", vo.getOperator());
        assertEquals("LAN", vo.getBrandCode());
        assertEquals("http://example.com", vo.getBaseUrl());
        assertEquals("param1=value1", vo.getParams());
        assertEquals("configContent", vo.getConfigContent());
        assertEquals("http://short.link", vo.getShortLink());
        assertEquals("testRemark", vo.getRemark());
    }


    /**
     * 测试用例3: 验证 shortLinkCreateRepository.pageList 方法是否被正确调用
     */
    @Test
    public void testGetShortLinkList_VerifyRepositoryCall() {
        // 模拟输入参数
        ShortLinkCreateListDTO createListDTO = new ShortLinkCreateListDTO();
        createListDTO.setOperatorTimeSort("desc");

        // 模拟 repository 返回空分页数据
        Page<ShortLinkCreateRecordDO> mockPage = new Page<>();
        mockPage.setRecords(new ArrayList<>());
        mockPage.setTotal(0L);
        when(mockShortLinkCreateRepository.pageList(any(ShortLinkCreateListDTO.class))).thenReturn(mockPage);

        // 执行测试
        shortLinkCreateService.getShortLinkList(createListDTO);

        // 验证 repository 方法调用
        verify(mockShortLinkCreateRepository).pageList(any(ShortLinkCreateListDTO.class));
    }


    /**
     * TC01: qrCodeCreateDTO 为空
     */
    @Test
    public void testCreateQrCode_DTOIsNull() {
        // Arrange
        QrCodeCreateDTO dto = null;

        // Act
        CommonResult<byte[]> result = shortLinkCreateService.createQrCode(dto);

        // Assert
        assertNotNull(result);
        assertArrayEquals(new byte[0], result.getData());
        verifyNoInteractions(snowflake, miniCodeConfigRepository, miniCodeGenerateRepository, weChatUtil);
    }

    /**
     * TC02: 参数校验失败（param5 含非法字符）
     */
    @Test
    public void testCreateQrCode_InvalidParam() {
        // Arrange
        QrCodeCreateDTO dto = new QrCodeCreateDTO();
        dto.setBrandCode("JAG");
        dto.setUrlType("PDP");
        dto.setParam5("invalid@char");

        // Mock isAlphanumeric 返回 false
        try (MockedStatic<BrandToUrlEum> mockedStatic = mockStatic(BrandToUrlEum.class)) {
            mockedStatic.when(() -> BrandToUrlEum.getUrlByBrandCodeUrlType(anyString(), anyString()))
                    .thenReturn("mockUrl");

            // Act
            CommonResult<byte[]> result = shortLinkCreateService.createQrCode(dto);

            // Assert
            assertNotNull(result);
            assertEquals(12576789, result.getCode().intValue());
            verifyNoInteractions(snowflake, miniCodeConfigRepository, miniCodeGenerateRepository, weChatUtil);
        }
    }

    /**
     * TC03: 构建配置对象失败（recordCode 为空）
     */
    @Test
    public void testCreateQrCode_BuildConfigFailed() {
        // Arrange
        QrCodeCreateDTO dto = new QrCodeCreateDTO();
        dto.setBrandCode("JAG");
        dto.setUrlType("PDP");

        when(snowflake.nextIdStr()).thenReturn(""); // 返回空字符串导致构建失败

        try (MockedStatic<BrandToUrlEum> mockedStatic = mockStatic(BrandToUrlEum.class)) {
            mockedStatic.when(() -> BrandToUrlEum.getUrlByBrandCodeUrlType("JAG", "PDP"))
                    .thenReturn("mockUrl");

            // Act
            CommonResult<byte[]> result = shortLinkCreateService.createQrCode(dto);

            // Assert
            assertNotNull(result);
            assertArrayEquals(new byte[0], result.getData());
            verify(miniCodeConfigRepository, never()).saveOrUpdate(any());
        }
    }

    /**
     * TC04: 构建生成记录失败（routePathUrl 为空）
     */
    @Test
    public void testCreateQrCode_BuildRecordFailed() {
        // Arrange
        QrCodeCreateDTO dto = new QrCodeCreateDTO();
        dto.setBrandCode("JAG");
        dto.setUrlType("PDP");

        when(snowflake.nextIdStr()).thenReturn("12345");

        try (MockedStatic<BrandToUrlEum> mockedStatic = mockStatic(BrandToUrlEum.class)) {
            mockedStatic.when(() -> BrandToUrlEum.getUrlByBrandCodeUrlType("JAG", "PDP"))
                    .thenReturn(null); // 返回 null 导致构建失败

            // Act
            CommonResult<byte[]> result = shortLinkCreateService.createQrCode(dto);

            // Assert
            assertNotNull(result);
            assertArrayEquals(new byte[0], result.getData());
            verify(miniCodeGenerateRepository, never()).saveOrUpdate(any());
        }
    }

    /**
     * TC05: 微信调用成功
     */
    @Test
    public void testCreateQrCode_WeChatSuccess() {
        // Arrange
        QrCodeCreateDTO dto = new QrCodeCreateDTO();
        dto.setBrandCode("JAG");
        dto.setUrlType("PDP");
        dto.setProductCode("PC123");
        dto.setProductName("Test Product");
        dto.setParam1("P1");
        dto.setParam2("P2");
        dto.setParam3("P3");
        dto.setParam4("P4");
        dto.setParam5("P5");

        String recordCode = "12345";
        when(snowflake.nextIdStr()).thenReturn(recordCode);

        MiniCodeConfigDO configDO = MiniCodeConfigDO.builder()
                .id(1L)
                .recordCode(recordCode)
                .build();
        when(miniCodeConfigRepository.saveOrUpdate(any(MiniCodeConfigDO.class))).thenReturn(true);

        String routePathUrl = "mockUrl";
        try (MockedStatic<BrandToUrlEum> mockedStatic = mockStatic(BrandToUrlEum.class)) {
            mockedStatic.when(() -> BrandToUrlEum.getUrlByBrandCodeUrlType("JAG", "PDP"))
                    .thenReturn(routePathUrl);

            MiniCodeGenerateRecordDO recordDO = MiniCodeGenerateRecordDO.builder()
                    .configId(1L)
                    .recordCode(recordCode)
                    .routePathUrl(routePathUrl)
                    .build();
            String weChatToken = "token";

            WeChatQrCodeResp resp = new WeChatQrCodeResp();
            resp.setData("mockData".getBytes());

            // Act
            CommonResult<byte[]> result = shortLinkCreateService.createQrCode(dto);

            // Assert
            assertNotNull(result);
        }
    }

    /**
     * TC06: 微信调用失败
     */
    @Test
    public void testCreateQrCode_WeChatFailure() {
        // Arrange
        QrCodeCreateDTO dto = new QrCodeCreateDTO();
        dto.setBrandCode("JAG");
        dto.setUrlType("PDP");

        when(snowflake.nextIdStr()).thenReturn("12345");

        MiniCodeConfigDO configDO = MiniCodeConfigDO.builder()
                .id(1L)
                .recordCode("12345")
                .build();
        when(miniCodeConfigRepository.saveOrUpdate(any(MiniCodeConfigDO.class))).thenReturn(true);

        String routePathUrl = "mockUrl";
        try (MockedStatic<BrandToUrlEum> mockedStatic = mockStatic(BrandToUrlEum.class)) {
            mockedStatic.when(() -> BrandToUrlEum.getUrlByBrandCodeUrlType("JAG", "PDP"))
                    .thenReturn(routePathUrl);

            MiniCodeGenerateRecordDO recordDO = MiniCodeGenerateRecordDO.builder()
                    .configId(1L)
                    .recordCode("12345")
                    .routePathUrl(routePathUrl)
                    .build();

            String weChatToken = "token";

            WeChatQrCodeResp resp = new WeChatQrCodeResp();
            resp.setErrorCode("4001");
            resp.setErrorMsg("Token invalid");
            // Act
            CommonResult<byte[]> result = shortLinkCreateService.createQrCode(dto);

            // Assert
            assertNotNull(result);
        }
    }


    @Test
    public void testGetCreateQrCodeList_WhenDataEmpty_ReturnsEmptyPageResult() {
        // Arrange
        ShortLinkCreateListDTO createListDTO = new ShortLinkCreateListDTO();
        createListDTO.setPageNo(1);
        createListDTO.setPageSize(10);

        Page<MiniCodeConfigDO> emptyPage = new Page<>();
        emptyPage.setRecords(new ArrayList<>());
        emptyPage.setTotal(0L);

        when(miniCodeConfigRepository.pageList(createListDTO)).thenReturn(emptyPage);

        // Act
        PageResult<QrCodeCreateVO> result = shortLinkCreateService.getCreateQrCodeList(createListDTO);

        // Assert
        assertNotNull(result);
        assertTrue(result.getList().isEmpty());
        assertEquals(0L, result.getTotal().longValue());
        verify(miniCodeConfigRepository).pageList(createListDTO);
    }

    @Test
    public void testGetCreateQrCodeList_WhenDataExists_ReturnsConvertedVOs() {
        // Arrange
        ShortLinkCreateListDTO createListDTO = new ShortLinkCreateListDTO();
        createListDTO.setPageNo(2);
        createListDTO.setPageSize(5);

        LocalDateTime createdTime = LocalDateTime.of(2023, 10, 5, 12, 30, 45);

        MiniCodeConfigDO configDO = new MiniCodeConfigDO();
        configDO.setId(1L);
        configDO.setWmpBrand("JAG");
        configDO.setRoutePageType("PDP");
        configDO.setProductNameParam("Product1");
        configDO.setParam1("val1");
        configDO.setParam2(""); // 空字符串
        configDO.setParam3(null); // null 值
        configDO.setCreatedBy("admin");
        configDO.setCreatedTime(createdTime);

        Page<MiniCodeConfigDO> page = new Page<>();
        page.setRecords(Arrays.asList(configDO));
        page.setTotal(1L);

        when(miniCodeConfigRepository.pageList(createListDTO)).thenReturn(page);

        try (MockedStatic<BrandToUrlEum> brandMockedStatic = mockStatic(BrandToUrlEum.class)) {
            brandMockedStatic.when(() -> BrandToUrlEum.getMiniProgramNameByBrandCode("JAG")).thenReturn("捷豹小程序");
            brandMockedStatic.when(() -> BrandToUrlEum.getUrlNameByUrlType("PDP")).thenReturn("商品详情页");

            // Act
            PageResult<QrCodeCreateVO> result = shortLinkCreateService.getCreateQrCodeList(createListDTO);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getList().size());
            QrCodeCreateVO vo = result.getList().get(0);
            assertEquals("admin", vo.getOperator());
            assertEquals("2023/10/05 12:30:45", vo.getCreateTime());
            assertEquals("捷豹小程序，商品详情页，Product1，p1=val1", vo.getConfigContent());
            assertEquals(1L, result.getTotal().longValue());
            verify(miniCodeConfigRepository).pageList(createListDTO);
        }
    }

    /**
     * TC01: createDTO 为 null，返回 true
     */
    @Test
    public void testCheckQrCodeCreateParam_NullDTO_ReturnsTrue() {
        assertTrue(shortLinkCreateService.checkQrCodeCreateParam(null));
    }

    /**
     * TC02: 所有参数均合法，返回 true
     */
    @Test
    public void testCheckQrCodeCreateParam_AllParamsValid_ReturnsTrue() {
        QrCodeCreateDTO dto = new QrCodeCreateDTO();
        dto.setParam1("valid123");
        dto.setParam2("valid");
        dto.setParam3("123");
        dto.setParam4(null); // 空参数不检查
        dto.setParam5("valid5");
        assertTrue(shortLinkCreateService.checkQrCodeCreateParam(dto));
    }

    /**
     * TC03: param1 非法，返回 false
     */
    @Test
    public void testCheckQrCodeCreateParam_Param1Invalid_ReturnsFalse() {
        QrCodeCreateDTO dto = new QrCodeCreateDTO();
        dto.setParam1("invalid@123");
        dto.setParam2("valid");
        dto.setParam3("valid");
        dto.setParam4("valid");
        dto.setParam5("valid5");
        assertFalse(shortLinkCreateService.checkQrCodeCreateParam(dto));
    }

    /**
     * TC04: param2 非法，返回 false
     */
    @Test
    public void testCheckQrCodeCreateParam_Param2Invalid_ReturnsFalse() {
        QrCodeCreateDTO dto = new QrCodeCreateDTO();
        dto.setParam1("valid");
        dto.setParam2("invalid!234");
        dto.setParam3("valid");
        dto.setParam4("valid");
        dto.setParam5("valid5");
        assertFalse(shortLinkCreateService.checkQrCodeCreateParam(dto));
    }

    /**
     * TC05: param3 非法，返回 false
     */
    @Test
    public void testCheckQrCodeCreateParam_Param3Invalid_ReturnsFalse() {
        QrCodeCreateDTO dto = new QrCodeCreateDTO();
        dto.setParam1("valid");
        dto.setParam2("valid");
        dto.setParam3("invalid#345");
        dto.setParam4("valid");
        dto.setParam5("valid5");
        assertFalse(shortLinkCreateService.checkQrCodeCreateParam(dto));
    }

    /**
     * TC06: param4 非法，返回 false
     */
    @Test
    public void testCheckQrCodeCreateParam_Param4Invalid_ReturnsFalse() {
        QrCodeCreateDTO dto = new QrCodeCreateDTO();
        dto.setParam1("valid");
        dto.setParam2("valid");
        dto.setParam3("valid");
        dto.setParam4("invalid$456");
        dto.setParam5("valid5");
        assertFalse(shortLinkCreateService.checkQrCodeCreateParam(dto));
    }

    /**
     * TC07: param5 非法，返回 false
     */
    @Test
    public void testCheckQrCodeCreateParam_Param5Invalid_ReturnsFalse() {
        QrCodeCreateDTO dto = new QrCodeCreateDTO();
        dto.setParam5("invalid%567");
        assertFalse(shortLinkCreateService.checkQrCodeCreateParam(dto));
    }

    /**
     * TC08: 多个参数非法，返回 false
     */
    @Test
    public void testCheckQrCodeCreateParam_MultipleParamsInvalid_ReturnsFalse() {
        QrCodeCreateDTO dto = new QrCodeCreateDTO();
        dto.setParam1("invalid@");
        dto.setParam5("invalid%");
        assertFalse(shortLinkCreateService.checkQrCodeCreateParam(dto));
    }

    /**
     * TC09: param5 为 null，返回 true
     */
    @Test
    public void testCheckQrCodeCreateParam_Param5IsNull_ReturnsTrue() {
        QrCodeCreateDTO dto = new QrCodeCreateDTO();
        dto.setParam5(null);
        assertTrue(shortLinkCreateService.checkQrCodeCreateParam(dto));
    }

    /**
     * TC10: param5 为空字符串，返回 true
     */
    @Test
    public void testCheckQrCodeCreateParam_Param5IsEmptyString_ReturnsTrue() {
        QrCodeCreateDTO dto = new QrCodeCreateDTO();
        dto.setParam5("");
        assertTrue(shortLinkCreateService.checkQrCodeCreateParam(dto));
    }

    @Test
    public void testIsAlphanumeric_Null_ReturnsTrue() {
        // Arrange + Act
        boolean result = shortLinkCreateService.isAlphanumeric(null);
        // Assert
        assertTrue(result);
    }

    @Test
    public void testIsAlphanumeric_Empty_ReturnsTrue() {
        // Arrange + Act
        boolean result = shortLinkCreateService.isAlphanumeric("");
        // Assert
        assertTrue(result);
    }

    @Test
    public void testIsAlphanumeric_AllLetters_ReturnsTrue() {
        // Arrange + Act
        boolean result = shortLinkCreateService.isAlphanumeric("abcXYZ");
        // Assert
        assertTrue(result);
    }

    @Test
    public void testIsAlphanumeric_AllDigits_ReturnsTrue() {
        // Arrange + Act
        boolean result = shortLinkCreateService.isAlphanumeric("123456");
        // Assert
        assertTrue(result);
    }

    @Test
    public void testIsAlphanumeric_LettersAndDigits_ReturnsTrue() {
        // Arrange + Act
        boolean result = shortLinkCreateService.isAlphanumeric("a1b2c3");
        // Assert
        assertTrue(result);
    }

    @Test
    public void testIsAlphanumeric_WithSpecialChar_ReturnsFalse() {
        // Arrange + Act
        boolean result = shortLinkCreateService.isAlphanumeric("a@b");
        // Assert
        assertFalse(result);
    }

    @Test
    public void testIsAlphanumeric_WithSpace_ReturnsFalse() {
        // Arrange + Act
        boolean result = shortLinkCreateService.isAlphanumeric("ab c");
        // Assert
        assertFalse(result);
    }

    @Test
    public void testBuildQrCodeCreateVOList_withNullInput_returnsEmptyList() {
        // Arrange
        List<MiniCodeConfigDO> configDOList = null;

        // Act
        List<QrCodeCreateVO> result = shortLinkCreateService.buildQrCodeCreateVOList(configDOList);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBuildQrCodeCreateVOList_withEmptyList_returnsEmptyList() {
        // Arrange
        List<MiniCodeConfigDO> configDOList = new ArrayList<>();

        // Act
        List<QrCodeCreateVO> result = shortLinkCreateService.buildQrCodeCreateVOList(configDOList);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBuildQrCodeCreateVOList_withSingleElement_returnsPopulatedList() {
        // Arrange
        LocalDateTime testTime = LocalDateTime.of(2023, 10, 5, 14, 30, 0);
        String brandCode = "JAG";
        String routePageType = "PDP";
        String createdBy = "testUser";

        MiniCodeConfigDO configDO = new MiniCodeConfigDO();
        configDO.setCreatedTime(testTime);
        configDO.setCreatedBy(createdBy);
        configDO.setWmpBrand(brandCode);
        configDO.setRoutePageType(routePageType);

        List<MiniCodeConfigDO> configDOList = List.of(configDO);

        try (MockedStatic<BrandToUrlEum> mockedBrandStatic = mockStatic(BrandToUrlEum.class)) {
            String expectedMiniProgramName = "捷豹小程序";
            String expectedUrlName = "商品详情页";

            mockedBrandStatic.when(() -> BrandToUrlEum.getMiniProgramNameByBrandCode(brandCode))
                    .thenReturn(expectedMiniProgramName);
            mockedBrandStatic.when(() -> BrandToUrlEum.getUrlNameByUrlType(routePageType))
                    .thenReturn(expectedUrlName);

            // Act
            List<QrCodeCreateVO> result = shortLinkCreateService.buildQrCodeCreateVOList(configDOList);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.size());

            QrCodeCreateVO vo = result.get(0);
            String expectedTimeStr = TimeFormatUtil.timeToStringByFormat(testTime, TimeFormatUtil.formatter_6);
            assertEquals(expectedTimeStr, vo.getCreateTime());
            assertEquals(createdBy, vo.getOperator());
            String expectedContent = expectedMiniProgramName + "，" + expectedUrlName;
            assertEquals(expectedContent, vo.getConfigContent());
        }
    }

    @Test
    public void testBuildQrCodeCreateVOList_withMultipleElements_returnsAllConverted() {
        // Arrange
        LocalDateTime time1 = LocalDateTime.of(2023, 10, 5, 14, 30, 0);
        LocalDateTime time2 = LocalDateTime.of(2023, 10, 6, 15, 45, 0);

        MiniCodeConfigDO config1 = new MiniCodeConfigDO();
        config1.setCreatedTime(time1);
        config1.setCreatedBy("user1");
        config1.setWmpBrand("JAG");
        config1.setRoutePageType("PDP");

        MiniCodeConfigDO config2 = new MiniCodeConfigDO();
        config2.setCreatedTime(time2);
        config2.setCreatedBy("user2");
        config2.setWmpBrand("LAN");
        config2.setRoutePageType("PLP");

        List<MiniCodeConfigDO> configDOList = List.of(config1, config2);

        try (MockedStatic<BrandToUrlEum> mockedBrandStatic = mockStatic(BrandToUrlEum.class)) {
            mockedBrandStatic.when(() -> BrandToUrlEum.getMiniProgramNameByBrandCode("JAG"))
                    .thenReturn("捷豹小程序");
            mockedBrandStatic.when(() -> BrandToUrlEum.getUrlNameByUrlType("PDP"))
                    .thenReturn("商品详情页");
            mockedBrandStatic.when(() -> BrandToUrlEum.getMiniProgramNameByBrandCode("LAN"))
                    .thenReturn("路虎小程序");
            mockedBrandStatic.when(() -> BrandToUrlEum.getUrlNameByUrlType("PLP"))
                    .thenReturn("商品列表页");

            // Act
            List<QrCodeCreateVO> result = shortLinkCreateService.buildQrCodeCreateVOList(configDOList);

            // Assert
            assertNotNull(result);
            assertEquals(2, result.size());

            QrCodeCreateVO vo1 = result.get(0);
            assertEquals(TimeFormatUtil.timeToStringByFormat(time1, TimeFormatUtil.formatter_6), vo1.getCreateTime());
            assertEquals("user1", vo1.getOperator());
            assertEquals("捷豹小程序，商品详情页", vo1.getConfigContent());

            QrCodeCreateVO vo2 = result.get(1);
            assertEquals(TimeFormatUtil.timeToStringByFormat(time2, TimeFormatUtil.formatter_6), vo2.getCreateTime());
            assertEquals("user2", vo2.getOperator());
            assertEquals("路虎小程序，商品列表页", vo2.getConfigContent());
        }
    }



    /**
     * 测试用例：configDO 非空
     * 验证 createTime、operator、configContent 是否正确赋值
     */


    @Test
    public void testGetConfigContent_NullConfig() {
        // TC01: configDO 为 null
        ShortLinkCreateServiceImpl service = new ShortLinkCreateServiceImpl();
        String result = service.getConfigContent(null);
        assertEquals("", result);
    }

    @Test
    public void testGetConfigContent_BasicFields() {
        // TC02: 仅基础字段
        ShortLinkCreateServiceImpl service = new ShortLinkCreateServiceImpl();

        MiniCodeConfigDO config = new MiniCodeConfigDO();
        config.setWmpBrand("JAG");

        try (MockedStatic<BrandToUrlEum> mockedStatic = Mockito.mockStatic(BrandToUrlEum.class)) {
            mockedStatic.when(() -> BrandToUrlEum.getMiniProgramNameByBrandCode("JAG"))
                    .thenReturn("捷豹小程序");

            String result = service.getConfigContent(config);
            assertEquals("捷豹小程序", result);
        }
    }

    @Test
    public void testGetConfigContent_MixedFields() {
        // TC03-TC04: 混合字段组合
        ShortLinkCreateServiceImpl service = new ShortLinkCreateServiceImpl();

        MiniCodeConfigDO config = new MiniCodeConfigDO();
        config.setWmpBrand("LAN");
        config.setRoutePageType("PDP");
        config.setProductNameParam("车模");
        config.setParam1("v1");
        config.setParam5("v5");

        try (MockedStatic<BrandToUrlEum> mockedStatic = Mockito.mockStatic(BrandToUrlEum.class)) {
            mockedStatic.when(() -> BrandToUrlEum.getMiniProgramNameByBrandCode("LAN"))
                    .thenReturn("路虎小程序");
            mockedStatic.when(() -> BrandToUrlEum.getUrlNameByUrlType("PDP"))
                    .thenReturn("商品详情页");

            String result = service.getConfigContent(config);
            assertEquals("路虎小程序，商品详情页，车模，p1=v1，p5=v5", result);
        }
    }

    @Test
    public void testGetConfigContent_AllFields() {
        // TC05: 所有字段非空
        ShortLinkCreateServiceImpl service = new ShortLinkCreateServiceImpl();

        MiniCodeConfigDO config = new MiniCodeConfigDO();
        config.setWmpBrand("JAG");
        config.setRoutePageType("PLP");
        config.setProductNameParam("配件");
        config.setParam1("v1");
        config.setParam2("v2");
        config.setParam3("v3");
        config.setParam4("v4");
        config.setParam5("v5");

        try (MockedStatic<BrandToUrlEum> mockedStatic = Mockito.mockStatic(BrandToUrlEum.class)) {
            mockedStatic.when(() -> BrandToUrlEum.getMiniProgramNameByBrandCode("JAG"))
                    .thenReturn("捷豹小程序");
            mockedStatic.when(() -> BrandToUrlEum.getUrlNameByUrlType("PLP"))
                    .thenReturn("商品列表页");

            String result = service.getConfigContent(config);
            assertEquals("捷豹小程序，商品列表页，配件，p1=v1，p2=v2，p3=v3，p4=v4，p5=v5", result);
        }
    }


    /**
     * 测试：generateRecordDO 为 null 时不更新
     */
    @Test
    public void testUpdateMiniCodeGenerateRecord_withNullRecord_shouldReturnWithoutUpdate() {
        // Arrange
        WeChatQrCodeResp resp = mock(WeChatQrCodeResp.class);

        // Act
        shortLinkCreateService.updateMiniCodeGenerateRecord(null, resp);

        // Assert
        verify(miniCodeGenerateRepository, never()).updateById(any(MiniCodeGenerateRecordDO.class));
    }

    /**
     * 测试：resp 为 null 时不更新
     */
    @Test
    public void testUpdateMiniCodeGenerateRecord_withNullResp_shouldReturnWithoutUpdate() {
        // Arrange
        MiniCodeGenerateRecordDO record = mock(MiniCodeGenerateRecordDO.class);

        // Act
        shortLinkCreateService.updateMiniCodeGenerateRecord(record, null);

        // Assert
        verify(miniCodeGenerateRepository, never()).updateById(any(MiniCodeGenerateRecordDO.class));
    }

    /**
     * 测试：resp.errorCode 和 errorMsg 均为空时，result=0 且错误信息清空
     */
    @Test
    public void testUpdateMiniCodeGenerateRecord_withEmptyErrorInfo_shouldSetResultZero() {
        // Arrange
        MiniCodeGenerateRecordDO record = new MiniCodeGenerateRecordDO();
        record.setResult(1); // 初始值设为非0

        WeChatQrCodeResp resp = mock(WeChatQrCodeResp.class);
        when(resp.getErrorCode()).thenReturn("");

        // Act
        shortLinkCreateService.updateMiniCodeGenerateRecord(record, resp);

        // Assert
        assertEquals(Integer.valueOf(0), record.getResult());
        assertNull(record.getErrorCode());
        assertNull(record.getErrorMessage());
        verify(miniCodeGenerateRepository, times(1)).updateById(eq(record));
    }

    /**
     * 测试：resp.errorCode 非空时，result=1 且设置错误信息
     */
    @Test
    public void testUpdateMiniCodeGenerateRecord_withErrorCode_shouldSetResultOneAndErrorInfo() {
        // Arrange
        MiniCodeGenerateRecordDO record = new MiniCodeGenerateRecordDO();
        record.setResult(0); // 初始值设为0

        WeChatQrCodeResp resp = mock(WeChatQrCodeResp.class);
        when(resp.getErrorCode()).thenReturn("ERR123");
        when(resp.getErrorMsg()).thenReturn("Error Message");

        // Act
        shortLinkCreateService.updateMiniCodeGenerateRecord(record, resp);

        // Assert
        assertEquals(Integer.valueOf(1), record.getResult());
        assertEquals("ERR123", record.getErrorCode());
        assertEquals("Error Message", record.getErrorMessage());
        verify(miniCodeGenerateRepository, times(1)).updateById(eq(record));
    }

    /**
     * 测试：resp.errorCode 为空白字符串时，result=0 且错误信息清空
     */
    @Test
    public void testUpdateMiniCodeGenerateRecord_withBlankErrorCode_shouldSetResultZero() {
        // Arrange
        MiniCodeGenerateRecordDO record = new MiniCodeGenerateRecordDO();
        record.setResult(1); // 初始值设为1

        WeChatQrCodeResp resp = mock(WeChatQrCodeResp.class);
        when(resp.getErrorCode()).thenReturn(" "); // 空白字符串

        // Act
        shortLinkCreateService.updateMiniCodeGenerateRecord(record, resp);

        // Assert
        assertEquals(Integer.valueOf(0), record.getResult());
        assertNull(record.getErrorCode());
        assertNull(record.getErrorMessage());
        verify(miniCodeGenerateRepository, times(1)).updateById(eq(record));
    }

    @Test
    public void testBuildMiniCodeGenerateRecordDO_WithValidParams_ShouldReturnRecord() {
        // Arrange
        String routePathUrl = "testUrl";
        String recordCode = "code123";
        Long configId = 1L;

        // Act
        MiniCodeGenerateRecordDO result = shortLinkCreateService.buildMiniCodeGenerateRecordDO(routePathUrl, recordCode, configId);

        // Assert
        assertNotNull(result);
        assertEquals(routePathUrl, result.getRoutePathUrl());
        assertEquals(recordCode, result.getRecordCode());
        assertEquals(configId, result.getConfigId());
    }

    @Test
    public void testBuildMiniCodeGenerateRecordDO_WithEmptyRoutePathUrl_ShouldReturnNull() {
        // Arrange & Act
        MiniCodeGenerateRecordDO result = shortLinkCreateService.buildMiniCodeGenerateRecordDO("", "code123", 1L);

        // Assert
        assertNull(result);
    }

    @Test
    public void testBuildMiniCodeGenerateRecordDO_WithEmptyRecordCode_ShouldReturnNull() {
        // Arrange & Act
        MiniCodeGenerateRecordDO result = shortLinkCreateService.buildMiniCodeGenerateRecordDO("testUrl", "", 1L);

        // Assert
        assertNull(result);
    }

    @Test
    public void testBuildMiniCodeGenerateRecordDO_WithNullConfigId_ShouldReturnNull() {
        // Arrange & Act
        MiniCodeGenerateRecordDO result = shortLinkCreateService.buildMiniCodeGenerateRecordDO("testUrl", "code123", null);

        // Assert
        assertNull(result);
    }

    @Test
    public void testBuildMiniCodeGenerateRecordDO_WithAllEmptyParams_ShouldReturnNull() {
        // Arrange & Act
        MiniCodeGenerateRecordDO result = shortLinkCreateService.buildMiniCodeGenerateRecordDO("", "", null);

        // Assert
        assertNull(result);
    }


    @Test
    public void testBuildMiniCodeConfigDO_WhenParamsValid_ShouldMapFieldsCorrectly() {
        // Arrange
        QrCodeCreateDTO dto = new QrCodeCreateDTO();
        dto.setBrandCode("LAN");
        dto.setUrlType("PLP");
        dto.setProductCode("PROD002");
        dto.setProductName("Another Product");
        dto.setParam1("value1");
        dto.setParam2("value2");
        dto.setParam3("value3");
        dto.setParam4("value4");
        dto.setParam5("value5");
        String recordCode = "recordCode 应正确映射";

        // Act
        MiniCodeConfigDO result = shortLinkCreateService.buildMiniCodeConfigDO(dto, recordCode);

        // Assert
        assertEquals(recordCode, result.getRecordCode(), "recordCode 应正确映射");
        assertEquals(dto.getBrandCode(), result.getWmpBrand(), "LAN");
        assertEquals(dto.getUrlType(), result.getRoutePageType(), "PLP");
        assertEquals(dto.getProductCode(), result.getProductCodeParam(), "PROD002");
        assertEquals(dto.getProductName(), result.getProductNameParam(), "Another Product");
        assertEquals(dto.getParam1(), result.getParam1(), "value1");
        assertEquals(dto.getParam2(), result.getParam2(), "value2");
        assertEquals(dto.getParam3(), result.getParam3(), "value3");
        assertEquals(dto.getParam4(), result.getParam4(), "value4");
        assertEquals(dto.getParam5(), result.getParam5(), "value5");
    }


}
