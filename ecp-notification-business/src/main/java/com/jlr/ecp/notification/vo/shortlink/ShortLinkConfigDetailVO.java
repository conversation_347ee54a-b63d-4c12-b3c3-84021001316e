package com.jlr.ecp.notification.vo.shortlink;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Schema(description = "短链配置VO")
@Data
public class ShortLinkConfigDetailVO implements Serializable {
    @Schema(description = "configId", requiredMode = Schema.RequiredMode.REQUIRED, example = "configId")
    private Long configId;

    @Schema(description = "remoteService短链商品SPU", requiredMode = Schema.RequiredMode.REQUIRED, example = "skuxxx1")
    private String remoteServiceSpu ;

    @Schema(description = "remoteService短链商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "spuNamexxxxxx")
    private String remoteServiceSpuName ;

    @Schema(description = "PIVIService短链商品SPU", requiredMode = Schema.RequiredMode.REQUIRED, example = "spuxxx1")
    private String piviServiceSpu ;

    @Schema(description = "PIVIService短链商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "spuNamexxxxxx")
    private String piviServiceSpuName ;

    @Schema(description = "品牌表示", requiredMode = Schema.RequiredMode.REQUIRED,example = "LAN:路虎，JAG：捷豹")
    private String brandCode;
}
