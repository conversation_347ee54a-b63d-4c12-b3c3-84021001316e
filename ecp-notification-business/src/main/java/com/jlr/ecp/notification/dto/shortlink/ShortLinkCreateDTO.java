package com.jlr.ecp.notification.dto.shortlink;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;

@Schema(description = "短链生成的DTO")
@Data
public class ShortLinkCreateDTO {

    @NotBlank(message = "品牌标识不能为空")
    @Schema(description = "品牌表示", requiredMode = Schema.RequiredMode.REQUIRED,example = "LAN:路虎，JAG：捷豹")
    private String brandCode;

    @NotBlank(message = "url类型不能为空")
    @Schema(description = "url类型", requiredMode = Schema.RequiredMode.REQUIRED,example = "PDP:商品详情页，PLP：商品列表页")
    private String urlType;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED,example = "Remote远程车控")
    private String productName;

    @Schema(description = "跳转路径需要的参数", requiredMode = Schema.RequiredMode.REQUIRED,example ="{\n" +
            "\t\"spuId\": \"BS00936763E040E4B0EC7F10756859\"\n" +
            "}")
    private Map<String, Object> params;
}
