apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  annotations:
    #强制同步secretManager的时间戳，可配置
    force-sync: "1661306064"
  name: ecp-notification-service-externalsecret
  #k8s的namespace，需要检查一下
#  namespace: jlr-ecp-system-dev
#  namespace: external-secrets
spec:
#  data:
#    - secretKey: db_user
#      remoteRef:
#        key: test/jlr_ecp
#        property: db_user
#  data:
#    - secretKey: trust_store_password
#      remoteRef:
#        key: dev/jlr_ecp
#        property: trust_store_password
  dataFrom:
  - extract:
      conversionStrategy: Default
      #对应aws上创建的secret manager的name
      key: dev/jlr_ecp
  refreshInterval: 1h
  #关联secretStore配置
  secretStoreRef:
    kind: SecretStrore
    name: secret-store
  target:
    creationPolicy: Orphan
    deletionPolicy: Retain
    name: ecp-notification-service-externalsecret