package com.jlr.ecp.notification.kafka.send;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *  发送第三方代理商返回的结果
 *
 * <AUTHOR>
 * */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SendSmsResp {
    /**
     * 消息Id
     * */
    private String msgid;

    /**
     * 结果的code 0表示成功，其他都失败
     * */
    private String result;

    /**
     * 结果描述
     * */
    private String desc;

    /**
     * 失败号码
     * */
    private String failPhones;
}
