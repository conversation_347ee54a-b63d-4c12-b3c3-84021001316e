package com.jlr.ecp.notification.vo.task;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

// VO对象定义
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Schema(description = "手动任务编辑详情")
public class ManualTaskHistoryDetailVO {
    @Schema(description = "修改时间", required = true)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime operateTime;

    @Schema(description = "操作人")
    private String operateUser;

    @Schema(description = "日志ID")
    private Long id;

    @Schema(description = "修改模块")
    private String modifyModule;

    @Schema(description = "修改字段数量")
    private Integer modifyFieldCount;

    @Schema(description = "修改前字段值（JSON格式）")
    private String modifyFieldOldValue;

    @Schema(description = "修改后字段值（JSON格式）")
    private String modifyFieldNewValue;

    // 如果需要展示字段级变更明细，可以添加以下结构
    /*
    @Schema(description = "字段变更明细")
    private List<FieldChange> changes;

    @Data
    public static class FieldChange {
        @Schema(description = "字段名称")
        private String fieldName;
        @Schema(description = "旧值")
        private String oldValue;
        @Schema(description = "新值")
        private String newValue;
    }
    */
}