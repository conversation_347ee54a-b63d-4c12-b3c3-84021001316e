package com.jlr.ecp.notification.dto.crc;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Schema(description = "阅读CRC未读的消息")
public class ReadCrcMessageDTO {

    @Schema(description = "jirId 全局唯一id")
    @NotBlank
    private String jlrid;

    @Schema(description = "业务线编码")
    private String businessCode;
}
