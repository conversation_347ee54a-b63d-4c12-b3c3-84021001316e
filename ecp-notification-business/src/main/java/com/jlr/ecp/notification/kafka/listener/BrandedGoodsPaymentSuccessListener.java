package com.jlr.ecp.notification.kafka.listener;

import com.jlr.ecp.notification.kafka.message.BrandedGoodsPaymentSuccessMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 精选好物支付成功通知监听器
 */
@Component
@Slf4j
public class BrandedGoodsPaymentSuccessListener extends BaseNotificationListener<BrandedGoodsPaymentSuccessMessage> {
    
    @KafkaListener(topics = "${branded-goods.kafka.topics.payment-success}", 
                  groupId = "${branded-goods.kafka.groups.payment-success}", 
                  batch = "true",
                  properties = "max.poll.records:100")
    public void listen(List<ConsumerRecord<String, String>> records) {
        log.info("精选好物支付成功通知消息，records:{}", records);
        processMessages(records, BrandedGoodsPaymentSuccessMessage.class);
    }
} 