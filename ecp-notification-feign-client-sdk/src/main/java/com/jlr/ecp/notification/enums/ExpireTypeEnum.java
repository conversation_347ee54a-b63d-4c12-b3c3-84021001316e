package com.jlr.ecp.notification.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ExpireTypeEnum {
    REMOTE_THREE_DAY_EXPIRE(1, "remote-service-expire-code-three-day", "Remote服务到期前3天通知"),
    REMOTE_THIRTY_DAY_EXPIRE(2, "remote-service-expire-code-thirty-day", "Remote服务到期前30天通知"),
    PIVI_THREE_DAY_EXPIRE(3, "pivi-service-expire-code-three-day", "PIVI服务到期前3天通知"),
    PIVI_THIRTY_DAY_EXPIRE(4, "pivi-service-expire-code-thirty-day", "PIVI服务到期前30天通知"),
    PIVI_NINETY_TWO_DAY_EXPIRE(5, "pivi-service-expire-code-ninety-two-day", "PIVI服务到期前92天通知"),
    REMOTE_THIRTY_DAY_EXPIRE_AFTER(6, "remote-service-expire-code-thirty-day-after", "Remote服务到期30天后通知"),
    PIVI_THIRTY_DAY_EXPIRE_AFTER(7, "pivi-service-expire-code-thirty-day-after", "PIVI服务到期30天后通知"),
    PIVI_THREE_DAY_EXPIRE_UNNAMEED(8, "pivi-service-expire-code-three-day-unnamed", "PIVI服务到期未实名3天通知"),
    PIVI_THRITY_DAY_EXPIRE_UNNAMEED(9, "pivi-service-expire-code-thirty-day-unnamed", "PIVI服务到期未实名30天通知"),
    PIVI_NINETY_TWO_DAY_EXPIRE_UNNAMEED(10, "pivi-service-expire-code-ninety-two-day-unnamed", "PIVI服务到期未实名92天通知");



    private final Integer type;

    private final String taskCode;

    private final String taskName;

    public static boolean isRemoteService(Integer type) {
        return ExpireTypeEnum.REMOTE_THREE_DAY_EXPIRE.getType().equals(type)
                || ExpireTypeEnum.REMOTE_THIRTY_DAY_EXPIRE.getType().equals(type)
                || ExpireTypeEnum.REMOTE_THIRTY_DAY_EXPIRE_AFTER.getType().equals(type);
    }

    public static boolean isPIVIService(Integer type) {
        return ExpireTypeEnum.PIVI_THREE_DAY_EXPIRE.getType().equals(type)
                || ExpireTypeEnum.PIVI_THIRTY_DAY_EXPIRE.getType().equals(type)
                || ExpireTypeEnum.PIVI_NINETY_TWO_DAY_EXPIRE.getType().equals(type)
                || ExpireTypeEnum.PIVI_THIRTY_DAY_EXPIRE_AFTER.getType().equals(type)
                || ExpireTypeEnum.PIVI_THREE_DAY_EXPIRE_UNNAMEED.getType().equals(type)
                || ExpireTypeEnum.PIVI_THRITY_DAY_EXPIRE_UNNAMEED.getType().equals(type)
                || ExpireTypeEnum.PIVI_NINETY_TWO_DAY_EXPIRE_UNNAMEED.getType().equals(type);
    }

    public static String getTaskCodeByType(Integer expireType) {
        for (ExpireTypeEnum expireTypeEnum : ExpireTypeEnum.values()) {
            if (expireTypeEnum.getType().equals(expireType)) {
                return expireTypeEnum.getTaskCode();
            }
        }
        return "";
    }
}

