package com.jlr.ecp.notification.api.jaguarlandover;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.api.dto.ShortLinkReqDto;
import com.jlr.ecp.notification.service.ShortLinkService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
public class ShortLinkImpl implements ShorLinkAPI{

    @Resource
    ShortLinkService shortLinkService;


    @Override
    public CommonResult<String> genShortLink(ShortLinkReqDto shortLinkReqDto) {
        return shortLinkService.genShortLink(shortLinkReqDto.getPath());
    }

    @Override
    public CommonResult<String> genJaguarLink(ShortLinkReqDto shortLinkReqDto) {
        return shortLinkService.genJaguarLink(shortLinkReqDto.getPath(), shortLinkReqDto.getQuery());
    }
}
