package com.jlr.ecp.notification.dal.repository.mp;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.notification.dal.dataobject.mp.MpNoticeExpireSubscriptionDO;

import java.util.List;

public interface MpNoticeExpireSubscriptionRepository extends IService<MpNoticeExpireSubscriptionDO> {

    /**
     * 根据JLR ID查询到期订阅信息
     *
     * @param jlrId 经销商ID，用于查询特定的到期订阅记录
     * @param openId 微信公众号的openId
     * @return 返回查询到的到期订阅记录对象，如果没有找到则返回null
     */
    MpNoticeExpireSubscriptionDO queryExpireSubscribeByJlrAndOpenId(String jlrId, String openId);

    /**
     * 选择可以推送已过期的MP订阅信息
     * @return List<MpNoticeExpireSubscriptionDO> 返回已过期的MP消息列表
     */
    List<MpNoticeExpireSubscriptionDO> selectExpiredMpMessageAllId();


    /**
     * 根据ID列表查询已到期的会员通知订阅信息
     *
     * @param idList 会员ID列表，用于查询已到期的订阅信息
     * @return 返回已到期的会员通知订阅信息列表如果传入的ID列表为空，则返回空列表
     */
    List<MpNoticeExpireSubscriptionDO> queryExpiredMpByIdList(List<Long> idList);

    /**
     * 根据监理人ID列表查询已过期的公众号订阅通知
     *
     * @param jlrIdList ID列表，用于查询对应的公众号订阅通知
     * @return 返回符合条件的公众号订阅通知列表，如果输入列表为空，则返回空列表
     */
    List<MpNoticeExpireSubscriptionDO> queryMpExpiredListByJlrId(List<String> jlrIdList);
}
