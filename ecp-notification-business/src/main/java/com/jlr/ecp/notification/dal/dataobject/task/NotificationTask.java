package com.jlr.ecp.notification.dal.dataobject.task;

import com.baomidou.mybatisplus.annotation.*;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_notification_task")
public class NotificationTask extends BaseDO {
    /**
     * 主键id, 自增
     * */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务线编码
     * */
    @TableField("business_code")
    private String businessCode;

    /**
     * 任务编码，雪花算法
     */
    @TableField("task_code")
    private String taskCode;

    /**
     * 任务名称
     */
    @TableField("task_name")
    private String taskName;

    /**
     * 触发逻辑
     */
    @TableField("trigger_action")
    private String triggerAction;

    /**
     * 定时发送时间CRON;当发送类型为定时发送时的时间的CRON表达式
     */
    @TableField(value = "task_send_schedule_time", updateStrategy = FieldStrategy.IGNORED)
    private String taskSendScheduleTime;

    /**
     *  发送类型 1：实时发送 2：定时发送
     */
    @TableField(value = "task_send_type")
    private Integer taskSendType;

    /**
     * 	通知模板编码
     */
    @TableField(value = "message_template_code")
    private String messageTemplateCode;

    /**
     * 通知状态 0停用 1启用
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 是否是草稿 0否 1是
     */
    @TableField(value = "draft_version")
    private Integer draftVersion;


    @TableField(value = "message_file", updateStrategy = FieldStrategy.IGNORED)
    private String messageFile;

    /**
     * 启用时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime activateTime;

    /**
     * 停用时间
     * */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime deactivateTime;

    /**
     * 实时发送任务时间
     * */
    @TableField(value = "task_send_real_time", updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime taskSendRealTime;

    /**
     *   任务类型 1自动任务 2手动任务
     * */
    @TableField(value = "task_type")
    private Integer taskType;


    @TableField(value = "error_msg_file", updateStrategy = FieldStrategy.IGNORED)
    private String errorMsgFile;

    /**
     *  上传文件类型 1：手机号 2：VIN+手机号
     * */
    @TableField(value = "submit_file_type")
    private Integer submitFileType;

    /**
     *  品牌签名内容 1路虎中国 2捷豹路虎中国
     * */
    @TableField(value = "sign_brand_text")
    private Integer signBrandText;

    /**
     *  发送通道 1营销短信 2通知短信
     * */
    @TableField(value = "send_channel")
    private Integer sendChannel;

    /**
     *  自动通知任务类型，1：周期通知任务 2：事件触发任务
     * */
    @TableField(value = "auto_task_type")
    private Integer autoTaskType;

    /**
     * 租户号
     * */
    @TableField(value = "tenant_id")
    private Integer tenantId;
}
