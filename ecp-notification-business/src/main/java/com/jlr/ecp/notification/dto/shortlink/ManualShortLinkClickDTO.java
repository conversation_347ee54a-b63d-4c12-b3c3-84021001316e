package com.jlr.ecp.notification.dto.shortlink;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Schema(description = "手动短链点击率更新DTO")
@Data
public class ManualShortLinkClickDTO {
    @Schema(description = "短链唯一编码")
    @NotBlank
    private String urlCode;

    @Schema(description = "jlrId")
    private String jlrId;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "是否是第一次授权")
    private Boolean isFirstAuthorize;
}
