package com.jlr.ecp.notification.service.mp.impl;

import com.jlr.ecp.notification.dal.dataobject.mp.MpNoticeExpireSubscriptionDO;
import com.jlr.ecp.notification.dal.dataobject.mp.MpNoticeOrderSubscriptionDO;
import com.jlr.ecp.notification.dal.repository.mp.MpNoticeExpireSubscriptionRepository;
import com.jlr.ecp.notification.dal.repository.mp.MpNoticeOrderSubscriptionRepository;
import com.jlr.ecp.notification.dto.mp.MpTemplateMsgSubscriptionDTO;
import com.jlr.ecp.notification.enums.mp.MpMsgSubcribeEnum;
import com.jlr.ecp.notification.service.mp.MpTemplateMsgAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

@Service
@Slf4j
public class MpTemplateMsgAppServiceImpl implements MpTemplateMsgAppService {

    @Resource
    private MpNoticeOrderSubscriptionRepository mpNoticeOrderRepository;

    @Resource
    private MpNoticeExpireSubscriptionRepository mpNoticeExpireRepository;

    /**
     * 保存小程序模板消息订阅信息
     *
     * @param mpTemplateMsgDTO 小程序模板消息订阅信息的数据传输对象
     * @return 返回保存结果的字符串表示，通常用于调试或日志记录
     */
    @Override
    public String saveMpTemplateMsgSubscribe(MpTemplateMsgSubscriptionDTO mpTemplateMsgDTO) {
        log.info("保存小程序模板消息订阅信息, mpTemplateMsgDTO:{}", mpTemplateMsgDTO);
        if (MpMsgSubcribeEnum.NO_SUBSCRIBE.getCode().equals(mpTemplateMsgDTO.getOrderSuccessFlag())
                && MpMsgSubcribeEnum.NO_SUBSCRIBE.getCode().equals(mpTemplateMsgDTO.getOrderCancelFlag())) {
            log.info("保存小程序模板消息订阅信息, 订单取消订阅为未订阅，订单成功订阅为未订阅，不保存, mpTemplateMsgDTO:{}", mpTemplateMsgDTO);
        } else {
            saveMpMsgOrderSubscribe(mpTemplateMsgDTO);
        }
        if (MpMsgSubcribeEnum.NO_SUBSCRIBE.getCode().equals(mpTemplateMsgDTO.getServiceExpirationConsent())) {
            log.info("保存小程序模板消息订阅信息, 服务到期提醒订阅为未订阅，不保存, mpTemplateMsgDTO:{}", mpTemplateMsgDTO);
        } else {
            saveOrUpdateMpExpireSubscribe(mpTemplateMsgDTO);
        }
        return "保存小程序模板消息订阅信息成功";
    }

    /**
     * 保存或更新小程序授权到期订阅信息
     *
     * @param mpTemplateMsgDTO 包含小程序模板消息订阅信息的数据传输对象
     */
    private void saveOrUpdateMpExpireSubscribe(MpTemplateMsgSubscriptionDTO mpTemplateMsgDTO) {
        MpNoticeExpireSubscriptionDO expireDO = mpNoticeExpireRepository.queryExpireSubscribeByJlrAndOpenId(
                mpTemplateMsgDTO.getJlrId(), mpTemplateMsgDTO.getOpenId());
        if (Objects.isNull(expireDO)) {
            expireDO = buildMpNoticeExpireSubscriptionDO(mpTemplateMsgDTO);
        } else {
            Integer consent = expireDO.getServiceExpirationConsent();
            expireDO.setServiceExpirationConsent(consent + mpTemplateMsgDTO.getServiceExpirationConsent());
            expireDO.setUpdatedTime(LocalDateTime.now());
        }
        mpNoticeExpireRepository.saveOrUpdate(expireDO);
    }

    /**
     * 根据模板消息订阅数据传输对象构建MP通知到期订阅数据对象
     *
     * @param mpTemplateMsgDTO 模板消息订阅数据传输对象，包含用户订阅信息
     * @return 返回构建的MP通知到期订阅数据对象，如果输入为null，则返回null
     */
    private MpNoticeExpireSubscriptionDO buildMpNoticeExpireSubscriptionDO(MpTemplateMsgSubscriptionDTO mpTemplateMsgDTO) {
         if (Objects.isNull(mpTemplateMsgDTO)) {
             return null;
         }
         return MpNoticeExpireSubscriptionDO.builder()
                 .jlrId(mpTemplateMsgDTO.getJlrId())
                 .openId(mpTemplateMsgDTO.getOpenId())
                 .serviceExpirationConsent(mpTemplateMsgDTO.getServiceExpirationConsent())
                 .build();
    }

    /**
     * 保存微信消息订单订阅信息
     *
     * @param mpTemplateMsgDTO 微信模板消息订阅DTO，包含了需要保存的订阅信息
     */
    private void saveMpMsgOrderSubscribe(MpTemplateMsgSubscriptionDTO mpTemplateMsgDTO) {
        MpNoticeOrderSubscriptionDO orderSubscribeDO = mpNoticeOrderRepository.queryOrderSubscribeByOrderNoAndJlrId(
                mpTemplateMsgDTO.getJlrId(), mpTemplateMsgDTO.getOrderNo());
        if (Objects.isNull(orderSubscribeDO)) {
            orderSubscribeDO = buildMpNoticeOrderSubscriptionDO(mpTemplateMsgDTO);
        } else {
            orderSubscribeDO.setOpenId(mpTemplateMsgDTO.getOpenId());
            orderSubscribeDO.setOrderSuccessFlag(mpTemplateMsgDTO.getOrderSuccessFlag());
            orderSubscribeDO.setOrderCancelFlag(mpTemplateMsgDTO.getOrderCancelFlag());
            orderSubscribeDO.setUpdatedTime(LocalDateTime.now());
        }
        mpNoticeOrderRepository.saveOrUpdate(orderSubscribeDO);
    }

    /**
     * 根据模板消息订阅数据传输对象构建MP通知订单订阅数据对象
     *
     * @param mpTemplateMsgDTO 模板消息订阅数据传输对象，包含用户订阅信息
     * @return 返回构建的MP通知订单订阅数据对象，如果输入为null，则返回一个空的构建对象
     */
    private MpNoticeOrderSubscriptionDO buildMpNoticeOrderSubscriptionDO(MpTemplateMsgSubscriptionDTO mpTemplateMsgDTO) {
        if (Objects.isNull(mpTemplateMsgDTO)) {
            return MpNoticeOrderSubscriptionDO.builder().build();
        }
        return MpNoticeOrderSubscriptionDO.builder()
                .jlrId(mpTemplateMsgDTO.getJlrId())
                .openId(mpTemplateMsgDTO.getOpenId())
                .orderSuccessFlag(mpTemplateMsgDTO.getOrderSuccessFlag())
                .orderCancelFlag(mpTemplateMsgDTO.getOrderCancelFlag())
                .orderNo(mpTemplateMsgDTO.getOrderNo())
                .build();
    }
}
