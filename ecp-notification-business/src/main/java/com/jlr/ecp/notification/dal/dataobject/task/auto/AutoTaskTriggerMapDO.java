package com.jlr.ecp.notification.dal.dataobject.task.auto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;


@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_auto_task_trigger_map")
public class AutoTaskTriggerMapDO extends BaseDO {

    /**
     * 主键，自增
     * 对应数据库字段：id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 任务编码；该编码用于唯一标识一个自动任务
     * 对应数据库字段：task_code
     */
    @TableField("task_code")
    private String taskCode;

    /**
     * 条件ID；关联到条件表的条件ID，用于指定触发任务的条件
     * 对应数据库字段：condition_id
     */
    @TableField("condition_id")
    private String conditionId;

    /**
     * 条件值；指定条件的具体值，例如对于到期日期类型的条件，这里可以存储具体的天数，如30
     * 对应数据库字段：condition_value
     */
    @TableField("condition_value")
    private String conditionValue;

    /**
     * 租户号；表示该记录所属的租户
     * 对应数据库字段：tenant_id
     */
    @TableField("tenant_id")
    private Integer tenantId;
}
