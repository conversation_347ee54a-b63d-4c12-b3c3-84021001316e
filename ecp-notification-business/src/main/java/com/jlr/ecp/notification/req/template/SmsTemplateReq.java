package com.jlr.ecp.notification.req.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 模板编辑入参
 * <AUTHOR>
 */
@Schema(description = "通知服务 - 模板编辑入参")
@Data
public class SmsTemplateReq {
    @Schema(description = "业务线code")
    private String businessCode;

    @Schema(description = "模板code")
    private String templateCode;

    @Schema(description = "模板名称")
    private String templateName;

    @Schema(description = "模板内容")
    private String templateContent;

    @Schema(description = "场景说明")
    private String templateRemarks;
}
