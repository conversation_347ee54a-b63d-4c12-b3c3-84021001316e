package com.jlr.ecp.notification.dal.repository.mp.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.notification.dal.dataobject.mp.MpTemplateNoticeRecordDO;
import com.jlr.ecp.notification.dal.mysql.mp.MpTemplateNoticeRecordMapper;
import com.jlr.ecp.notification.dal.repository.mp.MpTemplateNoticeRecordRepository;
import org.springframework.stereotype.Service;

@Service
public class MpTemplateNoticeRecordRepositoryImpl extends ServiceImpl<MpTemplateNoticeRecordMapper,
        MpTemplateNoticeRecordDO> implements MpTemplateNoticeRecordRepository {

}
