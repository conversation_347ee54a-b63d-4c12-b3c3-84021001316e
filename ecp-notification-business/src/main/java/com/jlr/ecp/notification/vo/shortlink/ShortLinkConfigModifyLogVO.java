package com.jlr.ecp.notification.vo.shortlink;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "短链修改VO")
@Data
public class ShortLinkConfigModifyLogVO {
    @Schema(description = "配置id", requiredMode = Schema.RequiredMode.REQUIRED, example = "配置id")
    private Long configId;

    /**
     * 修改内容
     */
    @Schema(description = "修改内容", requiredMode = Schema.RequiredMode.REQUIRED, example = "修改内容")
    private String modifyContent;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2023/05/06 11:34:38",format = "yyyy/MM/dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyDate;

    /**
     * 修改人
     */
    @Schema(description = "修改人", requiredMode = Schema.RequiredMode.REQUIRED, example = "修改者")
    private String modifyUser;
}
