package com.jlr.ecp.notification.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class ReportResp {

     /**
      *  短信编号
      * */
     private String msgid;

     /**
      *   下行手机号码
      * */
     private String phone;

     /**
      * 短信发送结果：
      * 0——成功；1——接口处理失败；2——运营商网关失败；
      * */
     private String status;

     /**
      * 当status 为1 时，以desc 的错误码为准
      * */
     private String desc;

     /**
      *  当status 为2 时，表示运营商网关返回的原始值；
      * */
     private String wgcode;

     /**
      *  短信发送时间, 时间格式为yyyy-MM-dd HH:mm:ss
      * */
     private String time;

     /**
      *  长短信条数
      * */
     private Integer smsCount;

     /**
      *  长短信第几条标示
      * */
     private Integer smsIndex;
}
