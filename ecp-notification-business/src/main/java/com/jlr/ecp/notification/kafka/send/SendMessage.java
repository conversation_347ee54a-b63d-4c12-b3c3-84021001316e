package com.jlr.ecp.notification.kafka.send;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class SendMessage {

    /**
     *  消息id, 必填
     * */
    private String msgid;

    /**
     * 电话号码, 必填
     * */
    private String phones;

    /**
     *  发送内容, 必填
     * */
    private String content;

    /**
     * 签名, 必填
     * */
    private String sign;

    /**
     * 短信签名子码(由网络提供) 通常建议不要填写
     * */
    private String subcode;

    /**
     * 发送时间  必填  格式："201405051230"
     * */
    private String sendtime;
}
