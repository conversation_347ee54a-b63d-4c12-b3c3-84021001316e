package com.jlr.ecp.notification.kafka.listener;

import com.jlr.ecp.notification.kafka.message.BrandedGoodsReturnRefundMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 精选好物退货退款通知监听器
 */
@Component
@Slf4j
public class BrandedGoodsReturnRefundListener extends BaseNotificationListener<BrandedGoodsReturnRefundMessage> {
    
    @KafkaListener(topics = "${branded-goods.kafka.topics.return-refund}", 
                  groupId = "${branded-goods.kafka.groups.return-refund}", 
                  batch = "true",
                  properties = "max.poll.records:100")
    public void listen(List<ConsumerRecord<String, String>> records) {
        log.info("精选好物退货退款通知消息，records:{}", records);
        processMessages(records, BrandedGoodsReturnRefundMessage.class);
    }
} 