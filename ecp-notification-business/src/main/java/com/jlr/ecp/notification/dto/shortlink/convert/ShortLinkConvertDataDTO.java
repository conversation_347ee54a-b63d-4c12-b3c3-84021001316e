package com.jlr.ecp.notification.dto.shortlink.convert;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ShortLinkConvertDataDTO {
    /**
     *  短链地址
     * */
    private String shortUrl;

    /**
     * 短链Code
     * */
    private String code;

    /**
     *  原始地址
     * */
    private String originalUrl;

    /**
     * 请求系统
     * */
    private String sourceSystem;

    /**
     *  链接类型
     * */
    private String urlType;

    /**
     *  业务场景
     * */
    private String bizCase;

    /**
     *  有效类型： 短期（SHORT_TERM） 和 长期（LONG_TERM）
     * */
    private String validityType;

    /**
     *  过期日期
     * */
    private String expirationDate;

    /**
     * 创建时间
     * */
    private String createdAt;

    /**
     *  备注
     * */
    private String remark;
}
