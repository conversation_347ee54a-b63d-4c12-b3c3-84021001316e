package com.jlr.ecp.notification;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;


@SpringBootApplication
@EnableFeignClients("com.jlr.ecp.*")
//@SpringBootApplication(exclude = {SecurityAutoConfiguration.class, ManagementWebSecurityAutoConfiguration.class})
@Slf4j
public class AppServerApplication {

    public static void main(String[] args) {
        try {
            SpringApplication.run(AppServerApplication.class, args);
        }catch (Exception e){
            log.info("notification, 启动异常：{}", e.getMessage());
        }
    }
}
