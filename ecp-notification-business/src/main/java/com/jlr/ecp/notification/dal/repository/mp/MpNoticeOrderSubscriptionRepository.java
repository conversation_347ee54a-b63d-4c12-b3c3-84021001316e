package com.jlr.ecp.notification.dal.repository.mp;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.notification.dal.dataobject.mp.MpNoticeOrderSubscriptionDO;

import java.util.List;
import java.util.Map;

public interface MpNoticeOrderSubscriptionRepository extends IService<MpNoticeOrderSubscriptionDO> {

    /**
     * 根据订单号和机构ID查询订单订阅信息
     *
     * @param jlrId 机构ID，标识查询的机构
     * @param orderNo 订单号，标识查询的特定订单
     * @return 返回查询到的订单订阅信息对象，如果没有找到匹配的订阅信息，则返回null
     */
    MpNoticeOrderSubscriptionDO queryOrderSubscribeByOrderNoAndJlrId(String jlrId, String orderNo);

    /**
     * 根据订单号和机构ID查询订单订阅信息
     *
     * @param orderNoList 订单号列表，标识查询的特定订单
     * @param mpType mp模板类型，MpMsgTypeEnum
     * @return 返回查询到的订单订阅信息对象，如果没有找到匹配的订阅信息
     */
    List<MpNoticeOrderSubscriptionDO> queryOrderSubscribeByOrderNoList(List<String> orderNoList, Integer mpType);

    /**
     * 根据订单号列表查询订单订阅信息映射
     *
     * @param orderNoList 订单号列表，用于查询订阅信息
     * @return 返回一个映射，键是订单号，值是对应的订阅信息对象MpNoticeOrderSubscriptionDO
     */
    Map<String, MpNoticeOrderSubscriptionDO> queryOrderSubscribeMapByList(List<String> orderNoList, Integer mpType);
}
