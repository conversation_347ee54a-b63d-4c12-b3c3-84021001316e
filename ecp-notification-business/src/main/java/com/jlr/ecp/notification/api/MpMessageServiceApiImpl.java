package com.jlr.ecp.notification.api;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import com.alibaba.fastjson.JSON;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.api.dto.MpExpireSubscriptionDTO;
import com.jlr.ecp.notification.api.dto.SendMpMsgDTO;
import com.jlr.ecp.notification.api.mp.MpMessageServiceApi;
import com.jlr.ecp.notification.constant.MpEventTypeConstants;
import com.jlr.ecp.notification.dal.dataobject.mp.MpNoticeExpireSubscriptionDO;
import com.jlr.ecp.notification.dal.dataobject.mp.MpTemplateNoticeRecordDO;
import com.jlr.ecp.notification.dal.repository.mp.MpNoticeExpireSubscriptionRepository;
import com.jlr.ecp.notification.dal.repository.mp.MpTemplateNoticeRecordRepository;
import com.jlr.ecp.notification.dto.mp.MpExpireNotifyMsgDTO;
import com.jlr.ecp.notification.dto.mp.MpExpireParametersDTO;
import com.jlr.ecp.notification.enums.BrandCodeEnum;
import com.jlr.ecp.notification.enums.mp.ServiceNameEnum;
import com.jlr.ecp.notification.properties.MpMsgProperties;
import com.jlr.ecp.notification.properties.SnsProperties;
import com.jlr.ecp.notification.util.mp.SnsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;
import software.amazon.awssdk.services.sns.SnsClient;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@Validated
@Slf4j
public class MpMessageServiceApiImpl implements MpMessageServiceApi {
    @Resource
    private MpNoticeExpireSubscriptionRepository mpNoticeExpireRepository;

    @Resource
    private SnsUtil snsUtil;

    @Resource
    private MpMsgProperties mpMsgProperties;

    @Resource
    private SnsProperties snsProperties;

    @Resource
    private Snowflake snowflake;

    @Resource
    private MpTemplateNoticeRecordRepository mpRecordRepository;

    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 获取MP到期提醒消息所有id列表
     *
     * @return CommonResult<String> 返回执行结果的通用对象，包含提醒消息发送的结果信息
     */
    @Override
    public CommonResult<List<Long>> getMpExpireMsgAllId() {
        List<MpNoticeExpireSubscriptionDO> mpExpireList = mpNoticeExpireRepository.selectExpiredMpMessageAllId();
        log.info("获取MP到期提醒消息所有id列表, mpExpireList的数量：{}", mpExpireList.size());
        if (CollUtil.isEmpty(mpExpireList)) {
            return new CommonResult<>();
        }
        List<Long> mpExpireMsgAllId = mpExpireList.stream()
                .map(MpNoticeExpireSubscriptionDO::getId)
                .collect(Collectors.toList());
        return CommonResult.success(mpExpireMsgAllId);
    }

    /**
     * 根据ID列表获取小程序到期订阅信息列表
     *
     * @param idList 小程序ID列表，用于查询到期订阅信息
     * @return 包含小程序到期订阅信息DTO列表的通用响应对象
     */
    @Override
    public CommonResult<List<MpExpireSubscriptionDTO>> getMpExpireMsgByIdList(List<Long> idList) {
        log.info("根据ID列表获取小程序到期订阅信息列表, idList数量:{}, idList:{}", idList.size(), idList);
        if(CollUtil.isEmpty(idList)) {
            return CommonResult.success(new ArrayList<>());
        }
        List<MpNoticeExpireSubscriptionDO> mpExpireDOList = mpNoticeExpireRepository.queryExpiredMpByIdList(idList);
        log.info("根据ID列表获取小程序到期订阅信息列表, mpExpireDOList的数量:{}", mpExpireDOList.size());
        return CommonResult.success(buildMpExpireSubscriptionDTOList(mpExpireDOList));
    }

    /**
     * 发送小程序到期提醒消息
     *
     * @param sendMpMsgDTOList 待发送的消息数据列表（非null，但可能为空列表）
     * @return 包含发送结果数量
     */
    @Override
    public CommonResult<Integer> sendMpExpireMsg(List<SendMpMsgDTO> sendMpMsgDTOList) {
        if (CollUtil.isEmpty(sendMpMsgDTOList)) {
            log.info("发送小程序到期提醒消息, sendMpMsgDTOList为空");
            return CommonResult.success(0);
        }
        log.info("发送小程序到期提醒消息, sendMpMsgDTOList的数量:{}, sendMpMsgDTOList:{}", sendMpMsgDTOList.size(), sendMpMsgDTOList);
        SnsClient snsClient = snsUtil.getSnsClient();
        int cnt = 0;
        List<MpTemplateNoticeRecordDO> mpRecordDOList = new ArrayList<>();
        Map<String, Integer> jlrIdSendMap = new HashMap<>();
        for (SendMpMsgDTO sendMpMsgDTO : sendMpMsgDTOList) {
            try {
                MpExpireNotifyMsgDTO expireMessage = buildMpExpireNotifyMsgDTO(sendMpMsgDTO);
                String messageGroupId =  getExpireEventType(sendMpMsgDTO.getBrandCode());
                String messageDeduplicationId = snowflake.nextIdStr();
                String msgJson = JSON.toJSONString(expireMessage);
                String messageId = snsUtil.publishFIFOTopic(msgJson, snsProperties.getTopicArn(), snsClient, messageGroupId, messageDeduplicationId);
                if (StringUtils.isBlank(messageId)) {
                    log.info("发送小程序到期提醒消息, 消息发送失败，sendMpMsgDTO：{}", sendMpMsgDTO);
                } else {
                    cnt++;
                    Integer jirIdSendCount = jlrIdSendMap.getOrDefault(sendMpMsgDTO.getJlrId(), 0);
                    jlrIdSendMap.put(sendMpMsgDTO.getJlrId(), jirIdSendCount + 1);
                }
                MpTemplateNoticeRecordDO mpNoticeRecordDO = buildMpTemplateNoticeRecordDO(sendMpMsgDTO, msgJson, messageId);
                if (Objects.nonNull(mpNoticeRecordDO)) {
                    mpRecordDOList.add(mpNoticeRecordDO);
                }
            } catch (Exception e) {
                log.info("发送小程序到期提醒消息异常：{}", e.getMessage());
            }
        }
        log.info("发送小程序到期提醒消息, 发送结果数量:{}, mpRecordDOList:{}", cnt, mpRecordDOList);
        transactionTemplate.executeWithoutResult(status -> {
            // 批量更新已发送到期提醒的consent
            batchUpdateMpExpireConsent(jlrIdSendMap);
            // 记录小程序模板消息发送记录
            mpRecordRepository.saveBatch(mpRecordDOList);
        });
        return CommonResult.success(cnt);
    }

    /**
     * 批量更新过期订阅通知的同意次数。
     *
     * @param jlrIdSendMap 包含 JLR ID 和对应发送次数的映射。如果为空，则函数直接返回。
     */
   public void batchUpdateMpExpireConsent(Map<String, Integer> jlrIdSendMap) {
       log.info("批量更新过期订阅通知的同意次数, jlrIdSendMap:{}", jlrIdSendMap);
        if (CollUtil.isEmpty(jlrIdSendMap)) {
            return;
        }
        List<String> jlrIdList = new ArrayList<>(jlrIdSendMap.keySet());
        List<MpNoticeExpireSubscriptionDO> mpNoticeExpireList = mpNoticeExpireRepository.queryMpExpiredListByJlrId(jlrIdList);
        log.info("批量更新过期订阅通知的同意次数, mpNoticeExpireList:{}", mpNoticeExpireList);
        if (CollUtil.isEmpty(mpNoticeExpireList)) {
            return ;
        }
        for (MpNoticeExpireSubscriptionDO mpNoticeExpireDO : mpNoticeExpireList) {
            Integer sendCount = jlrIdSendMap.getOrDefault(mpNoticeExpireDO.getJlrId(), 0);
            if (sendCount >= mpNoticeExpireDO.getServiceExpirationConsent()) {
                mpNoticeExpireDO.setServiceExpirationConsent(0);
            } else {
                mpNoticeExpireDO.setServiceExpirationConsent(mpNoticeExpireDO.getServiceExpirationConsent() - sendCount);
            }
        }
        mpNoticeExpireRepository.updateBatchById(mpNoticeExpireList);
   }

    /**
     * 根据传入参数构建微信公众号模板消息通知记录实体
     *
     * @param sendMpMsgDTO 发送消息数据传输对象，包含消息关联的业务ID(jlrId)、用户openId、车架号等业务信息
     * @param message 实际发送的模板消息内容（JSON格式字符串）
     * @return 组装完成的记录实体对象
     *
     */
    private MpTemplateNoticeRecordDO buildMpTemplateNoticeRecordDO(SendMpMsgDTO sendMpMsgDTO, String message, String messageId) {
        if (Objects.isNull(sendMpMsgDTO) || StringUtils.isBlank(message)) {
            return null;
        }
        return MpTemplateNoticeRecordDO.builder()
                .messageId(messageId)
                .jlrId(sendMpMsgDTO.getJlrId())
                .openId(sendMpMsgDTO.getOpenId())
                .snsTopic("ECP_VCS_TEMPLATE_MESSAGE")
                .eventType(getExpireEventType(sendMpMsgDTO.getBrandCode()))
                .carVin(sendMpMsgDTO.getCarVin())
                .sendMessage(message)
                .build();
    }

    /**
     * 构建微信公众号服务到期通知消息DTO对象
     *
     * @param sendMpMsgDTO 包含消息发送所需基础数据的传输对象
     * @return 组装完成的到期提醒消息DTO对象，包含完整的模板消息推送要素
     */
    private MpExpireNotifyMsgDTO buildMpExpireNotifyMsgDTO(SendMpMsgDTO sendMpMsgDTO) {
        MpExpireNotifyMsgDTO expireMessage = MpExpireNotifyMsgDTO.builder()
                .clientId("VCS_ECP")
                .jlrId(sendMpMsgDTO.getJlrId())
                .businessNo(snowflake.nextIdStr())
                .app(mpMsgProperties.getApp())
                .openId(sendMpMsgDTO.getOpenId())
                .build();
        expireMessage.setEventType(getExpireEventType(sendMpMsgDTO.getBrandCode()));
        MpExpireParametersDTO parameters = MpExpireParametersDTO.builder()
                .serviceName(getServiceNameByType(sendMpMsgDTO.getServiceType()))
                .deviceName(sendMpMsgDTO.getCarVin())
                .expirationDate(sendMpMsgDTO.getExpiryDate())
                .reminder("服务即将到期，可点击详情即刻续订。")
                .build();
        expireMessage.setParameters(parameters);
        return expireMessage;
    }

    /**
     * 根据品牌编码获取对应的过期事件类型
     *
     * @param brandCode 品牌编码字符串
     * @return String 对应品牌的事件类型常量
     */
    private String getExpireEventType(String brandCode) {
        if (BrandCodeEnum.LAND_ROVER.getBrandCode().equals(brandCode)) {
            return MpEventTypeConstants.VCS_LAN_EXPIRE_EVENT_TYPE;
        } else if (BrandCodeEnum.JAGUAR.getBrandCode().equals(brandCode)) {
            return MpEventTypeConstants.VCS_JAG_EXPIRE_EVENT_TYPE;
        }
        return "";
    }

    /**
     * 根据服务类型获取对应的服务名称
     *
     * @param serviceType 服务类型编码，允许为空
     * @return 对应服务类型的名称字符串，未匹配时返回默认名称
     */
    private String getServiceNameByType(Integer serviceType) {
        if (Objects.isNull(serviceType)) {
            return ServiceNameEnum.DEFAULT_NAME.getServiceName();
        }
        if (ServiceNameEnum.REMOTE.getServiceType().equals(serviceType)) {
            return ServiceNameEnum.REMOTE.getServiceName();
        }
        if (ServiceNameEnum.PIVI.getServiceType().equals(serviceType)) {
            return ServiceNameEnum.PIVI.getServiceName();
        }
        return ServiceNameEnum.DEFAULT_NAME.getServiceName();
    }



    /**
     * 根据过期通知订阅数据列表构建过期订阅DTO列表
     *
     * @param mpExpireDOList 过期通知订阅数据对象列表，不能为空
     * @return 过期订阅DTO列表，如果输入列表为空，则返回空列表
     */
    private List<MpExpireSubscriptionDTO> buildMpExpireSubscriptionDTOList(List<MpNoticeExpireSubscriptionDO> mpExpireDOList) {
        List<MpExpireSubscriptionDTO> resp = new ArrayList<>();
        if (CollUtil.isEmpty(mpExpireDOList)) {
            return resp;
        }
        for (MpNoticeExpireSubscriptionDO mpExpireDO : mpExpireDOList) {
            resp.add(buildMpExpireSubscriptionDTO(mpExpireDO));
        }
        return resp;
    }

    /**
     * 根据到期通知订阅数据对象构建到期通知订阅DTO
     *
     * @param mpExpireDO 到期通知订阅数据对象，包含从数据库中获取的订阅信息
     * @return 返回构建好的MpExpireSubscriptionDTO对象，用于进一步的操作或传输
     */
    private MpExpireSubscriptionDTO buildMpExpireSubscriptionDTO(MpNoticeExpireSubscriptionDO mpExpireDO) {
        MpExpireSubscriptionDTO mpExpireSubscriptionDTO = new MpExpireSubscriptionDTO();
        mpExpireSubscriptionDTO.setId(mpExpireDO.getId());
        mpExpireSubscriptionDTO.setJlrId(mpExpireDO.getJlrId());
        mpExpireSubscriptionDTO.setOpenId(mpExpireDO.getOpenId());
        mpExpireSubscriptionDTO.setServiceExpirationConsent(mpExpireDO.getServiceExpirationConsent());
        return mpExpireSubscriptionDTO;
    }

}
