package com.jlr.ecp.notification.enums.task;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *  品牌签名内容 1路虎中国 2捷豹路虎中国  3捷豹路虎中国
 * */
@Getter
@AllArgsConstructor
public enum SignBrandTextEnum {
    LAND_ROVER(1,"【路虎中国】", "LAN"),
    JAGUAR(2, "【捷豹路虎中国】", "JAG"),
    JAGUAR_LAND_ROVER(3, "【捷豹路虎中国】", "JAG-LAN");

    private final Integer signCode;

    private final String signDesc;

    private final String brandCode;

    /**
     * 根据签名码获取品牌代码
     *
     * @param signCode 签名码，一个整数值，用于识别特定的品牌
     * @return 品牌代码，如果找到匹配的签名码则返回对应的品牌代码，否则返回空字符串
     */
    public static String getBrandCodeBySignCode(Integer signCode) {
        for (SignBrandTextEnum signBrandTextEnum : SignBrandTextEnum.values()) {
            if (signBrandTextEnum.getSignCode().equals(signCode)) {
                return signBrandTextEnum.getBrandCode();
            }
        }
        return "";
    }

    /**
     * 根据签名码获取签名描述
     *
     * @param signCode 签名码，一个整数值，用于识别特定的品牌
     * @return 签名描述，如果找到匹配的签名码则返回对应的签名描述，否则返回空字符串
     */
    public static String getSignDescBySignCode(Integer signCode) {
        if (signCode == null) {
            return "";
        }

        for (SignBrandTextEnum signBrandTextEnum : SignBrandTextEnum.values()) {
            if (signBrandTextEnum.getSignCode().equals(signCode)) {
                return signBrandTextEnum.getSignDesc();
            }
        }
        return "";
    }
}
