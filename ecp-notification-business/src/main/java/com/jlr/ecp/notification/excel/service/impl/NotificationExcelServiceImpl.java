package com.jlr.ecp.notification.excel.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.dto.VinAndPhoneDTO;
import com.jlr.ecp.notification.enums.ErrorCodeConstants;
import com.jlr.ecp.notification.enums.ExcelTemplateTypeEnum;
import com.jlr.ecp.notification.enums.UploadExcelTypeEnum;
import com.jlr.ecp.notification.excel.listener.PhoneNumberListenerCheck;
import com.jlr.ecp.notification.excel.listener.PhoneReadListener;
import com.jlr.ecp.notification.excel.listener.VinAndPhoneListenerCheck;
import com.jlr.ecp.notification.excel.listener.VinAndPhoneReadListener;
import com.jlr.ecp.notification.excel.pojo.PhoneNumber;
import com.jlr.ecp.notification.excel.pojo.VinAndPhoneNumber;
import com.jlr.ecp.notification.excel.service.NotificationExcelService;
import com.jlr.ecp.notification.excel.vo.UploadExcelRespVO;
import com.jlr.ecp.notification.file.service.FileService;
import com.jlr.ecp.notification.util.PIPLDataUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class NotificationExcelServiceImpl implements NotificationExcelService {
    @Value("${excel.templateUrl.vin}")
    private String vinTemplateUrl;

    @Value("${excel.templateUrl.phone}")
    private String phoneTemplateUrl;

    @Resource
    private FileService fileService;

    @Resource(name = "asyncThreadPool")
    private ThreadPoolTaskExecutor asyncThreadPool;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private PIPLDataUtil piplDataUtil;

    private static final String FILE_CODE = "AWS_S3_FILE";

    private static final String EXCEL_FORMATTER = ".xlsx";

    /**
     *  获取excel模板地址
     * @param excelTemplateType 模板类型
     * @return String 模板地址
     * */
    @Override
    public String getTemplateUrl(Integer excelTemplateType) {
        if (ExcelTemplateTypeEnum.PHONE_EXCEL.getType().equals(excelTemplateType)) {
            return phoneTemplateUrl;
        } else if (ExcelTemplateTypeEnum.VIN_PHONE_EXCEL.getType().equals(excelTemplateType)) {
            return vinTemplateUrl;
        }
        return "";
    }

    /**
     * 上传Excel文件。
     *
     * @param file 需要上传的Excel文件，使用MultipartFile类型，代表上传的文件。
     * @param excelTemplateType Excel模板类型，通过整数来区分不同的模板类型。
     * @return CommonResult<UploadExcelRespVO>
     */
    @Override
    public CommonResult<UploadExcelRespVO> uploadExcel(MultipartFile file, Integer excelTemplateType) {
        CommonResult<UploadExcelRespVO> resp = new CommonResult<>();
        if (ExcelTemplateTypeEnum.PHONE_EXCEL.getType().equals(excelTemplateType)) {
            resp = getUploadPhoneRespVO(file, excelTemplateType);
        } else if (ExcelTemplateTypeEnum.VIN_PHONE_EXCEL.getType().equals(excelTemplateType)) {
            resp = getUploadVinAndPhoneRespVO(file, excelTemplateType);
        } else {
            return CommonResult.error(ErrorCodeConstants.UPLOAD_EXCEL_TYPE_ERROR);
        }
        if (resp.isError() || Objects.isNull(resp.getData()))  {
            return resp;
        }
        if (Objects.nonNull(resp.getData()) && StringUtils.isNoneBlank(resp.getData().getUploadExcelPath())) {
            UploadExcelRespVO uploadExcelRespVO = resp.getData();
            uploadExcelRespVO.setUploadExcelPath(piplDataUtil.getEncryptText(uploadExcelRespVO.getUploadExcelPath()));
            resp.setData(uploadExcelRespVO);
        }
        return resp;
    }


    /**
     * 处理上传的电话号码Excel文件并返回处理结果
     *
     * @param file            用户上传的Excel文件
     * @param excelTemplateType  Excel模板类型，用于区分不同的文件格式
     * @return                返回一个通用结果对象，包含处理后的数据
     */
    private CommonResult<UploadExcelRespVO> getUploadPhoneRespVO(MultipartFile file, Integer excelTemplateType) {
        UploadExcelRespVO uploadExcelRespVO = null;
        File errorDetailFile = null;
        try {
            PhoneNumberListenerCheck phoneCheck = getPhoneNumberListenerCheck(file);
            if (Boolean.TRUE.equals(phoneCheck.getFormatError())) {
                return CommonResult.error(ErrorCodeConstants.UPLOAD_FILE_FORMAT_ERROR);
            }
            if (CollUtil.isEmpty(phoneCheck.getAllDataList())) {
                return CommonResult.error(ErrorCodeConstants.UPLOAD_FILE_CONTENT_EMPTY);
            }
            errorDetailFile = phoneCheck.getPhoneErrorDetailFile();
            uploadExcelRespVO = getUploadExcelRespVO(file, errorDetailFile, null, excelTemplateType);
        } catch (Exception e) {
            log.info("处理上传的电话号码Excel文件异常：", e);
        } finally {
            // 删除临时文件
            if (Objects.nonNull(errorDetailFile)) {
                FileUtil.del(errorDetailFile.getParentFile());
            }
        }
        return CommonResult.success(uploadExcelRespVO);
    }

    /**
     * 处理上传的Excel文件并返回相应的结果
     *
     * @param file Excel文件，包含VIN和联系电话信息
     * @param excelTemplateType Excel模板类型，用于区分不同的文件格式
     * @return 包含上传结果的CommonResult对象
     */
    private CommonResult<UploadExcelRespVO> getUploadVinAndPhoneRespVO(MultipartFile file, Integer excelTemplateType) {
        File errorDetailFile = null;
        File correctFile = null;
        UploadExcelRespVO uploadExcelRespVO = null;
        try {
            VinAndPhoneListenerCheck vinAndPhoneCheck = getVinAndPhoneListenerCheck(file);
            if (Boolean.TRUE.equals(vinAndPhoneCheck.getFormatError())) {
                return CommonResult.error(ErrorCodeConstants.UPLOAD_FILE_FORMAT_ERROR);
            }
            if (CollUtil.isEmpty(vinAndPhoneCheck.getAllDataList())) {
                return CommonResult.error(ErrorCodeConstants.UPLOAD_FILE_CONTENT_EMPTY);
            }
            errorDetailFile = vinAndPhoneCheck.getVinAndPhoneErrorDetailFile();
            correctFile = vinAndPhoneCheck.getVinAndPhoneCorrectDetailFile();
            uploadExcelRespVO = getUploadExcelRespVO(file, errorDetailFile, correctFile, excelTemplateType);
        } catch (Exception e) {
            log.info("处理上传Vin+Phone的Excel文件并返回相应异常：", e);
        } finally {
            // 删除临时文件
            if (Objects.nonNull(errorDetailFile)) {
                FileUtil.del(errorDetailFile.getParentFile());
            }
        }
        return CommonResult.success(uploadExcelRespVO);
    }


    /**
     * 从上传的文件中读取电话号码并进行校验。
     *
     * @param file 上传的文件，预期为Excel格式，其中包含电话号码数据。
     * @return PhoneNumberListenerCheck 对象，包含了校验后的结果。
     */
    private PhoneNumberListenerCheck getPhoneNumberListenerCheck(MultipartFile file) {
        long startTime = System.currentTimeMillis();
        PhoneNumberListenerCheck phoneCheck = new PhoneNumberListenerCheck();
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            try {
                EasyExcel.read(file.getInputStream(), PhoneNumber.class, phoneCheck).sheet().doRead();
            } catch (IOException e) {
                log.error("读取上传phone的excel文件异常:", e);
            }
        }, asyncThreadPool);
        // 等待异步任务执行完成，以确保读取和校验过程已完成
        try {
            future.get();
        } catch (Exception e) {
            log.error("从上传phone文件中进行校验, 获取结果异常:", e);
            Thread.currentThread().interrupt();
        }
        long endTime = System.currentTimeMillis();
        log.info("phone类型excel进行校验花费时间：{}毫秒", (endTime-startTime));
        return phoneCheck;
    }

    /**
     * 通过给定的MultipartFile文件对象，异步读取并校验Excel文件中的车辆识别码（VIN）和电话号码信息。
     *
     * @param file MultipartFile文件对象，包含待读取的Excel文件。
     * @return VinAndPhoneListenerCheck 校验结果的监听器对象，包含了校验过程中收集到的信息。
     */
    private VinAndPhoneListenerCheck getVinAndPhoneListenerCheck(MultipartFile file) {
        long startTime = System.currentTimeMillis();
        VinAndPhoneListenerCheck vinAndPhoneCheck = applicationContext.getBean(VinAndPhoneListenerCheck.class);
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            try {
                EasyExcel.read(file.getInputStream(), VinAndPhoneNumber.class, vinAndPhoneCheck).sheet().doRead();
            } catch (IOException e) {
                log.error("读取上传VinAndPhone的excel文件异常:", e);
            }
        }, asyncThreadPool);
        // 等待异步任务执行完成，以确保读取和校验过程已完成
        try {
            future.get();
        } catch (Exception e) {
            log.error("从上传phone文件中进行校验, 获取结果异常:", e);
            Thread.currentThread().interrupt();
        }
        long endTime = System.currentTimeMillis();
        log.info("Vin+phone异步读取并校验Excel文件，花费时间：{}毫秒", (endTime - startTime));
        return vinAndPhoneCheck;
    }

    /**
     * 根据上传的Excel文件获取上传响应信息。
     * 如果上传过程中存在错误详情文件，则该方法会处理错误详情文件的上传并返回其路径；
     * 如果不存在错误详情文件，则处理上传的Excel文件并返回其上传后的路径。
     *
     * @param multipartFile 上传的Excel文件，可能是普通上传或包含错误详情的上传。
     * @param errorDetailFile 错误详情文件，如果上传过程中有错误详情需要单独上传，则该参数不为空。
     * @param correctFile 正确数据文件，如果上传过程中有正确数据需要单独上传，则该参数不为空。
     * @return 返回一个包含上传Excel文件路径（或错误详情文件路径）的对象。
     */
    private UploadExcelRespVO getUploadExcelRespVO(MultipartFile multipartFile, File errorDetailFile,
                                                   File correctFile, Integer excelTemplateType) {
        long startTime = System.currentTimeMillis();
        log.info("根据上传的Excel文件获取上传响应信息, multipartFile:{}, errorDetailFile:{}, correctFile:{}",
                multipartFile, errorDetailFile, correctFile);
        UploadExcelRespVO uploadExcelRespVO = new UploadExcelRespVO();
        if (Objects.isNull(multipartFile) || StringUtils.isBlank(multipartFile.getOriginalFilename())) {
            return uploadExcelRespVO;
        }
        String[] names = multipartFile.getOriginalFilename().split("\\.");
        String fileName;
        try {
            if (names.length > 0) {
                fileName = names[0] + "_" + System.currentTimeMillis() + EXCEL_FORMATTER;
            } else {
                fileName = System.currentTimeMillis() + EXCEL_FORMATTER;
            }
            if (Objects.nonNull(errorDetailFile)) {
                uploadExcelRespVO = doUploadExcelRespVO(FileUtil.readBytes(errorDetailFile),
                        "excelErrorDetailFile", fileName, UploadExcelTypeEnum.ERROR_DETAIL.getExcelType());
            } else if (ExcelTemplateTypeEnum.PHONE_EXCEL.getType().equals(excelTemplateType)){
                uploadExcelRespVO = doUploadExcelRespVO(multipartFile.getBytes(), "excelUploadFile",
                        fileName, UploadExcelTypeEnum.UPLOAD_EXCEL.getExcelType());
            } else if (ExcelTemplateTypeEnum.VIN_PHONE_EXCEL.getType().equals(excelTemplateType)) {
                uploadExcelRespVO = doUploadExcelRespVO(FileUtil.readBytes(correctFile), "excelUploadFile",
                        fileName, UploadExcelTypeEnum.UPLOAD_EXCEL.getExcelType());
            }
        } catch (Exception e) {
            log.info("根据上传的Excel文件获取上传响应信息, 异常: ", e);
        }
        long endTime = System.currentTimeMillis();
        log.info("根据上传的Excel文件获取上传响应信息花费时间：{}毫秒", (endTime-startTime));
        return uploadExcelRespVO;
    }

    /**
     * 生成上传Excel的响应体
     *
     * @param fileBytes 文件字节流
     * @param excelPath Excel文件存储的路径
     * @param fileName Excel文件的名称
     * @param uploadExcelType 上传Excel的类型，决定文件如何处理和存储
     * @return UploadExcelRespVO 上传响应体，包含文件处理结果和路径等信息
     */
    private UploadExcelRespVO doUploadExcelRespVO(byte[] fileBytes, String excelPath, String fileName,
                                                   Integer uploadExcelType) {
        long startTime = System.currentTimeMillis();
        log.info("doUploadExcelRespVO, excelPath:{}, fileName:{}, uploadExcelType:{}", excelPath, fileName,
                uploadExcelType);
        UploadExcelRespVO uploadExcelRespVO = new UploadExcelRespVO();
        try {
            String uploadFilePath = fileService.createFile(null, excelPath + File.separator
                    + fileName, fileBytes, FILE_CODE);
            if (UploadExcelTypeEnum.ERROR_DETAIL.getExcelType().equals(uploadExcelType)) {
                uploadExcelRespVO.setErrorDetailPath(uploadFilePath);
                uploadExcelRespVO.setCheckResult(false);
            } else if (UploadExcelTypeEnum.UPLOAD_EXCEL.getExcelType().equals(uploadExcelType)) {
                uploadExcelRespVO.setUploadExcelPath(uploadFilePath);
                uploadExcelRespVO.setCheckResult(true);
            }
            uploadExcelRespVO.setFileName(fileName);
            uploadExcelRespVO.setCheckStatus(true);
            return uploadExcelRespVO;
        } catch (Exception e) {
            log.error("上传excel类型：{}，path:{}, 异常：", uploadExcelType, excelPath, e);
        }
        long endTime = System.currentTimeMillis();
        log.info("生成上传Excel的响应体, fileName:{}, uploadExcelType:{}, 花费的时间:{}", fileName, uploadExcelType,
                (endTime-startTime));
        return uploadExcelRespVO;
    }

    /**
     * 从指定的Excel文件中获取电话号码列表。
     * @param uploadExcelPath 上传的Excel文件的路径
     *
     * @return 一个包含电话号码列表的二维字符串数组，每个子列表代表一行电话号码数据
     */
    public List<List<String>> getPhoneList(String uploadExcelPath) {
        String uploadPathKey= getUploadPathKey(uploadExcelPath);
        log.info("读取s3到本地, uploadExcelPath:{}, uploadPathKey:{}", uploadExcelPath, uploadPathKey);
        List<List<String>> phoneList = new ArrayList<>();
        try {
            byte[] content = fileService.getFileContent(FILE_CODE, uploadPathKey);
            phoneList = getPhoneListByExcel(content);
        } catch (Exception e) {
            log.error("从s3读取excel异常:", e);
        }
        return phoneList;
    }

    @Override
    public List<List<VinAndPhoneDTO>> getVinPhoneList(String uploadExcelPath) {
        String uploadPathKey= getUploadPathKey(uploadExcelPath);
        log.info("读取s3到本地, uploadExcelPath:{}, uploadPathKey:{}", uploadExcelPath, uploadPathKey);
        List<List<VinAndPhoneDTO>> phoneList = new ArrayList<>();
        try {
            byte[] content = fileService.getFileContent(FILE_CODE, uploadPathKey);
            phoneList = getVinPhoneListByExcel(content);
        } catch (Exception e) {
            log.error("从s3读取excel异常:", e);
        }
        return phoneList;
    }

    /**
     * 获取上传路径的键值。
     * 该方法处理传入的上传Excel路径，从路径中提取并返回“file/”之后的部分。
     * 如果传入的路径为空或不包含“file/”，则返回空字符串。
     *
     * @param uploadExcelPath 上传的Excel文件路径
     * @return 处理后的路径，即从“file/”开始至路径末尾的部分；如果不存在“file/”则返回空字符串。
     */
    private String getUploadPathKey(String uploadExcelPath) {
        log.info("getUploadPathKey, uploadExcelPath:{}", uploadExcelPath);
        if (StringUtils.isBlank(uploadExcelPath)) {
            return "";
        }
        // 找到 "file/" 子串在 URL 中的起始位置
        int startIndex = uploadExcelPath.indexOf("file/") + "file/".length();
        if (startIndex < 0) {
            return "";
        }
        // 从找到的起始位置截取至字符串末尾
        return uploadExcelPath.substring(startIndex);
    }

    /**
     * 通过Excel文件获取电话号码列表。
     *
     * @param bytes Excel文件的字节数组。
     *
     * @return 返回一个包含电话号码信息的列表，每个子列表代表一行数据，其中包含的字段根据excelType的不同而变化。
     */
    public List<List<String>> getPhoneListByExcel(byte[] bytes) {
        List<List<String>> resp = new ArrayList<>();
        String tempDir = System.getProperty("java.io.tmpdir");
        String fileName = "reads3Excel" + UUID.randomUUID().toString().replace("-", "") + EXCEL_FORMATTER;
        String filePath = new File(tempDir, fileName).getPath();
        log.info("Creating temporary Excel file at {}", filePath);
        PhoneReadListener phoneReadListener =  new PhoneReadListener();
        try (OutputStream outputStream = new FileOutputStream(fileName)) {
            outputStream.write(bytes);
            outputStream.flush();
            EasyExcel.read(fileName, PhoneNumber.class, phoneReadListener).sheet().doRead();
            resp = phoneReadListener.getPhoneList();
            log.info("Successfully processed Excel file at {}", filePath);
        } catch (Exception e) {
            log.error("解析下载excel文件异常:", e);
        } finally {
            FileUtil.del(fileName);
        }
        return resp;
    }

    @Override
    public List<List<VinAndPhoneDTO>> getVinPhoneListByExcel(byte[] bytes) {
        List<List<VinAndPhoneDTO>> resp = new ArrayList<>();
        String tempDir = System.getProperty("java.io.tmpdir");
        String fileName = "reads3Excel" + System.currentTimeMillis() + EXCEL_FORMATTER;
        String filePath = new File(tempDir, fileName).getPath();
        log.info("Creating temporary Excel file at {}", filePath);
        VinAndPhoneReadListener vinAndPhoneReadListener = new VinAndPhoneReadListener();
        try (OutputStream outputStream = new FileOutputStream(fileName)) {
            outputStream.write(bytes);
            outputStream.flush();
            EasyExcel.read(fileName, VinAndPhoneNumber.class, vinAndPhoneReadListener).sheet().doRead();
            resp = vinAndPhoneReadListener.getVinPhoneList();
        log.info("Successfully processed Excel file at {}", filePath);
        } catch (Exception e) {
            log.error("解析下载excel文件异常:", e);
        } finally {
            FileUtil.del(fileName);
        }
        return resp;
    }

    /**
     * 删除指定路径的Excel文件。
     *
     * @param filePath 要删除的文件路径。
     * @return 返回操作结果，成功返回true，失败返回false。
     */
    @Override
    public CommonResult<Boolean> deleteExcel(String filePath) {
        String pathKey = getUploadPathKey(filePath);
        log.info("删除excel文件，filePath:{}, pathKey:{}", filePath, pathKey);
        CompletableFuture<Boolean> completableFuture = CompletableFuture.supplyAsync(
                () -> fileService.deleteFile(FILE_CODE, pathKey), asyncThreadPool);
        Boolean resp = null;
        try {
            resp = completableFuture.get(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("获取s3删除结果异常，filePath:{}, 原因：", filePath, e);
            Thread.currentThread().interrupt();
        }
        return CommonResult.success(resp);
    }

}
