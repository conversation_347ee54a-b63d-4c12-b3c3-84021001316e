package com.jlr.ecp.notification.util.sign;

import com.jlr.ecp.notification.util.sign.AbstractSignature;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.TreeMap;
import java.util.UUID;

/**
 * SS 加签工具
 */
@Slf4j
public class SignatureEncoder extends AbstractSignature {
    protected String key;

    private SignatureEncoder() {
    }

    public SignatureEncoder(String path, String httpMethod, Map<String, String> headers, byte[] inputStreamBytes, String key, String secret) {
        this(path, httpMethod, headers, inputStreamBytes, key, secret, null);
    }

    public SignatureEncoder(String path, String httpMethod, Map<String, String> headers, byte[] inputStreamBytes, String key, String secret, String signatureMethod) {
        super(path, httpMethod, headers, signatureMethod);
        this.key = key;
        this.secret = secret;
        this.addCaToHeaders();
        this.setContentMD5(inputStreamBytes);
    }

    private void setContentMD5(byte[] inputStreamBytes) {
        addCaToHeaders(CA_SIGNATURE_CONTENT_MD5, base64AndMD5((null == inputStreamBytes) ? new byte[]{} : inputStreamBytes));
    }

    private void addCaToHeaders() {
        this.headers.put(CA_KEY, key);
        this.headers.put(CA_SIGNATURE_METHOD, signatureMethod);
        this.headers.put(CA_NONCE, UUID.randomUUID().toString());
        this.headers.put(CA_TIMESTAMP, String.valueOf(System.currentTimeMillis()));
    }

    /**
     * 加签成功后，返回新的headers并放入请求头部
     */
    @Override
    public void build(Map<String, Object> paramsMap, Map<String, Object> formParamsMap) throws NoSuchAlgorithmException, InvalidKeyException, UnsupportedEncodingException {
        String signString = buildStringToSign(paramsMap, formParamsMap);
        String sign = stringToSign(signString, this.signatureMethod, this.secret);
        log.info("生成的签名sign:==========>{}",sign);
        this.addCaToHeaders(CA_SIGNATURE, sign);
    }

    /**
     * 将headers合成一个字符串
     * 需要注意的是，HTTP头需要按照字母排序加入签名字符串
     * 同时所有加入签名的头的列表，需要用逗号分隔形成一个字符串，加入一个新HTTP头@"X-Ca-Signature-Headers"
     */
    @Override
    protected String buildHeaders() {
        //使用TreeMap,默认按照字母排序
        Map<String, String> headersToSign = new TreeMap<>();
        StringBuilder signHeadersStringBuilder = new StringBuilder();

        int flag = 0;
        for (Map.Entry<String, String> header : headers.entrySet()) {
            if (header.getKey().startsWith(CA_HEADER_TO_SIGN_PREFIX_SYSTEM)) {
                if (flag != 0) {
                    signHeadersStringBuilder.append(",");
                }
                flag++;
                signHeadersStringBuilder.append(header.getKey());
                headersToSign.put(header.getKey(), header.getValue());
            }
        }
        //同时所有加入签名的头的列表，需要用逗号分隔形成一个字符串，加入一个新HTTP头@"X-Ca-Signature-Headers"
        addCaToHeaders(CA_SIGNATURE_HEADERS, signHeadersStringBuilder.toString());

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> e : headersToSign.entrySet()) {
            sb.append(e.getKey()).append(':').append(e.getValue()).append(LF);
        }
        return sb.toString();
    }
}