package com.jlr.ecp.notification.dal.dataobject.history;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

@TableName("t_notification_history")
@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class NotificationHistory extends BaseDO {
    /**
     * 主键id, 自增
     * */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务线编码
     * */
    @TableField("business_code")
    private String businessCode;

    /**
     * 任务编码，雪花算法
     */
    @TableField("task_code")
    private String taskCode;

    /**
     *  任务实例编码
     * */
    @TableField("task_instance_code")
    private String taskInstanceCode;

    /**
     * 定时发送时间CRON;当发送类型为定时发送时的时间的CRON表达式
     */
    @TableField(value = "task_send_time")
    private LocalDateTime taskSendTime;

    /**
     * 总推送条数
     */
    @TableField(value = "send_total_count")
    private Integer sendTotalCount;

    /**
     * 推送成功条数
     */
    @TableField(value = "send_success_count")
    private Integer sendSuccessCount;

    /**
     * 推送失败条数
     */
    @TableField(value = "send_fail_count")
    private Integer sendFailCount;

    /**
     *  触达用户成功条数
     * */
    @TableField(value = "reach_user_success_count")
    private Integer reachUserSuccessCount;

    /**
     *  触达用户失败条数
     * */
    @TableField(value = "reach_user_fail_count")
    private Integer reachUserFailCount;

    /**
     *  触达用户结果统计是否完成, 0-没有完成；1-已完成
     * */
    @TableField(value = "reach_result_status")
    private Integer reachResultStatus;

    /**
     *  短链点击数量
     * */
    @TableField(value = "open_short_link_count")
    private Integer openShortLinkCount;

    /**
     *  租户号
     * */
    @TableField(value = "tenant_id")
    private Integer tenantId;
}
