package com.jlr.ecp.notification.enums.task;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *  条件类型 1：品牌 2：车辆产地 3：到期服务 4：实名状态 5：到期区间'
 *
 */
@AllArgsConstructor
@Getter
public enum AutoTaskConditionTypeEnum {
    BRAND(1, "品牌", "单选"),
    VEHICLE_ORIGIN(2, "车辆产地", "单选"),
    EXPIRATION_SERVICE(3, "到期服务", "单选"),
    REAL_NAME_STATUS(4, "实名状态", "仅支持在线服务相关通知"),
    EXPIRATION_INTERVAL(5, "到期区间", "最多支持365天");

    private final Integer type;

    private final String desc;

    private final String note;

    /**
     * 根据类型获取描述
     *
     * @param type 自动任务条件类型的代码，如果与枚举中的类型匹配，则返回对应的描述
     * @return 返回匹配类型的描述如果找不到匹配的类型，则返回空字符串
     */
    public static String getDescByType(Integer type) {
        for (AutoTaskConditionTypeEnum item : AutoTaskConditionTypeEnum.values()) {
            if (item.getType().equals(type)) {
                return item.getDesc();
            }
        }
        return "";
    }

    /**
     * 根据类型获取备注信息
     *
     * @param type 自动任务条件类型的整数表示，用于在枚举中查找匹配项
     * @return 如果找到匹配项，则返回备注信息；否则返回空字符串
     */
    public static String getNoteByType(Integer type) {
        for (AutoTaskConditionTypeEnum item : AutoTaskConditionTypeEnum.values()) {
            if (item.getType().equals(type)) {
                return item.getNote();
            }
        }
        return "";
    }
}
