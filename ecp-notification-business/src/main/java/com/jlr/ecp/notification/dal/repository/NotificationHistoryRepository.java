package com.jlr.ecp.notification.dal.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.notification.dal.dataobject.history.NotificationHistory;
import com.jlr.ecp.notification.req.history.SendLogPageReq;

import java.util.List;

public interface NotificationHistoryRepository {

    /**
     * 插入通知历史记录。
     *
     * @param notificationHistory 通知历史对象，包含需要插入的历史信息。
     * @return 返回一个布尔值，成功插入返回true，失败返回false。
     */
    Boolean insertHistory(NotificationHistory notificationHistory);

    /**
     * 根据实例代码更新通知历史记录。
     *
     * @param notificationHistory 通知历史记录对象，包含需要更新的数据。
     * @return 返回一个布尔值，如果更新成功则返回true，如果遇到异常则返回false。
     */
    Boolean updateByInstanceCode(NotificationHistory notificationHistory);

    /**
     * 根据实例代码列表查询通知历史记录。
     *
     * @param instanceCodeList 实例代码列表，用于查询通知历史的条件之一。
     * @return 返回一个通知历史记录的列表。如果查询过程中出现异常或者输入的实例代码列表为空，则返回一个空的列表。
     */
    List<NotificationHistory> queryHistoryByInstanceCodeList(List<String> instanceCodeList);


    /**
     * 批量根据ID更新通知历史记录。
     *
     * @param historyList 通知历史记录列表，不应为空。
     * @return 返回操作结果，如果操作成功返回true，否则返回false。
     */
    Boolean batchUpdateByIds(List<NotificationHistory> historyList);

    /**
     * 根据ID更新通知历史记录
     *
     * @param history 要更新的通知历史对象，包含要更新的字段和值
     * @return 返回一个布尔值，表示是否成功更新了数据库中的记录
     */
    Boolean updateById(NotificationHistory history);

    /**
     * 更新通知历史记录中的短链打开次数。
     *
     * @param instanceCode 任务实例编码，不能为空。
     * @param openShortLinkCount 新的短链打开次数。
     * @return 如果更新成功返回true，否则返回false。
     */
    Boolean updateHistoryOpenShortLinkCount(String instanceCode, Integer openShortLinkCount);

    /**
     * "根据任务实例代码查询通知历史记录"
     *
     * @param taskInstanceCode 任务实例代码，用于标识特定的任务实例
     * @return 如果找到对应的通知历史记录则返回该记录，否则返回null
     */
    NotificationHistory queryHistoryByInstanceCode(String taskInstanceCode);

    /**
     * 根据任务实例代码列表和发送日志分页请求
     *
     * @param sendLogPageReq 发送日志分页请求对象，包含分页查询所需的页码和每页大小等信息
     * @param allTaskCodeList 全部taskCodeList
     * @return 返回一个列表，其中包含查询到的通知历史记录
     */
    Page<NotificationHistory> selectPageByTaskInstanceCode(List<String> allTaskCodeList, SendLogPageReq sendLogPageReq);
}
