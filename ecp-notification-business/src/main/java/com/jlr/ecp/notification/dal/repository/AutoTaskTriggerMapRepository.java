package com.jlr.ecp.notification.dal.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.notification.dal.dataobject.task.auto.AutoTaskTriggerMapDO;

import java.util.List;

public interface AutoTaskTriggerMapRepository extends IService<AutoTaskTriggerMapDO> {

    /**
     * 批量插入自动任务触发器映射
     *
     * @param taskTriggerMapDOList 一组自动任务触发器映射对象
     * @return 如果批量保存成功，返回true；否则返回false
     */
    boolean batchInsertAutoTaskTrigger(List<AutoTaskTriggerMapDO> taskTriggerMapDOList);

    /**
     * 根据任务编码获取自动触发器列表
     *
     * @param taskCode 任务编码，用于查询自动触发器映射表中对应的任务
     * @return 返回一个自动任务触发器映射对象（AutoTaskTriggerMapDO）的列表，这些对象与给定任务编码相关联
     */
    List<AutoTaskTriggerMapDO> getAutoTriggerByTaskCode(String taskCode);
}
