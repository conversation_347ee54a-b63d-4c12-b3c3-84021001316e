package com.jlr.ecp.notification.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import javax.net.ssl.SSLContext;

@Slf4j
public class HttpClientUtil {
    public static CloseableHttpClient getNoSslHttpClient(Integer socketTimeout, Integer connectTimeOut) {
        CloseableHttpClient httpClient = null;
        SSLConnectionSocketFactory sslSocketFactory = null;
        try {
            // 忽略服务器证书验证
            SSLContext sslContext = SSLContextBuilder.create()
                    .loadTrustMaterial((chain, authType) -> true)
                    .build();

            sslSocketFactory = new SSLConnectionSocketFactory(
                    sslContext,
                    NoopHostnameVerifier.INSTANCE
            );
            httpClient = HttpClients.custom()
                    .setDefaultRequestConfig(RequestConfig.custom()
                            .setSocketTimeout(socketTimeout)
                            .setConnectTimeout(connectTimeOut)
                            .build())
                    .setSSLSocketFactory(sslSocketFactory)
                    .build();

        } catch (Exception e) {
            log.error("生成SSLConnectionSocketFactory异常：", e);
        }
        return httpClient;
    }

    public static CloseableHttpClient getHttpClient(Integer socketTimeout, Integer connectTimeOut) {
        CloseableHttpClient httpClient = null;
        try {
            httpClient = HttpClients.custom()
                    .setDefaultRequestConfig(RequestConfig.custom()
                            .setSocketTimeout(socketTimeout)
                            .setConnectTimeout(connectTimeOut)
                            .build())
                    .build();
        } catch (Exception e) {
            log.error("生成HttpClient异常：", e);
        }
        return httpClient;
    }
}
