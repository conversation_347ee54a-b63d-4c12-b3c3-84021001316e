package com.jlr.ecp.notification.controller.admin;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.notification.constant.BusinessConstants;
import com.jlr.ecp.notification.req.task.*;
import com.jlr.ecp.notification.req.task.auto.AutoTaskCreatReq;
import com.jlr.ecp.notification.req.task.auto.AutoTaskModifyTimeReq;
import com.jlr.ecp.notification.service.SmsAutoTaskService;
import com.jlr.ecp.notification.vo.task.SmsTaskPageListVo;
import com.jlr.ecp.notification.vo.task.TaskHistoryDetailVO;
import com.jlr.ecp.notification.vo.task.TaskHistoryPageListVo;
import com.jlr.ecp.notification.vo.task.TaskStatusVO;
import com.jlr.ecp.notification.vo.task.auto.AutoTaskConditionVO;
import com.jlr.ecp.notification.vo.task.auto.AutoTaskDetailVO;
import com.jlr.ecp.notification.vo.task.auto.AutoTemplateNameListVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Tag(name = "自动任务管理")
@RestController
@RequestMapping("/sms/task")
@Slf4j
public class SmsAutoTaskController {
    @Resource
    private SmsAutoTaskService autoTaskService;

    @PostMapping("/list")
    @Operation(summary = "自动任务列表页")
    @PreAuthorize("@ss.hasAnyPermissions('notification:smstask-automatic:list', 'notification:lresmstask-automatic:list', 'notification:bgsmstask-automatic:list')")
    public CommonResult<PageResult<SmsTaskPageListVo>> taskPageList(@RequestBody TaskPageListReq taskPageListReq) {
        log.info("开始短信任务管理列表页查询, taskPageListReq:{}", taskPageListReq);
        taskPageListReq.setTaskType(BusinessConstants.TASK_TYPE.AUTO);
        PageResult<SmsTaskPageListVo> taskPageListVo = autoTaskService.getTaskPageList(taskPageListReq);
        return CommonResult.success(taskPageListVo);
    }

    @GetMapping("/statusDesc")
    @Operation(summary = "自动任务列表页-任务状态描述")
    @PreAuthorize("@ss.hasPermission('notification:smstask-automatic:list')")
    public CommonResult<List<TaskStatusVO>> taskPageList() {
        log.info("自动任务列表页-任务状态描述");
        return CommonResult.success(autoTaskService.getAutoTaskStatusVO());
    }

    @GetMapping("/nameList")
    @Operation(summary = "通知日志明细页面 通知任务名称列表")
    @Parameter(name = "taskType", description = "任务类型 1自动任务 2手动任务 默认不传", required = false, example = "1")
    @PreAuthorize("@ss.hasAnyPermissions('notification:smstask-automatic:detail', 'notification:lresmstask-automatic:detail', 'notification:bgsmstask-automatic:detail')")
    public CommonResult<List<String>> getTaskNameList(@RequestParam(required = false) Integer taskType) {
        return CommonResult.success(autoTaskService.getTaskNameList(taskType));
    }

    @GetMapping("/detail")
    @Operation(summary = "自动任务详情页")
    @PreAuthorize("@ss.hasAnyPermissions('notification:smstask-automatic:detail', 'notification:lresmstask-automatic:detail', 'notification:bgsmstask-automatic:detail')")
    public CommonResult<AutoTaskDetailVO> taskDetail(@Validated @SpringQueryMap TaskDetailReq taskDetailReq) {
        log.info("开始短信通知任务详情查询, taskDetailReq:{}", taskDetailReq);
        return autoTaskService.getAutoTaskDetail(taskDetailReq);
    }


    @GetMapping("/history")
    @Operation(summary = "自动任务编辑记录历史")
    @PreAuthorize("@ss.hasAnyPermissions('notification:smstask-automatic:detail', 'notification:lresmstask-automatic:detail', 'notification:bgsmstask-automatic:detail')")
    public CommonResult<PageResult<TaskHistoryPageListVo>> taskHistoryPageList(@Validated @SpringQueryMap
                                                                                   TaskHistoryReq taskHistoryReq) {
        log.info("开始短信编辑历史记录, taskHistoryReq:{}", taskHistoryReq);
        PageResult<TaskHistoryPageListVo> pageResult = autoTaskService.getTaskHistoryPageList(taskHistoryReq);
        return CommonResult.success(pageResult);
    }

    /**
     * 新增的编辑记录详情接口
     */
    @GetMapping("/history/detail")
    @Operation(summary = "自动任务编辑记录详情")
    @PreAuthorize("@ss.hasAnyPermissions('notification:smstask-automatic:detail', 'notification:lresmstask-automatic:detail', 'notification:bgsmstask-automatic:detail')")
    public CommonResult<TaskHistoryDetailVO> getModifyDetail(
            @RequestParam("id") @Schema(description = "日志ID") Long id) {
        log.info("获取自动任务编辑记录详情, id:{}", id);
        TaskHistoryDetailVO detail = autoTaskService.getAutoTaskModifyDetail(id);
        return CommonResult.success(detail);
    }

    @PostMapping("/modify/time")
    @Operation(summary = "自动任务编辑")
    @PreAuthorize("@ss.hasAnyPermissions('notification:smstask-automatic:edit', 'notification:lresmstask-automatic:edit', 'notification:bgsmstask-automatic:edit')")
    public CommonResult<Boolean> modifyTaskTime(@Validated @RequestBody AutoTaskModifyTimeReq taskModifyReq) {
        log.info("通知任务编辑日志入参, taskModifyReq:{}", taskModifyReq);
        return autoTaskService.modifySendTime(taskModifyReq);
    }

    @PostMapping("/modify/status/start")
    @Operation(summary = "自动任务启用")
    @PreAuthorize("@ss.hasAnyPermissions('notification:smstask-automatic:start-using', 'notification:lresmstask-automatic:start-using', 'notification:bgsmstask-automatic:start-using')")
    public CommonResult<Boolean> modifyTaskStatusStart(@Validated @RequestBody TaskModifyStatusReq taskModifyStatusReq) {
        log.info("启用入参, taskModifyStatusReq:{}", taskModifyStatusReq);
        return autoTaskService.modifyTaskStatus(taskModifyStatusReq);
    }

    @PostMapping("/modify/status/stop")
    @Operation(summary = "自动任务禁用")
    @PreAuthorize("@ss.hasAnyPermissions('notification:smstask-automatic:stop-using', 'notification:lresmstask-automatic:stop-using', 'notification:bgsmstask-automatic:stop-using')")
    public CommonResult<Boolean> modifyTaskStatusStop(@Validated @RequestBody TaskModifyStatusReq taskModifyStatusReq) {
        log.info("禁用入参, taskModifyStatusReq:{}", taskModifyStatusReq);
        return autoTaskService.modifyTaskStatus(taskModifyStatusReq);
    }

    @GetMapping("/template/nameList")
    @Operation(summary = "获取自动任务模板名称列表")
    @PreAuthorize("@ss.hasAnyPermissions('notification:smstask-automatic:detail', 'notification:lresmstask-automatic:detail', 'notification:bgsmstask-automatic:detail')")
    public CommonResult<List<AutoTemplateNameListVO>> getAutoTemplateNameList(@RequestParam(value = "businessCode", required = false) String businessCode) {
        return CommonResult.success(autoTaskService.getAutoTemplateNameList(businessCode));
    }

    @GetMapping("/template/content")
    @Operation(summary = "依据taskCode获取自动模板内容")
    @PreAuthorize("@ss.hasAnyPermissions('notification:smstask-automatic:detail', 'notification:lresmstask-automatic:detail', 'notification:bgsmstask-automatic:detail')")
    public CommonResult<String> getAutoTemplateContent(@RequestParam(value = "taskCode", required = true) String taskCode) {
        return CommonResult.success(autoTaskService.getAutoTemplateContent(taskCode));
    }

    @GetMapping("/condition")
    @Operation(summary = "自动任务筛选条件")
    @PreAuthorize("@ss.hasAnyPermissions('notification:smstask-automatic:detail', 'notification:lresmstask-automatic:detail', 'notification:bgsmstask-automatic:detail')")
    public CommonResult<AutoTaskConditionVO> getAutoTaskCondition() {
        return CommonResult.success(autoTaskService.queryAutoTaskCondition());
    }

    @PutMapping("/add")
    @Operation(summary = "新增自动通知任务")
    @PreAuthorize("@ss.hasAnyPermissions('notification:smstask-automatic:create', 'notification:lresmstask-automatic:create', 'notification:bgsmstask-automatic:create')")
    public CommonResult<Boolean> addManualTask(@Validated @RequestBody AutoTaskCreatReq autoTaskCreatReq) {
        log.info("新增自动通知任务, autoTaskCreatReq:{}", autoTaskCreatReq);
        return autoTaskService.addAutoTask(autoTaskCreatReq);
    }

    @PostMapping("/batchStart")
    @Operation(summary = "自动任务批量启用")
    @PreAuthorize("@ss.hasPermission('notification:smstask-automatic:start-using')")
    public CommonResult<String> autoTaskBatchStart(@RequestBody BatchTaskModifyStatusReq batchStatusReq) {
        return autoTaskService.batchStartTaskStatus(batchStatusReq);
    }

    @PostMapping("/batchStop")
    @Operation(summary = "自动任务批量禁用")
    @PreAuthorize("@ss.hasPermission('notification:smstask-automatic:stop-using')")
    public CommonResult<String> autoTaskBatchStop(@RequestBody BatchTaskModifyStatusReq batchStatusReq) {
        return autoTaskService.batchStopTaskStatus(batchStatusReq);
    }
}
