package com.jlr.ecp.notification.service.manual.impl;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.dal.dataobject.shortlink.MiniCodeConfigDO;
import com.jlr.ecp.notification.dal.dataobject.shortlink.ShortLinkClickTotalDO;
import com.jlr.ecp.notification.dal.dataobject.shortlink.ShortLinkClickUserDO;
import com.jlr.ecp.notification.dal.repository.shortlink.MiniCodeConfigRepository;
import com.jlr.ecp.notification.dal.repository.shortlink.ShortLinkClickTotalRepository;
import com.jlr.ecp.notification.dal.repository.shortlink.ShortLinkClickUserRepository;
import com.jlr.ecp.notification.dto.shortlink.ManualShortLinkClickDTO;
import com.jlr.ecp.notification.vo.shortlink.QrCodeConfigParamVO;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ManualShortLinkClickServiceImplTest {

    @Mock
    private ShortLinkClickTotalRepository mockShortLinkClickTotalRepository;
    @Mock
    private ShortLinkClickUserRepository mockShortLinkClickUserRepository;

    @InjectMocks
    @Spy
    private ManualShortLinkClickServiceImpl manualShortLinkClickService;


    @Mock
    private ShortLinkClickTotalRepository shortLinkClickTotalRepository;

    @Mock
    private ShortLinkClickUserRepository shortLinkClickUserRepository;

    @Mock
    private MiniCodeConfigRepository miniCodeConfigRepository;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试用例1：jlrId为空，数据库中存在对应的ShortLinkClickTotalDO记录
     */
    @Test
    public void testUpdateShortLinkClick_JlrIdNull_ExistingTotalRecord() {
        // 准备测试数据
        ManualShortLinkClickDTO clickDTO = new ManualShortLinkClickDTO();
        clickDTO.setUrlCode("testUrlCode");

        ShortLinkClickTotalDO existingTotalDO = ShortLinkClickTotalDO.builder()
                .urlCode("testUrlCode")
                .clickTotal(5L)
                .build();

        // Mock行为
        when(shortLinkClickTotalRepository.selectTotalByUrlCode("testUrlCode")).thenReturn(existingTotalDO);

        // 执行测试
        CommonResult<String> result = manualShortLinkClickService.updateShortLinkClick(clickDTO);

        // 验证结果
        assertEquals(CommonResult.success("手动短链点击率更新成功"), result);
        verify(shortLinkClickTotalRepository).saveOrUpdate(argThat(totalDO ->
                "testUrlCode".equals(totalDO.getUrlCode()) && totalDO.getClickTotal() == 6L));
    }

    /**
     * 测试用例2：jlrId为空，数据库中不存在对应的ShortLinkClickTotalDO记录
     */
    @Test
    public void testUpdateShortLinkClick_JlrIdNull_NoTotalRecord() {
        // 准备测试数据
        ManualShortLinkClickDTO clickDTO = new ManualShortLinkClickDTO();
        clickDTO.setUrlCode("testUrlCode");

        // Mock行为
        when(shortLinkClickTotalRepository.selectTotalByUrlCode("testUrlCode")).thenReturn(null);

        // 执行测试
        CommonResult<String> result = manualShortLinkClickService.updateShortLinkClick(clickDTO);

        // 验证结果
        assertEquals(CommonResult.success("手动短链点击率更新成功"), result);
        verify(shortLinkClickTotalRepository).saveOrUpdate(argThat(totalDO ->
                "testUrlCode".equals(totalDO.getUrlCode()) && totalDO.getClickTotal() == 1L));
    }

    /**
     * 测试用例3：jlrId不为空，数据库中存在对应的ShortLinkClickUserDO记录
     */
//    @Test
//    public void testUpdateShortLinkClick_JlrIdNotNull_ExistingUserRecord() {
//        // 准备测试数据
//        ManualShortLinkClickDTO clickDTO = new ManualShortLinkClickDTO();
//        clickDTO.setUrlCode("testUrlCode");
//        clickDTO.setJlrId("testJlrId");
//
//        ShortLinkClickUserDO existingUserDO = ShortLinkClickUserDO.builder()
//                .urlCode("testUrlCode")
//                .jlrId("testJlrId")
//                .clickCount(3)
//                .build();
//
//        // Mock行为
//        when(shortLinkClickUserRepository.selectUserByJlrId("testUrlCode", "testJlrId")).thenReturn(existingUserDO);
//
//        // 执行测试
//        CommonResult<String> result = manualShortLinkClickService.updateShortLinkClick(clickDTO);
//
//        // 验证结果
//        assertEquals(CommonResult.success("手动短链点击率更新成功"), result);
//        verify(shortLinkClickUserRepository).saveOrUpdate(argThat(userDO ->
//                "testUrlCode".equals(userDO.getUrlCode()) && userDO.getClickCount() == 4));
//    }

    /**
     * 测试用例4：jlrId不为空，数据库中不存在对应的ShortLinkClickUserDO记录
     */
    @Test
    public void testUpdateShortLinkClick_JlrIdNotNull_NoUserRecord() {
        // 准备测试数据
        ManualShortLinkClickDTO clickDTO = new ManualShortLinkClickDTO();
        clickDTO.setUrlCode("testUrlCode");
        clickDTO.setJlrId("testJlrId");

        // Mock行为
        when(shortLinkClickUserRepository.selectUserByJlrId("testUrlCode", "testJlrId")).thenReturn(null);

        // 执行测试
        CommonResult<String> result = manualShortLinkClickService.updateShortLinkClick(clickDTO);

        // 验证结果
        assertEquals(CommonResult.success("手动短链点击率更新成功"), result);
        verify(shortLinkClickUserRepository).saveOrUpdate(argThat(userDO ->
                "testUrlCode".equals(userDO.getUrlCode()) && userDO.getClickCount() == 1));
    }

    /**
     * 测试用例5：urlCode为空，验证函数是否正确处理空值
     */
    @Test
    public void testUpdateShortLinkClick_UrlCodeNull() {
        // 准备测试数据
        ManualShortLinkClickDTO clickDTO = new ManualShortLinkClickDTO();
        clickDTO.setUrlCode("");

        // 执行测试
        CommonResult<String> result = manualShortLinkClickService.updateShortLinkClick(clickDTO);

        // 验证结果
        assertEquals(CommonResult.success("手动短链点击率更新成功"), result);
    }

    /**
     * TC01: configId无效，配置不存在
     * 预期：返回空VO，状态为SUCCESS
     */
    @Test
    public void testGetQrCodeConfigParam_ConfigIdNotFound() {
        // Arrange
        String configId = "invalidId";
        when(miniCodeConfigRepository.selectByConfigId(configId)).thenReturn(null);

        // Act
        CommonResult<QrCodeConfigParamVO> result = manualShortLinkClickService.getQrCodeConfigParam(configId);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        QrCodeConfigParamVO paramVO = result.getData();
        assertNotNull(paramVO);
        assertNull(paramVO.getProductCode());
        assertNull(paramVO.getParam1());
        assertNull(paramVO.getParam2());
        assertNull(paramVO.getParam3());
        assertNull(paramVO.getParam4());
        assertNull(paramVO.getParam5());
    }

    /**
     * TC02: 配置存在但所有参数为空
     * 预期：返回空VO，状态为SUCCESS
     */
    @Test
    public void testGetQrCodeConfigParam_AllParamsEmpty() {
        // Arrange
        String configId = "validId";
        MiniCodeConfigDO configDO = new MiniCodeConfigDO();
        when(miniCodeConfigRepository.selectByConfigId(configId)).thenReturn(configDO);

        // Act
        CommonResult<QrCodeConfigParamVO> result = manualShortLinkClickService.getQrCodeConfigParam(configId);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        QrCodeConfigParamVO paramVO = result.getData();
        assertNotNull(paramVO);
        assertNull(paramVO.getProductCode());
        assertNull(paramVO.getParam1());
        assertNull(paramVO.getParam2());
        assertNull(paramVO.getParam3());
        assertNull(paramVO.getParam4());
        assertNull(paramVO.getParam5());
    }

    /**
     * TC03: 配置存在且部分参数非空
     * 预期：仅非空字段被赋值
     */
    @Test
    public void testGetQrCodeConfigParam_SomeParamsSet() {
        // Arrange
        String configId = "validId";
        MiniCodeConfigDO configDO = new MiniCodeConfigDO();
        configDO.setProductCodeParam("CAR-2023");
        configDO.setParam1("value1");
        configDO.setParam3("value3");
        when(miniCodeConfigRepository.selectByConfigId(configId)).thenReturn(configDO);

        // Act
        CommonResult<QrCodeConfigParamVO> result = manualShortLinkClickService.getQrCodeConfigParam(configId);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        QrCodeConfigParamVO paramVO = result.getData();
        assertNotNull(paramVO);
        assertEquals("CAR-2023", paramVO.getProductCode());
        assertEquals("value1", paramVO.getParam1());
        assertNull(paramVO.getParam2());
        assertEquals("value3", paramVO.getParam3());
        assertNull(paramVO.getParam4());
        assertNull(paramVO.getParam5());
    }

    /**
     * TC04: 配置存在且所有参数非空
     * 预期：完整VO返回
     */
    @Test
    public void testGetQrCodeConfigParam_AllParamsSet() {
        // Arrange
        String configId = "validId";
        MiniCodeConfigDO configDO = new MiniCodeConfigDO();
        configDO.setProductCodeParam("CAR-2023");
        configDO.setParam1("p1");
        configDO.setParam2("p2");
        configDO.setParam3("p3");
        configDO.setParam4("p4");
        configDO.setParam5("p5");
        when(miniCodeConfigRepository.selectByConfigId(configId)).thenReturn(configDO);

        // Act
        CommonResult<QrCodeConfigParamVO> result = manualShortLinkClickService.getQrCodeConfigParam(configId);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        QrCodeConfigParamVO paramVO = result.getData();
        assertNotNull(paramVO);
        assertEquals("CAR-2023", paramVO.getProductCode());
        assertEquals("p1", paramVO.getParam1());
        assertEquals("p2", paramVO.getParam2());
        assertEquals("p3", paramVO.getParam3());
        assertEquals("p4", paramVO.getParam4());
        assertEquals("p5", paramVO.getParam5());
    }


}
