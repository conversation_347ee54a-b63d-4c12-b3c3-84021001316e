package com.jlr.ecp.notification.req.task;

import com.jlr.ecp.framework.common.validation.InEnum;
import com.jlr.ecp.notification.enums.BusinessEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 *  通知任务编辑入参
 *
 * <AUTHOR>
 * */

@Data
@Schema(description = "通知任务编辑入参")
public class TaskModifyReq {

    @Schema(description = "任务code")
    @NotBlank(message = "任务code不能为空")
    private String taskCode;

    @Schema(description = "任务名称")
    @NotBlank(message = "任务名称不能为空")
    private String taskName;

    @Schema(description = "校验通过文件url 提交时必传")
    private String messageFile;

    @Schema(description = "错误消息文件URL 可以先保存为草稿")
    private String errorMsgFile;

    @Schema(description = "发送类型 1：实时发送 2：定时发送")
    @NotNull(message = "发送类型不能为空")
    @InEnum(value = BusinessEnum.TaskSendType.class,message = "发送类型必须在指定范围 {value}")
    private Integer taskSendType;

    @Schema(description = "引用的通知模板code")
    private String messageTemplateCode;

    @Schema(description = "上传文件类型 1：手机号 2：VIN+手机号")
    @NotNull(message = "手机号文件类型不能为空")
    private Integer submitFileType;

    @Schema(description = "通知发送时间, 格式 yyyy-MM-dd HH:mm 选择定时发送必传")
    private String sendTime;

    @Schema(description = "保存操作类型, 1 保存草稿 2保存并提交")
    @NotNull(message = "操作类型不能为空")
    @InEnum(value = BusinessEnum.OperationType.class,message = "操作类型必须在指定范围 {value}")
    private Integer addDataType;

    @Schema(description = "品牌签名内容 1路虎中国 2捷豹路虎中国")
    private Integer signBrandType;

    @Schema(description = "发送通道 1营销短信 2通知短信")
    private Integer sendChannelType;
}
