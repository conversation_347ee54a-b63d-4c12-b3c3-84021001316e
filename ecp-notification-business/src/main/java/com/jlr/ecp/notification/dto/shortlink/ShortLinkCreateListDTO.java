package com.jlr.ecp.notification.dto.shortlink;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Schema(description = "短链生成记录列表页")
@Data
public class ShortLinkCreateListDTO extends PageParam {

    @NotBlank(message = "操作时间排序不能为空")
    @Schema(description = "操作时间排序", requiredMode = Schema.RequiredMode.REQUIRED,example = "desc:降序，asc：升序")
    private String operatorTimeSort;
}
