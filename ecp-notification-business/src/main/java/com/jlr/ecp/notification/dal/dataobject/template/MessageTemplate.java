package com.jlr.ecp.notification.dal.dataobject.template;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;


/**
 *  短信模板实体
 *
 * <AUTHOR>
 * */
@TableName("t_message_template")
@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class MessageTemplate extends BaseDO {
    /**
     * 主键id, 自增
     * */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务线编码
     * */
    @TableField(value = "business_code")
    private String businessCode;

    /**
     * 模板编码
     * */
    @TableField(value = "template_code")
    private String templateCode;

    /**
     * 模板名称
     * */
    @TableField(value = "template_name")
    private String templateName;

    /**
     * 模板类型, @see com.jlr.ecp.notification.enums.template.SmsTemplateTypeEnum
     * */
    @TableField(value = "template_type")
    private Integer templateType;

    /**
     *  自动模板类型：1系统实时类 2：自动配置类
     * */
    @TableField(value = "auto_type")
    private Integer autoType;

    /**
     *  模板内容
     * */
    @TableField(value = "template_content")
    private String templateContent;

    /**
     * 模板的变量
     * */
    @TableField(value = "template_variables")
    private String templateVariables;

    /**
     * 模板场景说明
     * */
    @TableField(value = "template_remarks")
    private String templateRemarks;

    /**
     *  租户号
     * */
    @TableField(value = "tenant_id")
    private Integer tenantId;
}
