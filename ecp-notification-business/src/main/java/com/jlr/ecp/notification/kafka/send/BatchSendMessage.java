package com.jlr.ecp.notification.kafka.send;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchSendMessage {
    /**
     * 账号, 必填
     * */
    private String account;

    /**
     * 密码, 必填
     * */
    private String password;

    /**
     * 发送数据，限制100， 必填
     * */
    private List<SendMessage> data;
}
