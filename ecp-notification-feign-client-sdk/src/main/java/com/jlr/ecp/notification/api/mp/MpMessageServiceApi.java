package com.jlr.ecp.notification.api.mp;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.api.dto.MpExpireSubscriptionDTO;
import com.jlr.ecp.notification.api.dto.SendMpMsgDTO;
import com.jlr.ecp.notification.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 小程序模板消息相关服务")
public interface MpMessageServiceApi {
    String PREFIX = ApiConstants.PREFIX;


    @GetMapping(PREFIX + "/get/mp/expire/message/allId")
    @Operation(summary = "获取小程序到期模板消息所有id")
    CommonResult<List<Long>> getMpExpireMsgAllId();

    @PostMapping(PREFIX + "/get/mp/expire/message/data")
    @Operation(summary = "获取小程序到期模板消息")
    CommonResult<List<MpExpireSubscriptionDTO>> getMpExpireMsgByIdList(@RequestBody List<Long> idList);

    @PostMapping(PREFIX + "/get/mp/expire/message/data/by/taskCode")
    @Operation(summary = "发送小程序到期消息")
    CommonResult<Integer> sendMpExpireMsg(@RequestBody List<SendMpMsgDTO> sendMpMsgDTOList);
}
