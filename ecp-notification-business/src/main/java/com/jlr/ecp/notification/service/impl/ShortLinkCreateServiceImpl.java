package com.jlr.ecp.notification.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.notification.dal.dataobject.shortlink.MiniCodeConfigDO;
import com.jlr.ecp.notification.dal.dataobject.shortlink.MiniCodeGenerateRecordDO;
import com.jlr.ecp.notification.dal.dataobject.shortlink.ShortLinkClickTotalDO;
import com.jlr.ecp.notification.dal.dataobject.shortlink.ShortLinkCreateRecordDO;
import com.jlr.ecp.notification.dal.repository.ShortLinkCreateRepository;
import com.jlr.ecp.notification.dal.repository.shortlink.MiniCodeConfigRepository;
import com.jlr.ecp.notification.dal.repository.shortlink.MiniCodeGenerateRepository;
import com.jlr.ecp.notification.dal.repository.shortlink.ShortLinkClickTotalRepository;
import com.jlr.ecp.notification.dal.repository.shortlink.ShortLinkClickUserRepository;
import com.jlr.ecp.notification.dto.shortlink.QrCodeCreateDTO;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkCreateDTO;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkCreateListDTO;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkRemarkDTO;
import com.jlr.ecp.notification.enums.BrandCodeEnum;
import com.jlr.ecp.notification.enums.BrandToUrlEum;
import com.jlr.ecp.notification.enums.shortlink.ShortLinkStatusEnum;
import com.jlr.ecp.notification.resp.WeChatQrCodeResp;
import com.jlr.ecp.notification.service.ShortLinkCreateService;
import com.jlr.ecp.notification.service.ShortLinkService;
import com.jlr.ecp.notification.util.GetParamUtil;
import com.jlr.ecp.notification.util.OperatorUtil;
import com.jlr.ecp.notification.util.WeChatUtil;
import com.jlr.ecp.notification.util.sms.TimeFormatUtil;
import com.jlr.ecp.notification.vo.shortlink.QrCodeCreateVO;
import com.jlr.ecp.notification.vo.shortlink.ShortLinkClickVO;
import com.jlr.ecp.notification.vo.shortlink.ShortLinkCreateBaseUrlVO;
import com.jlr.ecp.notification.vo.shortlink.ShortLinkCreateListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class ShortLinkCreateServiceImpl implements ShortLinkCreateService {
    @Resource
    private Snowflake snowflake;

    @Resource
    private ShortLinkCreateRepository shortLinkCreateRepository;

    @Resource
    private ShortLinkService shortLinkService;

    @Resource
    private ShortLinkClickTotalRepository clickTotalRepository;

    @Resource
    private ShortLinkClickUserRepository userClickRepository;

    @Resource
    private MiniCodeConfigRepository miniCodeConfigRepository;

    @Resource
    private MiniCodeGenerateRepository miniCodeGenerateRepository;

    @Resource
    private WeChatUtil weChatUtil;


    /**
     * 根据品牌代码获取基础URL名称
     *
     * @param brandCode 品牌代码，用于识别特定品牌和对应的资源位置
     * @return 返回基础URLVO
     */
    @Override
    public List<ShortLinkCreateBaseUrlVO> getBaseUrlByBrandCode(String brandCode) {
        log.info("根据品牌代码获取基础URL, brandCode:{}", brandCode);
        List<BrandToUrlEum> brandToUrlEumList = BrandToUrlEum.getPathInfoByBrandCode(brandCode);
        List<ShortLinkCreateBaseUrlVO> resp = new ArrayList<>();
        if (CollUtil.isEmpty(brandToUrlEumList)) {
            return resp;
        }
        for (BrandToUrlEum brandToUrlEum : brandToUrlEumList) {
            ShortLinkCreateBaseUrlVO baseUrlVO = ShortLinkCreateBaseUrlVO.builder()
                    .urlName(brandToUrlEum.getUrlName()+"("+brandToUrlEum.getUrlType()+")")
                    .urlType(brandToUrlEum.getUrlType())
                    .url(brandToUrlEum.getBrandToUrl())
                    .brandId(brandToUrlEum.getBrandId())
                    .build();
            resp.add(baseUrlVO);
        }
        return resp;
    }

    /**
     * 创建短链
     *
     * @param createDTO 包含创建短链所需信息的数据传输对象
     * @return 创建的短链的字符串表示
     */
    @Override
    public String createShortLink(ShortLinkCreateDTO createDTO) {
        log.info("创建短链, createDTO:{}", createDTO);
        if (Objects.isNull(createDTO)) {
            return "";
        }
        //1.入库生成短链的信息
        String urlCode = "URL" + snowflake.nextIdStr();
        ShortLinkCreateRecordDO createRecordDO = buildShortLinkCreateRecordDO(createDTO, urlCode);
        shortLinkCreateRepository.save(createRecordDO);
        //2.调用短链生成
        String shortLink = genShortLink(createDTO, urlCode);
        //3.更新短链生成的结果
        if (StringUtils.isNotBlank(shortLink)) {
            createRecordDO.setShortLink(shortLink);
            createRecordDO.setExpiryDate(LocalDateTime.now().plusDays(30));
            LambdaQueryWrapper<ShortLinkCreateRecordDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ShortLinkCreateRecordDO::getConfigId, createRecordDO.getConfigId());
            shortLinkCreateRepository.update(createRecordDO, queryWrapper);
        }
        return shortLink;
    }


    /**
     * 获取短链列表信息
     *
     * @param createListDTO 短链创建列表的查询条件传输对象，包含分页和查询参数
     * @return 返回一个包含短链创建列表视图对象的分页结果，以及总记录数
     */
    @Override
    public PageResult<ShortLinkCreateListVO> getShortLinkList(ShortLinkCreateListDTO createListDTO) {
        log.info("获取短链列表信息, createListDTO:{}", createListDTO);
        Page<ShortLinkCreateRecordDO> pageResp = shortLinkCreateRepository.pageList(createListDTO);
        List<ShortLinkCreateRecordDO> createRecordDOList = pageResp.getRecords();
        if (CollUtil.isEmpty(createRecordDOList)) {
            return PageResult.empty();
        }
        List<ShortLinkCreateListVO> createListVOList = buildShortLinkCreateListVO(createRecordDOList);
        return new PageResult<>(createListVOList, pageResp.getTotal());
    }

    /**
     * 更新短链接的备注信息
     *
     * @param remarkDTO 包含配置ID和新备注信息的DTO
     * @return 更新操作的结果字符串
     */
    @Override
    public CommonResult<String> updateRemark(ShortLinkRemarkDTO remarkDTO) {
        ShortLinkCreateRecordDO createRecordDO = shortLinkCreateRepository.getRecordDoByConfigId(remarkDTO.getConfigId());
        if (Objects.isNull(createRecordDO)) {
            return CommonResult.error(303061, "依据configId查询数据为空");
        }
        createRecordDO.setRemark(remarkDTO.getRemarks());
        shortLinkCreateRepository.updateById(createRecordDO);
        return CommonResult.success("短链生成成功");
    }

    /**
     *  查询短链的点击详情
     *  @param urlCode 短链的唯一编码
     *  @return CommonResult<ShortLinkClickVO> 返回值为短链点击VO
     * */
    @Override
    public CommonResult<ShortLinkClickVO> queryShortLinkClick(String urlCode) {
        log.info("查询短链的点击详情, urlCode:{}", urlCode);
        if (StringUtils.isBlank(urlCode)) {
            return CommonResult.success(null);
        }
        ShortLinkClickVO shortLinkClickVO = new ShortLinkClickVO();
        ShortLinkClickTotalDO clickTotalDO = clickTotalRepository.selectTotalByUrlCode(urlCode);
        if (Objects.isNull(clickTotalDO)) {
            shortLinkClickVO.setClickTotal(0L);
            shortLinkClickVO.setUserClickCount(0L);
            return CommonResult.success(shortLinkClickVO);
        }
        if (Objects.isNull(clickTotalDO.getClickTotal())) {
            shortLinkClickVO.setClickTotal(0L);
        } else {
            shortLinkClickVO.setClickTotal(clickTotalDO.getClickTotal());
        }
        Long userClickCount = userClickRepository.queryUserCountByUrlCode(urlCode);
        if (Objects.isNull(userClickCount)) {
            shortLinkClickVO.setUserClickCount(0L);
        } else {
            shortLinkClickVO.setUserClickCount(userClickCount);
        }
        return CommonResult.success(shortLinkClickVO);
    }


    /**
     * 根据提供的信息生成短链接
     *
     * @param createDTO 包含创建短链接所需信息的数据传输对象
     * @param urlCode d短链唯一编码
     * @return 生成的短链接字符串，如果无法生成则返回空字符串
     */
    public String genShortLink(ShortLinkCreateDTO createDTO, String urlCode) {
        log.info("根据提供的信息生成短链接, createDTO:{}, urlCode:{}", createDTO, urlCode);
        CommonResult<String> urlResp;
        String baseUrl = BrandToUrlEum.getUrlByBrandCodeUrlType(createDTO.getBrandCode(), createDTO.getUrlType());
        String path = GetParamUtil.getParamPath(baseUrl, createDTO.getParams());
        path += "&urlCode=" + urlCode;
        if (BrandCodeEnum.LAND_ROVER.getBrandCode().equals(createDTO.getBrandCode())) {
            urlResp = shortLinkService.genShortLink(path);
        } else {
            urlResp = shortLinkService.genJaguarLink(path, null);
        }
        if (Objects.nonNull(urlResp)) {
            return urlResp.getData();
        }
        return "";
    }

    /**
     * 根据短链创建记录列表构建短链创建列表
     *
     * @param createRecordDOList 短链创建记录列表，代表从数据库或其他来源获取的原始数据记录
     * @return 返回一个短链创建列表视图对象列表，每个视图对象对应一个输入记录，并且包含了界面展示所需的信息
     */
    private List<ShortLinkCreateListVO> buildShortLinkCreateListVO(List<ShortLinkCreateRecordDO> createRecordDOList) {
        List<ShortLinkCreateListVO> resp = new ArrayList<>();
        for (ShortLinkCreateRecordDO createRecordDO : createRecordDOList) {
            resp.add(buildShortLinkCreateVO(createRecordDO));
        }
        return resp;
    }

    /**
     * 根据短链创建记录数据对象构建短链创建列表值对象
     *
     * @param createRecordDO 短链创建记录数据对象，包含短链的相关信息如果为null，则返回一个空的ShortLinkCreateListVO对象
     * @return ShortLinkCreateListVO对象，包含转换后的短链信息用于前端展示
     */
    private ShortLinkCreateListVO buildShortLinkCreateVO(ShortLinkCreateRecordDO createRecordDO ) {
        if (Objects.isNull(createRecordDO)) {
            return ShortLinkCreateListVO.builder().build();
        }
        ShortLinkCreateListVO createListVO = ShortLinkCreateListVO.builder()
                .configId(createRecordDO.getConfigId())
                .urlCode(createRecordDO.getUrlCode())
                .operator(createRecordDO.getOperator())
                .createTime(TimeFormatUtil.timeToStringByFormat(createRecordDO.getCreatedTime(), TimeFormatUtil.formatter_6))
                .brandCode(createRecordDO.getBrandCode())
                .baseUrl(createRecordDO.getBaseUrl())
                .params(createRecordDO.getParams())
                .configContent(createRecordDO.getConfigContent())
                .shortLink(createRecordDO.getShortLink())
                .expiryDate(TimeFormatUtil.timeToStringByFormat(createRecordDO.getExpiryDate(), TimeFormatUtil.formatter_7))
                .remark(createRecordDO.getRemark())
                .build();
        if (Objects.nonNull(createRecordDO.getExpiryDate())
                && LocalDateTime.now().isAfter(createRecordDO.getExpiryDate())) {
            createListVO.setShortLinkStatus(ShortLinkStatusEnum.UNAVAILABLE.getStatus());
        } else {
            createListVO.setShortLinkStatus(ShortLinkStatusEnum.AVAILABLE.getStatus());
        }
        return createListVO;
    }



    /**
     * 构建短链接创建记录对象
     *
     * @param createDTO 短链接创建请求数据传输对象，包含了创建短链接所需的信息
     * @param urlCode 短链唯一编码
     * @return ShortLinkCreateRecordDO 短链接创建记录对象，包含了映射后的数据
     */
    private ShortLinkCreateRecordDO buildShortLinkCreateRecordDO(ShortLinkCreateDTO createDTO, String urlCode) {
        log.info("构建短链接创建记录对象, createDTO:{}, urlCode:{}", createDTO, urlCode);
        if (Objects.isNull(createDTO)) {
            return ShortLinkCreateRecordDO.builder().build();
        }
        ShortLinkCreateRecordDO createRecordDO = ShortLinkCreateRecordDO.builder()
                .configId(snowflake.nextId())
                .urlCode(urlCode)
                .operator(OperatorUtil.getOperator())
                .brandCode(createDTO.getBrandCode())
                .baseUrl(BrandToUrlEum.getUrlByBrandCodeUrlType(createDTO.getBrandCode(), createDTO.getUrlType()))
                .build();
        if (CollUtil.isNotEmpty(createDTO.getParams())) {
            createRecordDO.setParams(JSON.toJSONString(createDTO.getParams()));
        }
        createRecordDO.setConfigContent(buildConfigContent(createDTO));
        return createRecordDO;
    }

    /**
     * 根据ShortLinkCreateDTO构建配置内容
     *
     * @param createDTO 包含品牌代码和URL类型等信息的ShortLinkCreateDTO对象
     * @return 对应的配置内容字符串，格式为"小程序名称,URL名称,产品名称"，如果找不到配置则返回空字符串
     */
    private String buildConfigContent(ShortLinkCreateDTO createDTO) {
        log.info("获取配置内容, createDTO:{}", createDTO);
        BrandToUrlEum brandToUrlEum = BrandToUrlEum.getBrandToUrlEumByCodeUrlType(createDTO.getBrandCode(), createDTO.getUrlType());
        if (Objects.isNull(brandToUrlEum)) {
            return "";
        }
        String resp = brandToUrlEum.getMiniProgramName() + "，" + brandToUrlEum.getUrlName();
        if  (StringUtils.isNotBlank(createDTO.getProductName())) {
            resp += "，" + createDTO.getProductName();
        }
        return  resp;
    }

    /**
     * 生成太阳码并创建对应的配置及生成记录，同时调用微信接口生成短链。
     *
     * @param qrCodeCreateDTO 包含生成太阳码所需参数的数据传输对象，包括品牌编码、URL类型等配置信息
     * @return String 生成的短链URL字符串，若出现参数校验失败或构建过程异常则返回对应的错误描述信息
     */
    @Override
    public CommonResult<byte[]> createQrCode(QrCodeCreateDTO qrCodeCreateDTO) {
        log.info("生成太阳码, qrCodeCreateDTO:{}", qrCodeCreateDTO);
        if (Objects.isNull(qrCodeCreateDTO)) {
            log.info("qrCodeCreateDTO为空");
            return CommonResult.success(new byte[0]);
        }
        if (!checkQrCodeCreateParam(qrCodeCreateDTO)) {
            log.info("参数校验失败, qrCodeCreateDTO:{}", qrCodeCreateDTO);
            return CommonResult.error(12576789, "仅支持输入英文字母及数字");
        }
        //1.入库生成太阳码信息
        String recordCode = snowflake.nextIdStr();
        MiniCodeConfigDO miniCodeConfigDO = buildMiniCodeConfigDO(qrCodeCreateDTO, recordCode);
        if (Objects.isNull(miniCodeConfigDO)) {
            log.info("miniCodeConfigDO为空, qrCodeCreateDTO:{}", qrCodeCreateDTO);
            return CommonResult.success(new byte[0]);
        }
        miniCodeConfigRepository.saveOrUpdate(miniCodeConfigDO);
        log.info("生成太阳码, miniCodeConfigDO:{}", miniCodeConfigDO);
        String  routePathUrl = BrandToUrlEum.getUrlByBrandCodeUrlType(qrCodeCreateDTO.getBrandCode(),
                qrCodeCreateDTO.getUrlType());
        MiniCodeGenerateRecordDO generateRecordDO = buildMiniCodeGenerateRecordDO(routePathUrl, recordCode, miniCodeConfigDO.getId());
        if (Objects.isNull(generateRecordDO)) {
            log.info("generateRecordDO为空, qrCodeCreateDTO:{}", qrCodeCreateDTO);
            return CommonResult.success(new byte[0]);
        }
        miniCodeGenerateRepository.saveOrUpdate(generateRecordDO);
        //2.调用wechat生成太阳码
        String weChatToken = weChatUtil.getWeChatTokenByHttpGet();
        WeChatQrCodeResp resp = weChatUtil.getWeChatQrCode(weChatToken, routePathUrl,
                String.valueOf(generateRecordDO.getConfigId()));
        //3.更新wechat生成太阳码的结果
        if (Objects.nonNull(resp)) {
            updateMiniCodeGenerateRecord(generateRecordDO, resp);
        }
        return CommonResult.success(resp.getData());
    }

    /**
     * 获取太阳码生成列表信息，分页查询并转换数据.
     *
     * @param createListDTO 分页查询参数，包含分页信息和查询条件
     * @return PageResult<QrCodeCreateVO> 查询结果封装对象，包含转换后的数据列表和总记录数
     */
    @Override
    public PageResult<QrCodeCreateVO> getCreateQrCodeList(ShortLinkCreateListDTO createListDTO) {
        log.info("获取太阳码生成的列表信息, createListDTO:{}", createListDTO);
        Page<MiniCodeConfigDO> pageResp = miniCodeConfigRepository.pageList(createListDTO);
        List<MiniCodeConfigDO> configDOList = pageResp.getRecords();
        if (CollUtil.isEmpty(configDOList)) {
            return PageResult.empty();
        }
        List<QrCodeCreateVO> createListVOList = buildQrCodeCreateVOList(configDOList);
        return new PageResult<>(createListVOList, pageResp.getTotal());
    }

    /**
     * 检查二维码创建参数对象中的字段是否符合规范要求。
     * 验证规则：
     * 1. 当createDTO为null时直接返回true
     * 2. param1-param4字段若不为空白字符串，必须为字母数字组合
     * 3. param5字段必须为字母数字组合
     *
     * @param createDTO 二维码创建参数数据传输对象
     * @return 当所有非空字段均符合字母数字要求且param5有效时返回true，否则返回false
     */
    public boolean checkQrCodeCreateParam(QrCodeCreateDTO createDTO) {
        if (Objects.isNull(createDTO)) {
            return true;
        }
        if (StringUtils.isNotBlank(createDTO.getParam1()) && !isAlphanumeric(createDTO.getParam1())) {
            return  false;
        }
        if (StringUtils.isNotBlank(createDTO.getParam2()) && !isAlphanumeric(createDTO.getParam2())) {
            return  false;
        }
        if (StringUtils.isNotBlank(createDTO.getParam3()) && !isAlphanumeric(createDTO.getParam3())) {
            return  false;
        }
        if (StringUtils.isNotBlank(createDTO.getParam4()) && !isAlphanumeric(createDTO.getParam4())) {
            return  false;
        }
        return isAlphanumeric(createDTO.getParam5());
    }

    /**
     * 检查给定的字符串是否为非空且所有字符均为字母或数字。
     *
     * @param str 需要检查的字符串，可能为null
     * @return 如果str不为null且非空，并且所有字符都是字母或数字，则返回true；
     *         否则返回false
     */
    public boolean isAlphanumeric(String str) {
        if (Objects.isNull(str)) {
            return true;
        }
        for (char c : str.toCharArray()) {
            if (!Character.isLetterOrDigit(c)) {
                return false;
            }
        }
        return true;
    }


    /**
     * 将MiniCodeConfigDO配置对象列表转换为QrCodeCreateVO视图对象列表
     *
     * @param configDOList 微信码配置数据对象列表（可能为空）
     * @return 转换后的二维码创建视图对象列表，不会返回null但可能是空列表
     */
    public List<QrCodeCreateVO> buildQrCodeCreateVOList(List<MiniCodeConfigDO> configDOList) {
        List<QrCodeCreateVO> resp = new ArrayList<>();
        if (CollUtil.isEmpty(configDOList)) {
            return new ArrayList<>();
        }
        for (MiniCodeConfigDO configDO : configDOList) {
            resp.add(buildQrCodeCreateVO(configDO));
        }
        return resp;
    }

    /**
     * 构建QrCodeCreateVO对象，将MiniCodeConfigDO配置数据转换为VO传输对象。
     *
     * @param configDO 配置数据对象，包含创建二维码所需的配置信息
     * @return 构建完成的QrCodeCreateVO对象，包含创建时间和操作人等信息
     */
    public QrCodeCreateVO buildQrCodeCreateVO(MiniCodeConfigDO configDO) {
        QrCodeCreateVO qrCodeCreateVO = new QrCodeCreateVO();
        qrCodeCreateVO.setCreateTime(TimeFormatUtil.timeToStringByFormat(configDO.getCreatedTime(), TimeFormatUtil.formatter_6));
        qrCodeCreateVO.setOperator(configDO.getCreatedBy());
        qrCodeCreateVO.setConfigContent(getConfigContent(configDO));
        return qrCodeCreateVO;
    }

    /**
     * 根据配置对象生成小程序配置内容字符串
     *
     * @param configDO 配置数据对象，允许为null
     * @return 拼接后的配置内容字符串，包含小程序名称、页面类型及可选参数
     *         当configDO为null时返回空字符串
     */
    public String getConfigContent(MiniCodeConfigDO configDO) {
        StringBuilder resp = new StringBuilder();
        if (Objects.isNull(configDO)) {
            return "";
        }
        resp.append(BrandToUrlEum.getMiniProgramNameByBrandCode(configDO.getWmpBrand()));
        if (StringUtils.isNotBlank(configDO.getRoutePageType())) {
            resp.append("，").append(BrandToUrlEum.getUrlNameByUrlType(configDO.getRoutePageType()));
        }
        if (StringUtils.isNotBlank(configDO.getProductNameParam())) {
            resp.append("，").append(configDO.getProductNameParam());
        }
        if (StringUtils.isNotBlank(configDO.getParam1())) {
            resp.append("，").append("p1=").append(configDO.getParam1());
        }
        if (StringUtils.isNotBlank(configDO.getParam2())) {
            resp.append("，").append("p2=").append(configDO.getParam2());
        }
        if (StringUtils.isNotBlank(configDO.getParam3())) {
            resp.append("，").append("p3=").append(configDO.getParam3());
        }
        if (StringUtils.isNotBlank(configDO.getParam4())) {
            resp.append("，").append("p4=").append(configDO.getParam4());
        }
        if (StringUtils.isNotBlank(configDO.getParam5())) {
            resp.append("，").append("p5=").append(configDO.getParam5());
        }
        return resp.toString();
    }

    /**
     * 更新小程序码生成记录的状态和错误信息
     *
     * @param generateRecordDO 小程序码生成记录数据对象，包含生成任务的基础信息和结果存储字段
     * @param resp             微信二维码生成接口响应对象，包含生成结果的状态码和错误信息
     *
     */
    public void updateMiniCodeGenerateRecord(MiniCodeGenerateRecordDO generateRecordDO, WeChatQrCodeResp resp) {
        log.info("更新MiniCodeGenerateRecordDO对象, generateRecordDO:{}, resp:{}", generateRecordDO, resp);
        if (Objects.isNull(generateRecordDO) || Objects.isNull(resp)) {
            return;
        }
        if (StringUtils.isNotBlank(resp.getErrorCode()) || StringUtils.isNotBlank(resp.getErrorCode())) {
            generateRecordDO.setResult(1);
            generateRecordDO.setErrorCode(resp.getErrorCode());
            generateRecordDO.setErrorMessage(resp.getErrorMsg());
        } else {
            generateRecordDO.setResult(0);
        }
        miniCodeGenerateRepository.updateById(generateRecordDO);
    }


    /**
     * 构建MiniCodeGenerateRecordDO对象的方法。
     *
     * @param routePathUrl 路由路径URL，不能为空
     * @param recordCode   记录代码，不能为空
     * @param configId     配置ID，不能为空
     * @return 构建的MiniCodeGenerateRecordDO对象，若任一参数为空则返回null
     */
    public MiniCodeGenerateRecordDO buildMiniCodeGenerateRecordDO(String routePathUrl, String recordCode, Long configId) {
        log.info("构建MiniCodeGenerateRecordDO对象, routePathUrl:{}, recordCode:{}, configId:{}", routePathUrl, recordCode, configId);
        if (StringUtils.isBlank(routePathUrl) || StringUtils.isBlank(recordCode) || Objects.isNull(configId)) {
            return null;
        }
        return MiniCodeGenerateRecordDO.builder()
                .routePathUrl(routePathUrl)
                .recordCode(recordCode)
                .configId(configId)
                .build();
    }

    /**
     * 构建小程序码配置数据对象
     *
     * @param qrCodeCreateDTO 二维码创建参数传输对象
     * @param recordCode      记录标识码（非空字符串）
     * @return 构建好的小程序码配置数据对象，当输入参数为空时返回null
     */
    public MiniCodeConfigDO buildMiniCodeConfigDO(QrCodeCreateDTO qrCodeCreateDTO, String recordCode) {
        log.info("构建小程序码配置数据对象, qrCodeCreateDTO:{}, recordCode:{}", qrCodeCreateDTO, recordCode);
        if (Objects.isNull(qrCodeCreateDTO) || StringUtils.isBlank(recordCode)) {
            return null;
        }
        return MiniCodeConfigDO.builder()
                .recordCode(recordCode)
                .wmpBrand(qrCodeCreateDTO.getBrandCode())
                .routePageType(qrCodeCreateDTO.getUrlType())
                .productCodeParam(qrCodeCreateDTO.getProductCode())
                .productNameParam(qrCodeCreateDTO.getProductName())
                .param1(qrCodeCreateDTO.getParam1())
                .param2(qrCodeCreateDTO.getParam2())
                .param3(qrCodeCreateDTO.getParam3())
                .param4(qrCodeCreateDTO.getParam4())
                .param5(qrCodeCreateDTO.getParam5())
                .build();

    }
}
