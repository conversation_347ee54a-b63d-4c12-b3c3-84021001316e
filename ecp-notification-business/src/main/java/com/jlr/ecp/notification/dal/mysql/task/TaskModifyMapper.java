package com.jlr.ecp.notification.dal.mysql.task;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.notification.dal.dataobject.task.TaskModifyLog;
import com.jlr.ecp.notification.req.task.TaskHistoryReq;
import org.apache.ibatis.annotations.Mapper;


/**
 * 发送任务编辑
 *
 * <AUTHOR>
 * */
@Mapper
public interface TaskModifyMapper extends BaseMapperX<TaskModifyLog> {

}
