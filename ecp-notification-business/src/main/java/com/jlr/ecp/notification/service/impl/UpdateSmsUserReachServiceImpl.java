package com.jlr.ecp.notification.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jlr.ecp.framework.forgeRock.core.dto.AccessTokenResponse;
import com.jlr.ecp.framework.forgeRock.core.util.AccessTokenUtil;
import com.jlr.ecp.notification.dal.dataobject.history.NotificationHistory;
import com.jlr.ecp.notification.dal.dataobject.history.SendHistoryDetail;
import com.jlr.ecp.notification.dal.dataobject.history.SmsReportRespDO;
import com.jlr.ecp.notification.dal.dataobject.valet.OrderNotificationDetailDO;
import com.jlr.ecp.notification.dal.repository.NotificationHistoryRepository;
import com.jlr.ecp.notification.dal.repository.OrderNotificationDetailRepository;
import com.jlr.ecp.notification.dal.repository.SendHistoryDetailRepository;
import com.jlr.ecp.notification.dal.repository.SmsReportRespRepository;
import com.jlr.ecp.notification.dto.UpdateSmsSendStatusDTO;
import com.jlr.ecp.notification.enums.MsgResultEnum;
import com.jlr.ecp.notification.enums.ReachResultStatusEnum;
import com.jlr.ecp.notification.enums.SmsSendResultEnum;
import com.jlr.ecp.notification.enums.SmsSendStatusEnum;
import com.jlr.ecp.notification.resp.ReportResp;
import com.jlr.ecp.notification.resp.UpdateSmsSendStatusResp;
import com.jlr.ecp.notification.service.UpdateSmsUserReachService;
import com.jlr.ecp.notification.util.HttpClientUtil;
import com.jlr.ecp.notification.util.HttpPostUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Slf4j
public class UpdateSmsUserReachServiceImpl implements UpdateSmsUserReachService {

    @Resource
    private NotificationHistoryRepository historyRepository;

    @Resource
    private SendHistoryDetailRepository detailRepository;

    @Resource
    private AccessTokenUtil accessTokenUtil;

    @Resource
    private SmsReportRespRepository reportRespRepository;


    private static final int SOCKET_TIMEOUT = 60000; // 设置连接和读取超时时间为60秒
    private static final int CONNECT_TIMEOUT = 60000;


    @Value("${send.smsUrl}")
    private String smsBaseUrl;

    @Value("${send.apiKey}")
    private String apiKey;

    @Value("${send.apiSecret}")
    private String apiSecret;

    @Value("${send.updatePath}")
    private String updatePath;

    @Resource
    private OrderNotificationDetailRepository orderDetailRepository;

    @Resource(name = "scheduledThreadPool")
    private ThreadPoolTaskScheduler taskScheduler;

    /**
     * 更新短信发送状态。
     *
     * @param updateSmsSendStatusDTO 包含需要更新的短信发送状态信息的数据传输对象。
     * @return 返回更新短信发送状态的响应对象，包含更新结果等信息。
     */
    @Override
    public UpdateSmsSendStatusResp updateSmsSendStatus(UpdateSmsSendStatusDTO updateSmsSendStatusDTO) {
        CloseableHttpClient httpClient = HttpClientUtil.getNoSslHttpClient(SOCKET_TIMEOUT, CONNECT_TIMEOUT);
        String requestBodyJson = JSON.toJSONString(updateSmsSendStatusDTO);
        String accessToken = getToken();
        String updateUrl = smsBaseUrl + updatePath;
        HttpPost httpPost = HttpPostUtil.getHttpPost(requestBodyJson, updateUrl, updatePath,
                accessToken, apiKey, apiSecret);
        UpdateSmsSendStatusResp updateSmsSendStatusResp = null;
        log.info("更新短信发送结果，请求, head:{}, body:{}, url:{}", httpPost.getAllHeaders(), requestBodyJson, updateUrl);
        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            if (SmsSendStatusEnum.SUCCESS.getCode().equals(response.getStatusLine().getStatusCode())) {
                String responseJson = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                JSONObject jsonObject = JSON.parseObject(responseJson);
                updateSmsSendStatusResp = JSON.toJavaObject(jsonObject, UpdateSmsSendStatusResp.class);
                if (Objects.isNull(updateSmsSendStatusResp)) {
                    log.info("更新短信发送结果，请求完成，updateSmsSendStatusResp为空, updateSmsSendStatusDTO:{}", updateSmsSendStatusDTO);
                    return null;
                }
                log.info("更新短信发送结果，请求成功, result:{}, desc:{}, 数量:{}", updateSmsSendStatusResp.getResult(),
                        updateSmsSendStatusResp.getDesc(), updateSmsSendStatusResp.getReports().size());
                insertSmsReportResp(updateSmsSendStatusResp.getReports());
            } else {
                // 处理错误响应
                log.error("更新短信发送结果，请求失败, response:{}, responseBody:{}", response,
                        EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8));
            }
        } catch (Exception e) {
            log.info("更新短信发送结果，请求异常：{}", e.getMessage());
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("更新短信发送结果，关闭http请求异常：", e);
            }
        }
        return updateSmsSendStatusResp;
    }

    /**
     *  模拟多次推送report
     * @param  reports 状态报告数据
     * */
     public void saveAndUpdateSmsSendReport(List<ReportResp> reports) {
        if (CollUtil.isEmpty(reports)) {
            log.info("保存和更新report数据为空");
            return ;
        }
        log.info("保存和更新report数据, 数量:{}", reports.size());
        // 获取当前时间，并添加5分钟的延迟
        Date fiveMinutesLater = new Date(System.currentTimeMillis() + 5 * 60 * 1000);
        Runnable task = () -> {
            insertSmsReportResp(reports);
            updateSmsUserReach(reports);
        };
        // 使用TaskScheduler安排任务在5分钟后执行一次
        taskScheduler.schedule(task, fiveMinutesLater);
    }

    /**
     * 插入短信发送结果响应数据
     *
     * @param reportRespList 短信发送结果响应数据列表，不能为空
     */
    public void insertSmsReportResp(List<ReportResp> reportRespList) {
        if (CollectionUtils.isEmpty(reportRespList)) {
            log.info("更新短信发送结果，需要插入短信发送结果数量为空");
            return ;
        }
        log.info("更新短信发送结果，开始插入短信发送结果, 数量:{}", reportRespList.size());
        List<SmsReportRespDO> smsReportRespDOList = buildSmsReportRespDOList(reportRespList);
        List<List<SmsReportRespDO>> reportGroup = ListUtils.partition(smsReportRespDOList, 1000);
        for (List<SmsReportRespDO> reportList : reportGroup) {
            CompletableFuture.runAsync(() -> reportRespRepository.saveBatch(reportList));
        }
    }

    /**
     * 根据报告响应列表构建短信报告响应DO列表
     *
     * @param reportRespList 报告响应列表，包含多个ReportResp对象
     * @return 返回转换后的短信报告响应DO列表
     */
    public List<SmsReportRespDO> buildSmsReportRespDOList(List<ReportResp> reportRespList) {
        List<SmsReportRespDO> reportRespDOList = new ArrayList<>();
        for (ReportResp reportResp : reportRespList) {
            reportRespDOList.add(buildSmsReportRespDO(reportResp));
        }
        return reportRespDOList;

    }

    /**
     * 构建短信报告响应对象
     * @param reportResp 短信报告响应对象，包含短信发送报告的详细信息
     * @return 返回一个构建好的SmsReportRespDO对象，用于后续的业务逻辑处理或响应
     */
    public SmsReportRespDO buildSmsReportRespDO(ReportResp reportResp) {
        return SmsReportRespDO.builder()
                .msgId(reportResp.getMsgid())
                .phone(reportResp.getPhone())
                .status(reportResp.getStatus())
                .description(reportResp.getDesc())
                .wgCode(reportResp.getWgcode())
                .sendTime(reportResp.getTime())
                .smsCount(reportResp.getSmsCount())
                .smsIndex(reportResp.getSmsIndex())
                .build();
    }

    /**
     * 更新短信用户达到信息。
     * 该方法会遍历传入的报告响应列表，并更新相应的短信发送详情结果。
     *
     * @param reportRespList 包含报告响应信息的列表，每个报告响应对应一条短信的发送详情。
     *                       通过更新这个列表中的信息，可以更新数据库中相应的短信发送状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSmsUserReach(List<ReportResp> reportRespList) {
        updateSmsSendDetailResult(reportRespList);
    }


    /**
     * 获取访问令牌。
     * 返回值：返回从accessTokenUtil获取的访问令牌字符串。
     * 如果在获取访问令牌的过程中遇到异常，则记录错误日志，并返回null。
     */
    private String getToken() {
        AccessTokenResponse tokenResponse = new AccessTokenResponse();
        try {
            tokenResponse = accessTokenUtil.fetchAccessToken();
        } catch (Exception e) {
            log.error("获取token异常:", e);
        }
        return tokenResponse.getAccessToken();
    }

    /**
     * 更新SMS发送详情的结果信息。
     * @param reportRespList 短信状态报告列表，包含每条短信的发送状态信息。
     * */
    public void updateSmsSendDetailResult(List<ReportResp> reportRespList) {
        if (CollectionUtils.isEmpty(reportRespList)) {
            log.info("更新sms的发送结果，状态报告为空");
            return ;
        }
        log.info("更新SMS触达详情的结果, reportRespList的数量:{}", reportRespList.size());
        List<String> msgIdList = reportRespList.stream().map(ReportResp::getMsgid).collect(Collectors.toList());
        Map<String, ReportResp> reportRespMap = reportRespList.stream()
                .collect(Collectors.toMap(ReportResp::getMsgid, Function.identity(), (v1, v2) -> v2));
        updateHistoryDetailResult(msgIdList, reportRespMap);
        updateOrderNotifyDetailResult(msgIdList, reportRespMap);
    }

    /**
     * 更新短信发送详情结果
     *
     * @param msgIdList 消息ID列表，用于查询发送历史详情
     * @param reportRespMap 报告响应映射，包含消息ID与报告响应的映射关系
     */
    public void updateHistoryDetailResult(List<String> msgIdList, Map<String, ReportResp> reportRespMap) {
        log.info("更新短信发送详情结果, msgIdList的数量:{}, reportRespMap的数量:{}", msgIdList.size(), reportRespMap.size());
        List<SendHistoryDetail> sendHistoryDetails = detailRepository.queryByMsgIdList(msgIdList);
        if (CollectionUtils.isEmpty(sendHistoryDetails)) {
            log.info("更新sms的发送结果，根据msgIdList查询的sendHistoryDetails为空, msgIdList:{},", msgIdList);
            return ;
        }
        log.info("更新SMS触达详情的结果, 更新前sendHistoryDetails的数量:{}", sendHistoryDetails.size());
        Set<String> instanceCodeSet = new HashSet<>();
        List<SendHistoryDetail> needUpdateDetailList = new ArrayList<>();
        for (SendHistoryDetail sendHistoryDetail : sendHistoryDetails) {
            if (Objects.nonNull(reportRespMap.get(sendHistoryDetail.getMsgId()))) {
                sendHistoryDetail.setMsgResult(reportRespMap.get(sendHistoryDetail.getMsgId()).getStatus());
                sendHistoryDetail.setMsgErrorDesc(reportRespMap.get(sendHistoryDetail.getMsgId()).getDesc());
                sendHistoryDetail.setWgCode(reportRespMap.get(sendHistoryDetail.getMsgId()).getWgcode());
                if (SmsSendResultEnum.WAITED.getCode().equals(sendHistoryDetail.getSendResult())) {
                    sendHistoryDetail.setSendResult(SmsSendResultEnum.SUCCESS.getCode());
                    needUpdateDetailList.add(sendHistoryDetail);
                }
                instanceCodeSet.add(sendHistoryDetail.getTaskInstanceCode());
            }
        }
        detailRepository.batchUpdateByIds(sendHistoryDetails);
        List<String> instanceCodeList = new ArrayList<>(instanceCodeSet);
        log.info("更新SMS触达详情的结果成功, instanceCodeSet数量:{}, instanceCodeList数量:{}",
                instanceCodeSet.size(), instanceCodeList.size());
        updateHistoryResult(instanceCodeList);
        updateByReachResult(needUpdateDetailList);
    }

    /**
     * 根据触达达结果更发送详情和统计结果
     *
     * @param detailList 发需要更新的发送详情列表
     */
    public void updateByReachResult(List<SendHistoryDetail> detailList) {
        log.info("根据触达达结果更发送详情和统计结果, detailList数量:{}", detailList.size());
        if (CollUtil.isEmpty(detailList)) {
            return ;
        }
        detailRepository.batchUpdateByIds(detailList);
        List<List<SendHistoryDetail>> detailGroupList = getDetailGroupByInstanceCode(detailList);
        for (List<SendHistoryDetail> details : detailGroupList) {
            String instanceCode = details.get(0).getTaskInstanceCode();
            NotificationHistory history = historyRepository.queryHistoryByInstanceCode(instanceCode);
            history.setSendSuccessCount(history.getSendSuccessCount() + details.size());
            historyRepository.updateById(history);
        }
    }

    /**
     * 根据实例代码对发送历史详情列表进行分组
     *
     * @param detailList 发送历史详情列表，预期为非null但可能为空的列表
     * @return 返回一个列表，其中每个元素都是发送历史详情列表，这些列表按任务实例代码分组
     */
    public List<List<SendHistoryDetail>> getDetailGroupByInstanceCode(List<SendHistoryDetail> detailList) {
        if (CollUtil.isEmpty(detailList)) {
            return new ArrayList<>();
        }

        Map<String, List<SendHistoryDetail>> groupedByInstanceCode = detailList.stream()
                .collect(Collectors.groupingBy(SendHistoryDetail::getTaskInstanceCode));

        return new ArrayList<>(groupedByInstanceCode.values());

    }

    /**
     * 更新代客下单通知详情结果
     *
     * @param msgIdList 消息ID列表，用于查询订单通知详情
     * @param reportRespMap 报告响应映射，包含消息ID与响应对象的映射
     */
    public void updateOrderNotifyDetailResult(List<String> msgIdList, Map<String, ReportResp> reportRespMap) {
        log.info("更新代客下单通知详情结果, msgIdList的数量：{}, reportRespMap的数量：{}", msgIdList.size(), reportRespMap.size());
        List<OrderNotificationDetailDO> orderDetailList = orderDetailRepository.selectByMsgIdList(msgIdList);
        if (CollUtil.isEmpty(orderDetailList)) {
            log.info("更新代客下单通知详情结果，根据msgIdList查询的orderDetailList为空, msgIdList:{},", msgIdList);
            return ;
        }
        for (OrderNotificationDetailDO orderDetail : orderDetailList) {
            if (Objects.nonNull(reportRespMap.get(orderDetail.getMsgId()))) {
                orderDetail.setMsgResult(reportRespMap.get(orderDetail.getMsgId()).getStatus());
                orderDetail.setMsgErrorDesc(reportRespMap.get(orderDetail.getMsgId()).getDesc());
                orderDetail.setWgCode(reportRespMap.get(orderDetail.getMsgId()).getWgcode());
            }
        }
        orderDetailRepository.saveOrUpdateBatch(orderDetailList);
    }

    /**
     * 更新通知历史统计结果信息。
     * @param instanceCodeList 实例编码列表，用于查询对应的发送历史。
     */
    public void updateHistoryResult(List<String> instanceCodeList) {
        log.info("更新用户触达统计信息, instanceCodeList:{}", instanceCodeList);
        List<NotificationHistory> historyList = historyRepository.queryHistoryByInstanceCodeList(instanceCodeList);
        if (CollectionUtils.isEmpty(historyList)) {
            log.info("按照instanceCode批量查询发送historyList为空, instanceCodeList:{}", instanceCodeList);
            return ;
        }
        log.info("更新用户触达统计信息, 更新前historyList的数量:{}", historyList.size());
        List<NotificationHistory> updateHistoryList = new ArrayList<>();
        for (NotificationHistory history : historyList) {
            Integer sendTotal = detailRepository.querySendCountByInstanceCode(history.getTaskInstanceCode());
            Integer reachSuccessCount = detailRepository.queryReachByInstanceAndStatus(history.getTaskInstanceCode(),
                    List.of(MsgResultEnum.SUCCESSFUL.getStatus()));
            Integer reachFailCount = detailRepository.queryReachByInstanceAndStatus(history.getTaskInstanceCode(),
                    List.of(MsgResultEnum.FAILED.getStatus(), MsgResultEnum.FAILED_GATEWAY.getStatus()));
            if (history.getSendTotalCount() > sendTotal && sendTotal > 0) {
                history.setSendTotalCount(sendTotal);
            }
            history.setReachUserSuccessCount(reachSuccessCount);
            history.setReachUserFailCount(reachFailCount);
            int reachTotal = reachSuccessCount + reachFailCount;
            if (sendTotal == reachTotal) {
                history.setReachResultStatus(ReachResultStatusEnum.FINISHED.getStatus());
            } else {
                history.setReachResultStatus(ReachResultStatusEnum.NO_FINISH.getStatus());
            }
            updateHistoryList.add(history);
        }
        historyRepository.batchUpdateByIds(updateHistoryList);
        log.info("更新用户触达统计信息, 更新成功，updateHistoryList的数量:{}", updateHistoryList.size());
    }
}
