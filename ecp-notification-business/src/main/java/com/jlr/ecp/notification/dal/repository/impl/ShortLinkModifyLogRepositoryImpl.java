package com.jlr.ecp.notification.dal.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.notification.dal.dataobject.shortlink.NotificationConfigModifyLog;
import com.jlr.ecp.notification.dal.mysql.shortlink.NotificationConfigModifyLogMapper;
import com.jlr.ecp.notification.dal.repository.ShortLinkModifyLogRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class ShortLinkModifyLogRepositoryImpl implements ShortLinkModifyLogRepository {
    @Resource
    private NotificationConfigModifyLogMapper modifyLogMapper;

    /**
     * 插入短链接修改日志
     *
     * @param notificationConfigModifyLog 短链接修改日志对象，包含需要插入的日志信息
     * @return 操作结果，true表示插入成功，false表示插入失败
     */
    @Override
    public Boolean insertShortLinkModifyLog(NotificationConfigModifyLog notificationConfigModifyLog) {
        return modifyLogMapper.insert(notificationConfigModifyLog) > 0;
    }

    /**
     * 查询指定配置的短链接修改日志
     *
     * @param configId 配置ID，用于定位特定的配置
     * @return 返回与指定配置相关的短链接修改日志列表，如果配置ID为空或无效，则返回空列表
     */
    @Override
    public List<NotificationConfigModifyLog> queryShortLinkModifyLog(Long configId) {
        if (Objects.isNull(configId)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<NotificationConfigModifyLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NotificationConfigModifyLog::getConfigId, configId)
                .eq(NotificationConfigModifyLog::getIsDeleted, false)
                .orderByDesc(NotificationConfigModifyLog::getCreatedTime);
        return modifyLogMapper.selectList(queryWrapper);
    }
}
