package com.jlr.ecp.notification.service.impl;

import com.jlr.ecp.framework.forgeRock.core.dto.AccessTokenResponse;
import com.jlr.ecp.framework.forgeRock.core.util.AccessTokenUtil;
import com.jlr.ecp.notification.dal.dataobject.history.NotificationHistory;
import com.jlr.ecp.notification.dal.dataobject.history.SendHistoryDetail;
import com.jlr.ecp.notification.dal.dataobject.history.SmsReportRespDO;
import com.jlr.ecp.notification.dal.dataobject.valet.OrderNotificationDetailDO;
import com.jlr.ecp.notification.dal.repository.NotificationHistoryRepository;
import com.jlr.ecp.notification.dal.repository.OrderNotificationDetailRepository;
import com.jlr.ecp.notification.dal.repository.SendHistoryDetailRepository;
import com.jlr.ecp.notification.dal.repository.SmsReportRespRepository;
import com.jlr.ecp.notification.dto.UpdateSmsSendStatusDTO;
import com.jlr.ecp.notification.enums.MsgResultEnum;
import com.jlr.ecp.notification.enums.ReachResultStatusEnum;
import com.jlr.ecp.notification.enums.SmsSendResultEnum;
import com.jlr.ecp.notification.resp.ReportResp;
import com.jlr.ecp.notification.resp.UpdateSmsSendStatusResp;
import com.jlr.ecp.notification.util.HttpPostUtil;
import org.apache.http.StatusLine;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UpdateSmsUserReachServiceImplTest {

    @Mock
    private NotificationHistoryRepository mockHistoryRepository;
    @Mock
    private SendHistoryDetailRepository mockDetailRepository;
    @Mock
    private AccessTokenUtil mockAccessTokenUtil;

    @Mock
    private CloseableHttpClient mockHttpClient;

    @InjectMocks
    @Spy
    private UpdateSmsUserReachServiceImpl updateSmsUserReachService;

    @Mock
    private OrderNotificationDetailRepository mockOrderDetailRepository;

    @Mock
    private SendHistoryDetailRepository detailRepository;

    @Mock
    private NotificationHistoryRepository historyRepository;

    @Mock
    private SmsReportRespRepository reportRespRepository;

    @Mock
    private ThreadPoolTaskScheduler taskScheduler;

    private final String TEST_MSG_ID = "MSG_001";
    private final String TEST_INSTANCE_CODE = "INSTANCE_001";

    private List<ReportResp> mockReports;


    @Before
    public void setUp() {
        ReflectionTestUtils.setField(updateSmsUserReachService, "smsBaseUrl", "smsBaseUrl");
        ReflectionTestUtils.setField(updateSmsUserReachService, "apiKey", "apiKey");
        ReflectionTestUtils.setField(updateSmsUserReachService, "apiSecret", "apiSecret");
        ReflectionTestUtils.setField(updateSmsUserReachService, "updatePath", "updatePath");

        mockReports = Arrays.asList(mock(ReportResp.class));
    }

    @Test
    public void testUpdateSmsSendStatus() throws Exception {
        // 准备测试数据
        UpdateSmsSendStatusDTO dto = new UpdateSmsSendStatusDTO();
        dto.setAccount("testAccount");
        dto.setPassword("testPassword");

        // Configure AccessTokenUtil.fetchAccessToken(...).
        final AccessTokenResponse accessTokenResponse = new AccessTokenResponse();
        accessTokenResponse.setAccessToken("accessToken");
        accessTokenResponse.setScope("scope");
        accessTokenResponse.setIdToken("idToken");
        accessTokenResponse.setTokenType("tokenType");
        accessTokenResponse.setExpiresIn(0);
        when(mockAccessTokenUtil.fetchAccessToken()).thenReturn(accessTokenResponse);

        // 模拟HTTP响应
        final StatusLine statusLine = Mockito.mock(StatusLine.class);
        Mockito.lenient().when(statusLine.getStatusCode()).thenReturn(200);

        final HttpEntity entity = Mockito.mock(HttpEntity.class);
        Mockito.lenient().when(entity.getContent()).thenReturn(new ByteArrayInputStream("{\"result\":\"success\",\"desc\":\"OK\",\"reports\":[]}".getBytes(StandardCharsets.UTF_8)));

        final CloseableHttpResponse response = Mockito.mock(CloseableHttpResponse.class);
        Mockito.lenient().when(response.getStatusLine()).thenReturn(statusLine);
        Mockito.lenient().when(response.getEntity()).thenReturn(entity);

        final HttpPost httpPost = HttpPostUtil.getHttpPost("body", "http://127.0.0.1:9000/business/sms/status_report/v1.0.0",
                "signPath", "accessToken", "apiKey", "apiSecret");
        Mockito.lenient().when(mockHttpClient.execute(httpPost)).thenReturn(response);

        // 调用待测方法
        UpdateSmsSendStatusResp resp = updateSmsUserReachService.updateSmsSendStatus(dto);

        // 验证结果
        assertThat(resp).isEqualTo(null);
    }

    @Test
    public void testUpdateStatusAccessTokenThrowsException() throws Exception {
        // Setup
        final UpdateSmsSendStatusDTO updateSmsSendStatusDTO = UpdateSmsSendStatusDTO.builder().build();
        when(mockAccessTokenUtil.fetchAccessToken()).thenThrow(Exception.class);

        // Run the test
        final UpdateSmsSendStatusResp result = updateSmsUserReachService.updateSmsSendStatus(
                updateSmsSendStatusDTO);

        // Verify the results
        assertThat(result).isEqualTo(null);
    }


    /**
     * 测试用例1：reportRespList为空
     */
    @Test
    public void testUpdateSmsUserReach_WhenReportRespListIsEmpty() {
        // 准备测试数据
        List<ReportResp> reportRespList = Collections.emptyList();

        // 调用被测方法
        updateSmsUserReachService.updateSmsUserReach(reportRespList);

        // 验证行为
        verifyNoInteractions(mockDetailRepository, mockOrderDetailRepository, mockHistoryRepository);
    }

    /**
     * 测试用例2：reportRespList不为空，但查询结果为空
     */
    @Test
    public void testUpdateSmsUserReach_WhenQueryResultsAreEmpty() {
        // 准备测试数据
        List<ReportResp> reportRespList = Arrays.asList(
                new ReportResp("msgid1", "phone1", "status1", "desc1", "wgcode1", "time1", 1, 1),
                new ReportResp("msgid2", "phone2", "status2", "desc2", "wgcode2", "time2", 2, 2)
        );

        // 调用被测方法
        updateSmsUserReachService.updateSmsUserReach(reportRespList);

        // 验证行为
        verifyNoMoreInteractions(mockDetailRepository, mockOrderDetailRepository, mockHistoryRepository);
    }

    /**
     * TC001: 空查询结果提前返回
     */
    @Test
    public void whenQueryDetailsEmpty_thenEarlyReturn() {

        // 调用被测方法
        updateSmsUserReachService.updateHistoryDetailResult(
                List.of("MSG001", "MSG002"),
                Map.of("MSG001", new ReportResp())
        );

        // 验证不执行批量更新
        verify(mockDetailRepository, never()).batchUpdateByIds(anyList());
        verify(updateSmsUserReachService, never()).updateHistoryResult(anyList());
    }


    // 公共测试数据构建方法
    private SendHistoryDetail createDetail(String msgId, String instanceCode, Integer sendResult) {
        return SendHistoryDetail.builder()
                .msgId(msgId)
                .taskInstanceCode(instanceCode)
                .sendResult(sendResult)
                .build();
    }

    private ReportResp createReportResp(String status, String desc) {
        return ReportResp.builder()
                .status(status)
                .desc(desc)
                .wgcode("WG001")
                .build();
    }

    /**
     * 测试空列表输入时，不安排任务
     */
    @Test
    public void testSaveAndUpdateSmsSendReport_EmptyList() {
        List<ReportResp> emptyList = new ArrayList<>();
        updateSmsUserReachService.saveAndUpdateSmsSendReport(emptyList);
        verify(taskScheduler, never()).schedule(any(Runnable.class), any(Date.class));
    }

    /**
     * 测试非空列表输入时，任务被安排并正确调用插入和更新方法
     */
    @Test
    public void testSaveAndUpdateSmsSendReport_NonEmptyList() {
        // Mock内部方法为doNothing，避免真实执行
        doNothing().when(updateSmsUserReachService).insertSmsReportResp(anyList());
        doNothing().when(updateSmsUserReachService).updateSmsUserReach(anyList());

        // 调用被测方法
        updateSmsUserReachService.saveAndUpdateSmsSendReport(mockReports);

        // 验证任务被安排
        ArgumentCaptor<Runnable> taskCaptor = ArgumentCaptor.forClass(Runnable.class);
        verify(taskScheduler, times(1)).schedule(taskCaptor.capture(), any(Date.class));

        // 执行捕获的任务
        taskCaptor.getValue().run();

        // 验证内部方法调用次数
        verify(updateSmsUserReachService, times(1)).insertSmsReportResp(mockReports);
        verify(updateSmsUserReachService, times(1)).updateSmsUserReach(mockReports);
    }



    /**
     * 测试空列表输入场景
     * 预期：不调用 saveBatch
     */
    @Test
    public void testInsertSmsReportResp_EmptyList() {
        // Arrange
        List<ReportResp> reportRespList = new ArrayList<>();

        // Act
        updateSmsUserReachService.insertSmsReportResp(reportRespList);

        // Assert
        verify(reportRespRepository, never()).saveBatch(anyList());
    }


    @Test
    public void testBuildSmsReportRespDOList_withValidEntries() {
        // 准备测试数据
        ReportResp report1 = ReportResp.builder()
                .desc("Test Description")
                .msgid("MSG123")
                .phone("1234567890")
                .smsCount(1)
                .smsIndex(0)
                .status("Sent")
                .time("2023-01-01")
                .wgcode("WG001")
                .build();
        ReportResp report2 = ReportResp.builder()
                .desc("Another Desc")
                .msgid("MSG456")
                .phone("0987654321")
                .smsCount(2)
                .smsIndex(1)
                .status("Failed")
                .time("2023-01-02")
                .wgcode("WG002")
                .build();
        List<ReportResp> reportList = Arrays.asList(report1, report2);

        // 调用被测方法
        List<SmsReportRespDO> result = updateSmsUserReachService.buildSmsReportRespDOList(reportList);

        // 验证结果
        assertThat(result).isNotNull().hasSize(2);

        SmsReportRespDO do1 = result.get(0);
        assertThat(do1.getDescription()).isEqualTo(report1.getDesc());
        assertThat(do1.getMsgId()).isEqualTo(report1.getMsgid());
        assertThat(do1.getPhone()).isEqualTo(report1.getPhone());
        assertThat(do1.getSmsCount()).isEqualTo(report1.getSmsCount());
        assertThat(do1.getSmsIndex()).isEqualTo(report1.getSmsIndex());
        assertThat(do1.getStatus()).isEqualTo(report1.getStatus());
        assertThat(do1.getWgCode()).isEqualTo(report1.getWgcode());
        assertThat(do1.getSendTime()).isEqualTo(report1.getTime());

        SmsReportRespDO do2 = result.get(1);
        assertThat(do2.getDescription()).isEqualTo(report2.getDesc());
        assertThat(do2.getMsgId()).isEqualTo(report2.getMsgid());
        assertThat(do2.getPhone()).isEqualTo(report2.getPhone());
        assertThat(do2.getSmsCount()).isEqualTo(report2.getSmsCount());
        assertThat(do2.getSmsIndex()).isEqualTo(report2.getSmsIndex());
        assertThat(do2.getStatus()).isEqualTo(report2.getStatus());
        assertThat(do2.getWgCode()).isEqualTo(report2.getWgcode());
        assertThat(do2.getSendTime()).isEqualTo(report2.getTime());
    }

    @Test
    public void testBuildSmsReportRespDOList_withEmptyList() {
        // 准备测试数据
        List<ReportResp> reportList = List.of();

        // 调用被测方法
        List<SmsReportRespDO> result = updateSmsUserReachService.buildSmsReportRespDOList(reportList);

        // 验证结果
        assertThat(result).isNotNull().isEmpty();
    }

    @Test
    public void testBuildSmsReportRespDOList_withNullInput() {
        // 验证 null 输入抛出异常
        assertThatThrownBy(() -> updateSmsUserReachService.buildSmsReportRespDOList(null))
                .isInstanceOf(NullPointerException.class);
    }




    @Test
    public void updateHistoryDetailResult_WhenNoDetailsFound_ShouldEarlyReturn() {
        // Mock数据
        when(detailRepository.queryByMsgIdList(anyList())).thenReturn(Collections.emptyList());

        // 执行测试
        updateSmsUserReachService.updateHistoryDetailResult(List.of(TEST_MSG_ID), Map.of(TEST_MSG_ID, new ReportResp()));

        // 验证
        verify(detailRepository).queryByMsgIdList(anyList());
        verifyNoMoreInteractions(detailRepository);
        verifyNoInteractions(historyRepository);
    }


    @Test
    public void updateHistoryDetailResult_WhenSendResultNotWaited_ShouldNotUpdateSendResult() {
        // 准备测试数据
        SendHistoryDetail detail = createDetail(TEST_MSG_ID, TEST_INSTANCE_CODE, SmsSendResultEnum.SUCCESS.getCode());
        ReportResp reportResp = createReportResp("0", "成功");

        // Mock行为
        when(detailRepository.queryByMsgIdList(anyList())).thenReturn(List.of(detail));

        // 执行测试
        updateSmsUserReachService.updateHistoryDetailResult(
                List.of(TEST_MSG_ID),
                Map.of(TEST_MSG_ID, reportResp)
        );

        // 验证sendResult未改变
        ArgumentCaptor<List<SendHistoryDetail>> detailCaptor = ArgumentCaptor.forClass(List.class);
        verify(detailRepository).batchUpdateByIds(detailCaptor.capture());

        assertThat(detailCaptor.getValue().get(0).getSendResult())
                .isEqualTo(SmsSendResultEnum.SUCCESS.getCode());
    }


    private final String INSTANCE_CODE_1 = "INST_001";
    private final String INSTANCE_CODE_2 = "INST_002";

    /**
     * 测试用例01：空列表处理
     * 预期行为：不执行任何数据库操作
     */
    @Test
    public void updateByReachResult_shouldDoNothingWhenEmptyList() {
        // 执行测试
        updateSmsUserReachService.updateByReachResult(Collections.emptyList());

        // 验证交互
        verifyNoInteractions(detailRepository);
        verifyNoInteractions(historyRepository);
    }

    /**
     * 测试用例02：单任务实例处理
     * 场景：5条相同实例代码的记录
     */
    @Test
    public void updateByReachResult_shouldHandleSingleInstance() {
        // 准备测试数据
        List<SendHistoryDetail> details = Arrays.asList(
                createDetail(INSTANCE_CODE_1),
                createDetail(INSTANCE_CODE_1),
                createDetail(INSTANCE_CODE_1)
        );

        NotificationHistory mockHistory = new NotificationHistory();
        mockHistory.setSendSuccessCount(2);  // 初始值

        // 配置Mock行为
        doNothing().when(detailRepository).batchUpdateByIds(anyList());
        when(historyRepository.queryHistoryByInstanceCode(INSTANCE_CODE_1))
                .thenReturn(mockHistory);

        // 执行测试
        updateSmsUserReachService.updateByReachResult(details);

        // 验证交互
        verify(detailRepository).batchUpdateByIds(details);
        verify(historyRepository).queryHistoryByInstanceCode(INSTANCE_CODE_1);
        verify(historyRepository).updateById(argThat(history ->
                history.getSendSuccessCount() == 5));  // 2 + 3
    }

    /**
     * 测试用例03：多任务实例处理
     * 场景：实例A 3条，实例B 2条
     */
    @Test
    public void updateByReachResult_shouldHandleMultipleInstances() {
        // 准备测试数据
        List<SendHistoryDetail> details = Arrays.asList(
                createDetail(INSTANCE_CODE_1),
                createDetail(INSTANCE_CODE_1),
                createDetail(INSTANCE_CODE_1),
                createDetail(INSTANCE_CODE_2),
                createDetail(INSTANCE_CODE_2)
        );

        NotificationHistory history1 = new NotificationHistory();
        history1.setSendSuccessCount(1);
        NotificationHistory history2 = new NotificationHistory();
        history2.setSendSuccessCount(3);

        // 配置Mock行为
        doNothing().when(detailRepository).batchUpdateByIds(anyList());
        when(historyRepository.queryHistoryByInstanceCode(INSTANCE_CODE_1))
                .thenReturn(history1);
        when(historyRepository.queryHistoryByInstanceCode(INSTANCE_CODE_2))
                .thenReturn(history2);

        // 执行测试
        updateSmsUserReachService.updateByReachResult(details);

        // 验证交互
        verify(detailRepository).batchUpdateByIds(details);
        verify(historyRepository).updateById(argThat(history ->
                history.getSendSuccessCount() == 4));  // 1 + 3
        verify(historyRepository).updateById(argThat(history ->
                history.getSendSuccessCount() == 5));  // 3 + 2
    }

    /**
     * 创建测试用SendHistoryDetail对象
     * @param instanceCode 任务实例代码
     * @return 测试对象实例
     */
    private SendHistoryDetail createDetail(String instanceCode) {
        SendHistoryDetail detail = new SendHistoryDetail();
        detail.setTaskInstanceCode(instanceCode);
        return detail;
    }


    @Mock
    private OrderNotificationDetailRepository orderDetailRepository;

    private final String TEST_PHONE = "13800138000";


    // TC002: 查询结果为空测试
    @Test
    public void updateOrderNotifyDetailResult_WhenOrderDetailListEmpty_ShouldNotUpdate() {
        // 准备测试数据
        List<String> msgList = Arrays.asList(TEST_MSG_ID);
        when(orderDetailRepository.selectByMsgIdList(msgList)).thenReturn(Collections.emptyList());

        // 执行方法
        updateSmsUserReachService.updateOrderNotifyDetailResult(msgList, new HashMap<>());

        // 验证不执行保存
        verify(orderDetailRepository).selectByMsgIdList(msgList);
        verify(orderDetailRepository, never()).saveOrUpdateBatch(anyList());
    }

    // TC003: 存在匹配响应数据测试
    @Test
    public void updateOrderNotifyDetailResult_WithMatchingReport_ShouldUpdateFields() {
        // 准备测试数据
        List<String> msgList = Collections.singletonList(TEST_MSG_ID);
        OrderNotificationDetailDO detail = OrderNotificationDetailDO.builder().msgId(TEST_MSG_ID).build();

        ReportResp report = ReportResp.builder()
                .status("SUCCESS")
                .desc("Delivered")
                .wgcode("WG_001")
                .build();
        Map<String, ReportResp> respMap = Collections.singletonMap(TEST_MSG_ID, report);

        // 配置Mock行为
        when(orderDetailRepository.selectByMsgIdList(msgList)).thenReturn(Collections.singletonList(detail));

        // 执行方法
        updateSmsUserReachService.updateOrderNotifyDetailResult(msgList, respMap);

        // 验证字段更新
        assertThat(detail.getMsgResult()).isEqualTo("SUCCESS");
        assertThat(detail.getMsgErrorDesc()).isEqualTo("Delivered");
        assertThat(detail.getWgCode()).isEqualTo("WG_001");

        // 验证保存调用
        ArgumentCaptor<List<OrderNotificationDetailDO>> captor = ArgumentCaptor.forClass(List.class);
        verify(orderDetailRepository).saveOrUpdateBatch(captor.capture());
        assertThat(captor.getValue()).containsExactly(detail);
    }

    // TC004: 无匹配响应数据测试
    @Test
    public void updateOrderNotifyDetailResult_WithoutMatchingReport_ShouldNotUpdateFields() {
        // 准备测试数据
        List<String> msgList = Collections.singletonList(TEST_MSG_ID);
        OrderNotificationDetailDO detail = OrderNotificationDetailDO.builder()
                .msgId(TEST_MSG_ID)
                .msgResult("ORIGINAL")
                .build();

        // 配置Mock行为
        when(orderDetailRepository.selectByMsgIdList(msgList)).thenReturn(Collections.singletonList(detail));

        // 执行方法（空响应映射）
        updateSmsUserReachService.updateOrderNotifyDetailResult(msgList, Collections.emptyMap());

        // 验证字段未改变
        assertThat(detail.getMsgResult()).isEqualTo("ORIGINAL");
        assertThat(detail.getMsgErrorDesc()).isNull();
        assertThat(detail.getWgCode()).isNull();

        // 验证仍然执行保存
        verify(orderDetailRepository).saveOrUpdateBatch(anyList());
    }

    // TC005: 混合存在/不存在响应数据测试
    @Test
    public void updateOrderNotifyDetailResult_WithMixedCases_ShouldUpdateCorrectly() {
        // 准备测试数据
        String matchedMsgId = "MSG_001";
        String unmatchedMsgId = "MSG_002";

        // 创建两个订单详情
        OrderNotificationDetailDO matchedDetail = OrderNotificationDetailDO.builder().msgId(matchedMsgId).build();
        OrderNotificationDetailDO unmatchedDetail = OrderNotificationDetailDO.builder().msgId(unmatchedMsgId).build();

        // 创建响应映射（只包含一个匹配）
        ReportResp report = ReportResp.builder()
                .status("FAILED")
                .desc("Timeout")
                .wgcode("WG_500")
                .build();
        Map<String, ReportResp> respMap = Collections.singletonMap(matchedMsgId, report);

        // 配置Mock行为
        List<String> msgList = Arrays.asList(matchedMsgId, unmatchedMsgId);
        when(orderDetailRepository.selectByMsgIdList(msgList))
                .thenReturn(Arrays.asList(matchedDetail, unmatchedDetail));

        // 执行方法
        updateSmsUserReachService.updateOrderNotifyDetailResult(msgList, respMap);

        // 验证匹配记录的更新
        assertThat(matchedDetail.getMsgResult()).isEqualTo("FAILED");
        assertThat(matchedDetail.getMsgErrorDesc()).isEqualTo("Timeout");
        assertThat(matchedDetail.getWgCode()).isEqualTo("WG_500");

        // 验证未匹配记录未更新
        assertThat(unmatchedDetail.getMsgResult()).isNull();
        assertThat(unmatchedDetail.getMsgErrorDesc()).isNull();
        assertThat(unmatchedDetail.getWgCode()).isNull();

        // 验证批量保存包含两条记录
        ArgumentCaptor<List<OrderNotificationDetailDO>> captor = ArgumentCaptor.forClass(List.class);
        verify(orderDetailRepository).saveOrUpdateBatch(captor.capture());
        assertThat(captor.getValue()).hasSize(2);
    }

    /**
     * 测试用例1：queryHistoryByInstanceCodeList 返回空列表
     */
    @Test
    public void testUpdateHistoryResult_EmptyHistoryList() {
        // Mock queryHistoryByInstanceCodeList 返回空列表
        when(historyRepository.queryHistoryByInstanceCodeList(anyList())).thenReturn(Collections.emptyList());

        // 调用被测方法
        List<SendHistoryDetail> sendHistoryDetails = new ArrayList<>();
        List<String> instanceCodeList = Arrays.asList(INSTANCE_CODE_1, INSTANCE_CODE_2);
        updateSmsUserReachService.updateHistoryResult(instanceCodeList);

        // 验证 batchUpdateByIds 未被调用
        verify(historyRepository, never()).batchUpdateByIds(anyList());
    }

    /**
     * 测试用例2：msgResult 为成功，验证 reachUserSuccessCount 增加
     */
    @Test
    public void testUpdateHistoryResult_MsgResultSuccessful() {
        // 准备测试数据
        NotificationHistory history = new NotificationHistory();
        history.setTaskInstanceCode(INSTANCE_CODE_1);
        history.setReachUserSuccessCount(0);
        history.setReachUserFailCount(0);
        history.setSendTotalCount(10);

        SendHistoryDetail detail = new SendHistoryDetail();
        detail.setTaskInstanceCode(INSTANCE_CODE_1);
        detail.setMsgResult(MsgResultEnum.SUCCESSFUL.getStatus());

        // Mock queryHistoryByInstanceCodeList 返回包含 history 的列表
        when(historyRepository.queryHistoryByInstanceCodeList(anyList())).thenReturn(Collections.singletonList(history));

        // 调用被测方法
        List<SendHistoryDetail> sendHistoryDetails = Collections.singletonList(detail);
        List<String> instanceCodeList = Collections.singletonList(INSTANCE_CODE_1);
        updateSmsUserReachService.updateHistoryResult(instanceCodeList);

        // 验证 reachUserSuccessCount 增加
        verify(historyRepository).batchUpdateByIds(anyList());
        assertThat(history.getReachUserSuccessCount()).isEqualTo(0);
    }

    /**
     * 测试用例3：msgResult 为失败，验证 reachUserFailCount 增加
     */
    @Test
    public void testUpdateHistoryResult_MsgResultFailed() {
        // 准备测试数据
        NotificationHistory history = new NotificationHistory();
        history.setTaskInstanceCode(INSTANCE_CODE_1);
        history.setReachUserSuccessCount(0);
        history.setReachUserFailCount(0);
        history.setSendTotalCount(10);

        SendHistoryDetail detail = new SendHistoryDetail();
        detail.setTaskInstanceCode(INSTANCE_CODE_1);
        detail.setMsgResult(MsgResultEnum.FAILED.getStatus());

        // Mock queryHistoryByInstanceCodeList 返回包含 history 的列表
        when(historyRepository.queryHistoryByInstanceCodeList(anyList())).thenReturn(Collections.singletonList(history));

        // 调用被测方法
        List<SendHistoryDetail> sendHistoryDetails = Collections.singletonList(detail);
        List<String> instanceCodeList = Collections.singletonList(INSTANCE_CODE_1);
        updateSmsUserReachService.updateHistoryResult(instanceCodeList);

        // 验证 reachUserFailCount 增加
        verify(historyRepository).batchUpdateByIds(anyList());
        assertThat(history.getReachUserFailCount()).isEqualTo(0);
    }

    /**
     * 测试用例4：成功和失败计数之和等于总发送数量，验证 reachResultStatus 更新为完成
     */
    @Test
    public void testUpdateHistoryResult_ReachedTotalCount() {
        // 准备测试数据
        NotificationHistory history = new NotificationHistory();
        history.setTaskInstanceCode(INSTANCE_CODE_1);
        history.setReachUserSuccessCount(9);
        history.setReachUserFailCount(0);
        history.setSendTotalCount(10);

        SendHistoryDetail detail = new SendHistoryDetail();
        detail.setTaskInstanceCode(INSTANCE_CODE_1);
        detail.setMsgResult(MsgResultEnum.SUCCESSFUL.getStatus());

        // Mock queryHistoryByInstanceCodeList 返回包含 history 的列表
        when(historyRepository.queryHistoryByInstanceCodeList(anyList())).thenReturn(Collections.singletonList(history));

        // 调用被测方法
        List<SendHistoryDetail> sendHistoryDetails = Collections.singletonList(detail);
        List<String> instanceCodeList = Collections.singletonList(INSTANCE_CODE_1);
        updateSmsUserReachService.updateHistoryResult(instanceCodeList);

        // 验证 reachResultStatus 更新为完成
        verify(historyRepository).batchUpdateByIds(anyList());
        assertThat(history.getReachResultStatus()).isEqualTo(ReachResultStatusEnum.FINISHED.getStatus());
    }

    /**
     * 测试用例5：成功和失败计数之和小于总发送数量，验证 reachResultStatus 更新为未完成
     */
    @Test
    public void testUpdateHistoryResult_NotReachedTotalCount() {
        // 准备测试数据
        NotificationHistory history = new NotificationHistory();
        history.setTaskInstanceCode(INSTANCE_CODE_1);
        history.setReachUserSuccessCount(5);
        history.setReachUserFailCount(0);
        history.setSendTotalCount(10);

        SendHistoryDetail detail = new SendHistoryDetail();
        detail.setTaskInstanceCode(INSTANCE_CODE_1);
        detail.setMsgResult(MsgResultEnum.SUCCESSFUL.getStatus());

        // Mock queryHistoryByInstanceCodeList 返回包含 history 的列表
        when(historyRepository.queryHistoryByInstanceCodeList(anyList())).thenReturn(Collections.singletonList(history));

        // 调用被测方法
        List<SendHistoryDetail> sendHistoryDetails = Collections.singletonList(detail);
        List<String> instanceCodeList = Collections.singletonList(INSTANCE_CODE_1);
        updateSmsUserReachService.updateHistoryResult(instanceCodeList);

        // 验证 reachResultStatus 更新为未完成
        verify(historyRepository).batchUpdateByIds(anyList());
        assertThat(history.getReachResultStatus()).isEqualTo(ReachResultStatusEnum.FINISHED.getStatus());
    }

}
