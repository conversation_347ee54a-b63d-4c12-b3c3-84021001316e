package com.jlr.ecp.notification.dal.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.notification.dal.dataobject.shortlink.ShortLinkCreateRecordDO;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkCreateListDTO;

public interface ShortLinkCreateRepository extends IService<ShortLinkCreateRecordDO> {

    /**
     * 根据条件分页查询短链创建记录列表
     *
     * @param listDTO 包含分页和排序信息的查询条件对象
     * @return 包含短链创建记录的分页对象
     */
    Page<ShortLinkCreateRecordDO> pageList(ShortLinkCreateListDTO listDTO);

    /**
     * 根据配置ID获取短链创建记录对象
     *
     * @param configId 配置ID，用于查询短链创建记录
     * @return ShortLinkCreateRecordDO 短链创建记录对象，如果不存在则返回null
     */
    ShortLinkCreateRecordDO getRecordDoByConfigId(Long configId);

}
