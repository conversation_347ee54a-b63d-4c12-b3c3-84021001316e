package com.jlr.ecp.notification.service.impl;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.notification.api.dto.ExpireNotifyConditionDTO;
import com.jlr.ecp.notification.dal.dataobject.task.TaskModifyLog;
import com.jlr.ecp.notification.dal.dataobject.task.auto.AutoTaskConditionDO;
import com.jlr.ecp.notification.dal.dataobject.task.auto.AutoTaskTriggerMapDO;
import com.jlr.ecp.notification.dal.dataobject.task.auto.NotificationAutoTaskDO;
import com.jlr.ecp.notification.dal.dataobject.template.MessageTemplate;
import com.jlr.ecp.notification.dal.repository.*;
import com.jlr.ecp.notification.enums.ErrorCodeConstants;
import com.jlr.ecp.notification.enums.task.*;
import com.jlr.ecp.notification.req.task.TaskDetailReq;
import com.jlr.ecp.notification.req.task.TaskHistoryReq;
import com.jlr.ecp.notification.req.task.TaskModifyStatusReq;
import com.jlr.ecp.notification.req.task.TaskPageListReq;
import com.jlr.ecp.notification.req.task.auto.AutoTaskCreatReq;
import com.jlr.ecp.notification.req.task.auto.AutoTaskModifyTimeReq;
import com.jlr.ecp.notification.req.task.auto.TriggerActionFieldReq;
import com.jlr.ecp.notification.req.task.auto.TriggerActionReq;
import com.jlr.ecp.notification.util.OperatorUtil;
import com.jlr.ecp.notification.util.sms.TimeFormatUtil;
import com.jlr.ecp.notification.vo.task.SmsTaskPageListVo;
import com.jlr.ecp.notification.vo.task.TaskHistoryPageListVo;
import com.jlr.ecp.notification.vo.task.auto.*;
import com.jlr.ecp.product.api.spu.ProductSpuApi;
import com.jlr.ecp.product.api.spu.dto.SpuInfoDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.Redisson;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class SmsAutoTaskServiceImplTest {

    @Mock
    private MessageTemplateRepository mockTemplateRepository;
    @Mock
    private NotificationAutoTaskRepository mockAutoTaskRepository;
    @Mock
    private MessageTaskRepository mockManualTaskRepository;
    @Mock
    private TaskModifyRepository mockTaskModifyRepository;
    @Mock
    private AutoTaskConditionRepository mockConditionRepository;
    @Mock
    private AutoTaskTriggerMapRepository mockTriggerMapRepository;
    @Mock
    private Snowflake mockSnowflake;
    @Mock
    private ProductSpuApi mockProductSpuApi;
    @Mock
    private Redisson mockRedisson;

    @InjectMocks
    @Spy
    private SmsAutoTaskServiceImpl smsAutoTaskService;

    private NotificationAutoTaskDO autoTaskDO;
    private TaskModifyStatusReq modifyStatusReq;

    @Before
    public void setUp() {
        // 初始化测试数据
        autoTaskDO = new NotificationAutoTaskDO();
        autoTaskDO.setId(1L);
        autoTaskDO.setTaskSendType(1); // 实时任务类型

        modifyStatusReq = new TaskModifyStatusReq();
        modifyStatusReq.setTaskCode("testTaskCode");
        modifyStatusReq.setModifyStatus(TaskStatusEnum.STOP.getStatus()); // 默认设置为禁用状态

        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试用例1：queryAutoTaskPageList 返回空记录
     */
    @Test
    public void testGetTaskPageList_EmptyResult() {
        // 准备测试数据
        TaskPageListReq taskPageListReq = new TaskPageListReq();
        taskPageListReq.setTaskCode("taskCode");

        // Mock queryAutoTaskPageList 返回空记录
        Page<NotificationAutoTaskDO> emptyPage = new Page<>();
        emptyPage.setTotal(0L);
        emptyPage.setRecords(Collections.emptyList());
        doReturn(emptyPage).when(mockAutoTaskRepository).queryAutoTaskPageList(taskPageListReq);

        // 执行测试
        PageResult<SmsTaskPageListVo> result = smsAutoTaskService.getTaskPageList(taskPageListReq);

        // 验证结果
        assertEquals(null, result.getTotal());
    }

    /**
     * 测试用例2：queryAutoTaskPageList 返回非空记录
     */
    @Test
    public void testGetTaskPageList_NonEmptyResult() {
        // 准备测试数据
        TaskPageListReq taskPageListReq = new TaskPageListReq();
        taskPageListReq.setTaskCode("taskCode");

        // Mock queryAutoTaskPageList 返回非空记录
        NotificationAutoTaskDO taskDO = new NotificationAutoTaskDO();
        taskDO.setTaskCode("taskCode");
        taskDO.setTaskName("taskName");
        taskDO.setTriggerAction("triggerAction");
        taskDO.setTaskSendType(1);
        taskDO.setRangeBeginDate(LocalDateTime.now());
        taskDO.setRangeEndDate(LocalDateTime.now());
        taskDO.setDailySendTime("12:00");
        taskDO.setMessageTemplateCode("templateCode");
        taskDO.setStatus(1);
        taskDO.setActivateTime(LocalDateTime.now());
        taskDO.setDeactivateTime(LocalDateTime.now());

        Page<NotificationAutoTaskDO> taskPage = new Page<>();
        taskPage.setRecords(List.of(taskDO));
        taskPage.setTotal(1L);
        when(mockAutoTaskRepository.queryAutoTaskPageList(taskPageListReq)).thenReturn(taskPage);

        // Mock getTemplateContentByCode 返回模板内容
        when(mockTemplateRepository.getTemplateContentByCode("templateCode")).thenReturn("templateContent");

        // 执行测试
        PageResult<SmsTaskPageListVo> result = smsAutoTaskService.getTaskPageList(taskPageListReq);

        // 验证结果
        assertEquals(1, result.getTotal().longValue());
        assertEquals(1, result.getList().size());

        SmsTaskPageListVo vo = result.getList().get(0);
        assertEquals("taskCode", vo.getTaskCode());
        assertEquals("taskName", vo.getTaskName());
        assertEquals("triggerAction", vo.getTriggerAction());
        assertEquals("实时", vo.getTaskSendTimeType());
       assertEquals("12:00", vo.getDailySendTime());
        assertEquals("templateContent", vo.getMessageTemplateContent());
        assertEquals(Integer.valueOf(1), vo.getStatus());
        assertEquals(Boolean.FALSE, vo.getExpirationFlag());
    }

    /**
     * 测试用例3：queryAutoTaskPageList 返回的记录中某些字段为空
     */
    @Test
    public void testGetTaskPageList_NullFields() {
        // 准备测试数据
        TaskPageListReq taskPageListReq = new TaskPageListReq();
        taskPageListReq.setTaskCode("taskCode");

        // Mock queryAutoTaskPageList 返回记录中某些字段为空
        NotificationAutoTaskDO taskDO = new NotificationAutoTaskDO();
        taskDO.setTaskCode("taskCode");
        taskDO.setTaskName(null); // 空字段
        taskDO.setTriggerAction(null); // 空字段
        taskDO.setTaskSendType(null); // 空字段
        taskDO.setRangeBeginDate(null); // 空字段
        taskDO.setRangeEndDate(null); // 空字段
        taskDO.setDailySendTime(null); // 空字段
        taskDO.setMessageTemplateCode(null); // 空字段
        taskDO.setStatus(null); // 空字段
        taskDO.setActivateTime(null); // 空字段
        taskDO.setDeactivateTime(null); // 空字段

        Page<NotificationAutoTaskDO> taskPage = new Page<>();
        taskPage.setRecords(List.of(taskDO));
        taskPage.setTotal(1L);
        when(mockAutoTaskRepository.queryAutoTaskPageList(taskPageListReq)).thenReturn(taskPage);

        // 执行测试
        PageResult<SmsTaskPageListVo> result = smsAutoTaskService.getTaskPageList(taskPageListReq);

        // 验证结果
        assertEquals(1, result.getTotal().longValue());
        assertEquals(1, result.getList().size());

        SmsTaskPageListVo vo = result.getList().get(0);
        assertEquals("taskCode", vo.getTaskCode());
        assertEquals(Boolean.FALSE, vo.getExpirationFlag()); // 默认值
    }



    @Test
    public void testQuerySpuNameByCode() {
        // Setup
        // Configure ProductSpuApi.getSpuInfoByCode(...).
        final SpuInfoDTO spuInfoDTO = new SpuInfoDTO();
        spuInfoDTO.setProductCode("productCode");
        spuInfoDTO.setBrandCode("brandCode");
        spuInfoDTO.setCategoryCodeLevel1("categoryCodeLevel1");
        spuInfoDTO.setCategoryCodeLevel2("categoryCodeLevel2");
        spuInfoDTO.setProductName("");
        final CommonResult<SpuInfoDTO> spuInfoDTOCommonResult = CommonResult.success(spuInfoDTO);
        when(mockProductSpuApi.getSpuInfoByCode("spuCode")).thenReturn(spuInfoDTOCommonResult);

        // Run the test
        final String result = smsAutoTaskService.querySpuNameByCode("spuCode");

        // Verify the results
        assertEquals("", result);
    }

    @Test
    public void testQuerySpuNameByCode_ProductSpuApiReturnsError() {
        // Setup
        // Configure ProductSpuApi.getSpuInfoByCode(...).
        final CommonResult<SpuInfoDTO> spuInfoDTOCommonResult = CommonResult.error(new ServiceException());
        when(mockProductSpuApi.getSpuInfoByCode("spuCode")).thenReturn(spuInfoDTOCommonResult);

        // Run the test
        final String result = smsAutoTaskService.querySpuNameByCode("spuCode");

        // Verify the results
        assertEquals("", result);
    }


    @Test
    public void testGetAllTaskTypeNameList() {
        // Setup
        when(mockAutoTaskRepository.querytAutoTaskNameList()).thenReturn(List.of("value"));

        // Run the test
        final List<String> result = smsAutoTaskService.getAllTaskTypeNameList();

        // Verify the results
        assertEquals(List.of("value"), result);
    }

    @Test
    public void testGetAllTaskTypeNameList_MessageTaskRepositoryReturnsNoItems() {
        // Setup
        when(mockAutoTaskRepository.querytAutoTaskNameList()).thenReturn(List.of("value"));

        // Run the test
        final List<String> result = smsAutoTaskService.getAllTaskTypeNameList();

        // Verify the results
        assertEquals(List.of("value"), result);
    }

    @Test
    public void testGetAutoTaskNameList() {
        // Setup
        when(mockAutoTaskRepository.querytAutoTaskNameList()).thenReturn(List.of("value"));

        // Run the test
        final List<String> result = smsAutoTaskService.getAutoTaskNameList();

        // Verify the results
        assertEquals(List.of("value"), result);
    }

    @Test
    public void testGetAutoTaskNameList_NotificationAutoTaskRepositoryReturnsNoItems() {
        // Setup
        when(mockAutoTaskRepository.querytAutoTaskNameList()).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = smsAutoTaskService.getAutoTaskNameList();

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }


    @Test
    public void testGetManualTaskNameList_MessageTaskRepositoryReturnsNoItems() {
        // Run the test
        final List<String> result = smsAutoTaskService.getManualTaskNameList();

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetAutoTemplateNameList_MessageTemplateRepositoryReturnsNoItems() {
        // Setup
        // Run the test
        final List<AutoTemplateNameListVO> result = smsAutoTaskService.getAutoTemplateNameList(
                "businessCode");

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testBuilderAutoTemplateNameListVOList() {
        // Setup
        final List<MessageTemplate> templateList = List.of(MessageTemplate.builder()
                .templateCode("templateCode")
                .templateName("templateName")
                .templateType(0)
                .templateContent("templateContent")
                .build());
        final List<AutoTemplateNameListVO> expectedResult = List.of(AutoTemplateNameListVO.builder()
                .templateName("templateName")
                .templateCode("templateCode")
                .build());

        // Run the test
        final List<AutoTemplateNameListVO> result = smsAutoTaskService.builderAutoTemplateNameListVOList(
                templateList);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testBuilderAutoTemplateNameListVO() {
        // Setup
        final MessageTemplate template = MessageTemplate.builder()
                .templateCode("templateCode")
                .templateName("templateName")
                .templateType(0)
                .templateContent("templateContent")
                .build();
        final AutoTemplateNameListVO expectedResult = AutoTemplateNameListVO.builder()
                .templateName("templateName")
                .templateCode("templateCode")
                .build();

        // Run the test
        final AutoTemplateNameListVO result = smsAutoTaskService.builderAutoTemplateNameListVO(template);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetAutoTemplateContent() {
        // Setup
        when(mockTemplateRepository.getTemplateContentByCode("templateCode")).thenReturn("result");

        // Run the test
        final String result = smsAutoTaskService.getAutoTemplateContent("templateCode");

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testAddAutoTask() {
        // Setup
        final AutoTaskCreatReq autoTaskCreatReq = new AutoTaskCreatReq();
        autoTaskCreatReq.setTaskName("taskName");
        autoTaskCreatReq.setTaskTimeType(0);
        autoTaskCreatReq.setRangeBeginDate("rangeBeginDate");
        autoTaskCreatReq.setRangeEndDate("rangeEndDate");
        autoTaskCreatReq.setDailySendTime("dailySendTime");
        autoTaskCreatReq.setTriggerActionString("triggerAction");
        final TriggerActionReq triggerAction = new TriggerActionReq();
        final TriggerActionFieldReq brandReq = new TriggerActionFieldReq();
        brandReq.setConditionId("conditionId");
        brandReq.setConditionValue("conditionValue");
        triggerAction.setBrandReq(brandReq);
        final TriggerActionFieldReq vehicleOriginReq = new TriggerActionFieldReq();
        vehicleOriginReq.setConditionId("conditionId");
        vehicleOriginReq.setConditionValue("conditionValue");
        triggerAction.setVehicleOriginReq(vehicleOriginReq);
        final TriggerActionFieldReq expirationServiceReq = new TriggerActionFieldReq();
        expirationServiceReq.setConditionId("conditionId");
        expirationServiceReq.setConditionValue("conditionValue");
        triggerAction.setExpirationServiceReq(expirationServiceReq);
        final TriggerActionFieldReq realNameStatusReq = new TriggerActionFieldReq();
        realNameStatusReq.setConditionId("conditionId");
        realNameStatusReq.setConditionValue("conditionValue");
        triggerAction.setRealNameStatusReq(realNameStatusReq);
        final TriggerActionFieldReq expirationIntervalReq = new TriggerActionFieldReq();
        expirationIntervalReq.setConditionId("conditionId");
        expirationIntervalReq.setConditionValue("conditionValue");
        triggerAction.setExpirationIntervalReq(expirationIntervalReq);
        autoTaskCreatReq.setTriggerAction(triggerAction);
        autoTaskCreatReq.setMessageTemplateCode("messageTemplateCode");
        autoTaskCreatReq.setNotifySpuCode("spuCode");

        final CommonResult<Boolean> expectedResult = CommonResult.success(false);
        when(mockSnowflake.nextIdStr()).thenReturn("taskCode");

        // Run the test
        final CommonResult<Boolean> result = smsAutoTaskService.addAutoTask(autoTaskCreatReq);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testBuilderAutoTaskTriggerMapDO() {
        // Setup
        final TriggerActionFieldReq triggerFieldReq = new TriggerActionFieldReq("conditionId", "conditionCode",
                "conditionValue");
        final AutoTaskTriggerMapDO expectedResult = AutoTaskTriggerMapDO.builder()
                .taskCode("taskCode")
                .conditionId("conditionId")
                .conditionValue("conditionValue")
                .build();

        // Run the test
        final AutoTaskTriggerMapDO result = smsAutoTaskService.builderAutoTaskTriggerMapDO("taskCode",
                triggerFieldReq);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    /**
     * 测试用例1：queryConditionByType 返回非空列表
     */
    @Test
    public void testQueryAutoTaskCondition_NonEmptyResult() {
        // 准备测试数据
        List<AutoTaskConditionDO> brandList = createConditionDOList("brand", 1);
        List<AutoTaskConditionDO> vehicleOriginList = createConditionDOList("vehicleOrigin", 2);
        List<AutoTaskConditionDO> expirationServiceList = createConditionDOList("expirationService", 3);
        List<AutoTaskConditionDO> realNameStatusList = createConditionDOList("realNameStatus", 4);
        List<AutoTaskConditionDO> expirationIntervalList = createConditionDOList("expirationInterval", 5);

        // Mock queryConditionByType 返回非空列表
        when(mockConditionRepository.queryConditionByType(AutoTaskConditionTypeEnum.BRAND.getType())).thenReturn(brandList);
        when(mockConditionRepository.queryConditionByType(AutoTaskConditionTypeEnum.VEHICLE_ORIGIN.getType())).thenReturn(vehicleOriginList);
        when(mockConditionRepository.queryConditionByType(AutoTaskConditionTypeEnum.EXPIRATION_SERVICE.getType())).thenReturn(expirationServiceList);
        when(mockConditionRepository.queryConditionByType(AutoTaskConditionTypeEnum.REAL_NAME_STATUS.getType())).thenReturn(realNameStatusList);
        when(mockConditionRepository.queryConditionByType(AutoTaskConditionTypeEnum.EXPIRATION_INTERVAL.getType())).thenReturn(expirationIntervalList);

        // 执行测试
        AutoTaskConditionVO result = smsAutoTaskService.queryAutoTaskCondition();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getAttributeVOList());
        assertEquals(5, result.getAttributeVOList().size());

        TaskConditionAttributeVO brandVO = result.getAttributeVOList().get(0);
        assertEquals("brandReq", brandVO.getRequestName());
        assertEquals("品牌", brandVO.getButtonName());
        assertEquals("单选", brandVO.getButtonDesc());
        assertFalse(brandVO.getHasAttribute());

        TaskConditionAttributeVO expirationIntervalVO = result.getAttributeVOList().get(4);
        assertEquals("expirationIntervalReq", expirationIntervalVO.getRequestName());
        assertTrue(expirationIntervalVO.getHasAttribute());
    }

    /**
     * 测试用例2：queryConditionByType 返回空列表
     */
    @Test
    public void testQueryAutoTaskCondition_EmptyResult() {
        // Mock queryConditionByType 返回空列表
        when(mockConditionRepository.queryConditionByType(anyInt())).thenReturn(Collections.emptyList());

        // 执行测试
        AutoTaskConditionVO result = smsAutoTaskService.queryAutoTaskCondition();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getAttributeVOList());
        assertEquals(5, result.getAttributeVOList().size());

        for (TaskConditionAttributeVO attributeVO : result.getAttributeVOList()) {
            assertNull(attributeVO.getButtonName());
            assertNull(attributeVO.getButtonDesc());
            assertNull(attributeVO.getConditionAttributeVOList());
        }
    }

    /**
     * 测试用例3：部分条件类型返回空列表
     */
    @Test
    public void testQueryAutoTaskCondition_PartialEmptyResult() {
        // 准备测试数据
        List<AutoTaskConditionDO> brandList = createConditionDOList("brand", 1);
        List<AutoTaskConditionDO> vehicleOriginList = Collections.emptyList();
        List<AutoTaskConditionDO> expirationServiceList = createConditionDOList("expirationService", 3);
        List<AutoTaskConditionDO> realNameStatusList = Collections.emptyList();
        List<AutoTaskConditionDO> expirationIntervalList = createConditionDOList("expirationInterval", 5);

        // Mock queryConditionByType 返回部分空列表
        when(mockConditionRepository.queryConditionByType(AutoTaskConditionTypeEnum.BRAND.getType())).thenReturn(brandList);
        when(mockConditionRepository.queryConditionByType(AutoTaskConditionTypeEnum.VEHICLE_ORIGIN.getType())).thenReturn(vehicleOriginList);
        when(mockConditionRepository.queryConditionByType(AutoTaskConditionTypeEnum.EXPIRATION_SERVICE.getType())).thenReturn(expirationServiceList);
        when(mockConditionRepository.queryConditionByType(AutoTaskConditionTypeEnum.REAL_NAME_STATUS.getType())).thenReturn(realNameStatusList);
        when(mockConditionRepository.queryConditionByType(AutoTaskConditionTypeEnum.EXPIRATION_INTERVAL.getType())).thenReturn(expirationIntervalList);

        // 执行测试
        AutoTaskConditionVO result = smsAutoTaskService.queryAutoTaskCondition();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getAttributeVOList());
        assertEquals(5, result.getAttributeVOList().size());

        TaskConditionAttributeVO brandVO = result.getAttributeVOList().get(0);
        assertNotNull(brandVO.getButtonName());
        assertNotNull(brandVO.getButtonDesc());
        assertNotNull(brandVO.getConditionAttributeVOList());

        TaskConditionAttributeVO vehicleOriginVO = result.getAttributeVOList().get(1);
        assertNull(vehicleOriginVO.getButtonName());
        assertNull(vehicleOriginVO.getButtonDesc());
        assertNull(vehicleOriginVO.getConditionAttributeVOList());
    }

    /**
     * 创建测试用的 AutoTaskConditionDO 列表
     */
    private List<AutoTaskConditionDO> createConditionDOList(String prefix, int type) {
        List<AutoTaskConditionDO> list = new ArrayList<>();
        AutoTaskConditionDO conditionDO = new AutoTaskConditionDO();
        conditionDO.setConditionId(prefix + "_id");
        conditionDO.setConditionCode(prefix + "_code");
        conditionDO.setConditionName(prefix + "_name");
        conditionDO.setConditionType(type);
        list.add(conditionDO);
        return list;
    }


    /**
     * 测试用例1：任务不存在
     */
    @Test
    public void testGetAutoTaskDetail_TaskNotFound() {
        // 准备测试数据
        TaskDetailReq taskDetailReq = new TaskDetailReq();
        taskDetailReq.setTaskCode("nonExistentTaskCode");

        // Mock queryAutoTaskByCode 返回 null
        when(mockAutoTaskRepository.queryAutoTaskByCode("nonExistentTaskCode")).thenReturn(null);

        // 执行测试
        CommonResult<AutoTaskDetailVO> result = smsAutoTaskService.getAutoTaskDetail(taskDetailReq);

        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertNull(result.getData().getTaskCode());
        assertNull(result.getData().getTaskName());
    }

    /**
     * 测试用例2：任务存在但无消息模板
     */
    @Test
    public void testGetAutoTaskDetail_NoMessageTemplate() {
        // 准备测试数据
        TaskDetailReq taskDetailReq = new TaskDetailReq();
        taskDetailReq.setTaskCode("taskCode");

        NotificationAutoTaskDO autoTask = new NotificationAutoTaskDO();
        autoTask.setTaskCode("taskCode");
        autoTask.setTaskName("taskName");
        autoTask.setMessageTemplateCode("templateCode");

        // Mock queryAutoTaskByCode 返回任务信息
        when(mockAutoTaskRepository.queryAutoTaskByCode("taskCode")).thenReturn(autoTask);

        // Mock getMessageTemplateByCode 返回 null
        when(mockTemplateRepository.getMessageTemplateByCode("templateCode")).thenReturn(null);

        // 执行测试
        CommonResult<AutoTaskDetailVO> result = smsAutoTaskService.getAutoTaskDetail(taskDetailReq);

        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals("taskCode", result.getData().getTaskCode());
        assertEquals("taskName", result.getData().getTaskName());
        assertNull(result.getData().getMessageTemplateCode());
        assertNull(result.getData().getTemplateName());
    }

    /**
     * 测试用例3：任务存在且有消息模板
     */
    @Test
    public void testGetAutoTaskDetail_WithMessageTemplate() {
        // 准备测试数据
        TaskDetailReq taskDetailReq = new TaskDetailReq();
        taskDetailReq.setTaskCode("taskCode");

        NotificationAutoTaskDO autoTask = new NotificationAutoTaskDO();
        autoTask.setTaskCode("taskCode");
        autoTask.setTaskName("taskName");
        autoTask.setMessageTemplateCode("templateCode");
        autoTask.setRangeBeginDate(LocalDateTime.now());
        autoTask.setRangeEndDate(LocalDateTime.now());

        MessageTemplate messageTemplate = new MessageTemplate();
        messageTemplate.setTemplateCode("templateCode");
        messageTemplate.setTemplateName("templateName");
        messageTemplate.setTemplateType(1);
        messageTemplate.setTemplateContent("templateContent");

        // Mock queryAutoTaskByCode 返回任务信息
        when(mockAutoTaskRepository.queryAutoTaskByCode("taskCode")).thenReturn(autoTask);

        // Mock getMessageTemplateByCode 返回消息模板信息
        when(mockTemplateRepository.getMessageTemplateByCode("templateCode")).thenReturn(messageTemplate);

        // 执行测试
        CommonResult<AutoTaskDetailVO> result = smsAutoTaskService.getAutoTaskDetail(taskDetailReq);

        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals("taskCode", result.getData().getTaskCode());
        assertEquals("taskName", result.getData().getTaskName());
        assertEquals("templateCode", result.getData().getMessageTemplateCode());
        assertEquals("templateName", result.getData().getTemplateName());
        assertEquals("templateContent", result.getData().getMessageTemplateContent());
    }

    /**
     * 测试用例4：触发动作为空
     */
    @Test
    public void testGetAutoTaskDetail_NoTriggerAction() {
        // 准备测试数据
        TaskDetailReq taskDetailReq = new TaskDetailReq();
        taskDetailReq.setTaskCode("taskCode");

        NotificationAutoTaskDO autoTask = new NotificationAutoTaskDO();
        autoTask.setTaskCode("taskCode");
        autoTask.setTaskName("taskName");

        // Mock queryAutoTaskByCode 返回任务信息
        when(mockAutoTaskRepository.queryAutoTaskByCode("taskCode")).thenReturn(autoTask);

        // Mock getAutoTriggerByTaskCode 返回空列表
        when(mockTriggerMapRepository.getAutoTriggerByTaskCode("taskCode")).thenReturn(Collections.emptyList());

        // 执行测试
        CommonResult<AutoTaskDetailVO> result = smsAutoTaskService.getAutoTaskDetail(taskDetailReq);

        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertNull(result.getData().getTriggerAction());
    }

    /**
     * 测试用例1：入参为空，验证函数是否直接返回
     */
    @Test
    public void testUpdateTaskStatus_NullInput() {
        // 执行测试
        smsAutoTaskService.updateTaskStatus(null, null);

        // 验证 repository 方法未被调用
        verify(mockAutoTaskRepository, never()).updateAutoTaskById(any());
    }

    /**
     * 测试用例2：状态为禁用 (STOP)，验证禁用时间是否正确设置
     */
    @Test
    public void testUpdateTaskStatus_StopStatus() {
        // 设置状态为禁用
        modifyStatusReq.setModifyStatus(TaskStatusEnum.STOP.getStatus());

        // 执行测试
        smsAutoTaskService.updateTaskStatus(autoTaskDO, modifyStatusReq);

        // 验证禁用时间被正确设置
        assertNotNull(autoTaskDO.getDeactivateTime());
        assertEquals(TaskStatusEnum.STOP.getStatus(), autoTaskDO.getStatus());

        // 验证 repository 方法被调用
        verify(mockAutoTaskRepository, times(1)).updateAutoTaskById(autoTaskDO);
    }

    /**
     * 测试用例3：状态为启用 (START)，验证启用时间是否正确设置，且禁用时间被清空
     */
    @Test
    public void testUpdateTaskStatus_StartStatus() {
        // 设置状态为启用
        modifyStatusReq.setModifyStatus(TaskStatusEnum.START.getStatus());

        // 执行测试
        smsAutoTaskService.updateTaskStatus(autoTaskDO, modifyStatusReq);

        // 验证启用时间被正确设置，且禁用时间被清空
        assertNotNull(autoTaskDO.getActivateTime());
        assertNull(autoTaskDO.getDeactivateTime());
        assertEquals(TaskStatusEnum.START.getStatus(), autoTaskDO.getStatus());

        // 验证 repository 方法被调用
        verify(mockAutoTaskRepository, times(1)).updateAutoTaskById(autoTaskDO);
    }

    /**
     * 测试用例4：调用 repository 方法，验证是否正确保存更新后的任务
     */
    @Test
    public void testUpdateTaskStatus_RepositoryCall() {
        // 设置状态为启用
        modifyStatusReq.setModifyStatus(TaskStatusEnum.START.getStatus());

        // 执行测试
        smsAutoTaskService.updateTaskStatus(autoTaskDO, modifyStatusReq);

        // 验证 repository 方法被调用
        verify(mockAutoTaskRepository, times(1)).updateAutoTaskById(autoTaskDO);
    }

    /**
     * 测试用例01：正常添加启用状态日志
     * 验证点：
     * 1. 正确设置任务编码
     * 2. 正确转换操作类型描述
     * 3. 自动记录操作人和时间
     */
//    @Test
//    public void testAddTaskModifyStatusLog_NormalCase() {
//        // 准备测试请求
//        TaskModifyStatusReq req = new TaskModifyStatusReq();
//        req.setTaskCode("TASK_2023");
//        req.setModifyStatus(TaskModifyTypeEnum.START.getModifyType());
//
//        // 执行测试方法
//        smsAutoTaskService.addTaskModifyStatusLog(req);
//
//        // 验证行为并捕获参数
//        ArgumentCaptor<TaskModifyLog> captor = ArgumentCaptor.forClass(TaskModifyLog.class);
//        verify(mockTaskModifyRepository).insertTaskModifyLog(captor.capture());
//
//        TaskModifyLog actualLog = captor.getValue();
//
//        // 断言字段值
//        assertEquals("任务编码不一致", "TASK_2023", actualLog.getTaskCode());
//        assertEquals("操作类型描述错误", TaskModifyTypeEnum.START.getDesc(), actualLog.getModifyContent());
//        assertEquals("操作人记录错误", OperatorUtil.getOperator(), actualLog.getModifyUser());
//    }



    /**
     * 测试用例03：添加非法操作类型日志
     * 验证点：
     * 1. 当操作类型非法时返回空描述
     * 2. 其他字段正常记录
     */
//    @Test
//    public void testAddTaskModifyStatusLog_InvalidModifyType() {
//        // 准备测试请求
//        TaskModifyStatusReq req = new TaskModifyStatusReq();
//        req.setTaskCode("TASK_2023");
//        req.setModifyStatus(999); // 不存在的操作类型
//
//        // 执行测试方法
//        smsAutoTaskService.addTaskModifyStatusLog(req);
//
//        // 验证行为并捕获参数
//        ArgumentCaptor<TaskModifyLog> captor = ArgumentCaptor.forClass(TaskModifyLog.class);
//        verify(mockTaskModifyRepository).insertTaskModifyLog(captor.capture());
//
//        TaskModifyLog actualLog = captor.getValue();
//
//        // 断言特殊字段
//        assertEquals("任务编码不一致", "TASK_2023", actualLog.getTaskCode());
//        assertEquals("变更内容应返回空", "", actualLog.getModifyContent());
//        assertEquals("操作人记录错误", OperatorUtil.getOperator(), actualLog.getModifyUser());
//    }

    /**
     * 测试用例1：taskType == null，验证返回值是否为所有任务类型的名称列表
     */
    @Test
    public void testGetTaskNameList_TaskTypeIsNull() {
        // Mock 自动任务名称列表
        List<String> autoTaskNames = Arrays.asList("AutoTask1", "AutoTask2");
        when(mockAutoTaskRepository.querytAutoTaskNameList()).thenReturn(autoTaskNames);

        // Mock 手动任务名称列表
        List<String> manualTaskNames = Arrays.asList("ManualTask1", "ManualTask2");
        when(mockManualTaskRepository.queryTaskNameListFilterAutoEvent(TaskTypeEnum.MANUAL.getType()))
                .thenReturn(manualTaskNames);

        // 执行测试
        List<String> result = smsAutoTaskService.getTaskNameList(null);

        // 验证结果
        assertEquals(4, result.size());
        assertEquals(Arrays.asList("AutoTask1", "AutoTask2", "ManualTask1", "ManualTask2"), result);

        // 验证 Mock 方法调用
        verify(mockAutoTaskRepository, times(1)).querytAutoTaskNameList();
        verify(mockManualTaskRepository, times(1)).queryTaskNameListFilterAutoEvent(TaskTypeEnum.MANUAL.getType());
    }

    /**
     * 测试用例2：taskType == TaskTypeEnum.AUTO_TASK.getType()，验证返回值是否为自动任务名称列表
     */
    @Test
    public void testGetTaskNameList_AutoTaskType() {
        // Mock 自动任务名称列表
        List<String> autoTaskNames = Arrays.asList("AutoTask1", "AutoTask2");
        when(mockAutoTaskRepository.querytAutoTaskNameList()).thenReturn(autoTaskNames);

        // 执行测试
        List<String> result = smsAutoTaskService.getTaskNameList(TaskTypeEnum.AUTO_TASK.getType());

        // 验证结果
        assertEquals(2, result.size());
        assertEquals(Arrays.asList("AutoTask1", "AutoTask2"), result);

        // 验证 Mock 方法调用
        verify(mockAutoTaskRepository, times(1)).querytAutoTaskNameList();
        verify(mockManualTaskRepository, never()).queryTaskNameListFilterAutoEvent(anyInt());
    }

    /**
     * 测试用例3：taskType == TaskTypeEnum.MANUAL.getType()，验证返回值是否为手动任务名称列表
     */
    @Test
    public void testGetTaskNameList_ManualTaskType() {
        // Mock 手动任务名称列表
        List<String> manualTaskNames = Arrays.asList("ManualTask1", "ManualTask2");
        when(mockManualTaskRepository.queryTaskNameListFilterAutoEvent(TaskTypeEnum.MANUAL.getType()))
                .thenReturn(manualTaskNames);

        // 执行测试
        List<String> result = smsAutoTaskService.getTaskNameList(TaskTypeEnum.MANUAL.getType());

        // 验证结果
        assertEquals(2, result.size());
        assertEquals(Arrays.asList("ManualTask1", "ManualTask2"), result);

        // 验证 Mock 方法调用
        verify(mockManualTaskRepository, times(1)).queryTaskNameListFilterAutoEvent(TaskTypeEnum.MANUAL.getType());
        verify(mockAutoTaskRepository, never()).querytAutoTaskNameList();
    }

    /**
     * 测试用例4：taskType 为无效值，验证返回值是否为空列表
     */
    @Test
    public void testGetTaskNameList_InvalidTaskType() {
        // 执行测试
        List<String> result = smsAutoTaskService.getTaskNameList(999);

        // 验证结果
        assertEquals(0, result.size());

        // 验证 Mock 方法调用
        verify(mockAutoTaskRepository, never()).querytAutoTaskNameList();
        verify(mockManualTaskRepository, never()).queryTaskNameListFilterAutoEvent(anyInt());
    }


    /**
     * 测试用例1：queryTaskHistoryPageList 返回正常记录
     */
//    @Test
//    public void testGetTaskHistoryPageList_NormalCase() {
//        // 准备测试数据
//        TaskHistoryReq taskHistoryReq = new TaskHistoryReq();
//        taskHistoryReq.setTaskCode("testTaskCode");
//
//        // Mock queryTaskHistoryPageList 返回正常记录
//        TaskModifyLog log1 = new TaskModifyLog();
//        log1.setTaskCode("testTaskCode");
//        log1.setModifyContent("修改内容1");
//        log1.setModifyUser("用户1");
//        log1.setModifyTime(LocalDateTime.now());
//
//        TaskModifyLog log2 = new TaskModifyLog();
//        log2.setTaskCode("testTaskCode");
//        log2.setModifyContent("修改内容2");
//        log2.setModifyUser("用户2");
//        log2.setModifyTime(LocalDateTime.now());
//
//        List<TaskModifyLog> records = new ArrayList<>();
//        records.add(log1);
//        records.add(log2);
//
//        Page<TaskModifyLog> page = new Page<>();
//        page.setRecords(records);
//        page.setTotal(2L);
//
//        when(mockTaskModifyRepository.queryTaskHistoryPageList(taskHistoryReq)).thenReturn(page);
//
//        // 执行测试
//        PageResult<TaskHistoryPageListVo> result = smsAutoTaskService.getTaskHistoryPageList(taskHistoryReq);
//
//        // 验证结果
//        assertEquals(2, result.getTotal().longValue());
//        assertEquals(2, result.getList().size());
//
//        TaskHistoryPageListVo vo1 = result.getList().get(0);
//        assertEquals("testTaskCode", vo1.getTaskCode());
//        assertEquals("修改内容1", vo1.getModifyContent());
//        assertEquals("用户1", vo1.getModifyUser());
//
//        TaskHistoryPageListVo vo2 = result.getList().get(1);
//        assertEquals("testTaskCode", vo2.getTaskCode());
//        assertEquals("修改内容2", vo2.getModifyContent());
//        assertEquals("用户2", vo2.getModifyUser());
//    }

    /**
     * 测试用例2：queryTaskHistoryPageList 返回空记录
     */
    @Test
    public void testGetTaskHistoryPageList_EmptyResult() {
        // 准备测试数据
        TaskHistoryReq taskHistoryReq = new TaskHistoryReq();
        taskHistoryReq.setTaskCode("testTaskCode");

        // Mock queryTaskHistoryPageList 返回空记录
        Page<TaskModifyLog> emptyPage = new Page<>();
        emptyPage.setTotal(0L);
        emptyPage.setRecords(Collections.emptyList());

        when(mockTaskModifyRepository.queryTaskHistoryPageList(taskHistoryReq)).thenReturn(emptyPage);

        // 执行测试
        PageResult<TaskHistoryPageListVo> result = smsAutoTaskService.getTaskHistoryPageList(taskHistoryReq);

        // 验证结果
        assertEquals(0, result.getTotal().longValue());
        assertEquals(0, result.getList().size());
    }

    /**
     * 测试用例3：queryTaskHistoryPageList 返回的记录中某些字段为空
     */
//    @Test
//    public void testGetTaskHistoryPageList_NullFields() {
//        // 准备测试数据
//        TaskHistoryReq taskHistoryReq = new TaskHistoryReq();
//        taskHistoryReq.setTaskCode("testTaskCode");
//
//        // Mock queryTaskHistoryPageList 返回记录中某些字段为空
//        TaskModifyLog log = new TaskModifyLog();
//        log.setTaskCode("testTaskCode");
//        log.setModifyContent(null); // 空字段
//        log.setModifyUser(null); // 空字段
//        log.setModifyTime(null); // 空字段
//
//        List<TaskModifyLog> records = Collections.singletonList(log);
//
//        Page<TaskModifyLog> page = new Page<>();
//        page.setRecords(records);
//        page.setTotal(1L);
//
//        when(mockTaskModifyRepository.queryTaskHistoryPageList(taskHistoryReq)).thenReturn(page);
//
//        // 执行测试
//        PageResult<TaskHistoryPageListVo> result = smsAutoTaskService.getTaskHistoryPageList(taskHistoryReq);
//
//        // 验证结果
//        assertEquals(1, result.getTotal().longValue());
//        assertEquals(1, result.getList().size());
//
//        TaskHistoryPageListVo vo = result.getList().get(0);
//        assertEquals("testTaskCode", vo.getTaskCode());
//    }

    /**
     * 测试用例4：queryTaskHistoryPageList 抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testGetTaskHistoryPageList_ExceptionCase() {
        // 准备测试数据
        TaskHistoryReq taskHistoryReq = new TaskHistoryReq();
        taskHistoryReq.setTaskCode("testTaskCode");

        // Mock queryTaskHistoryPageList 抛出异常
        when(mockTaskModifyRepository.queryTaskHistoryPageList(taskHistoryReq)).thenThrow(new RuntimeException("数据库查询失败"));

        // 执行测试
        smsAutoTaskService.getTaskHistoryPageList(taskHistoryReq);
    }

    /**
     * 测试用例2：taskModifyReq 非空，但字段部分为空
     */
//    @Test
//    public void testAddTaskModifyTimeLog_TaskModifyReqFieldsAreNull() {
//        try (MockedStatic<OperatorUtil> mockedOperatorUtil = Mockito.mockStatic(OperatorUtil.class)) {
//            // Mock OperatorUtil.getOperator 返回值
//            mockedOperatorUtil.when(OperatorUtil::getOperator).thenReturn("test-operator");
//
//            // 准备测试数据
//            AutoTaskModifyTimeReq taskModifyReq = new AutoTaskModifyTimeReq();
//            taskModifyReq.setTaskCode(null); // taskCode 为空
//
//            // 执行测试
//            smsAutoTaskService.addTaskModifyTimeLog(taskModifyReq, 2);
//
//            // 验证 insertTaskModifyLog 被调用
//            ArgumentCaptor<TaskModifyLog> captor = ArgumentCaptor.forClass(TaskModifyLog.class);
//            verify(mockTaskModifyRepository, times(1)).insertTaskModifyLog(captor.capture());
//
//            // 验证日志对象字段
//            TaskModifyLog capturedLog = captor.getValue();
//            assertNull(capturedLog.getTaskCode());
//            assertNotNull(capturedLog.getModifyUser());
//            assertEquals("test-operator", capturedLog.getModifyUser());
//            assertNotNull(capturedLog.getModifyTime());
//        }
//    }

    /**
     * 测试用例3：taskModifyReq 非空且字段完整
     */
//    @Test
//    public void testAddTaskModifyTimeLog_TaskModifyReqIsComplete() {
//        try (MockedStatic<OperatorUtil> mockedOperatorUtil = Mockito.mockStatic(OperatorUtil.class)) {
//            // Mock OperatorUtil.getOperator 返回值
//            mockedOperatorUtil.when(OperatorUtil::getOperator).thenReturn("test-operator");
//
//            // 准备测试数据
//            AutoTaskModifyTimeReq taskModifyReq = new AutoTaskModifyTimeReq();
//            taskModifyReq.setTaskCode("test-task-code");
//            taskModifyReq.setDailySendTime("12:00");
//            taskModifyReq.setRangeBeginDate("2023-01-01");
//            taskModifyReq.setRangeEndDate("2023-12-31");
//
//            // 执行测试
//            smsAutoTaskService.addTaskModifyTimeLog(taskModifyReq, 3);
//
//            // 验证 insertTaskModifyLog 被调用
//            ArgumentCaptor<TaskModifyLog> captor = ArgumentCaptor.forClass(TaskModifyLog.class);
//            verify(mockTaskModifyRepository, times(1)).insertTaskModifyLog(captor.capture());
//
//            // 验证日志对象字段
//            TaskModifyLog capturedLog = captor.getValue();
//            assertEquals("test-task-code", capturedLog.getTaskCode());
//            assertNotNull(capturedLog.getModifyUser());
//            assertEquals("test-operator", capturedLog.getModifyUser());
//            assertNotNull(capturedLog.getModifyTime());
//        }
//    }


    /**
     * 测试用例1：taskModifyTimeReq 为 null
     */
    @Test
    public void testCheckModifySendTimeArg_NullRequest() {
        // 执行测试
        CommonResult<Boolean> result = smsAutoTaskService.checkModifySendTimeArg(null);

        // 验证结果
        assertEquals(ErrorCodeConstants.TASK_REQ_NULL.getCode(), result.getCode());
        assertEquals(false, result.isSuccess());
    }

    /**
     * 测试用例2：taskCode 为空
     */
    @Test
    public void testCheckModifySendTimeArg_EmptyTaskCode() {
        // 准备测试数据
        AutoTaskModifyTimeReq request = new AutoTaskModifyTimeReq();
        request.setTaskCode(""); // 设置为空字符串
        request.setDailySendTime("12:00");

        // 执行测试
        CommonResult<Boolean> result = smsAutoTaskService.checkModifySendTimeArg(request);

        // 验证结果
        assertEquals(ErrorCodeConstants.TASK_CODE_NULL.getCode(), result.getCode());
        assertEquals(false, result.isSuccess());
    }

    /**
     * 测试用例3：dailySendTime 为空
     */
    @Test
    public void testCheckModifySendTimeArg_EmptyDailySendTime() {
        // 准备测试数据
        AutoTaskModifyTimeReq request = new AutoTaskModifyTimeReq();
        request.setTaskCode("testTaskCode");
        request.setDailySendTime(""); // 设置为空字符串

        // 执行测试
        CommonResult<Boolean> result = smsAutoTaskService.checkModifySendTimeArg(request);

        // 验证结果
        assertEquals(ErrorCodeConstants.TASK_SEND_TIME_NULL.getCode(), result.getCode());
        assertEquals(false, result.isSuccess());
    }

    /**
     * 测试用例4：dailySendTime 等于 "00:00"
     */
    @Test
    public void testCheckModifySendTimeArg_DailySendTimeZero() {
        // 准备测试数据
        AutoTaskModifyTimeReq request = new AutoTaskModifyTimeReq();
        request.setTaskCode("testTaskCode");
        request.setDailySendTime("00:00");

        // 执行测试
        CommonResult<Boolean> result = smsAutoTaskService.checkModifySendTimeArg(request);

        // 验证结果
        assertEquals(ErrorCodeConstants.TASK_SEND_TIME_ERROR.getCode(), result.getCode());
        assertEquals(false, result.isSuccess());
    }

    /**
     * 测试用例5：dailySendTime 不包含 ":"
     */
    @Test
    public void testCheckModifySendTimeArg_InvalidFormat_NoColon() {
        // 准备测试数据
        AutoTaskModifyTimeReq request = new AutoTaskModifyTimeReq();
        request.setTaskCode("testTaskCode");
        request.setDailySendTime("1200"); // 不包含 ":"

        // 执行测试
        CommonResult<Boolean> result = smsAutoTaskService.checkModifySendTimeArg(request);

        // 验证结果
        assertEquals(ErrorCodeConstants.TASK_SEND_TIME_FORMAT_ERROR.getCode(), result.getCode());
        assertEquals(false, result.isSuccess());
    }

    /**
     * 测试用例6：dailySendTime 按 ":" 分割后长度不为 2
     */
    @Test
    public void testCheckModifySendTimeArg_InvalidFormat_WrongSplitLength() {
        // 准备测试数据
        AutoTaskModifyTimeReq request = new AutoTaskModifyTimeReq();
        request.setTaskCode("testTaskCode");
        request.setDailySendTime("12:00:00"); // 分割后长度为 3

        // 执行测试
        CommonResult<Boolean> result = smsAutoTaskService.checkModifySendTimeArg(request);

        // 验证结果
        assertEquals(ErrorCodeConstants.TASK_SEND_TIME_FORMAT_ERROR.getCode(), result.getCode());
        assertEquals(false, result.isSuccess());
    }

    /**
     * 测试用例7：所有参数均合法
     */
    @Test
    public void testCheckModifySendTimeArg_ValidRequest() {
        // 准备测试数据
        AutoTaskModifyTimeReq request = new AutoTaskModifyTimeReq();
        request.setTaskCode("testTaskCode");
        request.setDailySendTime("12:00");

        // 执行测试
        CommonResult<Boolean> result = smsAutoTaskService.checkModifySendTimeArg(request);

        // 验证结果
        assertEquals(true, result.isSuccess());
        assertEquals(0, result.getCode().intValue());
        assertEquals(true, result.getData());
    }

}
