package com.jlr.ecp.notification.dal.dataobject.mp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 小程序模板服务到期通知订阅DO
 *
 */
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("t_mp_notice_expire_subscription")
public class MpNoticeExpireSubscriptionDO extends BaseDO {

    /**
     * 自增主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户全局唯一ID
     */
    @TableField("jlr_id")
    private String jlrId;

    /**
     * 小程序的openId
     */
    @TableField("open_id")
    private String openId;

    /**
     * 服务到期的consent
     */
    @TableField("service_expiration_consent")
    private Integer serviceExpirationConsent;

    /**
     * 租户号
     */
    @TableField("tenant_id")
    private Integer tenantId;
}
