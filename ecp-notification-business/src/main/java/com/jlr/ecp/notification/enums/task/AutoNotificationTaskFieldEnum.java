package com.jlr.ecp.notification.enums.task;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

@AllArgsConstructor
@Getter
public enum AutoNotificationTaskFieldEnum {

    // 编辑任务概览模块
    EDIT_SCHEDULE(0, "通知发送时间", "编辑任务概览", true),

    // 编辑通知状态模块
    EDIT_STATUS(1, "通知状态", "编辑通知状态", true);

    private final Integer code;
    private final String fieldName;     // 字段名（用于日志、展示）
    private final String moduleName;    // 修改模块
    private final Boolean showDetail;   // 是否显示修改详情

    /**
     * 根据 code 获取对应的枚举实例
     */
    public static AutoNotificationTaskFieldEnum fromCode(Integer code) {
        for (AutoNotificationTaskFieldEnum field : values()) {
            if (field.getCode().equals(code)) {
                return field;
            }
        }
        return null;
    }

    /**
     * 获取所有需要显示修改详情的字段名集合
     */
    public static Set<String> getAllDetailFields() {
        return Arrays.stream(values())
                .filter(e -> e.showDetail)
                .map(e -> e.fieldName)
                .collect(Collectors.toSet());
    }
}