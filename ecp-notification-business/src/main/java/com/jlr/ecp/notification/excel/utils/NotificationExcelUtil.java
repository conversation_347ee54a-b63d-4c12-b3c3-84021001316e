package com.jlr.ecp.notification.excel.utils;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.jlr.ecp.notification.excel.pojo.PhoneErrorDetail;
import com.jlr.ecp.notification.excel.pojo.VinAndPhoneErrorDetail;
import com.jlr.ecp.notification.excel.pojo.VinAndPhoneNumber;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.List;

@Slf4j
public class NotificationExcelUtil {

    private static final String EXCEL_FORMATTER = ".xlsx";

    /**
     * 批量写入电话号码错误详情数据到Excel文件。
     *
     * @param phoneErrorDetails 电话号码错误详情数据列表，不应为空。
     *                          列表中的每个元素都是一个包含电话错误详情的PhoneErrorDetail对象。
     * @return File
     */
    public static File batchWritePhoneError(List<List<PhoneErrorDetail>> phoneErrorDetails) {
        if (CollectionUtils.isEmpty(phoneErrorDetails)) {
            return null;
        }
        String fileName = "PhoneErrorDetail" + System.currentTimeMillis();
        File tempFile = FileUtil.touch("temp" + File.separator + fileName + EXCEL_FORMATTER);
        try {
            long startTime = System.currentTimeMillis();
            try (ExcelWriter excelWriter = EasyExcel.write(tempFile, PhoneErrorDetail.class).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet("PhoneErrorDetail").build();
                for (List<PhoneErrorDetail> data : phoneErrorDetails) {
                    excelWriter.write(data, writeSheet);
                }
            }
            long endTime = System.currentTimeMillis();
            log.info("写入手机号错误详情，花费时间:{}毫秒, tempFile:{}", (endTime-startTime), tempFile);
            return tempFile;
        } catch (Exception e) {
            log.error("批量写入电话号码错误详情数据异常:", e);
        }
        return null;
    }

    /**
     * 批量写入vin+电话号码错误详情数据到Excel文件。
     *
     * @param  vinAndPhoneErrorDetails，不应为空。
     *                          列表中的每个元素都是一个包含电话错误详情的VinAndPhoneErrorDetail对象。
     * @return File
     */

    public static File batchWriteVinAadPhoneError(List<List<VinAndPhoneErrorDetail>> vinAndPhoneErrorDetails) {
        if (CollectionUtils.isEmpty(vinAndPhoneErrorDetails)) {
            return null;
        }
        String fileName = "VinAndPhoneErrorDetail" + System.currentTimeMillis();
        File tempFile = FileUtil.touch("temp" + File.separator + fileName + EXCEL_FORMATTER);
        try {
            long startTime = System.currentTimeMillis();
            try (ExcelWriter excelWriter = EasyExcel.write(tempFile, VinAndPhoneErrorDetail.class).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet("VinAndPhoneErrorDetail").build();
                for (List<VinAndPhoneErrorDetail> data : vinAndPhoneErrorDetails) {
                    excelWriter.write(data, writeSheet);
                }
            }
            long endTime = System.currentTimeMillis();
            log.info("vin+phone错误详情写入数据，花费时间:{}毫秒, tempFile:{}", (endTime-startTime), tempFile);
            return tempFile;
        } catch (Exception e) {
            log.error("vin+phone批量写入错误详情数据异常:", e);
        }
        return null;
    }

    /**
     * 批量写入车辆识别码（VIN）和电话号码的正确信息到Excel文件中。
     *
     * @param vinAndPhoneDetails 包含VIN和电话号码详细信息的列表，每个列表项由VinAndPhoneNumber组成。
     *                           这是一个二维列表，外层列表表示不同的Excel页签，内层列表表示同一页签中的行数据。
     * @return 返回生成的临时Excel文件，如果处理失败返回null。
     */
    public static File batchWriteVinAadPhoneCorrect(List<List<VinAndPhoneNumber>> vinAndPhoneDetails) {
        if (CollectionUtils.isEmpty(vinAndPhoneDetails)) {
            return null;
        }
        String fileName = "VinAndPhoneCorrectDetail" + System.currentTimeMillis();
        File tempFile = FileUtil.touch("temp" + File.separator + fileName + EXCEL_FORMATTER);
        try {
            long startTime = System.currentTimeMillis();
            try (ExcelWriter excelWriter = EasyExcel.write(tempFile, VinAndPhoneNumber.class).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet("VinAndPhoneCorrectDetail").build();
                for (List<VinAndPhoneNumber> data : vinAndPhoneDetails) {
                    excelWriter.write(data, writeSheet);
                }
            }
            long endTime = System.currentTimeMillis();
            log.info("vin+phone批量写入正确数据，花费时间:{}毫秒, tempFile:{}", (endTime-startTime), tempFile);
            return tempFile;
        } catch (Exception e) {
            log.error("vin+phone批量写入正常数据异常:", e);
        }
        return null;
    }
}
