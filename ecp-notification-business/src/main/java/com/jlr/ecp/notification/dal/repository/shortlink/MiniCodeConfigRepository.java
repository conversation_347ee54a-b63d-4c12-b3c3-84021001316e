package com.jlr.ecp.notification.dal.repository.shortlink;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.notification.dal.dataobject.shortlink.MiniCodeConfigDO;
import com.jlr.ecp.notification.dal.dataobject.shortlink.ShortLinkCreateRecordDO;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkCreateListDTO;

public interface MiniCodeConfigRepository extends IService<MiniCodeConfigDO> {

    /**
     * 分页查询小程序配置列表，支持按创建时间排序。
     *
     * @param listDTO 分页查询条件参数，包含分页信息和排序方式
     * @return 分页结果对象，包含符合查询条件的小程序配置数据
     */
    Page<MiniCodeConfigDO> pageList(ShortLinkCreateListDTO listDTO);

    /**
     * 根据配置ID查询小程序配置信息
     *
     * @param configId 配置ID（字符串格式的数字）
     * @return 返回匹配的MiniCodeConfigDO对象，若未找到或参数无效则返回null
     */
    MiniCodeConfigDO selectByConfigId(String  configId);
}
