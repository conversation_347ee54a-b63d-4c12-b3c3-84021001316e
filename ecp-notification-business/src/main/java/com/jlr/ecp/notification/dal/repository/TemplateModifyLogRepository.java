package com.jlr.ecp.notification.dal.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.notification.dal.dataobject.template.TemplateModifyLog;
import com.jlr.ecp.notification.req.template.TemplateHistoryPageReq;

public interface TemplateModifyLogRepository extends IService<TemplateModifyLog> {

    /**
     *  分页查询模板编辑历史记录
     *
     * @param historyPageReq 模板编辑历史记录入参
     * @return Page<MessageTemplate>
     * */
    Page<TemplateModifyLog> queryTemplateModifyLogPage(TemplateHistoryPageReq historyPageReq);

    /**
     *  插入模板修改记录
     *
     * @param log 模板修改记录
     * */
    void insert(TemplateModifyLog log);
}
