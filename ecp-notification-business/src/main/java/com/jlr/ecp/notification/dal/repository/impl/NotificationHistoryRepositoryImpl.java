package com.jlr.ecp.notification.dal.repository.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.notification.dal.dataobject.history.NotificationHistory;
import com.jlr.ecp.notification.dal.mysql.history.SendHistoryMapper;
import com.jlr.ecp.notification.dal.repository.NotificationHistoryRepository;
import com.jlr.ecp.notification.enums.SortEnum;
import com.jlr.ecp.notification.req.history.SendLogPageReq;
import com.jlr.ecp.notification.util.sms.TimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class NotificationHistoryRepositoryImpl implements NotificationHistoryRepository {

    @Resource
    private SendHistoryMapper historyMapper;

    /**
     * 插入通知历史记录。
     *
     * @param notificationHistory 通知历史对象，包含需要插入的历史信息。
     * @return 返回一个布尔值，成功插入返回true，失败返回false。
     */
    @Override
    public Boolean insertHistory(NotificationHistory notificationHistory) {
        try {
            historyMapper.insert(notificationHistory);
        } catch (Exception e) {
            log.error("添加history异常:", e);
            return false;
        }
        return true;
    }


    /**
     * 根据实例代码更新通知历史记录。
     *
     * @param notificationHistory 通知历史记录对象，包含需要更新的数据。
     * @return 返回一个布尔值，如果更新成功则返回true，如果遇到异常则返回false。
     */
    @Override
    public Boolean updateByInstanceCode(NotificationHistory notificationHistory) {
        try {
            historyMapper.updateByInstanceCode(notificationHistory);
        } catch (Exception e) {
            log.error("更新history异常，notificationHistory:{}，原因：", notificationHistory, e);
            return false;
        }
        return true;
    }


    /**
     * 根据实例代码列表查询通知历史记录。
     *
     * @param instanceCodeList 实例代码列表，用于查询通知历史的条件之一。
     * @return 返回一个通知历史记录的列表。如果查询过程中出现异常或者输入的实例代码列表为空，则返回一个空的列表。
     */
    @Override
    public List<NotificationHistory> queryHistoryByInstanceCodeList(List<String> instanceCodeList) {
        if (CollectionUtils.isEmpty(instanceCodeList)) {
            log.info("按照instanceCode批量查询, instanceCodeList为空");
            return new ArrayList<>();
        }
        LambdaQueryWrapper<NotificationHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(NotificationHistory::getTaskInstanceCode, instanceCodeList)
                .eq(NotificationHistory::getIsDeleted,false);
        try {
            return historyMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.error("按照instanceCode批量查询, 异常:", e);
        }
        return new ArrayList<>();
    }


    /**
     * 批量根据ID更新通知历史记录。
     *
     * @param historyList 通知历史记录列表，不应为空。
     * @return 返回操作结果，如果操作成功返回true，否则返回false。
     */
    @Override
    public Boolean batchUpdateByIds(List<NotificationHistory> historyList) {
        if (CollectionUtils.isEmpty(historyList)) {
            log.info("批量更新history，historyList为空");
            return true;
        }
        try {
            historyMapper.updateBatch(historyList);
        } catch (Exception e) {
            log.error("批量更新history异常，historyList:{}，原因：", historyList, e);
            return false;
        }
        return true;
    }

    /**
     * 根据ID更新通知历史记录
     *
     * @param history 要更新的通知历史对象，包含要更新的字段和值
     * @return 返回一个布尔值，表示是否成功更新了数据库中的记录
     */
    @Override
    public Boolean updateById(NotificationHistory history) {
        return historyMapper.updateById(history) > 0;
    }

    /**
     * 更新通知历史记录中的短链打开次数。
     *
     * @param instanceCode 任务实例编码，不能为空。
     * @param openShortLinkCount 新的短链打开次数。
     * @return 如果更新成功返回true，否则返回false。
     */
    @Override
    public Boolean updateHistoryOpenShortLinkCount(String instanceCode, Integer openShortLinkCount) {
        if (StringUtils.isBlank(instanceCode)) {
            return false;
        }
        NotificationHistory notificationHistory = new NotificationHistory();
        notificationHistory.setUpdatedTime(LocalDateTime.now());
        notificationHistory.setOpenShortLinkCount(openShortLinkCount);
        LambdaUpdateWrapper<NotificationHistory> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(NotificationHistory::getTaskInstanceCode, instanceCode)
                .eq(NotificationHistory::getIsDeleted,false);
        try {
            return historyMapper.update(notificationHistory, updateWrapper) > 0;
        } catch (Exception e) {
            log.error("更新通知历史记录中的短链打开次数，异常：", e);
        }
        return false;
    }

    /**
     * "根据任务实例代码查询通知历史记录"
     *
     * @param taskInstanceCode 任务实例代码，用于标识特定的任务实例
     * @return 如果找到对应的通知历史记录则返回该记录，否则返回null
     */
    @Override
    public NotificationHistory queryHistoryByInstanceCode(String taskInstanceCode) {
        if (Objects.isNull(taskInstanceCode)) {
            return null;
        }
        LambdaQueryWrapper<NotificationHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NotificationHistory::getTaskInstanceCode, taskInstanceCode)
                .eq(NotificationHistory::getIsDeleted, false);
        List<NotificationHistory> resp = historyMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(resp)) {
            log.info("根据任务实例代码查询通知历史记录为空，taskInstanceCode:{}", taskInstanceCode);
            return null;
        }
        log.info("根据任务实例代码查询通知历史记录的数量：{}, taskInstanceCode:{}", resp.size(), taskInstanceCode);
        return resp.get(0);
    }

    /**
     * 根据任务实例代码列表和发送日志分页请求
     *
     * @param sendLogPageReq 发送日志分页请求对象，包含分页查询所需的页码和每页大小等信息
     * @return 返回一个列表，其中包含查询到的通知历史记录
     */
    @Override
    public Page<NotificationHistory> selectPageByTaskInstanceCode(List<String> taskCodeList, SendLogPageReq sendLogPageReq) {
        log.info("根据任务实例代码列表和发送日志分页请求, taskCodeList的数量:{}, sendLogPageReq:{}", taskCodeList.size(), sendLogPageReq);
        Page<NotificationHistory> pageReq = new Page<>(sendLogPageReq.getPageNo(), sendLogPageReq.getPageSize());
        LambdaQueryWrapper<NotificationHistory> queryWrapper = buildHistoryQueryWrapperByInstanceCode(taskCodeList, sendLogPageReq);
        try {
            Page<NotificationHistory> pageResult = historyMapper.selectPage(pageReq, queryWrapper);
            if (Objects.isNull(pageResult) || CollUtil.isEmpty(pageResult.getRecords())) {
                return new Page<>();
            }
            return pageResult;
        } catch (Exception e) {
            log.info("根据任务实例代码列表和发送日志分页请求异常：{}", e.getMessage());
        }
        return new Page<>();
    }

    /**
     * 构建发送历史查询条件
     *
     * @param taskCodeList 任务代码列表，用于筛选查询结果
     * @param sendLogPageReq 发送日志分页请求对象，包含分页信息和筛选条件
     * @return 返回一个LambdaQueryWrapper对象，用于执行NotificationHistory表的查询
     */
    private LambdaQueryWrapper<NotificationHistory> buildHistoryQueryWrapperByInstanceCode(List<String> taskCodeList,
                                                                                           SendLogPageReq sendLogPageReq) {
        log.info("构建发送历史查询条件, taskCodeList的数量：{}, sendLogPageReq:{}", taskCodeList.size(), sendLogPageReq);
        LocalDateTime startTime = TimeFormatUtil.stringToTimeByFormat(sendLogPageReq.getStartTime(), TimeFormatUtil.formatter_1);
        LocalDateTime endTime = TimeFormatUtil.stringToTimeByFormat(sendLogPageReq.getEndTime(), TimeFormatUtil.formatter_1);
        LambdaQueryWrapper<NotificationHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollUtil.isNotEmpty(taskCodeList), NotificationHistory::getTaskCode, taskCodeList)
                .in(CollUtil.isNotEmpty(sendLogPageReq.getInstanceCodeList()), NotificationHistory::getTaskInstanceCode,
                        sendLogPageReq.getInstanceCodeList())
                .ge(StringUtils.isNotBlank(sendLogPageReq.getStartTime()), NotificationHistory::getTaskSendTime, startTime)
                .le(StringUtils.isNotBlank(sendLogPageReq.getEndTime()), NotificationHistory::getTaskSendTime, endTime)
                .eq(NotificationHistory::getIsDeleted, false);
        if (SortEnum.ASC.getSortType().equals(sendLogPageReq.getTimeSortType())) {
            queryWrapper.orderByAsc(NotificationHistory::getTaskSendTime);
        } else {
            queryWrapper.orderByDesc(NotificationHistory::getTaskSendTime);
        }
        return queryWrapper;
    }
}
