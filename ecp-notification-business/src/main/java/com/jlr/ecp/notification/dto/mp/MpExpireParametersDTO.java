package com.jlr.ecp.notification.dto.mp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Schema(description = "小程序-到期提醒参数")
public class MpExpireParametersDTO {
    @JsonProperty("serviceName")
    private String serviceName;

    @JsonProperty("deviceName")
    private String deviceName;

    @JsonProperty("expirationDate")
    private String expirationDate;

    @JsonProperty("reminder")
    private String reminder;
}
