package com.jlr.ecp.notification.api;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jlr.ecp.framework.common.exception.ErrorCode;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.forgeRock.core.dto.AccessTokenResponse;
import com.jlr.ecp.framework.forgeRock.core.util.AccessTokenUtil;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.notification.api.dto.*;
import com.jlr.ecp.notification.api.notification.NotificationServiceApi;
import com.jlr.ecp.notification.api.vo.ShortLinkConfigVO;
import com.jlr.ecp.notification.config.RedisService;
import com.jlr.ecp.notification.constant.Constants;
import com.jlr.ecp.notification.dal.dataobject.history.SendHistoryDetail;
import com.jlr.ecp.notification.dal.dataobject.shortlink.NotificationConfigDO;
import com.jlr.ecp.notification.dal.dataobject.task.NotificationTask;
import com.jlr.ecp.notification.dal.dataobject.task.auto.AutoTaskConditionDO;
import com.jlr.ecp.notification.dal.dataobject.task.auto.AutoTaskTriggerMapDO;
import com.jlr.ecp.notification.dal.dataobject.task.auto.NotificationAutoTaskDO;
import com.jlr.ecp.notification.dal.dataobject.template.MessageTemplate;
import com.jlr.ecp.notification.dal.repository.*;
import com.jlr.ecp.notification.dto.HistoryDetailIdempotentDTO;
import com.jlr.ecp.notification.dto.UpdateSmsSendStatusDTO;
import com.jlr.ecp.notification.enums.*;
import com.jlr.ecp.notification.enums.sms.ShortLinkPathEnum;
import com.jlr.ecp.notification.enums.task.*;
import com.jlr.ecp.notification.kafka.message.ExpireMessage;
import com.jlr.ecp.notification.kafka.send.SendSmsResp;
import com.jlr.ecp.notification.kafka.send.SingleSendMessage;
import com.jlr.ecp.notification.kafka.send.SmsSendStatistics;
import com.jlr.ecp.notification.kafka.send.template.MessageContentUtil;
import com.jlr.ecp.notification.resp.ReportResp;
import com.jlr.ecp.notification.resp.UpdateSmsSendStatusResp;
import com.jlr.ecp.notification.service.ShortLinkService;
import com.jlr.ecp.notification.service.UpdateSmsUserReachService;
import com.jlr.ecp.notification.service.manual.ManualTaskSendService;
import com.jlr.ecp.notification.util.HttpClientUtil;
import com.jlr.ecp.notification.util.HttpPostUtil;
import com.jlr.ecp.notification.util.InstanceCodeGenerator;
import com.jlr.ecp.notification.util.sms.TemplateUtil;
import com.jlr.ecp.notification.util.sms.TimeFormatUtil;
import com.jlr.ecp.subscription.api.icrvehicle.vo.SeriesMappingVO;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.redisson.Redisson;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@Validated
@Slf4j
public class NotificationServiceApiImpl implements NotificationServiceApi {
    @Resource
    private MessageTaskRepository taskRepository;

    @Resource
    private MessageTemplateRepository templateRepository;

    @Resource
    private SendHistoryDetailRepository detailRepository;

    @Resource
    private SmsSendStatistics smsSendStatistics;

    @Resource
    private AccessTokenUtil accessTokenUtil;

    @Resource
    ShortLinkService shortLinkService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource(name = "asyncThreadPool")
    private ThreadPoolTaskExecutor asyncThreadPool;

    @Resource
    private ManualTaskSendService manualTaskSendService;

    @Resource
    private UpdateSmsUserReachService reachService;

    @Resource(name = "scheduledThreadPool")
    private ThreadPoolTaskScheduler taskScheduler;

    @Resource
    private InstanceCodeGenerator instanceCodeGenerator;

    @Resource
    private MsgBlackListDORepository msgBlackListDORepository;

    @Resource
    private ShortLinkConfigRepository shortLinkConfigRepository;

    @Resource
    private NotificationAutoTaskRepository autoTaskRepository;

    @Resource
    private AutoTaskTriggerMapRepository triggerMapRepository;

    @Resource
    private AutoTaskConditionRepository conditionRepository;

    @Resource
    private Redisson redisson;

    @Resource
    private RedisService redisService;


    private static final String BLANK = " ";


    private static final int SOCKET_TIMEOUT = 60000; // 设置连接和读取超时时间为60秒
    private static final int CONNECT_TIMEOUT = 60000;

    @Value("${send.smsUrl}")
    private String smsBaseUrl;

    @Value("${send.apiKey:NZyNtPIWWCanL2oKTFGizjnIZaJSNTK8MhQLQUD7}")
    private String apiKey;

    @Value("${send.apiSecret:Iunxd1xDDepOTT3VaMHRxeBEZZYNenPtUQod-5Fg}")
    private String apiSecret;

    @Value("${send.path:/business/sms/send/v1.0.0}")
    private String path;


    private static final Set<String> LAND_ROVER_CODE = new HashSet<>();

    private static final Set<String> JAGUAR_CODE = new HashSet<>();

    static {
        //路虎：brand_code
        LAND_ROVER_CODE.add("LAN");
        LAND_ROVER_CODE.add("LR");

        //捷豹：brand_code
        JAGUAR_CODE.add("JAG");
    }

    /**
     *  路虎电商通知-账号
     * */
    @Value("${sms.notification.account}")
    private String smsNotificationAccount;

    /**
     *  路虎电商通知-密码
     * */
    @Value("${sms.notification.password}")
    private String smsNotificationPassword;

    /**
     *  路虎电商营销-账号
     * */
    @Value("${sms.market.account}")
    private String smsMarketAccount;

    /**
     * 路虎电商营销-密码
     * */
    @Value("${sms.market.password}")
    private String smsMarketPassword;

    private static final String LAND_ROVER_SIGN = "【路虎中国】";

    private static final String JAGUAR_SIGN = "【捷豹路虎中国】";

    private static final String SERVICE = "激活服务";

    private static final String REMOTE_SERVICE = "InControl 远程车控";

    private static final String PIVI_SERVICE = "InControl 在线服务";

    private static final String NULL = "#null#";

    @Value("${sms.response.updateFlag}")
    private Boolean updateSmsResponseFlag;

    /**
     * 保存服务到期通知短信
     * @param phoneByCarVinDTOList 需要保存的数据
     * */
    @Override
    public CommonResult<Integer> saveWaitedSendMsg(List<PhoneByCarVinDTO> phoneByCarVinDTOList) {
        log.info("保存待发送服务到期数据，phoneByCarVinDTOList待保存的数量:{}", phoneByCarVinDTOList.size());
        if (CollectionUtils.isEmpty(phoneByCarVinDTOList)) {
            log.info("服务到期需要保存到短信详情的数据为空");
            return CommonResult.success(0);
        }
        if (Objects.nonNull(phoneByCarVinDTOList.get(0).getAppointedDate())) {
            phoneByCarVinDTOList = filterPhoneByCarVinDTOList(phoneByCarVinDTOList);
        }
        List<SendHistoryDetail> sendHistoryDetails = buildSendHistoryDetailList(phoneByCarVinDTOList);
        log.info("保存待发送服务到期数据，过滤后保存sendHistoryDetails的数量:{}", sendHistoryDetails.size());
        detailRepository.batchInsertDetail(sendHistoryDetails);
        // 添加数据成功后，数据发生变更，需要删除缓存后，下次重新获取，保证数据的一致性。每页操作成功后，都会来删除缓存导致的频率过高
        // 通过适当降低addSendHistoryDetailByCache中RMap的缓存时间，减少删除缓存的频率和添加数据的频率
        return CommonResult.success(sendHistoryDetails.size());
    }


    /**
     * 过滤车辆VIN号对应的电话号码列表
     *
     * @param phoneByCarVinDTOList 电话号码与车辆VIN号关联的信息列表
     * @return 过滤后的电话号码列表，仅包含未处理过的电话号码
     */
    public List<PhoneByCarVinDTO> filterPhoneByCarVinDTOList(List<PhoneByCarVinDTO> phoneByCarVinDTOList) {
        List<PhoneByCarVinDTO> resp = new ArrayList<>();
        if (CollUtil.isEmpty(phoneByCarVinDTOList) || Objects.isNull(phoneByCarVinDTOList.get(0).getAppointedDate())) {
            return resp;
        }
        String taskCode = getTaskCodeByExpireType(phoneByCarVinDTOList.get(0).getExpireType());
        String redissonMapKey = Constants.SAVE_SMS_IDEMPOTENT_KEY + taskCode + ":" + phoneByCarVinDTOList.get(0).getAppointedDate();
        log.info("过滤车辆VIN号对应的电话号码列表, redissonMapKey:{}", redissonMapKey);
        RBucket<Boolean> existsBucket = redisson.getBucket(redissonMapKey);
        if (Boolean.FALSE.equals(existsBucket.isExists())) {
            addSendHistoryDetailByCache(redissonMapKey, phoneByCarVinDTOList.get(0));
        }
        RMap<String, SendHistoryDetail> rMap = redisson.getMap(redissonMapKey);
        for (PhoneByCarVinDTO phoneByCarVinDTO : phoneByCarVinDTOList) {
            String phoneKey = taskCode + ":" + phoneByCarVinDTO.getContactPhone();
            if (rMap.containsKey(phoneKey)) {
                log.info("过滤车辆VIN号对应的电话号码列表, 只按照手机号进行过滤, sendDetail:{}", rMap.get(phoneKey));
                continue;
            }
            String phoneAndCarVinKey = phoneKey + ":" + phoneByCarVinDTO.getCarVin();
            if (rMap.containsKey(phoneAndCarVinKey)) {
                log.info("过滤车辆VIN号对应的电话号码列表, 按照手机号和carVin进行过滤, sendDetail:{}", rMap.get(phoneAndCarVinKey));
                continue;
            }
            resp.add(phoneByCarVinDTO);
        }
        return resp;
    }

    /**
     * 主要为了兼容老数据，即detail中没有carVin的数据
     * 根据指定的RedissonMap键和sms信息，添加发送详情到缓存中
     *
     * @param redissonMapKey Redisson Map的键，用于标识不同的Map集合
     * @param phoneByCarVinDTO 包含车辆VIN码和电话号码的信息对象
     */
    public void addSendHistoryDetailByCache(String redissonMapKey, PhoneByCarVinDTO phoneByCarVinDTO) {
        log.info("添加发送详情到缓存中, redissonMapKey:{}, phoneByCarVinDTO:{}", redissonMapKey, phoneByCarVinDTO);
        HistoryDetailIdempotentDTO detailIdempotentDTO = buildHistoryDetailIdempotentDTO(phoneByCarVinDTO);
        List<SendHistoryDetail> sendHistoryDetailList = detailRepository.queryHistoryDetailByIdempotent(detailIdempotentDTO);
        log.info("添加发送详情到缓存中, detailIdempotentDTO:{}, 已经发送的数据量:{}", detailIdempotentDTO, sendHistoryDetailList.size());
        if (CollUtil.isEmpty(sendHistoryDetailList)) {
            log.info("添加发送详情到缓存中, 未查询到发送详情, detailIdempotentDTO:{}", detailIdempotentDTO);
        }
        RMap<String, SendHistoryDetail> redissonMap = redisson.getMap(redissonMapKey);
        String taskCode = getTaskCodeByExpireType(phoneByCarVinDTO.getExpireType());
        Map<String, SendHistoryDetail> map = new HashMap<>();
        for (SendHistoryDetail sendHistoryDetail : sendHistoryDetailList) {
            String key = taskCode + ":" + sendHistoryDetail.getSendPhone();
            if (StringUtils.isNotBlank(sendHistoryDetail.getCarVin())) {
                key = taskCode + ":" + sendHistoryDetail.getSendPhone() + ":" + sendHistoryDetail.getCarVin();
            }
            map.put(key, sendHistoryDetail);
        }
        redissonMap.putAll(map);
        //过期时间设置5min
        redissonMap.expire(Duration.of(5, ChronoUnit.MINUTES));
        //printRedissonMapContent(redissonMap);
    }

    /**
     * 打印Redisson分布式Map的内容
     *
     * @param redissonMap Redisson分布式Map，它存储了字符串键和对应的SendHistoryDetail值
     */
    private void printRedissonMapContent(RMap<String, SendHistoryDetail> redissonMap) {
        log.info("RedissonMap的内容:");
        for (Map.Entry<String, SendHistoryDetail> entry : redissonMap.entrySet()) {
            log.info("Key: {}, Value: {}", entry.getKey(), entry.getValue());
        }
    }

    /**
     * 根据车辆VIN号和电话信息构建历史详情幂等对象
     *
     * @param phoneByCarVinDTO 车辆VIN号和电话信息的DTO对象，包含车辆和预约相关信息
     * @return 返回构建的历史详情幂等性DTO对象，如果输入参数为空，则返回null
     */
    public HistoryDetailIdempotentDTO buildHistoryDetailIdempotentDTO(PhoneByCarVinDTO phoneByCarVinDTO) {
        if (Objects.isNull(phoneByCarVinDTO)) {
            return null;
        }
        HistoryDetailIdempotentDTO idempotentDTO = new HistoryDetailIdempotentDTO();
        idempotentDTO.setTaskCode(getTaskCodeByExpireType(phoneByCarVinDTO.getExpireType()));
        if (Objects.nonNull(phoneByCarVinDTO.getAppointedDate())) {
            idempotentDTO.setStartTime(LocalDateTime.of(phoneByCarVinDTO.getAppointedDate(), LocalTime.MIN));
            idempotentDTO.setEndTime(LocalDateTime.of(phoneByCarVinDTO.getAppointedDate(), LocalTime.MAX));
        }
        return idempotentDTO;
    }

    /**
     * 根据到期类型获取短信发送历史记录中待过期的短信ID列表
     *
     * @param smsExpireIdsDTO 短信过期ID查询参数对象
     * @return 包含待过期短信ID的列表的CommonResult对象
     */
    @Override
    public CommonResult<List<Long>> getSmsDetailIdsByExpireType(GetSmsExpireIdsDTO smsExpireIdsDTO) {
        log.info("根据到期类型获取短信发送历史记录中待过期的短信ID列表, smsExpireIdsDTO:{}", smsExpireIdsDTO);
        if (!checkGetSmsExpireIdsDTOArgs(smsExpireIdsDTO)) {
            log.info("根据到期类型获取短信发送历史记录中待过期的短信ID列表, 参数错误smsExpireIdsDTO:{}",smsExpireIdsDTO);
            return CommonResult.error(new ErrorCode(1000, "参数错误"));
        }
        String taskCode = getTaskCodeByExpireType(smsExpireIdsDTO.getExpireType());
        LocalDateTime startTime = LocalDate.now().atTime(0, 0, 0);
        LocalDateTime endTime = LocalDate.now().atTime(23, 59, 59);
        if (Objects.nonNull(smsExpireIdsDTO.getStartDate()) && Objects.nonNull(smsExpireIdsDTO.getEndDate())) {
            startTime = smsExpireIdsDTO.getStartDate().atTime(0, 0, 0);
            endTime = smsExpireIdsDTO.getEndDate().atTime(23, 59, 59);
        }
        List<SendHistoryDetail> sendHistoryDetails = detailRepository.querySmsDetailByTaskCodeAndTime(taskCode,
                SmsSendResultEnum.WAITED.getCode(), startTime, endTime);
        List<Long> idList = new ArrayList<>();
        if (CollectionUtils.isEmpty(sendHistoryDetails)) {
            log.info("根据到期类型获取短信发送历史记录中待过期的短信ID列表, 未查询到待发送短信, taskCode:{}, startTime:{}, endTime:{}",
                    taskCode, startTime, endTime);
            return CommonResult.success(idList);
        }
        for (SendHistoryDetail sendHistoryDetail : sendHistoryDetails) {
            idList.add(sendHistoryDetail.getId());
        }
        return CommonResult.success(idList);
    }

    /**
     * 检查获取短信过期ID的参数是否有效
     *
     * @param smsExpireIdsDTO 包含短信过期ID查询参数的数据传输对象
     * @return 如果参数有效，则返回true；否则返回false
     */
    private boolean checkGetSmsExpireIdsDTOArgs(GetSmsExpireIdsDTO smsExpireIdsDTO) {
        if (Objects.isNull(smsExpireIdsDTO)) {
            return false;
        }
        if (Objects.nonNull(smsExpireIdsDTO.getStartDate()) && Objects.nonNull(smsExpireIdsDTO.getEndDate())) {
            return true;
        }
        return Objects.isNull(smsExpireIdsDTO.getStartDate()) && Objects.isNull(smsExpireIdsDTO.getEndDate());
    }

    /**
     * 发送定时消息
     * @param  sendExpireSmsParam id列表
     * @return Integer 保存数量
     * */
    @Override
    public CommonResult<Integer> scheduledSendMsg(SendExpireSmsParam sendExpireSmsParam) {
        log.info("发送定时消息, sendExpireSmsParam:{}", sendExpireSmsParam);
        String taskCode = getTaskCodeByExpireType(sendExpireSmsParam.getExpireType());
        NotificationTask notificationTask = taskRepository.queryTaskByCode(taskCode);
        if (Objects.isNull(notificationTask)) {
            log.error("服务到期通知任务不存在");
            return CommonResult.success(0);
        }
        if (!TaskStatusEnum.START.getStatus().equals(notificationTask.getStatus())) {
            log.warn("服务到期通知任务的状态为禁用, notificationTask:{}", notificationTask);
            return CommonResult.success(0);
        }
        List<SendHistoryDetail> sendHistoryDetails = detailRepository.querySmsDetailByIds(sendExpireSmsParam.getDetaileIdList(),
                SmsSendResultEnum.WAITED.getCode());
        if (CollectionUtils.isEmpty(sendHistoryDetails)) {
            log.info("服务到期通知任务, 要发送的数据为空, sendExpireSmsParam:{}", sendExpireSmsParam);
            return CommonResult.success(0);
        }
        sendMsg(sendHistoryDetails);
        return CommonResult.success(sendHistoryDetails.size());
    }

    /**
     *  获取服务到期短信发送定时任务的时间
     *  @return String
     * */
    @Override
    public CommonResult<String> getScheduledTime(Integer expireType) {
        String taskCode = getTaskCodeByExpireType(expireType);
        NotificationTask notificationTask = taskRepository.queryTaskByCode(taskCode);
        log.info("获取服务到期短信发送定时任务的时间,expireType:{}, taskCode:{}, notificationTask:{}", expireType,
                taskCode, notificationTask);
        if (Objects.isNull(notificationTask)) {
            return CommonResult.success(null);
        }
        return CommonResult.success(taskSendTimeChangeCore(notificationTask.getTaskSendScheduleTime()));
    }

    /**
     * 根据过期类型获取任务代码。
     *
     * @param expireType 过期类型，参考ExpireTypeEnum枚举。
     * @return 对应的过期类型的任务代码，如果找不到匹配的类型则返回null。
     */
    private String getTaskCodeByExpireType(Integer expireType) {
        return ExpireTypeEnum.getTaskCodeByType(expireType);
    }

    /**
     * 下单成功、取消订单成功、服务激活通知超时数据进行重试
     * @param day 天数
     * @return Integer
     * */
    @Override
    public CommonResult<Integer> retryNotification(Integer day) {
        log.info("下单成功、取消订单成功、服务激活通知超时数据进行重试, day:{}", day);
        List<SendHistoryDetail> sendHistoryDetails = detailRepository.queryRetryMsgDetail(day);
        if (CollectionUtils.isEmpty(sendHistoryDetails)) {
            log.info("下单成功、取消订单成功、服务激活通知超时数据进行重试的数据为空");
            return CommonResult.success(0);
        }
        CompletableFuture.runAsync(() -> sendMsg(sendHistoryDetails), asyncThreadPool);
        return CommonResult.success(sendHistoryDetails.size());
    }

    /**
     * 发送手动消息。
     * 该方法用于根据指定的任务代码发送一条手动消息。具体实现逻辑需要根据实际需求进行补充。
     *
     * @param taskCode 任务代码，用于标识需要发送的消息。
     */
    @Override
    public void sendManualMsg(String taskCode) {
        manualTaskSendService.sendTask(taskCode);
    }

    /**
     *  更新短信通知状态
     * */
    @Override
    public void updateSmsNotificationStatus() {
        UpdateSmsSendStatusDTO notificationStatusDTO = UpdateSmsSendStatusDTO.builder()
                .account(smsNotificationAccount)
                .password(smsNotificationPassword)
                .build();
        CompletableFuture.runAsync(() -> updateSmsNotificationStatus(notificationStatusDTO), asyncThreadPool);
    }

    /**
     * 更新短信发送状态的通知。
     *
     * @param notificationStatusDTO 包含需要更新的短信发送状态信息的数据传输对象。
     *                              其中应包含短信的唯一标识符等信息以供系统识别并更新状态。
     */
    private void updateSmsNotificationStatus(UpdateSmsSendStatusDTO notificationStatusDTO) {
        for (int i = 1; ; i++) {
            UpdateSmsSendStatusResp updateSmsSendStatusResp = reachService.updateSmsSendStatus(notificationStatusDTO);
            if (Objects.isNull(updateSmsSendStatusResp)) {
                log.info("更新短信通知状态, 当前第:{}次查找失败，退出查询", i);
                break;
            }
            if (!SmsResultErrorEnum.SUCCESS.getCode().equals(updateSmsSendStatusResp.getResult())) {
                log.info("更新短信通知状态, 当前第:{}次查找失败，退出查询", i);
                break;
            }
            if (CollectionUtils.isEmpty(updateSmsSendStatusResp.getReports())) {
                log.info("更新短信通知状态, 当前第:{}次查询结果为空，退出查询", i);
                break;
            }
            log.info("更新短信通知状态, 当前第:{}次查找, 获取report数量:{}", i, updateSmsSendStatusResp.getReports().size());
            List<ReportResp> reportRespList = updateSmsSendStatusResp.getReports();
            reachService.updateSmsUserReach(reportRespList);
        }
    }

    /**
     *  更新短信营销状态
     * */
    @Override
    public void updateSmsMarketStatus() {
        UpdateSmsSendStatusDTO marketStatusDTO = UpdateSmsSendStatusDTO.builder()
                .account(smsMarketAccount)
                .password(smsMarketPassword)
                .build();
        CompletableFuture.runAsync(() -> updateSmsMarketStatus(marketStatusDTO), asyncThreadPool);
    }

    /**
     * 校验carVin是否在黑名单中
     * @param carVinList 车架号列表
     * @return MsgBlackListDTO
     * */
    @Override
    public CommonResult<List<MsgBlackListDTO>> checkCarVinBlackList(List<String> carVinList) {
        return CommonResult.success(msgBlackListDORepository.checkBlackListByCarVinList(carVinList));
    }

    /**
     * 根据品牌编码获取短链配置
     *
     * @param brandCode 品牌编码，用于查询短链配置
     * @return 返回封装了短链配置的CommonResult对象，如果查询结果为空，则返回成功但数据为null的结果
     */
    @Override
    public CommonResult<ShortLinkConfigVO> getShortLinkConfig(String brandCode) {
        log.info("根据品牌编码获取短链配置，brandCode:{}", brandCode);
        NotificationConfigDO notificationConfigDO = shortLinkConfigRepository.queryShortLinkConfigByBrandCode(brandCode);
        if (Objects.isNull(notificationConfigDO)) {
            log.info("按照brandCode查询短链配置为空，brandCode:{}", brandCode);
            return CommonResult.success(null);
        }
        ShortLinkConfigVO shortLinkConfigVO = buildShortLinkConfigVO(notificationConfigDO);
        log.info("查询短链配置结果, brandCode:{}, notificationConfigDO:{}, shortLinkConfigVO:{}", brandCode,
                notificationConfigDO, shortLinkConfigVO);
        return CommonResult.success(shortLinkConfigVO);
    }

    /**
     * 获取到期通知条件
     *
     * @param expireDate 到期日期，用于查询到期通知任务
     * @return 包含到期通知条件列表的通用结果对象
     */
    @Override
    public CommonResult<List<ExpireNotifyConditionDTO>> getExpireNotifyCondition(String expireDate) {
        log.info("获取到期通知条件, expireDate:{}", expireDate);
        LocalDateTime expireDateTime = TimeFormatUtil.stringToTimeByFormat(expireDate, TimeFormatUtil.formatter_1);
        List<ExpireNotifyConditionDTO> resp = new ArrayList<>();
        List<NotificationAutoTaskDO> autoTaskDOList = autoTaskRepository.getExpireNotifyAutoTask(expireDateTime);
        if (CollUtil.isEmpty(autoTaskDOList)) {
            return CommonResult.success(resp);
        }
        log.info("获取到期通知条件, autoTaskDOList的数量:{}", autoTaskDOList.size());
        for (NotificationAutoTaskDO autoTaskDO : autoTaskDOList) {
            List<AutoTaskTriggerMapDO> taskTriggerMapDOList = triggerMapRepository.getAutoTriggerByTaskCode(autoTaskDO.getTaskCode());
            if (CollUtil.isEmpty(taskTriggerMapDOList)) {
                log.info("获取到期通知条件taskTriggerMapDOList为空, autoTaskDO:{}", autoTaskDO);
                continue;
            }
            List<String> conditionIdList = taskTriggerMapDOList.stream()
                    .map(AutoTaskTriggerMapDO::getConditionId)
                    .collect(Collectors.toList());
            Map<String, AutoTaskTriggerMapDO> taskTriggerMapDOMap = taskTriggerMapDOList.stream()
                    .collect(Collectors.toMap(
                            AutoTaskTriggerMapDO::getConditionId,
                            Function.identity(),
                            (v1, v2) -> v2));
            Map<Integer, AutoTaskConditionDO> conditionDOMap = conditionRepository.getConditionTypeMapByIds(conditionIdList);
            resp.add(buildExpireNotifyConditionDTO(autoTaskDO, taskTriggerMapDOMap, conditionDOMap));
        }
        addAutoTaskConditionToCache(resp);
        return CommonResult.success(resp);
    }

    /**
     * 根据taskCode获取详情ID列表
     *
     * @param detailIdsDTO 包含任务代码和可选日期范围的请求数据传输对象
     * @return 返回一个CommonResult对象，其中包含待发送短信的详情ID列表
     */
    @Override
    public CommonResult<List<Long>> getDetailIdsByTaskCode(GetDetailIdsByTaskCodeDTO detailIdsDTO) {
        log.info("根据taskCode获取详情ID列表, detailIdsDTO:{}", detailIdsDTO);
        if (Objects.isNull(detailIdsDTO) || StringUtils.isBlank(detailIdsDTO.getTaskCode())) {
            log.info("根据taskCode获取详情ID列表, 参数错误smsExpireIdsDTO:{}",detailIdsDTO);
            return CommonResult.success(new ArrayList<>());
        }
        String taskCode = detailIdsDTO.getTaskCode();
        LocalDateTime startTime = LocalDate.now().atTime(0, 0, 0);
        LocalDateTime endTime = LocalDate.now().atTime(23, 59, 59);
        if (Objects.nonNull(detailIdsDTO.getStartDate()) && Objects.nonNull(detailIdsDTO.getEndDate())) {
            startTime = detailIdsDTO.getStartDate().atTime(0, 0, 0);
            endTime = detailIdsDTO.getEndDate().atTime(23, 59, 59);
        }
        List<SendHistoryDetail> sendHistoryDetails = detailRepository.querySmsDetailByTaskCodeAndTime(taskCode,
                SmsSendResultEnum.WAITED.getCode(), startTime, endTime);
        List<Long> idList = new ArrayList<>();
        if (CollectionUtils.isEmpty(sendHistoryDetails)) {
            log.info("根据taskCode获取详情ID列表, 未查询到待发送短信, taskCode:{}, startTime:{}, endTime:{}",
                    taskCode, startTime, endTime);
            return CommonResult.success(idList);
        }
        for (SendHistoryDetail sendHistoryDetail : sendHistoryDetails) {
            idList.add(sendHistoryDetail.getId());
        }
        return CommonResult.success(idList);
    }

    /**
     * 根据taskCode发送到期消息
     *
     * @param sendExpireDTO 包含任务代码和详情ID列表的发送到期消息DTO
     * @return 发送成功后返回包含发送消息数量的CommonResult对象
     */
    @Override
    public CommonResult<Integer> sendExpireMsgByTaskCode(SendExpireByTaskCodeDTO sendExpireDTO) {
        log.info("根据taskCode发送到期消息, sendExpireDTO:{}", sendExpireDTO);
        NotificationAutoTaskDO autoTaskDO = autoTaskRepository.queryAutoTaskByCode(sendExpireDTO.getTaskCode());
        if (Objects.isNull(autoTaskDO)) {
            log.error("根据taskCode发送到期消息, 服务到期通知任务不存在");
            return CommonResult.success(0);
        }
        if (!TaskStatusEnum.START.getStatus().equals(autoTaskDO.getStatus())) {
            log.warn("根据taskCode发送到期消息,服务到期通知任务的状态为禁用, autoTaskDO:{}", autoTaskDO);
            return CommonResult.success(0);
        }
        List<SendHistoryDetail> sendHistoryDetails = detailRepository.querySmsDetailByTaskCodeAndTime(
                sendExpireDTO.getTaskCode(), SmsSendResultEnum.WAITED.getCode(),
                sendExpireDTO.getStartSendTime(), sendExpireDTO.getEndSendTime()
        );
        if (CollectionUtils.isEmpty(sendHistoryDetails)) {
            log.info("根据taskCode发送到期消息, 要发送的数据为空, sendExpireDTO:{}", sendExpireDTO);
            return CommonResult.success(0);
        }
        sendMsg(sendHistoryDetails);
        return CommonResult.success(sendHistoryDetails.size());
    }

    /**
     * 将自动任务的条件信息添加到缓存中 并设置过期时间为1天
     *
     * @param conditionDTOList 一组自动任务条件DTO，表示待添加到缓存的条件信息
     */
    private void addAutoTaskConditionToCache(List<ExpireNotifyConditionDTO> conditionDTOList) {
        String conditionKey = getAutoTaskConditionRMapKey();
        log.info("将自动任务的条件信息添加到缓存中, conditionKey:{}, ExpireNotifyConditionDTO:{}", conditionKey, conditionDTOList);
        RMap<String, Set<ExpireNotifyConditionDTO>> rMap = redisson.getMap(conditionKey);
        Map<String, Set<ExpireNotifyConditionDTO>> map = new HashMap<>();
        for (ExpireNotifyConditionDTO conditionDTO : conditionDTOList) {
            String key = getAutoConditionKey(conditionDTO.getDailySendTime());
            Set<ExpireNotifyConditionDTO> valueSet = map.getOrDefault(key, new HashSet<>());
            valueSet.add(conditionDTO);
            map.put(key, valueSet);
        }
        rMap.clear(); //先清空再放入
        rMap.putAll(map);
        // 设置rMap过期时间到今天最大时间
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime endOfDay = now.toLocalDate().atTime(LocalTime.MAX); //当天的最大时间
        Duration ttlDuration = Duration.between(now, endOfDay);
        long ttlMillis = ttlDuration.toMillis();
        // 设置过期时间为从现在到今天结束的时间
        rMap.expire(Duration.ofMillis(ttlMillis));
        log.info("完成自动任务条件缓存更新，rMapCache将在今天结束时失效, ttlMillis:{}ms", ttlMillis);
        //打印日志
        printRMapLog(rMap);
    }

    private void printRMapLog(RMap<String, Set<ExpireNotifyConditionDTO>> conditionRMap) {
        log.info("根据条件获取自动任务缓存, conditionRMap:");
        for (Map.Entry<String, Set<ExpireNotifyConditionDTO>> entry : conditionRMap.entrySet()) {
            log.info("conditionRMap, Key: {}, Value: {}", entry.getKey(), entry.getValue());
        }
    }

    /**
     * 根据每日发送时间生成自动条件键
     *
     * @param dailySendTime 每日发送时间，格式为字符串
     * @return 返回组合后的自动条件键字符串
     */
    private String getAutoConditionKey(String dailySendTime) {
        return LocalDate.now() + BLANK + dailySendTime;
    }

    /**
     * 生成自动任务条件的Redis映射键
     *
     * @return String 自动任务条件的Redis映射键
     */
    private String getAutoTaskConditionRMapKey() {
        return Constants.AUTO_TASK_CONDITION_KEY + LocalDate.now();
    }

    /**
     * 构建过期通知条件DTO
     *
     * @param autoTaskDO 自动任务DO，包含任务的基本信息和配置
     * @param taskTriggerMapDOMap 任务触发映射，用于确定任务的触发条件
     * @param conditionDOMap 条件DO映射，根据不同的条件类型获取相应的条件信息
     * @return 返回一个构建好的ExpireNotifyConditionDTO对象，它包含了自动任务和相关条件的详细信息
     */
    private ExpireNotifyConditionDTO buildExpireNotifyConditionDTO(NotificationAutoTaskDO autoTaskDO,
                                                                   Map<String, AutoTaskTriggerMapDO> taskTriggerMapDOMap,
                                                                   Map<Integer, AutoTaskConditionDO> conditionDOMap) {
        return ExpireNotifyConditionDTO.builder()
                .taskCode(autoTaskDO.getTaskCode())
                .taskName(autoTaskDO.getTaskName())
                .triggerAction(autoTaskDO.getTriggerAction())
                .taskTimeType(autoTaskDO.getTaskTimeType())
                .taskSendType(autoTaskDO.getTaskSendType())
                .rangeBeginDate(autoTaskDO.getRangeBeginDate())
                .rangeEndDate(autoTaskDO.getRangeEndDate())
                .messageTemplateCode(autoTaskDO.getMessageTemplateCode())
                .dailySendTime(autoTaskDO.getDailySendTime())
                .notifySpuCode(autoTaskDO.getNotifySpuCode())
                .status(autoTaskDO.getStatus())
                .brand(conditionDOMap.getOrDefault(AutoTaskConditionTypeEnum.BRAND.getType(), new AutoTaskConditionDO()).getConditionCode())
                .vehicleOrigin(conditionDOMap.getOrDefault(AutoTaskConditionTypeEnum.VEHICLE_ORIGIN.getType(), new AutoTaskConditionDO()).getConditionCode())
                .expirationService(conditionDOMap.getOrDefault(AutoTaskConditionTypeEnum.EXPIRATION_SERVICE.getType(), new AutoTaskConditionDO()).getConditionCode())
                .realNameStatus(conditionDOMap.getOrDefault(AutoTaskConditionTypeEnum.REAL_NAME_STATUS.getType(), new AutoTaskConditionDO()).getConditionCode())
                .expirationInterval(conditionDOMap.getOrDefault(AutoTaskConditionTypeEnum.EXPIRATION_INTERVAL.getType(), new AutoTaskConditionDO()).getConditionCode())
                .expireDays(getExpireDays(taskTriggerMapDOMap, conditionDOMap))
                .build();

    }

    /**
     * 获取过期天数
     *
     * @param taskTriggerMapDOMap 任务触发映射数据对象地图，用于查找任务触发条件
     * @param conditionDOMap 条件数据对象地图，用于获取过期时间条件
     * @return 过期天数如果解析失败或条件不存在，则返回0
     */
    private Integer getExpireDays(Map<String, AutoTaskTriggerMapDO> taskTriggerMapDOMap,
                                  Map<Integer, AutoTaskConditionDO> conditionDOMap) {
        try {
            String conditionId = conditionDOMap.getOrDefault(AutoTaskConditionTypeEnum.EXPIRATION_INTERVAL.getType(),
                    new AutoTaskConditionDO()).getConditionId();
            String expireValue = taskTriggerMapDOMap.get(conditionId).getConditionValue();
            return Integer.parseInt(expireValue);
        } catch (Exception e) {
            log.info("获取过期天数异常：", e);
        }
        return 0;
    }

    /**
     * 根据通知配置对象构建短链接配置VO
     *
     * @param notificationConfigDO 通知配置持久层对象，作为输入参数，用于获取构建VO所需的字段值
     * @return 返回构建的ShortLinkConfigVO对象，其中包含了从通知配置对象中提取的相关字段信息
     */
    private ShortLinkConfigVO buildShortLinkConfigVO(NotificationConfigDO notificationConfigDO) {
        if (Objects.isNull(notificationConfigDO)) {
            return null;
        }
        return ShortLinkConfigVO.builder()
                .brandCode(notificationConfigDO.getBrandCode())
                .configId(notificationConfigDO.getId())
                .piviServiceSpu(notificationConfigDO.getPiviServiceSpu())
                .piviServiceSpuName(notificationConfigDO.getPiviServiceSpuName())
                .remoteServiceSpu(notificationConfigDO.getRemoteServiceSpu())
                .remoteServiceSpuName(notificationConfigDO.getRemoteServiceSpuName())
                .build();
    }

    /**
     * 更新短信营销状态。
     * 该方法会尝试更新短信发送状态，直到成功或达到某种条件为止。
     *
     * @param marketStatusDTO 包含需要更新的短信发送状态信息的数据传输对象。
     */
    private void updateSmsMarketStatus(UpdateSmsSendStatusDTO marketStatusDTO) {
        for (int i = 1; ; i++) {
            UpdateSmsSendStatusResp updateSmsSendStatusResp = reachService.updateSmsSendStatus(marketStatusDTO);
            if (Objects.isNull(updateSmsSendStatusResp)) {
                log.info("更新短信营销状态, 当前第:{}次查找失败，退出查询", i);
                break;
            }
            if (!SmsResultErrorEnum.SUCCESS.getCode().equals(updateSmsSendStatusResp.getResult())) {
                log.info("更新短信营销状态, 当前第:{}次查找失败，退出查询", i);
                break;
            }
            if (CollectionUtils.isEmpty(updateSmsSendStatusResp.getReports())) {
                log.info("更新短信营销状态, 当前第:{}次查询结果为空，退出查询", i);
                break;
            }
            log.info("更新短信营销状态, 当前第:{}次查找, 获取report数量:{}", i, updateSmsSendStatusResp.getReports().size());
            List<ReportResp> reportRespList = updateSmsSendStatusResp.getReports();
            reachService.updateSmsUserReach(reportRespList);
        }
    }


    /***
     *  定时任务的发送时间
     *  @param sendTime 发送时间
     * @return String
     * */
    public String taskSendTimeChangeCore(String sendTime) {
        if(org.apache.commons.lang.StringUtils.isBlank(sendTime)) {
            return "";
        }
        String[] times = sendTime.split(":");
        if (times.length < 2) {
            log.error("发送时间格式错误, sendTime:{}", sendTime);
            return "";
        }
        return "0" + BLANK + times[1] + BLANK + times[0] + BLANK + "* * ?";
    }

    /**
     * 批量发送定时任务消息
     * @param sendHistoryDetails 待发送的数据
     * */
    private void sendMsg(List<SendHistoryDetail> sendHistoryDetails) {
        if (CollectionUtils.isEmpty(sendHistoryDetails)) {
            return ;
        }
        log.info("批量发送定时任务消息, 数量：{}", sendHistoryDetails.size());
        for (SendHistoryDetail detail : sendHistoryDetails) {
            SingleSendMessage sendMessage = buildSingleSendSmsMsg(detail);
            //第三方接口发送短信
            SendSmsResp sendResp = singleSendSms(sendMessage);
            //按照发送结果，更新短信消息的结果
            smsSendStatistics.updateSendResult(detail, sendResp);
        }
        if (Boolean.TRUE.equals(updateSmsResponseFlag)) {
            updateMarketSmsSendReport();
        }
    }

    /**
     * 更新营销短信发送报告。
     * 此方法不接受参数且无返回值。
     * 方法将当前时间延迟5分钟后执行一个任务，该任务用于更新短信发送状态。
     */
    private void updateMarketSmsSendReport() {
        // 获取当前时间，并添加5分钟的延迟
        Date fiveMinutesLater = new Date(System.currentTimeMillis() + 5 * 60 * 1000);
        Runnable task = () -> {
            UpdateSmsSendStatusDTO updateSmsSendStatusDTO = UpdateSmsSendStatusDTO.builder()
                    .account(smsMarketAccount)
                    .password(smsMarketPassword)
                    .build();
            // 执行具体的业务逻辑
            UpdateSmsSendStatusResp resp = reachService.updateSmsSendStatus(updateSmsSendStatusDTO);
            log.info("自动模板营销，实时更新短信状态报告, UpdateSmsSendStatusDTO:{}", updateSmsSendStatusDTO);
            if (Objects.nonNull(resp) && !CollectionUtils.isEmpty(resp.getReports())) {
                reachService.updateSmsUserReach(resp.getReports());
            }
        };
        // 使用TaskScheduler安排任务在5分钟后执行一次
        taskScheduler.schedule(task, fiveMinutesLater);
    }

    /**
     *  单条消息发送
     * @param singleSms 单条消息发送
     * @return SendSmsResp
     */
    public SendSmsResp singleSendSms(SingleSendMessage singleSms) {
        if (!checkMsgSendContent(singleSms)) {
            log.info("单条消息发送,校验结果为无需发送,singleSms:{}", singleSms);
            return SendSmsResp.builder()
                    .result(SmsResultErrorEnum.MESSAGE_CONTENT_ERROR.getCode())
                    .desc(SmsResultErrorEnum.MESSAGE_CONTENT_ERROR.getDesc())
                    .failPhones(singleSms.getPhones())
                    .build();
        }
        CloseableHttpClient httpClient = HttpClientUtil.getNoSslHttpClient(SOCKET_TIMEOUT, CONNECT_TIMEOUT);
        String requestBodyJson = JSON.toJSONString(singleSms);
        String accessToken = getToken();
        String smsApiUrl = smsBaseUrl + path;
        HttpPost httpPost = HttpPostUtil.getHttpPost(requestBodyJson, smsApiUrl, path, accessToken, apiKey, apiSecret);
        SendSmsResp sendSmsResp = null;
        log.info("请求短信代理商, head:{}, body:{}, path:{}", httpPost.getAllHeaders(), requestBodyJson, smsApiUrl);
        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            if (SmsSendStatusEnum.SUCCESS.getCode().equals(response.getStatusLine().getStatusCode())) {
                String responseJson = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                JSONObject jsonObject = JSON.parseObject(responseJson);
                sendSmsResp = JSON.toJavaObject(jsonObject, SendSmsResp.class);
                log.info("请求短信代理商成功, responseJson：{}", responseJson);
            } else {
                // 处理错误响应
                log.info("消息发送失败, response:{}, responseBody:{}", response,
                        EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8));
            }
        } catch (Exception e) {
            log.info("请求第三方短信代理商异常：{}", e.getMessage());
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("关闭http请求异常：", e);
            }
        }
        return sendSmsResp;
    }

    /**
     *  校验模板发送内容
     *  @param singleSms sms短信消息体
     *  @return boolean
     * */
    public boolean checkMsgSendContent(SingleSendMessage singleSms) {
        if (Objects.isNull(singleSms)) {
            return false;
        }
        if (StringUtils.isBlank(singleSms.getContent())) {
            return false;
        }
        return !singleSms.getContent().contains(NULL);
    }

    /**
     * 获取token
     * */
    public String getToken() {
        AccessTokenResponse tokenResponse = new AccessTokenResponse();
        try {
            tokenResponse = accessTokenUtil.fetchAccessToken();
        } catch (Exception e) {
            log.error("获取token异常:", e);
        }
        return tokenResponse.getAccessToken();
    }


    /**
     * 构建单条发送的消息体
     * @param sendHistoryDetail 发送消息体
     * @return  SingleSendMessage
     * */
    public SingleSendMessage buildSingleSendSmsMsg(SendHistoryDetail sendHistoryDetail) {
        if (Objects.isNull(sendHistoryDetail)) {
            return SingleSendMessage.builder().build();
        }
        return SingleSendMessage.builder()
                .account(smsMarketAccount)
                .password(smsMarketPassword)
                .msgid(sendHistoryDetail.getMsgId())
                .phones(sendHistoryDetail.getSendPhone())
                .content(sendHistoryDetail.getSendMessage())
                .sign(getSign(sendHistoryDetail))
                .sendtime(TimeFormatUtil.changeToSendTime(LocalDateTime.now()))
                .build();
    }

    /**
     * 获取签名
     * @param sendHistoryDetail 发送实体数据
     * @return String
     * */
    public String getSign(SendHistoryDetail sendHistoryDetail) {
        if (Objects.isNull(sendHistoryDetail)) {
            return "";
        }
        if (JAGUAR_CODE.contains(sendHistoryDetail.getBrandCode())) {
            return JAGUAR_SIGN;
        }
        return LAND_ROVER_SIGN;
    }


    /**
     * 批量构建SendHistoryDetail
     * @param phoneByCarVinDTOList 服务到期数据
     * @return  List<SendHistoryDetail>
     * */
    private List<SendHistoryDetail> buildSendHistoryDetailList(List<PhoneByCarVinDTO> phoneByCarVinDTOList) {
        List<SendHistoryDetail> resp = new ArrayList<>();
        if (CollectionUtils.isEmpty(phoneByCarVinDTOList)) {
            return resp;
        }

        // 处理seriesName
        handlePhoneByCarVinDTOsSeriesName(phoneByCarVinDTOList);

        for (PhoneByCarVinDTO phoneByCarVinDTO : phoneByCarVinDTOList) {
            resp.add(buildSendHistoryDetail(phoneByCarVinDTO));
        }
        return resp;
    }

    /**
     * 处理PhoneByCarVinDTO的seriesName
     * @param phoneByCarVinDTOList 服务到期数据
     */
    private void handlePhoneByCarVinDTOsSeriesName(List<PhoneByCarVinDTO> phoneByCarVinDTOList) {
        //一次性拉去redis数据
        Map<String, String> map = redisService.getCacheMap(Constants.REDIS_KEY.SERIES_CACHE_KEY);
        Map<String, SeriesMappingVO> seriesMapping = new HashMap<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            seriesMapping.put(entry.getKey(), JSON.parseObject(entry.getValue(), SeriesMappingVO.class));
        }
        //遍历phoneByCarVinDTOList,
        // 如果seriesCode存在，就在seriesMapping根据seriesCode取值seriesName,取不到seriesName就不变
        phoneByCarVinDTOList.stream()
                .filter(phoneByCarVinDTO -> StringUtils.isNotBlank(phoneByCarVinDTO.getSeriesCode()))
                .forEach(phoneByCarVinDTO -> {
                    SeriesMappingVO seriesMappingVO = seriesMapping.get(phoneByCarVinDTO.getSeriesCode());
                    if (Objects.nonNull(seriesMappingVO)&&StringUtils.isNotBlank(seriesMappingVO.getSeriesName())) {
                        phoneByCarVinDTO.setSeriesName(seriesMappingVO.getSeriesName());
                    }
                });
    }


    /**
     * 批量构建SendHistoryDetail
     * @param phoneByCarVinDTO 服务到期数据
     * @return  List<SendHistoryDetail>
     * */
    private SendHistoryDetail buildSendHistoryDetail(PhoneByCarVinDTO phoneByCarVinDTO) {
        if (Objects.isNull(phoneByCarVinDTO)) {
            return SendHistoryDetail.builder().build();
        }
        String taskCode = phoneByCarVinDTO.getTaskCode();
        if (StringUtils.isBlank(phoneByCarVinDTO.getTaskCode())) {
            taskCode = getTaskCodeByExpireType(phoneByCarVinDTO.getExpireType());
        }
        String instanceCode = getDetailInstanceCode(taskCode);
        ExpireMessage message = getExpireMessage(phoneByCarVinDTO, instanceCode);
        SendHistoryDetail historyDetail = new SendHistoryDetail();
        historyDetail.setBusinessCode(BusinessIdEnum.VCS.getCode());
        historyDetail.setTaskInstanceCode(instanceCode);
        historyDetail.setMsgId(message.getMessageId());
        historyDetail.setSendMessage(buildMsgContent(message, taskCode));
        historyDetail.setSendPhone(phoneByCarVinDTO.getContactPhone());
        historyDetail.setCarVin(phoneByCarVinDTO.getCarVin());
        historyDetail.setSendResult(SmsSendResultEnum.WAITED.getCode());
        historyDetail.setTaskCode(taskCode);
        historyDetail.setTaskSendType(TaskSendTypeEnum.SCHEDULED.getCode());
        historyDetail.setBrandCode(phoneByCarVinDTO.getBrandCode());
        historyDetail.setOpenShortLink(OpenShortLinkEnum.NO.getStatus());
        if (Objects.nonNull(phoneByCarVinDTO.getAppointedDate())) {
            historyDetail.setCreatedTime(phoneByCarVinDTO.getAppointedDate().atTime(12, 1, 1));
        }
        log.info("构建SendHistoryDetail:{}", historyDetail);
        return historyDetail;
    }

    /**
     * 获取每条发送记录的code
     * @return String
     * */
    public String getDetailInstanceCode(String taskCode) {
        String now = TimeFormatUtil.timeToStringByFormat(LocalDateTime.now(), TimeFormatUtil.formatter_3);
        String key = Constants.DETAIL_INSTANCE_CODE_KEY + now + ":" + taskCode;
        Object value= redisTemplate.opsForValue().get(key);
        if (Objects.isNull(value)) {
            String instanceCode = instanceCodeGenerator.generateTaskBatchId(InstanceCodeTypeEnum.AUTO_TIMED.getType());
            redisTemplate.opsForValue().set(key, instanceCode, 1, TimeUnit.DAYS);
            return instanceCode;
        }
        return (String) value;
    }


    /**
     *  获取过期服务通知消息
     * @param phoneByCarVinDTO 过期服务信息
     * @param instanceCode  每条发送记录的code
     * @return ExpireMessage
     * */
    private ExpireMessage getExpireMessage(PhoneByCarVinDTO phoneByCarVinDTO, String instanceCode) {
        log.info("获取过期服务通知消息, phoneByCarVinDTO:{}, instanceCode:{}", phoneByCarVinDTO, instanceCode);
        ExpireMessage expireMessage = new ExpireMessage();
        expireMessage.setPhoneNumber(phoneByCarVinDTO.getContactPhone());
        expireMessage.setTenantId(Objects.isNull(phoneByCarVinDTO.getTenantId()) ?
                TenantContextHolder.getTenantId() : phoneByCarVinDTO.getTenantId().longValue());
        expireMessage.setOrderNumber(phoneByCarVinDTO.getOrderCode());
        expireMessage.setBrandAndModel(phoneByCarVinDTO.getSeriesName());
        if (Objects.nonNull(phoneByCarVinDTO.getExpireType())) {
            expireMessage.setServiceName(getServiceNameByOldTask(phoneByCarVinDTO.getExpireType()));
        } else {
            expireMessage.setServiceName(getServiceNameByTaskCode(phoneByCarVinDTO.getTaskCode()));
        }
        expireMessage.setMessageId(UUID.randomUUID().toString().replace("-", ""));
        expireMessage.setWxUrl(getWxUrl(phoneByCarVinDTO, instanceCode));
        if (Objects.nonNull(phoneByCarVinDTO.getExpireDate())) {
            expireMessage.setExpireDate(TimeFormatUtil.timeToStringByFormat(phoneByCarVinDTO.getExpireDate(),
                    TimeFormatUtil.formatter_3));
        }
        log.info("获取过期服务通知消息, expireMessage:{}", expireMessage);
        return expireMessage;
    }

    /**
     * 根据任务code获取服务名称。
     *
     * @param taskCode 任务code
     * @return 对应的服务名称字符串。
     */
    private String getServiceNameByTaskCode(String taskCode) {
        log.info("根据任务code获取服务名称, taskCode:{}", taskCode);
        List<AutoTaskTriggerMapDO> taskTriggerMapDOList = triggerMapRepository.getAutoTriggerByTaskCode(taskCode);
        if (CollectionUtils.isEmpty(taskTriggerMapDOList)) {
            return SERVICE;
        }
        List<String> conditionIdList = taskTriggerMapDOList.stream()
                .map(AutoTaskTriggerMapDO::getConditionId)
                .collect(Collectors.toList());
        List<AutoTaskConditionDO> conditionList = conditionRepository.getConditionByIdList(conditionIdList);
        if (CollUtil.isEmpty(conditionList)) {
            return SERVICE;
        }
        for (AutoTaskConditionDO conditionDO : conditionList) {
            if (AutoServiceTypeEnum.PIVI.getCode().equals(conditionDO.getConditionCode())) {
                return PIVI_SERVICE;
            } else if (AutoServiceTypeEnum.REMOTE.getCode().equals(conditionDO.getConditionCode())) {
                return REMOTE_SERVICE;
            }
        }
        return SERVICE;
    }

    /**
     * 根据旧任务代码获取对应的服务名称
     *
     * @param expireType 到期类型
     * @return 对应的服务名称，可能是REMOTE_SERVICE、PIVI_SERVICE或SERVICE
     */
    private String getServiceNameByOldTask(Integer expireType) {
        log.info("根据旧任务代码获取对应的服务名称, expireType:{}", expireType);
        if (ExpireTypeEnum.isRemoteService(expireType)) {
            return REMOTE_SERVICE;
        } else if (ExpireTypeEnum.isPIVIService(expireType)) {
            return PIVI_SERVICE;
        }
        return SERVICE;
    }

    /**
     * 获取到期服务wxUrl
     * @param phoneByCarVinDTO 过期服务信息
     * @param instanceCode  每条发送记录的code
     * @return String
     * */
    public String getWxUrl(PhoneByCarVinDTO phoneByCarVinDTO, String instanceCode) {
        //生成短链
        String productCode = phoneByCarVinDTO.getProductCode();
        String vinCode = phoneByCarVinDTO.getCarVin();
        String carType = URLEncoder.encode(phoneByCarVinDTO.getSeriesName(), StandardCharsets.UTF_8);
        String phoneNumber = phoneByCarVinDTO.getContactPhone();
        if (LAND_ROVER_CODE.contains(phoneByCarVinDTO.getBrandCode())) {
            return getLandRoverWxUrl(productCode, vinCode, carType, instanceCode, phoneNumber);
        } else if (JAGUAR_CODE.contains(phoneByCarVinDTO.getBrandCode())) {
            return getJaguarWxUrl(productCode, vinCode, carType, instanceCode, phoneNumber);
        }
        return "";
    }

    /**
     * 获取路虎短链
     * @param productCode 产品code
     * @param vinCode vin号
     * @param carType 车型
     * @param instanceCode 每条发送记录的code
     * @param phoneNumber 手机号
     * @return String
     * */
    public String getLandRoverWxUrl(String productCode, String vinCode, String carType,
                                    String instanceCode, String phoneNumber) {
        String path = ShortLinkPathEnum.RENEWAL_REMINDER.getPath();
        path = path + "?productCode=" + productCode + "&vinCode=" + vinCode + "&carType=" + carType
                + "&instanceCode=" + instanceCode + "&phoneNumber=" + phoneNumber;
        log.info("获取路虎短链, path:{}", path);
        try {
            CommonResult<String> resp = shortLinkService.genShortLink(path);
            return resp.getData();
        } catch (Exception e) {
            log.error("路虎获取到期服务wxUrl，异常：", e);
            return null;
        }
    }

    /**
     * 返回捷豹的链接
     * @param productCode 产品code
     * @param vinCode vin号
     * @param carType 车型
     * @param instanceCode 每条发送记录的code
     * @param phoneNumber 手机号
     * @return String
     * */
    public String getJaguarWxUrl(String productCode, String vinCode, String carType,
                                 String instanceCode, String phoneNumber) {
        String path = ShortLinkPathEnum.RENEWAL_REMINDER.getPath();
        String query = "productCode=" + productCode + "&vinCode=" + vinCode + "&carType=" + carType
                + "&instanceCode=" + instanceCode + "&phoneNumber=" + phoneNumber;
        log.info("获取捷豹短链, path:{}, query:{}", path, query);
        try {
            CommonResult<String> resp = shortLinkService.genJaguarLink(path, query);
            return resp.getData();
        } catch (Exception e) {
            log.error("获取捷豹小程序短链异常：", e);
            return null;
        }
    }

    /**
     * 构建消息的发送内容
     * @param message 消息体
     * @return String
     * */
    public String buildMsgContent(ExpireMessage message, String taskCode) {
        NotificationAutoTaskDO autoTask = autoTaskRepository.queryAutoTaskByCode(taskCode);
        NotificationTask oldAutoTask = taskRepository.queryTaskByCode(taskCode);
        log.info("构建消息的发送内容, taskCode:{}, autoTask:{}, oldAutoTask:{}", taskCode, autoTask, oldAutoTask);
        MessageTemplate template = null;
        if (Objects.nonNull(autoTask)) {
            template = templateRepository.getMessageTemplateByCode(autoTask.getMessageTemplateCode());
        } else if (Objects.nonNull(oldAutoTask)) {
            template = templateRepository.getMessageTemplateByCode(oldAutoTask.getMessageTemplateCode());
        }
        log.info("构建消息的发送内容, 模板内容：{}", template.getTemplateContent());
        if (Objects.isNull(template) || StringUtils.isBlank(template.getTemplateContent())) {
            return "";
        }
        List<String> contents = TemplateUtil.contentClassify(template.getTemplateContent());
        if (CollectionUtils.isEmpty(contents)) {
            return "";
        }
        Map<String, String> map = MessageContentUtil.buildVariableMap(template.getTemplateVariables(), message);
        return MessageContentUtil.getReallyContent(contents, map);
    }

}
