package com.jlr.ecp.notification.dal.repository;

import com.jlr.ecp.notification.dal.dataobject.shortlink.NotificationConfigModifyLog;

import java.util.List;

public interface ShortLinkModifyLogRepository {
    /**
     * 插入短链接修改日志
     *
     * @param notificationConfigModifyLog 短链接修改日志对象，包含需要插入的日志信息
     * @return 操作结果，true表示插入成功，false表示插入失败
     */
    Boolean insertShortLinkModifyLog(NotificationConfigModifyLog notificationConfigModifyLog);

    /**
     * 查询指定配置的短链接修改日志
     *
     * @param configId 配置ID，用于定位特定的配置
     * @return 返回与指定配置相关的短链接修改日志列表，如果配置ID为空或无效，则返回空列表
     */
    List<NotificationConfigModifyLog> queryShortLinkModifyLog(Long configId);
}
