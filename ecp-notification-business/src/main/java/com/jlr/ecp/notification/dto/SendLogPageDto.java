package com.jlr.ecp.notification.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 短信通知dto
 *
 * <AUTHOR>
 * */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SendLogPageDto {
    /**
     * 任务发送批次号
     * */
    private String taskInstanceCode;

    /**
     *   任务名称
     * */
    private String taskName;

    /**
     * 发送类型 1自动任务 2手动任务
     * */
    private Integer taskType;

    /**
     * 发送时间
     * */
    private LocalDateTime taskSendTime;

    /**
     *  总推送条数
     * */
    private Integer sendTotalCount;


    /**
     *  推送成功条数
     * */
    private Integer sendSuccessCount;

    /**
     * 推送失败条数
     * */
    private Integer sendFailCount;

    /**
     *  触达用户成功条数
     * */
    private Integer reachUserSuccessCount;

    /**
     *  触达用户失败条数
     * */
    private Integer reachUserFailCount;

    /**
     * 短链点击数
     * */
    private Integer openShortLinkCount;
}
