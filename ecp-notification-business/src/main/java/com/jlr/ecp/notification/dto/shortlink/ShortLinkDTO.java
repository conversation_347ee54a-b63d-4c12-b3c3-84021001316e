package com.jlr.ecp.notification.dto.shortlink;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@Schema(description = "短链点击dto")
public class ShortLinkDTO {
    @Schema(description = "实例code, 必填")
    @NotNull(message = "任务code不能为空")
    private String instanceCode;

    @Schema(description = "手机号，必填")
    @NotNull(message = "手机号不能为空")
    private String phoneNumber;

    @Schema(description = "车架号，必填")
    private String vinCode;
}