package com.jlr.ecp.notification.controller.admin;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.notification.dto.shortlink.QrCodeCreateDTO;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkCreateDTO;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkCreateListDTO;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkRemarkDTO;
import com.jlr.ecp.notification.service.ShortLinkCreateService;
import com.jlr.ecp.notification.vo.shortlink.QrCodeCreateVO;
import com.jlr.ecp.notification.vo.shortlink.ShortLinkClickVO;
import com.jlr.ecp.notification.vo.shortlink.ShortLinkCreateBaseUrlVO;
import com.jlr.ecp.notification.vo.shortlink.ShortLinkCreateListVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Tag(name = "短链生成管理")
@RestController
@RequestMapping("/shortLink/create")
@Validated
public class ShortLinkCreateController {

    @Resource
    private ShortLinkCreateService shortLinkCreateService;
    @GetMapping("/getBaseUrlName")
    @Operation(summary = "通过品牌获取跳转的路径名称")
    @PreAuthorize("@ss.hasPermission('notification:shortlink:create-list')")
    public CommonResult<List<ShortLinkCreateBaseUrlVO>> getBaseUrlName(@RequestParam(value = "brandCode") String brandCode) {
        return CommonResult.success(shortLinkCreateService.getBaseUrlByBrandCode(brandCode));
    }

    @PostMapping("/url")
    @Operation(summary = "生成短链")
    @PreAuthorize("@ss.hasPermission('notification:shortlink:create-list')")
    public CommonResult<String> createShortLink(@RequestBody ShortLinkCreateDTO shortLinkCreateDTO) {
        return CommonResult.success(shortLinkCreateService.createShortLink(shortLinkCreateDTO));
    }

    @PostMapping("/queryGenUrlList")
    @Operation(summary = "生成短链的列表页")
    @PreAuthorize("@ss.hasPermission('notification:shortlink:create-list')")
    public CommonResult<PageResult<ShortLinkCreateListVO>> queryGenUrlList(@RequestBody ShortLinkCreateListDTO listDTO) {
        return CommonResult.success(shortLinkCreateService.getShortLinkList(listDTO));
    }

    @PostMapping("/modifyRemark")
    @Operation(summary = "编辑备注")
    @PreAuthorize("@ss.hasPermission('notification:shortlink:create-list')")
    public CommonResult<String> modifyRemark(@RequestBody @Validated ShortLinkRemarkDTO remarkDTO) {
        return shortLinkCreateService.updateRemark(remarkDTO);
    }

    @GetMapping("/click/query")
    @Operation(summary = "短链点击数据查询")
    @PreAuthorize("@ss.hasPermission('notification:shortlink:create-list')")
    public CommonResult<ShortLinkClickVO> clickQuery(@RequestParam(value = "urlCode") String urlCode) {
        return shortLinkCreateService.queryShortLinkClick(urlCode);
    }


    @PostMapping("/qrCode")
    @Operation(summary = "太阳码生成")
    @PreAuthorize("@ss.hasPermission('notification:shortlink:sun-code')")
    public CommonResult<byte[]> createQrCode(@RequestBody QrCodeCreateDTO qrCodeCreateDTO) {
        return shortLinkCreateService.createQrCode(qrCodeCreateDTO);
    }

    @PostMapping("/qrCode/modifyList")
    @Operation(summary = "太阳码编辑记录列表")
    @PreAuthorize("@ss.hasPermission('notification:shortlink:sun-code')")
    public CommonResult<PageResult<QrCodeCreateVO>> createQrCodeList(@RequestBody ShortLinkCreateListDTO createListDTO) {
        return CommonResult.success(shortLinkCreateService.getCreateQrCodeList(createListDTO));
    }
}
