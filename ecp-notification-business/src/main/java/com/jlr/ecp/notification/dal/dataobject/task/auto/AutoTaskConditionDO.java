package com.jlr.ecp.notification.dal.dataobject.task.auto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;


@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_auto_task_condition")
public class AutoTaskConditionDO extends BaseDO {

    /**
     * 主键id, 自增
     * */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 条件ID；条件ID，雪花算法ID
     * 对应数据库字段：condition_id
     */
    @TableField("condition_id")
    private String conditionId;

    /**
     * 条件名称；条件名称，对应条件类型的名称
     * 对应数据库字段：condition_name
     */
    @TableField("condition_name")
    private String conditionName;

    /**
     * 条件类型；条件类型包括但不限于：
     * 1：品牌
     * 2：车辆产地
     * 3：到期服务
     * 4：实名状态
     * 5：到期区间
     * 对应数据库字段：condition_type
     */
    @TableField("condition_type")
    private Integer conditionType;

    /**
     * 条件编码；不同类型的条件编码如下：
     * 品牌：JAGUAR,LANDROVER, ALL
     * 产地：CHINA_MADE,IMPORTED, ALL
     * 服务：REMOTE,PIVI
     * 实名状态：RNR_TRUE,RNR_FALSE, ALL
     * 到期时间：BEFORE_EXPIRED,AFTER_EXPIRED
     * 对应数据库字段：condition_code
     */
    @TableField("condition_code")
    private String conditionCode;

    /**
     * 租户号
     * 对应数据库字段：tenant_id
     */
    @TableField("tenant_id")
    private Integer tenantId;
}
