package com.jlr.ecp.notification.util.mp;

import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sts.StsClient;
import software.amazon.awssdk.services.sts.auth.StsAssumeRoleCredentialsProvider;

import java.util.UUID;

@Slf4j
public class StsUtil {


    public static StsAssumeRoleCredentialsProvider loadCredentials(String roleArn) {
        StsClient stsClient = StsClient.builder()
                .region(Region.CN_NORTHWEST_1)
                // For non-AWS AK-SK credentials
//                .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create(ACCESS_KEY_ID, SECRET_ACCESS_KEY)))
                .build();

        return StsAssumeRoleCredentialsProvider.builder()
                .stsClient(stsClient)
                .refreshRequest(r -> r.roleArn(roleArn).roleSessionName("session-" + UUID.randomUUID()))
                .build();
    }

    public static StsAssumeRoleCredentialsProvider loadCredentialsWithDoubleAssumeRole(String userRoleArn, String snsRoleArn) {
        // 创建初始的 STS 客户端
        StsClient initialStsClient = StsClient.builder()
                .region(Region.CN_NORTHWEST_1)
                // 如果需要使用非 AWS 的 AK-SK 凭证，取消下面这行的注释
//                 .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create(ACCESS_KEY_ID, SECRET_ACCESS_KEY)))
                .build();

        // 第一次角色切换：获取 A role 的凭证
        StsAssumeRoleCredentialsProvider roleACredentials = StsAssumeRoleCredentialsProvider.builder()
                .stsClient(initialStsClient)
                .refreshRequest(r -> r.roleArn(userRoleArn).roleSessionName("session-" + UUID.randomUUID()))
                .build();

        // 使用 A role 的凭证创建新的 STS 客户端
        StsClient stsClientWithRoleA = StsClient.builder()
                .region(Region.CN_NORTHWEST_1)
                .credentialsProvider(roleACredentials)
                .build();

        // 第二次角色切换：使用 A role 的凭证获取 B role 的凭证
        return StsAssumeRoleCredentialsProvider.builder()
                .stsClient(stsClientWithRoleA)
                .refreshRequest(r -> r.roleArn(snsRoleArn).roleSessionName("session-sns-" + UUID.randomUUID()))
                .build();
    }

}
