<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jlr.ecp.notification.dal.mysql.template.SmsTemplateMapper">
    <!-- 按照templateCode更新非空字段 -->
    <update id="updateByTemplateCode">
        UPDATE t_message_template
        <set>
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="messageTemplate.businessCode != null and messageTemplate.businessCode != ''">
                    business_code = #{messageTemplate.businessCode},
                </if>
                <if test="messageTemplate.templateName != null and messageTemplate.templateName != ''">
                    template_name = #{messageTemplate.templateName},
                </if>
                <if test="messageTemplate.templateType != null">
                    template_type = #{messageTemplate.templateType},
                </if>
                <if test="messageTemplate.templateContent != null and messageTemplate.templateContent != ''">
                    template_content = #{messageTemplate.templateContent},
                </if>
                <if test="messageTemplate.templateVariables != null and messageTemplate.templateVariables != ''">
                    template_variables = #{messageTemplate.templateVariables},
                </if>
                <if test="messageTemplate.templateRemarks != null and messageTemplate.templateRemarks != ''">
                    template_remarks = #{messageTemplate.templateRemarks},
                </if>
                <if test="messageTemplate.tenantId != null">
                    tenant_id = #{messageTemplate.tenantId},
                </if>
                <if test="messageTemplate.updatedTime != null">
                    updated_time = #{messageTemplate.updatedTime},
                </if>
                <if test="messageTemplate.updatedBy != null">
                    updated_by = #{messageTemplate.updatedBy}
                </if>
            </trim>
        </set>
        WHERE template_code = #{messageTemplate.templateCode} and is_deleted = false
    </update>

    <!-- 软删除，按照templateCode删除记录 -->
    <update id="deleteByTemplateCode">
        UPDATE t_message_template
        SET is_deleted = true
        WHERE template_code = #{templateCode}
    </update>
</mapper>