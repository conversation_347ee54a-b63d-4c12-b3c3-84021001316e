package com.jlr.ecp.notification.dal.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.notification.dal.dataobject.template.TemplateModifyLog;
import com.jlr.ecp.notification.dal.mysql.template.TemplateModifyLogMapper;
import com.jlr.ecp.notification.dal.repository.TemplateModifyLogRepository;
import com.jlr.ecp.notification.enums.IsDeleteEnum;
import com.jlr.ecp.notification.req.template.TemplateHistoryPageReq;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class TemplateModifyLogRepositoryImpl extends ServiceImpl<TemplateModifyLogMapper, TemplateModifyLog>
        implements TemplateModifyLogRepository {

    @Resource
    private TemplateModifyLogMapper templateModifyLogMapper;

    /**
     *  分页查询模板编辑历史记录
     *
     * @param historyPageReq 模板编辑历史记录入参
     * @return Page<MessageTemplate>
     * */
    @Override
    public Page<TemplateModifyLog> queryTemplateModifyLogPage(TemplateHistoryPageReq historyPageReq) {
        Page<TemplateModifyLog> pageReq = new Page<>(historyPageReq.getPageNo(), historyPageReq.getPageSize());
        LambdaQueryWrapper<TemplateModifyLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtil.isNotBlank(historyPageReq.getTemplateCode()),
                        TemplateModifyLog::getTemplateCode, historyPageReq.getTemplateCode())
                .eq(TemplateModifyLog::getIsDeleted, IsDeleteEnum.NO.getStatus())
                .orderByDesc(TemplateModifyLog::getOperateTime);
        return templateModifyLogMapper.selectPage(pageReq, wrapper);
    }

    @Override
    public void insert(TemplateModifyLog log) {
        save(log);
    }

}
