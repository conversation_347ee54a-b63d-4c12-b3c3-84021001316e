package com.jlr.ecp.notification.controller.admin.manual;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.datapermission.core.annotation.DataPermission;
import com.jlr.ecp.notification.dto.template.ManualTemplateDTO;
import com.jlr.ecp.notification.req.template.SmsTemplatePageReq;
import com.jlr.ecp.notification.req.template.TemplateHistoryPageReq;
import com.jlr.ecp.notification.service.manual.ManualTemplateService;
import com.jlr.ecp.notification.vo.template.ManualTemplateDetailVo;
import com.jlr.ecp.notification.vo.template.SmsTemplateHistoryDetailVO;
import com.jlr.ecp.notification.vo.template.SmsTemplateHistoryVo;
import com.jlr.ecp.notification.vo.template.SmsTemplateVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Tag(name = "手动模板管理")
@RestController
@RequestMapping("/manual/template")
@Slf4j
public class ManualTemplateController {
    @Resource
    private ManualTemplateService manualTemplateService;

    @GetMapping("/list")
    @Operation(summary = "手动模板列表页")
    @PreAuthorize("@ss.hasPermission('notification:template-automatic:list')")
    public CommonResult<PageResult<SmsTemplateVo>> pageList(@SpringQueryMap SmsTemplatePageReq pageReq) {
        log.info("开始查询模板页面, 当businessCode为空查询全部, pageReq:{}", pageReq);
        PageResult<SmsTemplateVo> pageResult = manualTemplateService.getManualTemplateList(pageReq);
        return CommonResult.success(pageResult);
    }

    @PostMapping("/add")
    @Operation(summary = "添加手动模板")
    @PreAuthorize("@ss.hasPermission('notification:template-manual:create')")
    public CommonResult<Boolean> addManualTemplate(@RequestBody @Validated ManualTemplateDTO manualTemplateDTO) {
        log.info("添加手动模板, manualTemplateDTO:{}", manualTemplateDTO);
        return manualTemplateService.addSmsManualTemplate(manualTemplateDTO);
    }

    @GetMapping("/history")
    @Operation(summary = "手动模板编辑历史")
    @PreAuthorize("@ss.hasAnyPermissions('notification:template-manual:edit', 'notification:template-manual:detail')")
    @DataPermission(enable = false)
    public CommonResult<PageResult<SmsTemplateHistoryVo>> modifyHistory(@SpringQueryMap
                                                                        TemplateHistoryPageReq historyPageReq) {
        log.info("模板编辑历史入参, historyPageReq:{}", historyPageReq);
        PageResult<SmsTemplateHistoryVo> historyVo = manualTemplateService.getManualTemplateHistory(historyPageReq);
        return CommonResult.success(historyVo);
    }

    @GetMapping("/history/detail")
    @Operation(summary = "手动模版 编辑详情")
    @PreAuthorize("@ss.hasPermission('notification:template-manual:detail')")
    public CommonResult<SmsTemplateHistoryDetailVO> getModifyDetail(
            @RequestParam("id") @Schema(description = "日志ID") Long id) {
        log.info("获取手动模版编辑记录详情, id:{}", id);

        SmsTemplateHistoryDetailVO detail = manualTemplateService.getManualTemplateModifyDetail(id);
        return CommonResult.success(detail);
    }

    @PostMapping("/update")
    @Operation(summary = "更新手动模板")
    @PreAuthorize("@ss.hasPermission('notification:template-manual:edit')")
    public CommonResult<Boolean> updateManualTemplate(@RequestBody @Validated ManualTemplateDTO manualTemplateDTO) {
        log.info("修改手动模板, manualTemplateDTO:{}", manualTemplateDTO);
        return manualTemplateService.updateSmsManualTemplate(manualTemplateDTO);
    }

    @GetMapping("/detail")
    @Operation(summary = "手动模板详情")
    @PreAuthorize("@ss.hasPermission('notification:template-manual:detail')")
    public CommonResult<ManualTemplateDetailVo> manualTemplateDetail(@RequestParam String templateCode) {
        log.info("手动模板详情, templateCode:{}", templateCode);
        return manualTemplateService.smsManualTemplateDetail(templateCode);
    }

    @PostMapping("/copy")
    @Operation(summary = "手动模板复制")
    @PreAuthorize("@ss.hasPermission('notification:template-manual:copy')")
    @DataPermission(enable = false)
    public CommonResult<ManualTemplateDetailVo> manualTemplateCopy(@RequestParam String templateCode) {
        log.info("复制模板, templateCode:{}", templateCode);
        return manualTemplateService.smsManualTemplateDetail(templateCode);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "手动模板删除")
    @PreAuthorize("@ss.hasPermission('notification:template-manual:delete')")
    public CommonResult<Boolean> deleteManualTemplate(@RequestParam String templateCode) {
        log.info("手动模板删除, templateCode:{}", templateCode);
        return manualTemplateService.deleteSmsManualTemplate(templateCode);
    }

}
