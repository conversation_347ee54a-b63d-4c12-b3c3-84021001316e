package com.jlr.ecp.notification.enums.task;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 *  发送通道 1营销短信 2通知短信
 * */
@AllArgsConstructor
@Getter
public enum SendChannelEnum {

    MARKET(1, "营销短信", "营销性质"),
    NOTIFICATION(2, "通知短信", "非营销性质");

    private final Integer channelCode;
    private final String channelDesc;
    private final String natureDesc; // 新增的描述字段

    /**
     * 根据 channelCode 获取 channelDesc
     *
     * @param channelCode 渠道代码
     * @return 渠道描述，如果找到匹配的 channelCode 则返回对应的渠道描述，否则返回空字符串
     */
    public static String getChannelDescByChannelCode(Integer channelCode) {
        if (channelCode == null) {
            return "";
        }

        for (SendChannelEnum sendChannelEnum : SendChannelEnum.values()) {
            if (Objects.equals(sendChannelEnum.getChannelCode(), channelCode)) {
                return sendChannelEnum.getChannelDesc();
            }
        }
        return "";
    }

    /**
     * 根据 channelCode 获取 natureDesc
     *
     * @param channelCode 渠道代码
     * @return 性质描述，如果找到匹配的 channelCode 则返回对应的性质描述，否则返回空字符串
     */
    public static String getNatureDescByChannelCode(Integer channelCode) {
        if (channelCode == null) {
            return "";
        }

        for (SendChannelEnum sendChannelEnum : SendChannelEnum.values()) {
            if (Objects.equals(sendChannelEnum.getChannelCode(), channelCode)) {
                return sendChannelEnum.getNatureDesc();
            }
        }
        return "";
    }
}
