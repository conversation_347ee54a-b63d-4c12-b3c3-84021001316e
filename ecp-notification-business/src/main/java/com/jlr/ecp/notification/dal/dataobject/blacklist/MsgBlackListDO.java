package com.jlr.ecp.notification.dal.dataobject.blacklist;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;


@TableName("t_msg_black_list")
@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class MsgBlackListDO extends BaseDO {
    // 主键
    @TableId
    private Long id;

    // 车辆VIN;
    @TableField("car_vin")
    private String carVin;

    // 黑名单手机号;
    @TableField("phone")
    private String phone;

    // 租户号
    @TableField("tenant_id")
    private Integer tenantId;
}
