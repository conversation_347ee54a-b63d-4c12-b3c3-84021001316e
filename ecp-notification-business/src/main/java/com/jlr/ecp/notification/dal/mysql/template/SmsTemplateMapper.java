package com.jlr.ecp.notification.dal.mysql.template;

import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.notification.dal.dataobject.template.MessageTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SmsTemplateMapper extends BaseMapperX<MessageTemplate> {
    /**
     * 根据模板代码更新消息模板。
     *
     * @param messageTemplate 包含要更新的消息模板信息的对象。该对象应包含模板的唯一标识符和其他需要更新的字段。
     *                        通过这个对象，函数将更新数据库中对应的消息模板记录。
     * 注意：此方法不返回任何值，即没有返回值类型。更新操作的结果（例如是否成功）不通过方法返回，可能需要通过其他方式（如日志、异常处理）来处理。
     */
    void updateByTemplateCode(@Param("messageTemplate") MessageTemplate messageTemplate);

    /**
     * 根据模板代码删除消息模板。
     *
     * @param templateCode 要删除的消息模板的唯一标识符。
     * 注意：此方法不返回任何值，即没有返回值类型。删除操作的结果（例如是否成功）不通过方法返回，可能需要通过其他方式（如日志、异常处理）来处理。
     */
    void deleteByTemplateCode(@Param("templateCode") String templateCode);
}
