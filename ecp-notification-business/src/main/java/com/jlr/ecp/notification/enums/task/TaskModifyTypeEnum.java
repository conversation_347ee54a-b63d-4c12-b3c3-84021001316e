package com.jlr.ecp.notification.enums.task;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *  任务操作类型枚举
 *
 * <AUTHOR>
 * */

@AllArgsConstructor
@Getter
public enum TaskModifyTypeEnum {
    STOP(0, "停用自动通知任务"),
    START(1, "启用自动通知任务"),
    TIMED(2, "修改通知时间"),
    EDIT(3, "编辑手动通知任务"),
    ;

    /**
     * 操作类型
     * */
    private final Integer modifyType;

    /**
     * 操作描述
     * */
    private final String desc;

    /**
     * 按照type返回对应的枚举
     * @param type 修改类型
     * @return TaskModifyTypeEnum
     *
     * */
    public static TaskModifyTypeEnum getTaskModifyTypeEnumByType(Integer type) {
        TaskModifyTypeEnum[] taskModifyTypeEnum = TaskModifyTypeEnum.values();
        for (TaskModifyTypeEnum modifyTypeEnum : taskModifyTypeEnum) {
            if (modifyTypeEnum.getModifyType().equals(type)) {
                return modifyTypeEnum;
            }
        }
        return null;
    }
}
