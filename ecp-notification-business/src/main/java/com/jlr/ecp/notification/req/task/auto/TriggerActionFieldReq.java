package com.jlr.ecp.notification.req.task.auto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "触发逻辑子请求")
@AllArgsConstructor
@NoArgsConstructor
public class TriggerActionFieldReq {
    /**
     * 条件ID；条件ID，雪花算法ID
     * 对应数据库字段：condition_id
     */
    @Schema(description = "条件id")
    @NotNull(message = "条件id不能为空")
    private String conditionId;

    /**
     * 条件编码；不同类型的条件编码如下：
     * 品牌：JAGUAR, LANDROVER, ALL
     * 产地：CHINA_MADE, IMPORTED, ALL
     * 服务：REMOTE,PIVI
     * 实名状态：RNR_TRUE,RNR_FALSE, ALL
     * 到期时间：BEFORE_EXPIRED,AFTER_EXPIRED
     * 对应数据库字段：condition_code
     */
    @Schema(description = "品牌：JAGUAR, LANDROVER, ALL" +
            "产地：CHINA_MADE, IMPORTED, ALL" +
            "服务：REMOTE, PIVI" +
            "实名状态：RNR_TRUE, RNR_FALSE, ALL" +
            "到期时间：BEFORE_EXPIRED, AFTER_EXPIRED")
    @NotBlank(message = "条件编码不能为空")
    private String conditionCode;

    /**
     *  只有条件为到期区间，该字段才有值
     * */
    @Schema(description = "条件值  只有条件为到期区间，该字段才有值")
    private String conditionValue;
}
