package com.jlr.ecp.notification.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.jaguar.api.wechat.ShortLinkByWechatApi;
import com.jlr.ecp.notification.enums.ErrorCodeConstants;
import com.jlr.ecp.notification.util.sign.AbstractSignature;
import com.jlr.ecp.notification.dto.shortlink.convert.ShortLinkConvertDataDTO;
import com.jlr.ecp.notification.dto.shortlink.convert.ShortLinkConvertResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;


import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
@Slf4j
public class ShortLinkServiceImplTest {

    @Mock
    private RestTemplate mockRestTemplate;

    @Mock
    private RestTemplate mockRestTemplateSkipSsl;

    @Mock
    private ShortLinkByWechatApi mockShorLinkByWechatApi;

    @InjectMocks
    @Spy
    private ShortLinkServiceImpl shortLinkServiceImplUnderTest;

    @Before
    public void setUp() {
        shortLinkServiceImplUnderTest = new ShortLinkServiceImpl();
        shortLinkServiceImplUnderTest.jlrShortLinkServiceUrl = "jlrShortLinkServiceUrl";
        shortLinkServiceImplUnderTest.appKey = "appKey";
        shortLinkServiceImplUnderTest.secret = "secret";
        shortLinkServiceImplUnderTest.restTemplate = mockRestTemplate;
        shortLinkServiceImplUnderTest.restTemplateSkipSsl = mockRestTemplateSkipSsl;
        shortLinkServiceImplUnderTest.shorLinkByWechatApi = mockShorLinkByWechatApi;
    }

    @Test
    public void testGenShortLinkError() throws Exception {
        // Setup
        final CommonResult<String> expectedResult = CommonResult.error(ErrorCodeConstants.SMS_SHORT_LINK_FETCH_FAIL); // 假设一个成功的预期结果
        JSONObject responseBody = new JSONObject(); // 假设一个响应体内容
        responseBody.put("code", 500);
        responseBody.put("result", "expectedShortLink");

        // 使用 eq 匹配器来匹配期望的第二个参数（JSONObject.class）
        when(mockRestTemplate.exchange(any(RequestEntity.class), eq(JSONObject.class))).thenReturn(new ResponseEntity<>(responseBody, HttpStatus.OK));

        // Run the test
        CommonResult<String> result = shortLinkServiceImplUnderTest.genShortLink("path");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGenShortLinkResultEmpty() throws Exception {
        // Setup
        final CommonResult<String> expectedResult = null;

        final ResponseEntity<JSONObject> jsonObjectResponseEntity = new ResponseEntity<>(new JSONObject(0, false),
                HttpStatus.OK);
        when(mockRestTemplate.exchange(any(RequestEntity.class), eq(JSONObject.class))).thenReturn(jsonObjectResponseEntity);

        CommonResult<String> result = null;

        try {
            // Run the test
            result = shortLinkServiceImplUnderTest.genShortLink("path");
        } catch (Exception e) {
            log.error("testGenShortLink Exception:", e);
        }

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGenShortLinkArgEmpty() throws Exception {
        // Setup
        final CommonResult<String> expectedResult = CommonResult.success("");


        CommonResult<String> result = shortLinkServiceImplUnderTest.genShortLink("");


        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGenJaguarLinkError() {
        // Setup
        final CommonResult<String> expectedResult = CommonResult.error(ErrorCodeConstants.SMS_SHORT_LINK_FETCH_FAIL);

        final ResponseEntity<JSONObject> jsonObjectResponseEntity = new ResponseEntity<>(new JSONObject(0, false),
                HttpStatus.INTERNAL_SERVER_ERROR);
        when(mockRestTemplate.exchange(any(RequestEntity.class), eq(JSONObject.class))).thenReturn(jsonObjectResponseEntity);

        // Run the test
        final CommonResult<String> result = shortLinkServiceImplUnderTest.genJaguarLink("path", "query");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testConvertToShortUrlSuccess() {
        // Setup
        String originalUrl = "http://example.com";
        String expectedShortUrl = "http://short.ly/abc123";

        // Create mock response objects
        ShortLinkConvertDataDTO dataDTO = new ShortLinkConvertDataDTO();
        dataDTO.setShortUrl(expectedShortUrl);

        ShortLinkConvertResponse<ShortLinkConvertDataDTO> response = new ShortLinkConvertResponse<>();
        response.setCode(200);
        response.setData(dataDTO);

        // Mock the restTemplateSkipSsl call
        when(mockRestTemplateSkipSsl.exchange(any(String.class), eq(HttpMethod.POST), any(), any(org.springframework.core.ParameterizedTypeReference.class)))
                .thenReturn(new ResponseEntity<>(response, HttpStatus.OK));

        // Run the test
        String result = shortLinkServiceImplUnderTest.convertToShortUrl(originalUrl);

        // Verify the results
        assertThat(result).isEqualTo(expectedShortUrl);
    }

    @Test
    public void testConvertToShortUrlWithBlankUrl() {
        // Run the test with blank URL
        String result = shortLinkServiceImplUnderTest.convertToShortUrl("");

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testConvertToShortUrlWithNullUrl() {
        // Run the test with null URL
        String result = shortLinkServiceImplUnderTest.convertToShortUrl(null);

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testGenShortLinkWithException() {
        // Setup
        final CommonResult<String> expectedResult = CommonResult.error(ErrorCodeConstants.SMS_SHORT_LINK_FETCH_FAIL);

        // Mock restTemplate to throw exception
        when(mockRestTemplate.exchange(any(RequestEntity.class), eq(JSONObject.class)))
                .thenThrow(new RestClientException("Connection failed"));

        // Run the test
        CommonResult<String> result = shortLinkServiceImplUnderTest.genShortLink("path");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGenShortLinkWithConvertToShortUrlReturnsNull() {
        // Setup
        final CommonResult<String> expectedResult = CommonResult.success(null);
        JSONObject responseBody = new JSONObject();
        responseBody.put("code", AbstractSignature.HTTP_SUCCESS_CODE);
        responseBody.put("result", "originalShortLink");

        // Mock restTemplate call
        when(mockRestTemplate.exchange(any(RequestEntity.class), eq(JSONObject.class)))
                .thenReturn(new ResponseEntity<>(responseBody, HttpStatus.OK));

        // Mock convertToShortUrl method to return null
        when(shortLinkServiceImplUnderTest.convertToShortUrl("originalShortLink"))
                .thenReturn(null);

        // Run the test
        CommonResult<String> result = shortLinkServiceImplUnderTest.genShortLink("path");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

}
