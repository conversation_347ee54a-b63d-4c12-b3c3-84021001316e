package com.jlr.ecp.notification.dal.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.notification.dal.dataobject.task.NotificationTask;
import com.jlr.ecp.notification.req.task.TaskPageListReq;

import java.util.List;
import java.util.Map;

public interface MessageTaskRepository {

    /**
     * 按照任务id查询任务数据
     * @param taskCode 任务code
     * */
    NotificationTask queryTaskByCode(String taskCode);

    /**
     * 根据任务ID更新通知任务信息。
     *
     * @param notificationTask 通知任务对象，不可为null。
     * @return 返回更新操作的成功与否，成功返回true，失败返回false。
     */
    Boolean updateTaskById(NotificationTask notificationTask);

    /**
     * 根据模板代码查询通知任务。
     *
     * @param templateCode 消息模板的代码，用于查询相应的通知任务。
     * @return 返回一个通知任务列表。如果模板代码为空或无效，则返回空列表。
     */
    List<NotificationTask> queryTasksByTemplateCode(String templateCode);


    /**
     * 查询任务名称列表，根据任务类型过滤。
     *
     * @param taskType 任务类型。可为空，为空时查询所有非删除状态的任务；如果提供任务类型，则只查询指定类型的任务。
     * @return 返回一个任务名称的列表。如果查询不到任何任务，则返回空列表。
     */
    List<String> queryTaskNameListFilterAutoEvent(Integer taskType);

    /**
     * 查询任务Map，根据任务编码列表
     *
     * @param taskCodeList 任务编码列表
     * @return 返回一个任务名称的列表。如果查询不到任何任务，则返回空列表。
     */
    Map<String, NotificationTask> getTaskMapByCodes(List<String> taskCodeList);

    /**
     * 查询任务列表，根据任务编码列表
     *
     * @param taskCodeList 任务编码列表
     * @return 返回一个任务名称的列表。如果查询不到任何任务，则返回空列表。
     */
    List<NotificationTask> getTaskListByCodes(List<String> taskCodeList);

    /**
     *  分页查询短信通知任务列表页
     * @param taskPageListReq 分页插叙通知任务列表页入参
     * @return Page<NotificationTask>
     * */
    Page<NotificationTask>  queryTaskPageList(TaskPageListReq taskPageListReq);

    /**
     * 根据任务名称和发送类型获取手动任务列表
     *
     * @param taskCodeList 任务代码列表，用于筛选特定任务代码的任务
     * @param taskType 任务类型，用于筛选特定类型的任務
     * @param taskSendType 任务发送类型，用于筛选特定发送类型的任务
     * @param taskName 任务名称，用于模糊匹配任务名称
     * @return 返回符合筛选条件的手动任务列表
     */
    List<NotificationTask> getManualTaskByNameAndSendType(Integer taskType,
                                                          Integer taskSendType, String taskName);
}
