package com.jlr.ecp.notification.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Schema(description = "通知日志 - 个案查询入参")
public class NotificationLogSingleDTO {

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "carVin")
    private String carVin;

    @Schema(description = "业务编码")
    private String businessCode;
}
