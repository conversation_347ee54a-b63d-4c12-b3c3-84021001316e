package com.jlr.ecp.notification.dal.dataobject.shortlink;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_short_link_create_record")
public class ShortLinkCreateRecordDO extends BaseDO {

    /**
     *  自增主键id
     * */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 雪花算法生成
     * */
    @TableField(value = "config_id")
    private Long configId;

    /**
     *  每条短链的唯一编码
     * */
    @TableField(value = "url_code")
    private String urlCode;

    /**
     * 操作人
     * */
    @TableField(value = "operator")
    private String operator;

    /**
     * 品牌编码：路虎-LAN, 捷豹-JAG
     * */
    @TableField(value = "brand_code")
    private String brandCode;

    /**
     *  基础跳转路径
     * */
    @TableField(value = "base_url")
    private String baseUrl;

    /**
     *  跳转路径的参数
     * */
    @TableField(value = "params")
    private String params;

    /**
     *  跳转路径的配置内容
     * */
    @TableField(value = "config_content")
    private String configContent;

    /**
     * 生成的短链
     * */
    @TableField(value = "short_link")
    private String shortLink;

    /**
     * 短链到期时间
     * */
    @TableField(value = "expiry_date")
    private LocalDateTime expiryDate;

    /**
     *  备注
     * */
    @TableField(value = "remark")
    private String remark;

    /**
     * 租户号
     * */
    @TableField(value = "tenant_id")
    private Integer tenantId;
}
