package com.jlr.ecp.notification.service;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.notification.dto.shortlink.QrCodeCreateDTO;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkCreateDTO;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkCreateListDTO;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkRemarkDTO;
import com.jlr.ecp.notification.vo.shortlink.QrCodeCreateVO;
import com.jlr.ecp.notification.vo.shortlink.ShortLinkClickVO;
import com.jlr.ecp.notification.vo.shortlink.ShortLinkCreateBaseUrlVO;
import com.jlr.ecp.notification.vo.shortlink.ShortLinkCreateListVO;

import java.util.List;

public interface ShortLinkCreateService {

    /**
     * 根据品牌代码获取基础URL名称
     *
     * @param brandCode 品牌代码，用于识别特定品牌和对应的资源位置
     * @return 返回基础URLVO
     */
    List<ShortLinkCreateBaseUrlVO> getBaseUrlByBrandCode(String brandCode);

    /**
     * 创建短链
     *
     * @param createDTO 包含创建短链所需信息的数据传输对象
     * @return 创建的短链的字符串表示
     */
    String createShortLink(ShortLinkCreateDTO createDTO);

    /**
     * 获取短链列表信息
     *
     * @param createListDTO 短链创建列表的查询条件传输对象，包含分页和查询参数
     * @return 返回一个包含短链创建列表视图对象的分页结果，以及总记录数
     */
    PageResult<ShortLinkCreateListVO> getShortLinkList(ShortLinkCreateListDTO createListDTO);

    /**
     * 更新短链接的备注信息
     *
     * @param remarkDTO 包含配置ID和新备注信息的DTO
     * @return 更新操作的结果字符串
     */
    CommonResult<String> updateRemark(ShortLinkRemarkDTO remarkDTO);

    /**
     *  查询短链的点击详情
     *  @param urlCode 短链的唯一编码
     *  @return CommonResult<ShortLinkClickVO> 返回值为短链点击VO
     * */
    CommonResult<ShortLinkClickVO> queryShortLinkClick(String urlCode);

    /**
     * 生成太阳码并创建对应的配置及生成记录，同时调用微信接口生成短链。
     *
     * @param qrCodeCreateDTO 包含生成太阳码所需参数的数据传输对象，包括品牌编码、URL类型等配置信息
     * @return String 生成的短链URL字符串，若出现参数校验失败或构建过程异常则返回对应的错误描述信息
     */
    CommonResult<byte[]> createQrCode(QrCodeCreateDTO qrCodeCreateDTO);

    /**
     * 获取太阳码生成列表信息，分页查询并转换数据.
     *
     * @param createListDTO 分页查询参数，包含分页信息和查询条件
     * @return PageResult<QrCodeCreateVO> 查询结果封装对象，包含转换后的数据列表和总记录数
     */
    PageResult<QrCodeCreateVO> getCreateQrCodeList(ShortLinkCreateListDTO createListDTO);

}
