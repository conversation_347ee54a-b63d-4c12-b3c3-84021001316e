package com.jlr.ecp.notification.vo.shortlink;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Schema(description = "生成短链跳转页面VO")
@Data
@Builder
public class ShortLinkCreateBaseUrlVO {
    @Schema(description = "url名称，如：商品详情页（PDP）")
    String urlName;

    @Schema(description = "url类型，如：PDP")
    String urlType;

    @Schema(description = "跳转路径，如：ecp/pages/mine/order/detail/index")
    String url;

    @Schema(description = "品牌Id，如：路虎-BS0001, 捷豹-BS0002")
    private String brandId;
}
