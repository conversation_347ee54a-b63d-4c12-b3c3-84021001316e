package com.jlr.ecp.notification.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.forgeRock.core.dto.AccessTokenResponse;
import com.jlr.ecp.framework.forgeRock.core.util.AccessTokenUtil;
import com.jlr.ecp.jaguar.api.wechat.ShortLinkByWechatApi;
import com.jlr.ecp.notification.dto.shortlink.convert.ShortLinkConvertDataDTO;
import com.jlr.ecp.notification.dto.shortlink.convert.ShortLinkConvertRequestDTO;
import com.jlr.ecp.notification.dto.shortlink.convert.ShortLinkConvertResponse;
import com.jlr.ecp.notification.enums.ErrorCodeConstants;
import com.jlr.ecp.notification.service.ShortLinkService;
import com.jlr.ecp.notification.util.sign.AbstractSignature;
import com.jlr.ecp.notification.util.sign.SignatureUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;

import java.net.URI;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.jlr.ecp.framework.common.pojo.CommonResult.error;
import static com.jlr.ecp.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ShortLinkServiceImpl implements ShortLinkService {

    @Value("${jaguarlandrover.shortLinkUrl}")
    String jlrShortLinkServiceUrl;

    @Value("${jaguarlandrover.appKey}")
    String appKey;

    @Value("${jaguarlandrover.secret}")
    String secret;

    @Value("${send.smsUrl}")
    private String apimUrl;

    @Value("${send.apiKey}")
    private String apiKey;

    @Resource(name = "restTemplate")
    RestTemplate restTemplate;

    @Resource(name = "restTemplateSkipSsl")
    RestTemplate restTemplateSkipSsl;

    @Resource
    ShortLinkByWechatApi shorLinkByWechatApi;

    @Resource
    private AccessTokenUtil accessTokenUtil;

    private static final String shortLinkConvertPath = "/ss/tool/api/short-url/generate";
    @Override
    public CommonResult<String> genShortLink(String path) {
        log.info("短链生成的方法，path：{}", path);
        if (StrUtil.isBlank(path)) {
            return success("");
        }
        // 发起调用获取短链
        //年后会改为 POST 请求，并加签校验
        Map<String, String> postParameters = new HashMap<>();
        postParameters.put("path", path);
        ResponseEntity<JSONObject> response;
        try {
            HttpHeaders headers = SignatureUtil.headers(postParameters, "/wxma/wx/urlLink", HttpMethod.POST.name(), appKey, secret);
            URI uri = UriComponentsBuilder.fromUriString(jlrShortLinkServiceUrl).build().toUri();
            RequestEntity<Map<String, String>> requestEntity = RequestEntity.post(uri).headers(headers).body(postParameters);
            log.info("请求的完整path：{}", path);
            response = restTemplate.exchange(requestEntity, JSONObject.class);
        } catch (Exception e) {
            log.info("调用短链生成服务出错:", e);
            return error(ErrorCodeConstants.SMS_SHORT_LINK_FETCH_FAIL);
        }
        if (HttpStatus.OK.equals(response.getStatusCode())) {
            JSONObject body = response.getBody();
            Integer code = body.getInteger("code");
            if (code.equals(AbstractSignature.HTTP_SUCCESS_CODE)) {
                String result = body.getString("result");
                log.info("调用短链生成服务成功， path：{}， 生成短链result:{}", path, result);
                String convertResult = convertToShortUrl(result);
                return success(convertResult);
            } else {
                log.info("调用短链生成服务失败，状态码{}，响应体{}", response.getStatusCodeValue(), response.getBody());
            }
        }
        log.info("调用短链生成服务失败，状态码{}，响应体{}", response.getStatusCodeValue(), response.getBody());
        return error(ErrorCodeConstants.SMS_SHORT_LINK_FETCH_FAIL);
    }


    /**
     * 将原始URL转换为短链接
     *
     * @param originalUrl 原始URL地址，需要转换为短链接的完整URL
     * @return String 短链接，转换失败时返回null
     */
    public String convertToShortUrl(String originalUrl) {
        if (StringUtils.isBlank(originalUrl)) {
            log.info("短链转换失败，originalUrl为空");
            return null;
        }
        log.info("进行短链转换，originalUrl：{}", originalUrl);
        try {
            ShortLinkConvertRequestDTO request = new ShortLinkConvertRequestDTO();
            request.setOriginalUrl(originalUrl);
            request.setSourceSystem("ECP");
            request.setUrlType("WX_URL_LINK");
            request.setBizCase("ECP_PROMOTION");
            request.setValidityType("SHORT_TERM");
            request.setExpirationDays(30);
            request.setRemark("ECP微信短链转换");

            HttpHeaders headers = getHeaders();
            String requestUrl = apimUrl + shortLinkConvertPath;
            HttpEntity<ShortLinkConvertRequestDTO> entity = new HttpEntity<>(request, headers);
            log.info("短链转换, 请求路径requestUrl：{}， 参数entity：{}", requestUrl, entity);
            ResponseEntity<ShortLinkConvertResponse<ShortLinkConvertDataDTO>> response = restTemplateSkipSsl.exchange(
                    requestUrl,
                    HttpMethod.POST,
                    entity,
                    new ParameterizedTypeReference<ShortLinkConvertResponse<ShortLinkConvertDataDTO>>() {}
            );
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                ShortLinkConvertDataDTO convertDataDTO = response.getBody().getData();
                log.info("短链转换完成，convertDataDTO:{}", convertDataDTO);
                if (Objects.nonNull(convertDataDTO)) {
                    return convertDataDTO.getShortUrl();
                }
            } else {
                log.info("短链转换失败，originalUrl:{}，status:{}, response:{}", originalUrl, response.getStatusCode(), response.getBody());
            }
        } catch (Exception e) {
            log.info("短链转换异常, originalUrl:{}，原因：", originalUrl, e);
        }
        return null;
    }

    /**
     * 获取HTTP请求头信息
     *
     * @return HttpHeaders 包含认证信息和内容类型的HTTP请求头对象
     */
    public HttpHeaders getHeaders() {
        HttpHeaders headers = new HttpHeaders();
        // 添加认证相关请求头
        String timestamp = String.valueOf(Instant.now().toEpochMilli() / 1000);
        headers.add("Timestamp", timestamp);
        headers.add("X-API-Key", apiKey);
        String accessToken = getToken();
        headers.add("auth-token", "Bearer " + accessToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        return headers;
    }

    /**
     * 获取访问令牌
     *
     * @return 访问令牌字符串
     */
    public String getToken() {
        AccessTokenResponse tokenResponse = new AccessTokenResponse();
        try {
            tokenResponse = accessTokenUtil.fetchAccessToken();
        } catch (Exception e) {
            log.info("获取token异常:", e);
        }
        return tokenResponse.getAccessToken();
    }


    @Override
    public CommonResult<String> genJaguarLink(String path, String query) {
        log.info("捷豹短链生成入参, path:{}, query:{}", path, query);
        String request = path;
        if (StringUtils.isNotBlank(query)) {
            request = path +  "?" + query;
        }
        log.info("捷豹短链生成, request:{}", request);
        return genShortLink(request);
    }
}
