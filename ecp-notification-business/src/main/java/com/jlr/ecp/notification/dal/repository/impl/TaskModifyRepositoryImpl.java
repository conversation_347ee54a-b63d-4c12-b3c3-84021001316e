package com.jlr.ecp.notification.dal.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.notification.dal.dataobject.task.TaskModifyLog;
import com.jlr.ecp.notification.dal.mysql.task.TaskModifyMapper;
import com.jlr.ecp.notification.dal.repository.TaskModifyRepository;
import com.jlr.ecp.notification.enums.IsDeleteEnum;
import com.jlr.ecp.notification.req.task.TaskHistoryReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
@Component
public class TaskModifyRepositoryImpl extends ServiceImpl<TaskModifyMapper, TaskModifyLog>
        implements TaskModifyRepository {

    @Resource
    private TaskModifyMapper taskModifyMapper;

    /**
     * 分页查询编辑发送任务历史记录
     * @param taskHistoryReq 发送任务请求入参
     * @return  Page<NotificationHistory>
     * */
    public Page<TaskModifyLog> queryTaskHistoryPageList(TaskHistoryReq taskHistoryReq) {
        Page<TaskModifyLog> page = new Page<>(taskHistoryReq.getPageNo(), taskHistoryReq.getPageSize());
        LambdaQueryWrapper<TaskModifyLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(taskHistoryReq.getTaskCode()),
                        TaskModifyLog::getTaskCode, taskHistoryReq.getTaskCode())
                .eq(TaskModifyLog::getIsDeleted, IsDeleteEnum.NO.getStatus())
                .orderByDesc(TaskModifyLog::getOperateTime);
        return taskModifyMapper.selectPage(page, wrapper);
    }

    /**
     * 插入任务修改日志
     *
     * @param taskModifyLog 要插入的任务修改日志对象，包含任务修改的相关信息
     * @return 返回一个布尔值，表示日志是否成功插入true表示成功，false表示失败
     */
    @Override
    public boolean insertTaskModifyLog(TaskModifyLog taskModifyLog) {
        return save(taskModifyLog);
    }
}
