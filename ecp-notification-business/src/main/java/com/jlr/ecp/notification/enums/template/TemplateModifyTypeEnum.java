package com.jlr.ecp.notification.enums.template;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *  模板修改类型的枚举
 *
 * <AUTHOR>
 * */

@Getter
@AllArgsConstructor
public enum TemplateModifyTypeEnum {
    MODIFY_SEND_TEMPLATE(1, "编辑通知模板"),
    MODIFY_MANUAL_SEND_TEMPLATE(2, "编辑通知模板");

    /**
     *  修改类型code
     * */
    private final Integer code;

    /**
     *  修改描述
     * */
    private final String desc;

    /**
     * 按照code返回枚举
     * @param typeCode 模板修改类型code
     * @return TemplateModifyTypeEnum
     * */
    public static TemplateModifyTypeEnum getTemplateModifyTypeEnumByCode(Integer typeCode) {
        TemplateModifyTypeEnum[] typeEnums = TemplateModifyTypeEnum.values();
        for (TemplateModifyTypeEnum typeEnum : typeEnums) {
            if (typeEnum.getCode().equals(typeCode)) {
                return typeEnum;
            }
        }
        return null;
    }
}
