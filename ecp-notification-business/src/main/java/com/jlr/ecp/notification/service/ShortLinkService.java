package com.jlr.ecp.notification.service;

import com.jlr.ecp.framework.common.pojo.CommonResult;

/**
 * 短链服务
 * <AUTHOR>
 */
public interface ShortLinkService {

    /**
     * 生成路虎小程序对应类型的短链
     * 要拼参数的话自己拼在path后面
     * @return
     */
    CommonResult<String> genShortLink(String path);


    /**
     * 生成Jaguar小程序对应类型的短链
     * 要拼参数的话query传参  格式为 k1=v1&k2=v2
     * @return
     */
    CommonResult<String> genJaguarLink(String path, String query);
}
