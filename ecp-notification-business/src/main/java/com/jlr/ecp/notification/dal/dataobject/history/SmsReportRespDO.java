package com.jlr.ecp.notification.dal.dataobject.history;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("t_sms_report_resp")
public class SmsReportRespDO extends BaseDO {
    /**
     * 自增主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     *  短信编号
     * */
    @TableField(value = "msg_id")
    private String msgId;

    /**
     *   下行手机号码
     * */
    @TableField(value = "phone")
    private String phone;

    /**
     * 短信发送结果：
     * 0——成功；1——接口处理失败；2——运营商网关失败；
     * */
    @TableField(value = "status")
    private String status;

    /**
     * 当status 为1 时，以desc 的错误码为准
     * */
    @TableField(value = "description")
    private String description;

    /**
     *  当status 为2 时，表示运营商网关返回的原始值；
     * */
    @TableField(value = "wg_code")
    private String wgCode;

    /**
     *  短信发送时间, 时间格式为yyyy-MM-dd HH:mm:ss
     * */
    @TableField(value = "send_time")
    private String sendTime;

    /**
     *  长短信条数
     * */
    @TableField(value = "sms_count")
    private Integer smsCount;

    /**
     *  长短信第几条标示
     * */
    @TableField(value = "sms_index")
    private Integer smsIndex;
}
