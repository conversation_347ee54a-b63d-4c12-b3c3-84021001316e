package com.jlr.ecp.notification.vo.shortlink;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "生成短链记录列表页的VO")
@Data
@Builder
public class ShortLinkCreateListVO {

    /**
     * 雪花算法生成, 配置id全局唯一
     * */
    @Schema(description = "配置id全局唯一")
    private Long configId;

    @Schema(description = "短链编码")
    private String urlCode;

    /**
     * 操作人
     * */
    @Schema(description = "操作人")
    private String operator;

    @Schema(description = "短链生成时间")
    private String createTime;

    /**
     * 品牌编码：路虎-LAN, 捷豹-JAG
     * */
    @Schema(description = "品牌编码：路虎-LAN, 捷豹-JAG")
    private String brandCode;

    /**
     *  基础跳转路径
     * */
    @Schema(description = "基础跳转路径")
    private String baseUrl;

    /**
     *  跳转路径的参数
     * */
    @Schema(description = "跳转路径的参数")
    private String params;

    /**
     *  跳转路径的配置内容
     * */
    @Schema(description = "跳转路径的配置内容")
    private String configContent;

    /**
     * 生成的短链
     * */
    @Schema(description = "生成的短链")
    private String shortLink;

    @Schema(description = "短链状态：0-不可用，1-可用")
    private Integer shortLinkStatus;

    /**
     * 短链到期时间
     * */
    @Schema(description = "短链到期时间")
    private String expiryDate;

    /**
     *  备注
     * */
    @Schema (description = "备注")
    private String remark;

}
