package com.jlr.ecp.notification.service;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.notification.dto.template.AutoTemplateDTO;
import com.jlr.ecp.notification.req.template.SmsTemplatePageReq;
import com.jlr.ecp.notification.req.template.SmsTemplateReq;
import com.jlr.ecp.notification.req.template.TemplateHistoryPageReq;
import com.jlr.ecp.notification.vo.template.AutoTemplateVariableVO;
import com.jlr.ecp.notification.vo.template.SmsTemplateDetailVo;
import com.jlr.ecp.notification.vo.template.SmsTemplateHistoryVo;
import com.jlr.ecp.notification.vo.template.SmsTemplateVo;
import java.util.List;

/**
 *  Sms模板管理service
 *
 * <AUTHOR>
 * */
public interface SmsTemplateService {

    /**
     *  模板编辑
     * @param smsTemplateReq 模板编辑入参
     * @return CommonResult<Boolean>
     * */
    CommonResult<Boolean> smsTemplateModify(SmsTemplateReq smsTemplateReq);

    /**
     * 模板编辑详情页
     * @param templateCode 模板code
     * @return CommonResult<SmsTemplateDetailVo>
     * */
    CommonResult<SmsTemplateDetailVo> getSmsTemplateDetail(String templateCode);

    /**
     *  模板编辑历史记录
     * @param historyPageReq 历史记录入参
     * @return  CommonResult<SmsTemplateHistoryVo>
     * */
    PageResult<SmsTemplateHistoryVo> getTemplateHistory(TemplateHistoryPageReq historyPageReq);

    /**
     *  模板列表页
     * @param pageReq 模板列表页请入参
     * @return CommonResultPageResult<SmsTemplateVo>
     * */
    PageResult<SmsTemplateVo> getTemplateList(SmsTemplatePageReq pageReq);

    /**
     * 查询自动模板变量列表
     *
     * @return 返回一个包含自动模板变量信息的列表
     */
    List<AutoTemplateVariableVO> queryAutoTemplateVariableList(String businessCode);

    /**
     * 添加短信自动模板
     *
     * @param autoTemplateDTO 自动模板DTO，包含模板的必要信息
     * @return CommonResult<Boolean> 添加模板的结果，包括成功或错误信息
     */
    CommonResult<Boolean> addSmsAutoTemplate(AutoTemplateDTO autoTemplateDTO);

    /**
     * 删除自动模板
     *
     * @param templateCode 模板code
     * @return CommonResult<Boolean>
     * */
    CommonResult<Boolean> deleteAutoTemplate(String templateCode);

    /**
     * 根据模板类型查询短信模板
     *
     * @param templateType 模板类型，用于筛选短信模板的类型
     * @param autoType 自动类型，用于筛选短信模板的自动发送类型
     * @param businessCode 业务编码
     * @return 返回一个CommonResult对象，其中包含查询到的SmsTemplateVo对象列表
     */
    CommonResult<List<SmsTemplateVo>>  queryTemplateByType(Integer templateType, Integer autoType, String businessCode);
}
