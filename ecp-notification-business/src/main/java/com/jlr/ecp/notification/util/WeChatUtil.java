package com.jlr.ecp.notification.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jlr.ecp.framework.forgeRock.core.dto.AccessTokenResponse;
import com.jlr.ecp.framework.forgeRock.core.util.AccessTokenUtil;
import com.jlr.ecp.notification.dto.shortlink.GetUnlimitedQRCodeDTO;
import com.jlr.ecp.notification.properties.WeChatQrCodeProperties;
import com.jlr.ecp.notification.resp.WeChatQrCodeResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.Objects;

@Component
@Slf4j
public class WeChatUtil {
    @Resource
    private RestTemplate restTemplate;

    @Resource
    private AccessTokenUtil accessTokenUtil;

    @Value("${send.apiKey}")
    private String apiKey;

    @Value("${send.smsUrl}")
    private String apimBaseUrl;

    @Resource
    private WeChatQrCodeProperties qrCodeProperties;

    private static final int SOCKET_TIMEOUT = 60000; // 设置连接和读取超时时间为60秒
    private static final int CONNECT_TIMEOUT = 60000;

    /**
     * 获取微信小程序太阳码
     *
     * @param page  小程序页面路径（例如"pages/index/index"）
     * @param scene 场景值标识（最大32个字符，用于标识二维码来源）
     * @return      二维码图片的字节数据，若生成失败则返回null
     */
    public WeChatQrCodeResp getWeChatQrCode(String weChatToken, String page, String scene) {
        log.info("获取微信小程序太阳码, weChatToken:{}, page:{}, scene:{}", weChatToken, page, scene);
        String apiUrl = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + weChatToken;
        GetUnlimitedQRCodeDTO request = new GetUnlimitedQRCodeDTO();
        request.setPage(page);
        request.setScene(scene);
        request.setEnvVersion(qrCodeProperties.getEnvVersion());
        request.setIsHyaline(false);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<GetUnlimitedQRCodeDTO> entity = new HttpEntity<>(request, headers);
        try {
            ResponseEntity<byte[]> response = restTemplate.postForEntity(apiUrl, entity, byte[].class);
            log.info("获取微信小程序太阳码, response: {}", response);
            String contentType = "";
            if (Objects.nonNull(response.getHeaders().getContentType())) {
                contentType = response.getHeaders().getContentType().toString();
            }
            if (StringUtils.isNotBlank(contentType) && contentType.startsWith("image/")) {
                // 图片返回成功
                return WeChatQrCodeResp.builder()
                        .data(response.getBody())
                        .build();
            } else {
                // JSON 错误返回
                String errorJson = new String(Objects.requireNonNull(response.getBody()), StandardCharsets.UTF_8);
                JSONObject jsonObject = JSON.parseObject(errorJson);
                return WeChatQrCodeResp.builder()
                        .data(null)
                        .errorCode(jsonObject.getString("errcode"))
                        .errorMsg(jsonObject.getString("errmsg"))
                        .build();
            }
        } catch (Exception e) {
            log.error("获取微信小程序太阳码失败: ", e);
        }
        return null;
    }

    /**
     * 通过HTTP-GET请求获取微信访问令牌。
     *
     * @return 返回从服务器获取的微信访问令牌字符串；若请求失败或响应异常，返回空字符串
     */
    public String getWeChatTokenByHttpGet() {
        CloseableHttpClient httpClient = HttpClientUtil.getNoSslHttpClient(SOCKET_TIMEOUT, CONNECT_TIMEOUT);
        String url = apimBaseUrl + "/app/wxma/internal/wx/access-token";
        String accessToken = getForgeRockToken();
        HttpGet httpGet = new HttpGet(url);
        // 设置 Headers
        httpGet.setHeader("Content-Type", "application/json");
        httpGet.setHeader("auth-token", "Bearer " + accessToken);
        String timestamp = String.valueOf(Instant.now().toEpochMilli() / 1000); // 秒级时间戳
        httpGet.setHeader("Timestamp", timestamp);
        httpGet.setHeader("X-API-Key", apiKey);

        String weChatToken = "";
        log.info("通过HTTP-GET请求获取微信访问令牌, head:{}, url:{}", httpGet.getAllHeaders(), url);
        ObjectMapper objectMapper = new ObjectMapper(); // 可以注入或静态复用
        try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
            int statusCode = response.getStatusLine().getStatusCode();
            String responseJson = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            log.info("通过HTTP-GET请求获取微信访问令牌, statusCode: {}, responseBody: {}", statusCode, responseJson);
            // 解析 JSON 提取 access_token
            JsonNode jsonNode = objectMapper.readTree(responseJson);
            if (Objects.nonNull(jsonNode) && jsonNode.has("result")) {
                weChatToken = jsonNode.get("result").asText();
            } else {
                log.info("通过HTTP-GET请求获取微信访问令牌，响应中未找到result字段");
            }
        } catch (Exception e) {
            log.info("通过HTTP-GET请求获取微信访问令牌异常：", e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.info("通过HTTP-GET请求获取微信访问令牌，关闭http请求异常：", e);
            }
        }
        log.info("通过HTTP-GET请求获取微信访问令牌, weChatToken: {}", weChatToken);
        return weChatToken;
    }


    /**
     *   获取ForgeRockToken
     *   @return String
     * */
    public String getForgeRockToken() {
        AccessTokenResponse tokenResponse = new AccessTokenResponse();
        try {
            tokenResponse = accessTokenUtil.fetchAccessToken();
        } catch (Exception e) {
            log.error("获取token异常:", e);
        }
        return tokenResponse.getAccessToken();
    }
}
