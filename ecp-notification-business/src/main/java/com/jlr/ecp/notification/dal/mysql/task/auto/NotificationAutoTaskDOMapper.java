package com.jlr.ecp.notification.dal.mysql.task.auto;

import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.notification.dal.dataobject.task.auto.NotificationAutoTaskDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface NotificationAutoTaskDOMapper extends BaseMapperX<NotificationAutoTaskDO> {
    @Update("update t_notification_auto_task set range_begin_date = null, range_end_date = null where task_code = #{taskCode}")
    Integer updateBeginAndEndDateNull(String taskCode);
}
