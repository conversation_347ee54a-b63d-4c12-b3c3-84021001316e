package com.jlr.ecp.notification.controller.app;

import com.jlr.ecp.framework.forgeRock.core.dto.AccessTokenResponse;
import com.jlr.ecp.framework.forgeRock.core.util.AccessTokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/***
 * 测试 AccessTokenUtil
 * */
@RestController
@Slf4j
@RequestMapping("/token")
public class AccessTokenUtilController {
    @Resource
    private AccessTokenUtil accessTokenUtil;

    @GetMapping("/util")
    public void getToken() {
        try {
            AccessTokenResponse resp = accessTokenUtil.fetchAccessToken();
            log.info("resp:{}", resp);
        } catch (Exception e) {
            log.error("异常：", e);
        }
    }
}
