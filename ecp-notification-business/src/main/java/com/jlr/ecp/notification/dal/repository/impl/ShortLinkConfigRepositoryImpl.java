package com.jlr.ecp.notification.dal.repository.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.notification.dal.dataobject.shortlink.NotificationConfigDO;
import com.jlr.ecp.notification.dal.mysql.shortlink.NotificationConfigDOMapper;
import com.jlr.ecp.notification.dal.repository.ShortLinkConfigRepository;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class ShortLinkConfigRepositoryImpl implements ShortLinkConfigRepository {
    @Resource
    private NotificationConfigDOMapper notificationConfigDOMapper;

    /**
     * 插入短链接配置
     *
     * @param notificationConfigDO 短链接配置信息对象，包含待插入的配置数据
     * @return 操作结果，true表示成功插入，false表示插入失败
     */
    @Override
    public Boolean insertShortLinkConfig(NotificationConfigDO notificationConfigDO) {
        return notificationConfigDOMapper.insert(notificationConfigDO) > 0;
    }

    /**
     * 更新短链接配置
     *
     * @param notificationConfigDO 待更新的通知配置对象，包含需要更新的短链接设置信息
     * @return Boolean 表示通知配置更新操作是否成功的布尔值
     */
    @Override
    public Boolean updateShortLinkConfig(NotificationConfigDO notificationConfigDO) {
        return notificationConfigDOMapper.updateById(notificationConfigDO) > 0;
    }

    /**
     * 根据品牌代码查询短链接配置
     *
     * @param brandCode 品牌代码，用于筛选特定品牌的配置
     * @return 返回查询到的短链接配置信息，如果未找到则可能返回null
     */
    @Override
    public NotificationConfigDO queryShortLinkConfigByBrandCode(String brandCode) {
        log.info("根据品牌代码查询短链接配置, brandCode:{}", brandCode);
        if (StringUtil.isEmpty(brandCode)) {
            return null;
        }
        LambdaQueryWrapper<NotificationConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtil.isNotEmpty(brandCode)) {
            queryWrapper.eq(NotificationConfigDO::getBrandCode, brandCode);
        }
        queryWrapper.eq(NotificationConfigDO::getIsDeleted, false);
        List<NotificationConfigDO> resp = new ArrayList<>();
        try {
            resp = notificationConfigDOMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.info("根据品牌代码查询短链接配置异常:{}", e.getMessage());
        }
        if (CollUtil.isEmpty(resp)) {
            return null;
        }
        if (resp.size() > 1) {
            log.info("根据品牌代码查询短链接配置大于1条,resp:{}", resp);
        }
        return resp.get(0);
    }
}
