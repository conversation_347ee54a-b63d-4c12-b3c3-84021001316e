package com.jlr.ecp.notification.req.task.auto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 *  通知任务编辑入参
 *
 * <AUTHOR>
 * */

@Data
@Schema(description = "通知任务编辑入参")
public class AutoTaskModifyTimeReq {

    @Schema(description = "任务code")
    private String taskCode;

    @Schema(description = "发送时间类型 1：永久循环发送 2：限定时间发送")
    private Integer taskTimeType;

    @Schema(description = "范围开始时间")
    private String rangeBeginDate;

    @Schema(description = "范围结束时间")
    private String rangeEndDate;

    @Schema(description = "每日定时发送时间, 格式 xx:xx")
    private String dailySendTime;
}
