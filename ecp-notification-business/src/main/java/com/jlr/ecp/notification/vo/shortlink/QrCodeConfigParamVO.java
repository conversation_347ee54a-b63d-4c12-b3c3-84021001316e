package com.jlr.ecp.notification.vo.shortlink;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "太阳码生配置参数VO")
@Data
public class QrCodeConfigParamVO {
    @Schema(description = "产品Code")
    private String productCode;

    @Schema(description = "埋点参数1")
    private String param1;

    @Schema(description = "埋点参数2")
    private String param2;

    @Schema(description = "埋点参数3")
    private String param3;

    @Schema(description = "埋点参数4")
    private String param4;

    @Schema(description = "埋点参数5")
    private String param5;
}
