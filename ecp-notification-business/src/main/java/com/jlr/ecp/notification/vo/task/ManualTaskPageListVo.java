package com.jlr.ecp.notification.vo.task;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

import static com.jlr.ecp.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

/**
 * 短信通知任务列表页VO
 *
 * <AUTHOR>
 * */

@Data
@Schema(description = "手动通知任务列表页VO")
@Builder
public class ManualTaskPageListVo {

    @Schema(description = "任务编码")
    private String taskCode;

    @Schema(description = "任务名称")
    private String taskName;

    @Schema(description = "通知发送时间")
    private String taskSendTime;

    @Schema(description = "通知模板内容")
    private String templateContent;

    @Schema(description = "通知状态 0停用 1启用 不止这些")
    private String status;

    @Schema(description = "启用时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime activateTime;

    @Schema(description = "停用时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime deactivateTime;

    @Schema(description = "创建人")
    private String createdBy;

    @Schema(description = "品牌签名内容 1路虎中国 2捷豹路虎中国")
    private Integer signBrandType;

    @Schema(description = "发送通道 1营销短信 2通知短信")
    private Integer sendChannelType;
}
