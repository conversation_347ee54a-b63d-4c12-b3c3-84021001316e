package com.jlr.ecp.notification.dal.dataobject.crc;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

@TableName("t_crc_messages")
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class CrcMessagesDO extends BaseDO {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     *  消息唯一id;消息ID雪花算法ID
     * */
    @TableField(value = "message_id")
    private Long messageId;

    /**
     *  CRC消息唯一ID;
     * */
    @TableField(value = "crc_message_id")
    private String crcMessageId;

    /**
     *  用户JLRID
     * */
    @TableField(value = "jlr_id")
    private String jlrId;

    /**
     *  消息类型;1：文字 2：图片 3：视频 4：音频
     * */
    @TableField(value = "message_type")
    private Integer messageType;

    /**
     *  消息状态;0：未读 1：已读
     * */
    @TableField(value = "message_status")
    private Integer messageStatus;

    /**
     *  消息内容;客服留言消息，最大900字符
     * */
    @TableField(value = "message_content")
    private String messageContent;

    /**
     *  消息发送时间
     * */
    @TableField(value = "message_time")
    private LocalDateTime messageTime;

    /**
     * 租户号
     * */
    @TableField(value = "tenant_id")
    private Integer tenantId;

    /**
     * 业务线编码
     * */
    @TableField(value = "business_code")
    private String businessCode;
}
