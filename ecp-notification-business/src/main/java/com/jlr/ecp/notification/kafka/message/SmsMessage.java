package com.jlr.ecp.notification.kafka.message;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SmsMessage {
    /**
     * 消息Id
     * */
    private String messageId;

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     *  任务code
     * */
    private String taskCode;

    /**
     * 租户号
     * */
    private Long tenantId;

    /**
     *  服务名称
     * */
    private String serviceName;

    /**
     *  品牌code 1:路虎 2：捷豹
     * */
    private Integer brandCode;

    /**
     * 订单编号
     * */
    private String orderNumber;

    /**
     * 车辆vin码
     */
    private String carVin;
}
