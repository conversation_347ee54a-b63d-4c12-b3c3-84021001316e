package com.jlr.ecp.notification.api.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SendExpireSmsParam implements Serializable {
    /**
     *  到期类型 @see ExpireTypeEnum
     * */
    private Integer expireType;

    /**
     *  到期短信记录id列表
     * */
    private List<Long> detaileIdList;

}
