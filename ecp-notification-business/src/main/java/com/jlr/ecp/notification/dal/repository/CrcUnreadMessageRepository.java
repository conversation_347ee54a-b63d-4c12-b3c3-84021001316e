package com.jlr.ecp.notification.dal.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.notification.dal.dataobject.crc.CrcMessagesDO;

public interface CrcUnreadMessageRepository extends IService<CrcMessagesDO> {

    /**
     * 保存消息记录
     *
     * @param crcMessagesDO 消息记录对象，包含消息的相关信息
     * @return 返回保存操作是否成功true表示成功，false表示失败
     */
    boolean saveCrcMessagesDO(CrcMessagesDO crcMessagesDO);

    /**
     * 根据记录ID和消息状态查找CRC消息数量
     *
     * @param jilId 消息记录ID，用于定位特定的消息记录
     * @param status 消息的状态，用于过滤出具有特定状态的消息
     * @return 符合条件的消息数量
     * */
    Long findCrcMsgByJlrIdAndStatus(String jilId, Integer status, String businessCode);

    /**
     * 根据记录ID更新所有消息的状态为已读
     *
     * @param jilId 记录ID，用于标识需要更新的消息集合
     * @return 返回更新操作是否成功
     */
     boolean updateCrcAllMsgStatusByJlrId(String jilId, String businessCode);
}
