package com.jlr.ecp.notification.config;

//@Configuration
//public class RedissonConfg {
//
//    /**
//     * 本地配置
//     * @return
//     */
//    @Bean
//    public RedissonClient redissonClient() {
//        Config config = new Config();
//        config.useSingleServer()
//                .setAddress("redis://127.0.0.1:6379" )
//                .setDatabase(1);
//        return Redisson.create(config);
//    }
//}
