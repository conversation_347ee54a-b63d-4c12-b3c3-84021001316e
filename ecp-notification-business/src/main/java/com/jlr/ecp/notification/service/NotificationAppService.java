package com.jlr.ecp.notification.service;


import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkDTO;

public interface NotificationAppService {

    /**
     * 更新短链状态。
     *
     * @param shortLinkDTO 短链数据传输对象，包含实例代码和电话号码。
     * @return 返回操作成功与否的布尔值。如果输入参数不合法或更新过程中发生异常，则返回false；否则返回true。
     */
    CommonResult<Boolean> updateSmsShortLink(ShortLinkDTO shortLinkDTO);
}
