package com.jlr.ecp.notification.req.task;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 任务编辑历史日志的请求入参
 *
 * <AUTHOR>
 * */

@Data
@Schema(description = "任务编辑历史日志的请求入参")
public class TaskHistoryReq extends PageParam {

    @Schema(description = "任务code")
    @NotBlank(message = "任务code不能为空")
    private String taskCode;
}
