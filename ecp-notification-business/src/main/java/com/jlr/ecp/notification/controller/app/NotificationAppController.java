package com.jlr.ecp.notification.controller.app;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkDTO;
import com.jlr.ecp.notification.service.NotificationAppService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;

@RestController
@RequestMapping("v1")
@Tag(name = "小程序端 - notification")
@Validated
@Slf4j
public class NotificationAppController {
    @Resource
    private NotificationAppService notificationAppService;

    @PostMapping("/update/sms/shortLink")
    @PermitAll
    @Operation(summary = "更新短链点击")
    public CommonResult<Boolean> updateSmsShortLink(@RequestBody ShortLinkDTO shortLinkDTO) {
        log.info("NotificationAppController, 更新短链点击, shortLinkDTO:{}", shortLinkDTO);
        return notificationAppService.updateSmsShortLink(shortLinkDTO);
    }

}
