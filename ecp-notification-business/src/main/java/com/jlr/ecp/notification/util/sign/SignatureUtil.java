package com.jlr.ecp.notification.util.sign;

import com.alibaba.fastjson.JSON;
import org.springframework.http.HttpHeaders;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class SignatureUtil {
    private static final String HMAC_SHA256 = "HmacSHA256";

    public static String generateSignature(String method, String path, String timestamp, String apiSecret) {
        try {
            // 构造待签名字符串
            String stringToSign = method + "\n" + path;

            // 使用时间戳和api_secret计算签名密钥
            byte[] signKeyBytes = hmacSha256(timestamp.getBytes(StandardCharsets.UTF_8),
                    apiSecret.getBytes(StandardCharsets.UTF_8));

            // 对待签名字符串进行签名，生成最终的签名
            byte[] signatureBytes = hmacSha256(stringToSign.getBytes(StandardCharsets.UTF_8), signKeyBytes);

            // 转换为十六进制表示形式
            return bytesToHex(signatureBytes);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static byte[] hmacSha256(byte[] data, byte[] key) throws Exception {
        Mac mac = Mac.getInstance(HMAC_SHA256);
        mac.init(new SecretKeySpec(key, HMAC_SHA256));
        return mac.doFinal(data);
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

//    public static void main(String[] args) throws Exception {
//        String method = "POST";
//        String path = "/business/sms/send/v1.0.0";
//        String timestamp = "1705752717";
//        String apiSecret = "rtXJGd1kcE1pUlw8yfHK44ykyINVDpdCSTkSpJo1";
//
//        String signature = generateSignature(method, path, timestamp, apiSecret);
//        System.out.println("Generated Signature: " + signature);
//    }

    public static HttpHeaders headers(Object body, String path, String httpMethod, String appKey, String secret) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put(AbstractSignature.HTTP_HEADER_CONTENT_TYPE, AbstractSignature.CONTENT_TYPE_JSON);
        // 加签
        SignatureEncoder signatureEncoder = new SignatureEncoder(path, httpMethod, headers, JSON.toJSONString(body).getBytes(StandardCharsets.UTF_8), appKey, secret);
        Map<String, Object> formParamsMap = new HashMap<>(0); // no form parameter
        signatureEncoder.build(new HashMap<>(0), formParamsMap);

        final HttpHeaders httpHeaders = new HttpHeaders();
        signatureEncoder.getHeaders().forEach(httpHeaders::add);
        return httpHeaders;
    }
}
