package com.jlr.ecp.notification.config;

//@Slf4j
////@Configuration
////@ConditionalOnExpression("#{!'local'.equals(environment['spring.profiles.active'])}")
//public class RedissonConfig implements EnvironmentAware {
//
//    private final Logger logger = LoggerFactory.getLogger(getClass());
//
////    @Autowired
////    private RedisConfigProperties redisConfigProperties;
//
//    private Environment environment;
//
//
//    //添加redisson的bean
////    @Primary
////    @Bean
//    public Redisson redisson() throws IOException {
//
//        Config config = new Config();
//        MasterSlaveServersConfig masterSlaveConfig = config.useMasterSlaveServers();
//        try (InputStream inputStream = getClass().getResourceAsStream("/redisson.yaml");
//             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
//            //config.fromYAML(new File("classpath:redisson.yaml"));
//            config.fromYAML(reader);
//        }
//
//
//
//        logger.info("init redisson here");
//        return (Redisson) Redisson.create(config);
//    }
//
//
//
//
//
//    public void setEnvironment(Environment environment) {
//
//    }
//}