package com.jlr.ecp.notification.enums.task;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum UploadFileDataTypeEnum {

    PHONE(1, "仅手机号"),
    VIN_PHONE(2, "VIN+手机号");

    private final Integer code;
    private final String description;

    /**
     * 根据 code 获取对应的枚举实例
     */
    public static UploadFileDataTypeEnum fromCode(Integer code) {
        for (UploadFileDataTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}