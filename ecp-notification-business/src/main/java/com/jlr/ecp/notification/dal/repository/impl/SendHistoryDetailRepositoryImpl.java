package com.jlr.ecp.notification.dal.repository.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.notification.dal.dataobject.history.SendHistoryDetail;
import com.jlr.ecp.notification.dal.mysql.history.HistoryDetailMapper;
import com.jlr.ecp.notification.dal.repository.SendHistoryDetailRepository;
import com.jlr.ecp.notification.dto.HistoryDetailIdempotentDTO;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkDTO;
import com.jlr.ecp.notification.enums.IsDeleteEnum;
import com.jlr.ecp.notification.enums.OpenShortLinkEnum;
import com.jlr.ecp.notification.enums.SmsSendResultEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class SendHistoryDetailRepositoryImpl extends ServiceImpl<HistoryDetailMapper, SendHistoryDetail>
        implements SendHistoryDetailRepository {

    @Resource
    private HistoryDetailMapper historyDetailMapper;

    private static final String PHONE_PREFIX = "+86";


    /**
     * 批量插入发送历史详情记录。
     *
     * @param sendHistoryDetailList 包含待插入的发送历史详情记录的列表。列表中的每个元素都应是一个SendHistoryDetail类型的对象，
     *                              表示一条发送详情记录。该方法将批量插入这些记录到相应的数据库表中
     */
    @Override
    public void batchInsertDetail(List<SendHistoryDetail> sendHistoryDetailList) {
        saveBatch(sendHistoryDetailList);
    }

    /**
     * 批量通过手机号和实例编码查询发送历史详情
     * @param phoneNumbers 手机号列表
     * @param instanceCode 实例编码
     * @return SendHistoryDetail列表，包含匹配的发送历史详情
     */
    @Override
    public List<SendHistoryDetail> batchQueryDetailByPhoneAndInstanceCode(List<String> phoneNumbers, String instanceCode) {
        if (CollectionUtils.isEmpty(phoneNumbers) || StringUtils.isBlank(instanceCode)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SendHistoryDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SendHistoryDetail::getSendPhone, phoneNumbers)
                .eq(SendHistoryDetail::getTaskInstanceCode, instanceCode)
                .eq(SendHistoryDetail::getIsDeleted, false);
        List<SendHistoryDetail> resp = null;
        try {
            resp = historyDetailMapper.selectList(wrapper);
        } catch (Exception e) {
            log.error("instanceCode和phone批量查询SendHistoryDetail,异常：", e);
        }
        return resp;
    }

    /**
     * 批量更新发送历史详情记录。
     *
     * @param sendHistoryDetailList 包含待更新的发送历史详情记录的列表。列表中的每个元素都应是一个SendHistoryDetail类型的对象，
     *                              表示一条发送详情记录。该方法将批量更新这些记录到相应的数据库表中
     */
    @Override
    public void batchUpdateByIds(List<SendHistoryDetail> sendHistoryDetailList) {
        historyDetailMapper.updateBatch(sendHistoryDetailList);
    }

    /**
     * 根据消息ID列表查询发送历史详情列表。
     *
     * @param msgIdList 消息ID列表，用于查询的条件之一。
     * @return 返回匹配查询条件的发送历史详情列表。如果查询过程中出现异常，则返回空列表。
     */
    @Override
    public List<SendHistoryDetail> queryByMsgIdList(List<String> msgIdList) {
        LambdaQueryWrapper<SendHistoryDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SendHistoryDetail::getMsgId, msgIdList)
                .eq(SendHistoryDetail::getIsDeleted, false);
        try {
            return historyDetailMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.error("根据msgIdList查询SendHistoryDetail异常:", e);
        }
        return new ArrayList<>();
    }

    /**
     * 获取当天全部需要发送的数据
     * @param taskCode 任务code
     * @param sendResult 发送状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return List<SendHistoryDetail>
     * */
    public List<SendHistoryDetail> querySmsDetailByTaskCodeAndTime(String taskCode, Integer sendResult,
                                                                   LocalDateTime startTime, LocalDateTime endTime) {
        log.info("获取指定taskCode全部需要发送的数据, taskCode:{}, sendResult:{}, startTime:{}, endTime:{}",
                taskCode, sendResult, startTime, endTime);
        LambdaQueryWrapper<SendHistoryDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SendHistoryDetail::getSendResult, sendResult);
        wrapper.eq(SendHistoryDetail::getTaskCode, taskCode);
        wrapper.eq(SendHistoryDetail::getIsDeleted, IsDeleteEnum.NO.getStatus());
        wrapper.ge(SendHistoryDetail::getCreatedTime, startTime);
        wrapper.le(SendHistoryDetail::getCreatedTime, endTime);
        List<SendHistoryDetail> resp = new ArrayList<>();
        try {
            resp = historyDetailMapper.selectList(wrapper);
        } catch (Exception e) {
            log.info("获取指定taskCode全部需要发送的数据异常:{}", e.getMessage());
        }
        return resp;
    }

    /**
     * 获取当天全部需要发送的数据
     * @param idList 任务列表
     * @param sendResult 发送状态
     * @return List<SendHistoryDetail>
     * */
    @Override
    public List<SendHistoryDetail> querySmsDetailByIds(List<Long> idList, Integer sendResult) {
        LambdaQueryWrapper<SendHistoryDetail> sendHistoryDetails = new LambdaQueryWrapper<>();
        sendHistoryDetails.in(SendHistoryDetail::getId, idList)
                .eq(SendHistoryDetail::getSendResult, sendResult)
                .eq(SendHistoryDetail::getIsDeleted, IsDeleteEnum.NO.getStatus());
        List<SendHistoryDetail> resp = new ArrayList<>();
        try {
            resp = historyDetailMapper.selectList(sendHistoryDetails);
        } catch (Exception e) {
            log.info("根据idList查询SendHistoryDetail异常:{}", e.getMessage());
        }
        return resp;
    }


    /**
     * 查询指定天数内的重试消息详情。
     * 如果提供了天数参数，则查询该天数内的消息详情，若未提供或提供天数大于3，则默认查询最近3天的详情。
     * 如果提供的天数小于等于0，则查询当天的详情。
     * @param day 指定的天数，可用于查询相应时间范围内的消息重试详情。
     * @return 返回一个消息详情列表，包含指定时间范围内的所有等待重试的消息详情。
     */
    @Override
    public List<SendHistoryDetail> queryRetryMsgDetail(Integer day) {
        if (Objects.isNull(day) ||day >= 1) {
            day = 3;
        }
        if (day <= 0) {
            day = 0;
        }
        LocalDate startDay = LocalDate.now();
        LocalDate endDay = LocalDate.now();
        if (day > 0) {
            startDay = LocalDate.now().minusDays(day);
            endDay = LocalDate.now().minusDays(1);
        }
        LocalDateTime startTime = LocalDateTime.of(startDay, LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(endDay, LocalTime.MAX);
        log.info("获取需要超时重发的数据，day:{}, startTime:{}, endTime:{}", day, startTime, endTime);
        LambdaQueryWrapper<SendHistoryDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(SendHistoryDetail::getCreatedTime, startTime, endTime);
        wrapper.eq(SendHistoryDetail::getSendResult, SmsSendResultEnum.WAITED.getCode());
        wrapper.eq(SendHistoryDetail::getIsDeleted, IsDeleteEnum.NO.getStatus());
        return historyDetailMapper.selectList(wrapper);
    }

    /**
     * 更新短链状态。
     *
     * @param shortLinkDTO 短链数据传输对象，包含实例代码和电话号码。
     * @return 返回操作成功与否的布尔值。如果输入参数不合法或更新过程中发生异常，则返回false；否则返回true。
     */
    @Override
    public Boolean updateShortLinkStatus(ShortLinkDTO shortLinkDTO) {
        if (Objects.isNull(shortLinkDTO) || StringUtils.isBlank(shortLinkDTO.getInstanceCode())
            || StringUtils.isBlank(shortLinkDTO.getPhoneNumber())) {
            return false;
        }
        SendHistoryDetail sendHistoryDetail = new SendHistoryDetail();
        sendHistoryDetail.setOpenShortLink(OpenShortLinkEnum.YES.getStatus());
        LambdaUpdateWrapper<SendHistoryDetail> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SendHistoryDetail::getTaskInstanceCode, shortLinkDTO.getInstanceCode())
                .eq(SendHistoryDetail::getSendPhone, shortLinkDTO.getPhoneNumber())
                .eq(SendHistoryDetail::getCarVin, shortLinkDTO.getVinCode())
                .eq(SendHistoryDetail::getIsDeleted, false);
        try {
            return historyDetailMapper.update(sendHistoryDetail, updateWrapper) > 0;
        } catch (Exception e) {
            log.error("更新短链点击异常:", e);
        }
        return false;
    }

    /**
     * 根据实例编码查询打开次数。
     *
     * @param instanceCode 任务实例编码，用于查询相应的发送历史详情。
     * @return 返回符合条件的打开短链接次数，如果实例编码为空或查询结果为空，则返回0L。
     */
    @Override
    public Long queryOpenCountByInstanceCode(String instanceCode) {
        if (StringUtils.isBlank(instanceCode)) {
            return 0L;
        }
        LambdaQueryWrapper<SendHistoryDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SendHistoryDetail::getTaskInstanceCode, instanceCode)
                .eq(SendHistoryDetail::getOpenShortLink, OpenShortLinkEnum.YES.getStatus())
                .eq(SendHistoryDetail::getIsDeleted, false);
        return historyDetailMapper.selectCount(queryWrapper);
    }

    /**
     * 根据幂等性参数查询发送详情
     *
     * @param detailIdempotentDTO 请求的幂等性参数对象，包含任务码、手机号、时间范围和发送状态等信息
     * @return 返回一个列表，包含符合条件的发送历史详情对象
     */
    @Override
    public List<SendHistoryDetail> queryHistoryDetailByIdempotent(HistoryDetailIdempotentDTO detailIdempotentDTO) {
        log.info("根据幂等性参数查询发送详情, detailIdempotentDTO:{}", detailIdempotentDTO);
        List<SendHistoryDetail> resp = new ArrayList<>();
        if (Objects.isNull(detailIdempotentDTO)) {
            return resp;
        }
        LambdaQueryWrapper<SendHistoryDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SendHistoryDetail::getTaskCode, detailIdempotentDTO.getTaskCode())
                .ge(SendHistoryDetail::getCreatedTime, detailIdempotentDTO.getStartTime())
                .le(SendHistoryDetail::getCreatedTime, detailIdempotentDTO.getEndTime())
                .eq(SendHistoryDetail::getIsDeleted, false);
        try {
            resp = historyDetailMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.info("根据幂等性参数查询发送详情异常:{}", e.getMessage());
        }
        return resp;
    }

    /**
     * 根据手机号查询发送详情(过滤待发送)
     *
     * @param phone 手机号
     * @param businessCode 业务编码
     * @return 返回一个列表，包含符合条件的发送历史详情对象
     */
    @Override
    public List<SendHistoryDetail> getHistoryDetailByPhone(String phone, String businessCode) {
        if (StringUtils.isBlank(phone) || StringUtils.isBlank(businessCode)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SendHistoryDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SendHistoryDetail::getSendPhone, Arrays.asList(phone, PHONE_PREFIX+phone))
                .ne(SendHistoryDetail::getSendResult, SmsSendResultEnum.WAITED.getCode())
                .eq(SendHistoryDetail::getIsDeleted, false)
                .eq(SendHistoryDetail::getBusinessCode, businessCode)
                .orderByAsc(SendHistoryDetail::getSendTime);
        return historyDetailMapper.selectList(queryWrapper);
    }

    /**
     * 根据VIN查询发送详情(过滤待发)
     *
     * @param carVin 车辆编码
     * @param businessCode 业务编码
     * @return 返回一个列表，包含符合条件的发送历史详情对象
     */
    @Override
    public List<SendHistoryDetail> getHistoryDetailByVin(String carVin, String businessCode) {
        if (StringUtils.isBlank(carVin) || StringUtils.isBlank(businessCode)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SendHistoryDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SendHistoryDetail::getCarVin, carVin)
                .ne(SendHistoryDetail::getSendResult, SmsSendResultEnum.WAITED.getCode())
                .eq(SendHistoryDetail::getIsDeleted, false)
                .eq(SendHistoryDetail::getBusinessCode, businessCode)
                .orderByAsc(SendHistoryDetail::getSendTime);
        return historyDetailMapper.selectList(queryWrapper);
    }

    /**
     * 根据实例代码查询发送计数
     *
     * @param instanceCode 实例代码，用于查询发送历史记录
     * @return 发送历史记录的总数如果查询条件无效或没有找到匹配的记录，则返回0
     */
    @Override
    public Integer querySendCountByInstanceCode(String instanceCode) {
        if (StringUtils.isBlank(instanceCode)) {
            return 0;
        }
        LambdaQueryWrapperX<SendHistoryDetail> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(SendHistoryDetail::getTaskInstanceCode, instanceCode)
                .eq(SendHistoryDetail::getIsDeleted, false);
        Long count = historyDetailMapper.selectCount(queryWrapper);
        log.info("使用instanceCode查询发送总数, instanceCode:{}, count:{}", instanceCode, count);
        return count.intValue();
    }

    /**
     * 根据实例编码和触达状态查询触达详情的数量
     *
     * @param instanceCode 实例编码，用于标识特定的实例
     * @param reachStatus 触达状态列表，用于过滤查询结果
     * @return 返回触达详情的数量如果查询条件不满足，则返回0
     */
    @Override
    public Integer queryReachByInstanceAndStatus(String instanceCode, List<String> reachStatus) {
        if (StringUtils.isBlank(instanceCode) || CollUtil.isEmpty(reachStatus)) {
            return 0;
        }
        LambdaQueryWrapperX<SendHistoryDetail> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(SendHistoryDetail::getTaskInstanceCode, instanceCode)
                .in(SendHistoryDetail::getMsgResult, reachStatus)
                .eq(SendHistoryDetail::getIsDeleted, false);
        Long count = historyDetailMapper.selectCount(queryWrapper);
        log.info("根据实例编码和状态查询触达详情, instanceCode:{}, reachStatus:{}, count:{}", instanceCode, reachStatus, count);
        return count.intValue();
    }
}
