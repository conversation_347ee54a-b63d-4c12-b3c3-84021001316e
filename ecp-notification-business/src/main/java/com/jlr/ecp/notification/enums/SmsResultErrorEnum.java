package com.jlr.ecp.notification.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 短信发送结果错误码
 *
 * <AUTHOR>
 * */

@AllArgsConstructor
@Getter
public enum SmsResultErrorEnum {
    SUCCESS("0", "successfully"),
    ACCOUNT_INVALID("1", "Account number is invalid"),
    PASSWORD_WRONG("2", "wrong password"),
    MSGID_TOO_LONG("3", "The msgid is too long and must not exceed 64 bits"),
    WRONG_NUMBER("4", "Wrong number/restricted carrier number"),
    PHONE_NUMBER_TOO_LONG("5", "The number of cell phone numbers exceeds the maximum limit"),
    MESSAGE_TOO_LONG("6", "The text message content exceeds the maximum limit"),
    SUBNUMBER_INVALID("7", "The extension subnumber is invalid"),
    TIME_FORMAT_ERROR("8","Timing time format error"),
    PHONE_NUMBER_EMPTY("14", "The phone number is empty"),
    USER_DISABLED("19", "The user is banned or disabled"),
    IP_AUTHENTICATION_FAIL("20", "IP authentication failed"),
    MESSAGE_EMPTY("21", "The text message is empty"),
    NUMBER_AVAILABLE("24", "No number available"),
    SMS_EXCEED_LIMIT("25", "Bulk submission of SMS exceeds the maximum limit"),
    SYSTEM_BUSY("98", "The system is busy"),
    MESSAGE_FORMAT_ERROR("99", "Message format error"),
    MESSAGE_CONTENT_ERROR("100", "message content variables existence null");

    /**
     * 错误码
     * */
    private final String code;

    /**
     * 错误描述
     * */
    private final String desc;

    /**
     * 更加错误码返回错误枚举
     * @param code 错误码
     * @return SmsResultErrorEnum
     * */
    public static SmsResultErrorEnum getSmsResultErrorEnumByCode(String code) {
        SmsResultErrorEnum[] errorEnums = SmsResultErrorEnum.values();
        for (SmsResultErrorEnum errorEnum : errorEnums) {
            if (errorEnum.getCode().equals(code)) {
                return errorEnum;
            }
        }
        return null;
    }

    /**
     * 更加错误码返回错误描述
     * @param code 错误码
     * @return SmsResultErrorEnum
     * */
    public static String getErrorDescByCode(String code) {
        SmsResultErrorEnum errorEnum = getSmsResultErrorEnumByCode(code);
        if (Objects.isNull(errorEnum)) {
            return "";
        }
        return errorEnum.getDesc();
    }
}
