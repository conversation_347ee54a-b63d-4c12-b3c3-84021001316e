package com.jlr.ecp.notification.service.impl;

import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.notification.dal.dataobject.shortlink.NotificationConfigDO;
import com.jlr.ecp.notification.dal.dataobject.shortlink.NotificationConfigModifyLog;
import com.jlr.ecp.notification.dal.repository.ShortLinkConfigRepository;
import com.jlr.ecp.notification.dal.repository.ShortLinkModifyLogRepository;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkConfigDTO;
import com.jlr.ecp.notification.service.ShortLinkConfigService;
import com.jlr.ecp.notification.vo.shortlink.ShortLinkConfigDetailVO;
import com.jlr.ecp.notification.vo.shortlink.ShortLinkConfigModifyLogVO;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class ShortLinkConfigServiceImpl implements ShortLinkConfigService {
    @Resource
    private ShortLinkConfigRepository shortLinkConfigRepository;

    @Resource
    private ShortLinkModifyLogRepository shortLinkModifyLogRepository;

    private static final String LINE_FEED = "<br>";


    /**
     * 保存或更新短信短链配置
     *
     * @param shortLinkConfigDTO 短链配置数据传输对象，包含配置的相关信息
     * @return 操作是否成功的布尔值，插入或更新成功返回true，否则返回false
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveOrUpdateSmsShortLinkConfig(ShortLinkConfigDTO shortLinkConfigDTO) {
        log.info("保存或更新短信短链配置, shortLinkConfigDTO:{}", shortLinkConfigDTO);
        if (Objects.isNull(shortLinkConfigDTO)) {
            return false;
        }
        NotificationConfigDO newConfigDO = buildNotificationConfigDO(shortLinkConfigDTO);
        NotificationConfigDO oldConfigDO = shortLinkConfigRepository.queryShortLinkConfigByBrandCode(shortLinkConfigDTO.getBrandCode());
        if (Objects.isNull(oldConfigDO)) {
            return shortLinkConfigRepository.insertShortLinkConfig(newConfigDO);
        }
        NotificationConfigModifyLog modifyLog = buildNotificationConfigModifyLog(oldConfigDO, newConfigDO);
        if (Objects.isNull(modifyLog)) {
            log.info("保存或更新短信短链配置,没有更新短链配置");
            return true;
        }
        shortLinkModifyLogRepository.insertShortLinkModifyLog(modifyLog);
        return shortLinkConfigRepository.updateShortLinkConfig(newConfigDO);
    }

    /**
     * 根据ID和品牌代码获取短信短链接配置
     *
     * @param brandCode 品牌代码
     * @return 返回配置的详细信息对象
     */
    @Override
    public ShortLinkConfigDetailVO getSmsShortConfig(String brandCode) {
        log.info("根据ID和品牌代码获取短信短链接配置, brandCode:{}", brandCode);
        NotificationConfigDO configDO = shortLinkConfigRepository.queryShortLinkConfigByBrandCode(brandCode);
        if (Objects.isNull(configDO)) {
            return null;
        }
        return buildShortLinkConfigDetailVO(configDO);
    }

    /**
     * 根据配置ID查询短链接配置修改日志
     *
     * @param configId 短链接配置的唯一标识符
     * @return 一个包含短链接配置修改日志的列表，以ShortLinkConfigModifyLogVO的形式
     */
    @Override
    public List<ShortLinkConfigModifyLogVO> queryModifyLog(Long configId) {
        log.info("根据配置ID查询短链接配置修改日志, configId:{}", configId);
        List<NotificationConfigModifyLog> modifyLogList = shortLinkModifyLogRepository.queryShortLinkModifyLog(configId);
        List<ShortLinkConfigModifyLogVO> resp = new ArrayList<>();
        for (NotificationConfigModifyLog configModifyLog : modifyLogList) {
            resp.add(buildShortLinkConfigModifyLogVO(configModifyLog));
        }
        return resp;
    }

    /**
     * 构建短链接配置修改日志对象
     *
     * @param notificationConfigModifyLog 通知配置修改日志对象，包含配置修改的详细信息
     * @return 返回构建的短链接配置修改日志对象
     */
    private ShortLinkConfigModifyLogVO buildShortLinkConfigModifyLogVO(NotificationConfigModifyLog notificationConfigModifyLog) {
        ShortLinkConfigModifyLogVO shortLinkConfigModifyLogVO = new ShortLinkConfigModifyLogVO();
        shortLinkConfigModifyLogVO.setConfigId(notificationConfigModifyLog.getConfigId());
        shortLinkConfigModifyLogVO.setModifyContent(notificationConfigModifyLog.getModifyContent());
        shortLinkConfigModifyLogVO.setModifyDate(notificationConfigModifyLog.getModifyDate());
        shortLinkConfigModifyLogVO.setModifyUser(notificationConfigModifyLog.getModifyUser());
        return shortLinkConfigModifyLogVO;
    }

    /**
     * 根据通知配置对象构建短链接配置详情VO
     *
     * @param notificationConfigDO 从数据库中查询到的通知配置对象
     * @return 构建的ShortLinkConfigDetailVO对象，包含通知配置的关键信息
     */
    private ShortLinkConfigDetailVO buildShortLinkConfigDetailVO(NotificationConfigDO notificationConfigDO) {
        ShortLinkConfigDetailVO shortLinkConfigDetailVO = new ShortLinkConfigDetailVO();
        shortLinkConfigDetailVO.setConfigId(notificationConfigDO.getId());
        shortLinkConfigDetailVO.setBrandCode(notificationConfigDO.getBrandCode());
        shortLinkConfigDetailVO.setPiviServiceSpu(notificationConfigDO.getPiviServiceSpu());
        shortLinkConfigDetailVO.setRemoteServiceSpu(notificationConfigDO.getRemoteServiceSpu());
        shortLinkConfigDetailVO.setRemoteServiceSpuName(notificationConfigDO.getRemoteServiceSpuName());
        shortLinkConfigDetailVO.setPiviServiceSpuName(notificationConfigDO.getPiviServiceSpuName());
        return shortLinkConfigDetailVO;
    }

    /**
     * 构建通知配置修改日志对象
     *
     * @param oldConfigDO 数据库中已经存在的配置信息
     * @param newConfigDO  即将更新的配置信息
     * @return 返回构建完成的通知配置修改日志对象
     */
    private NotificationConfigModifyLog buildNotificationConfigModifyLog(NotificationConfigDO oldConfigDO,
                                                                         NotificationConfigDO newConfigDO) {
        String updateContent = getConfigDOUpdateContent(oldConfigDO, newConfigDO);
        if (StringUtil.isBlank(updateContent)) {
            return null;
        }
        NotificationConfigModifyLog notificationConfigModifyLog = new NotificationConfigModifyLog();
        notificationConfigModifyLog.setConfigId(oldConfigDO.getId());
        notificationConfigModifyLog.setModifyContent(updateContent);
        notificationConfigModifyLog.setModifyDate(LocalDateTime.now());
        if (StringUtil.isNotEmpty(WebFrameworkUtils.getLoginUserName())) {
            notificationConfigModifyLog.setModifyUser(WebFrameworkUtils.getLoginUserName());
        } else {
            notificationConfigModifyLog.setModifyUser("system");
        }
        return notificationConfigModifyLog;
    }

    /**
     * 获取配置更新日志
     *
     * @param oldConfigDO 旧的通知配置对象
     * @param newConfigDO 新的通知配置对象
     * @return 返回包含更新信息的字符串，若无更新则返回空字符串
     */
    private String getConfigDOUpdateContent(NotificationConfigDO oldConfigDO, NotificationConfigDO newConfigDO) {
        String resp = "";
        if (Objects.isNull(oldConfigDO) || Objects.isNull(newConfigDO)) {
            return resp;
        }
        if (!oldConfigDO.getBrandCode().equals(newConfigDO.getBrandCode())) {
            log.info("获取配置更新日志, 配置信息的brandCode不一致, oldConfigDO:{}, newConfigDO:{}", oldConfigDO, newConfigDO);
            return resp;
        }
        if (!oldConfigDO.getRemoteServiceSpu().equals(newConfigDO.getRemoteServiceSpu())) {
            resp += "Remote Service 到期短信修改为：" + newConfigDO.getRemoteServiceSpuName() + LINE_FEED;
        }
        if (!oldConfigDO.getPiviServiceSpu().equals(newConfigDO.getPiviServiceSpu())) {
            resp += "Online Subscription 到期短信修改为：" + newConfigDO.getPiviServiceSpuName() + LINE_FEED;
        }
        return resp;
    }

    /**
     * 根据短链接配置DTO构建通知配置实体
     *
     * @param shortLinkConfigDTO 短链接配置DTO，包含通知配置所需的信息
     * @return NotificationConfigDO 根据短链接配置DTO构建的通知配置实体
     */
    private NotificationConfigDO buildNotificationConfigDO(ShortLinkConfigDTO shortLinkConfigDTO) {
        NotificationConfigDO notificationConfigDO = new NotificationConfigDO();
        if (Objects.nonNull(shortLinkConfigDTO.getConfigId())) {
            notificationConfigDO.setId(shortLinkConfigDTO.getConfigId());
        }
        notificationConfigDO.setRemoteServiceSpu(shortLinkConfigDTO.getRemoteServiceSpu());
        notificationConfigDO.setRemoteServiceSpuName(shortLinkConfigDTO.getRemoteServiceSpuName());
        notificationConfigDO.setPiviServiceSpu(shortLinkConfigDTO.getPiviServiceSpu());
        notificationConfigDO.setPiviServiceSpuName(shortLinkConfigDTO.getPiviServiceSpuName());
        notificationConfigDO.setBrandCode(shortLinkConfigDTO.getBrandCode());
        notificationConfigDO.setUpdatedTime(LocalDateTime.now());
        return notificationConfigDO;
    }
}
