package com.jlr.ecp.notification.controller.app;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.dto.shortlink.ManualShortLinkClickDTO;
import com.jlr.ecp.notification.service.manual.ManualShortLinkClickService;
import com.jlr.ecp.notification.vo.shortlink.QrCodeConfigParamVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;

@RestController
@RequestMapping("/v1/manual/shortLink")
@Tag(name = "手动短链点击统计")
@Validated
public class ManualShortLinkController {
    @Resource
    private ManualShortLinkClickService manualShortLinkClickService;

    @PostMapping("/updateClick")
    @PermitAll
    @Operation(summary = "手动短链点击率更新")
    public CommonResult<String> updateShortLinkClick(@RequestBody @Validated ManualShortLinkClickDTO clickDTO) {
        return manualShortLinkClickService.updateShortLinkClick(clickDTO);
    }


    @GetMapping("/getQrCode/configParam")
    @PermitAll
    @Operation(summary = "获取太阳码的配置参数")
    public CommonResult<QrCodeConfigParamVO> getQrCodeParam(@RequestParam(value = "configId") String configId) {
        return manualShortLinkClickService.getQrCodeConfigParam(configId);
    }
}
