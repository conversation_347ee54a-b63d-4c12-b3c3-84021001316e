package com.jlr.ecp.notification.kafka.listener;

import com.alibaba.fastjson.JSON;
import com.jlr.ecp.notification.kafka.message.EcouponOrderMessage;
import com.jlr.ecp.notification.kafka.send.template.SmsSendTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

@Component
@Slf4j
public class EcouponOrderRefundListener {
    @Resource
    private SmsSendTemplate smsSendTemplate;

    @KafkaListener(topics = "order-ecoupon-refund-topic", groupId = "order-ecoupon-refund-group", batch = "true",
            properties = "max.poll.records:100")
    public void listen(List<ConsumerRecord<String, String >> records) {
        log.info("LRE体验券订单仅退款成功消息，records:{}", records);
        List<EcouponOrderMessage> ecouponOrderMessageList = buildEcouponOrderMessageList(records);
        for (EcouponOrderMessage ecouponOrderMessage : ecouponOrderMessageList) {
            smsSendTemplate.sendBgOrLreSmsMsg(ecouponOrderMessage);
        }
    }

    /**
     *  构建订单消息
     *  @param records 消息体
     *  @return  List<EcouponEcouponOrderMessage>
     * */
    public List<EcouponOrderMessage> buildEcouponOrderMessageList(List<ConsumerRecord<String, String >> records) {
        List<EcouponOrderMessage> ecouponOrderMessageList = new ArrayList<>();
        if (CollectionUtils.isEmpty(records)) {
            return ecouponOrderMessageList;
        }
        for (ConsumerRecord<String, String> record : records) {
            EcouponOrderMessage orderMessage = JSON.parseObject(record.value(), EcouponOrderMessage.class);
            ecouponOrderMessageList.add(orderMessage);
        }
        return ecouponOrderMessageList;
    }

}
