package com.jlr.ecp.notification.dto.shortlink;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Schema(description = "短链配置编辑DTO")
@Data
public class ShortLinkConfigDTO implements Serializable {
    @Schema(description = "短链配置id", requiredMode = Schema.RequiredMode.REQUIRED, example = "3355")
    private Long configId;

    @NotBlank(message = "remoteService短链商品SPU不能为空")
    @Schema(description = "remoteService短链商品SPU", requiredMode = Schema.RequiredMode.REQUIRED, example = "spuxxx1")
    private String remoteServiceSpu;

    @NotBlank(message = "remoteService短链商品名称不能为空")
    @Schema(description = "remoteService短链商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "spuxxx1")
    private String remoteServiceSpuName;

    @NotBlank(message = "PIVIService短链商品SPU不能为空")
    @Schema(description = "PIVIService短链商品SPU", requiredMode = Schema.RequiredMode.REQUIRED, example = "spuxxx1")
    private String piviServiceSpu;

    @NotBlank(message = "PIVIService短链商品名称不能为空")
    @Schema(description = "PIVIService短链商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "spuxxx1")
    private String piviServiceSpuName;

    @NotBlank(message = "品牌标识不能为空")
    @Schema(description = "品牌表示", requiredMode = Schema.RequiredMode.REQUIRED,example = "LAN:路虎，JAG：捷豹")
    private String brandCode;
}
