package com.jlr.ecp.notification.dal.dataobject.shortlink;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_minicode_config")
public class MiniCodeConfigDO extends BaseDO {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("record_code")
    private String recordCode;

    @TableField("wmp_brand")
    private String wmpBrand;

    @TableField("route_page_type")
    private String routePageType;

    @TableField("product_code_param")
    private String productCodeParam;

    @TableField("product_name_param")
    private String productNameParam;

    @TableField("param_1")
    private String param1;

    @TableField("param_2")
    private String param2;

    @TableField("param_3")
    private String param3;

    @TableField("param_4")
    private String param4;

    @TableField("param_5")
    private String param5;

    @TableField("tenant_id")
    private Integer tenantId;
}
