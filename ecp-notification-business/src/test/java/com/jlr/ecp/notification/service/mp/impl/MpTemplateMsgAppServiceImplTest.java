package com.jlr.ecp.notification.service.mp.impl;

import com.jlr.ecp.notification.dal.dataobject.mp.MpNoticeExpireSubscriptionDO;
import com.jlr.ecp.notification.dal.dataobject.mp.MpNoticeOrderSubscriptionDO;
import com.jlr.ecp.notification.dal.repository.mp.MpNoticeExpireSubscriptionRepository;
import com.jlr.ecp.notification.dal.repository.mp.MpNoticeOrderSubscriptionRepository;
import com.jlr.ecp.notification.dto.mp.MpTemplateMsgSubscriptionDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MpTemplateMsgAppServiceImplTest {

    @Mock
    private MpNoticeOrderSubscriptionRepository mockMpNoticeOrderRepository;
    @Mock
    private MpNoticeExpireSubscriptionRepository mockMpNoticeExpireRepository;

    @InjectMocks
    private MpTemplateMsgAppServiceImpl mpTemplateMsgAppServiceImplUnderTest;

    @Test
    public void testSaveMpTemplateMsgSubscribe() {
        // Setup
        final MpTemplateMsgSubscriptionDTO mpTemplateMsgDTO = MpTemplateMsgSubscriptionDTO.builder()
                .jlrId("jlrId")
                .openId("openId")
                .orderSuccessFlag(1)
                .orderCancelFlag(1)
                .orderNo("orderNo")
                .serviceExpirationConsent(1)
                .build();

        // Configure MpNoticeOrderSubscriptionRepository.queryOrderSubscribeByOrderNoAndJlrId(...).
        final MpNoticeOrderSubscriptionDO orderSubscriptionDO = MpNoticeOrderSubscriptionDO.builder()
                .jlrId("jlrId")
                .openId("openId")
                .orderSuccessFlag(1)
                .orderCancelFlag(1)
                .orderNo("orderNo")
                .build();
        when(mockMpNoticeOrderRepository.queryOrderSubscribeByOrderNoAndJlrId("jlrId", "orderNo"))
                .thenReturn(orderSubscriptionDO);

        // Configure MpNoticeExpireSubscriptionRepository.queryExpireSubscribeByJlrAndOpenId(...).
        final MpNoticeExpireSubscriptionDO expireSubscriptionDO = MpNoticeExpireSubscriptionDO.builder()
                .jlrId("jlrId")
                .openId("openId")
                .serviceExpirationConsent(1)
                .build();
        when(mockMpNoticeExpireRepository.queryExpireSubscribeByJlrAndOpenId("jlrId", "openId"))
                .thenReturn(expireSubscriptionDO);

        // Run the test
        final String result = mpTemplateMsgAppServiceImplUnderTest.saveMpTemplateMsgSubscribe(mpTemplateMsgDTO);

        // Verify the results
        assertEquals("保存小程序模板消息订阅信息成功", result);
        verify(mockMpNoticeOrderRepository).saveOrUpdate(orderSubscriptionDO);
        verify(mockMpNoticeExpireRepository).saveOrUpdate(expireSubscriptionDO);
    }

    @Test
    public void testSaveMpTemplateMsgSubscribe_OrderNull() {
        // Setup
        final MpTemplateMsgSubscriptionDTO mpTemplateMsgDTO = MpTemplateMsgSubscriptionDTO.builder()
                .jlrId("jlrId")
                .openId("openId")
                .orderSuccessFlag(1)
                .orderCancelFlag(1)
                .orderNo("orderNo")
                .serviceExpirationConsent(1)
                .build();

        when(mockMpNoticeOrderRepository.queryOrderSubscribeByOrderNoAndJlrId("jlrId", "orderNo"))
                .thenReturn(null);

        // Configure MpNoticeExpireSubscriptionRepository.queryExpireSubscribeByJlrAndOpenId(...).
        final MpNoticeExpireSubscriptionDO expireSubscriptionDO = MpNoticeExpireSubscriptionDO.builder()
                .jlrId("jlrId")
                .openId("openId")
                .serviceExpirationConsent(1)
                .build();
        when(mockMpNoticeExpireRepository.queryExpireSubscribeByJlrAndOpenId("jlrId", "openId"))
                .thenReturn(expireSubscriptionDO);

        // Run the test
        final String result = mpTemplateMsgAppServiceImplUnderTest.saveMpTemplateMsgSubscribe(mpTemplateMsgDTO);

        // Verify the results
        assertEquals("保存小程序模板消息订阅信息成功", result);
        verify(mockMpNoticeOrderRepository).saveOrUpdate(
                MpNoticeOrderSubscriptionDO.builder()
                        .jlrId(mpTemplateMsgDTO.getJlrId())
                        .openId(mpTemplateMsgDTO.getOpenId())
                        .orderSuccessFlag(mpTemplateMsgDTO.getOrderSuccessFlag())
                        .orderCancelFlag(mpTemplateMsgDTO.getOrderCancelFlag())
                        .orderNo(mpTemplateMsgDTO.getOrderNo())
                        .build()
        );
        verify(mockMpNoticeExpireRepository).saveOrUpdate(expireSubscriptionDO);
    }


    @Test
    public void testSaveMpTemplateMsgSubscribe_subscribeNull() {
        // Setup
        final MpTemplateMsgSubscriptionDTO mpTemplateMsgDTO = MpTemplateMsgSubscriptionDTO.builder()
                .jlrId("jlrId")
                .openId("openId")
                .orderSuccessFlag(1)
                .orderCancelFlag(1)
                .orderNo("orderNo")
                .serviceExpirationConsent(1)
                .build();

        // Configure MpNoticeOrderSubscriptionRepository.queryOrderSubscribeByOrderNoAndJlrId(...).
        final MpNoticeOrderSubscriptionDO orderSubscriptionDO = MpNoticeOrderSubscriptionDO.builder()
                .jlrId("jlrId")
                .openId("openId")
                .orderSuccessFlag(1)
                .orderCancelFlag(1)
                .orderNo("orderNo")
                .build();
        when(mockMpNoticeOrderRepository.queryOrderSubscribeByOrderNoAndJlrId("jlrId", "orderNo"))
                .thenReturn(orderSubscriptionDO);

        // Configure MpNoticeExpireSubscriptionRepository.queryExpireSubscribeByJlrAndOpenId(...).
        final MpNoticeExpireSubscriptionDO expireSubscriptionDO = MpNoticeExpireSubscriptionDO.builder()
                .jlrId("jlrId")
                .openId("openId")
                .serviceExpirationConsent(1)
                .build();
        when(mockMpNoticeExpireRepository.queryExpireSubscribeByJlrAndOpenId("jlrId", "openId"))
                .thenReturn(null);

        // Run the test
        final String result = mpTemplateMsgAppServiceImplUnderTest.saveMpTemplateMsgSubscribe(mpTemplateMsgDTO);

        // Verify the results
        assertEquals("保存小程序模板消息订阅信息成功", result);
        verify(mockMpNoticeOrderRepository).saveOrUpdate(orderSubscriptionDO);
        verify(mockMpNoticeExpireRepository).saveOrUpdate(
                MpNoticeExpireSubscriptionDO.builder()
                        .jlrId(mpTemplateMsgDTO.getJlrId())
                        .openId(mpTemplateMsgDTO.getOpenId())
                        .serviceExpirationConsent(mpTemplateMsgDTO.getServiceExpirationConsent())
                        .build()
        );
    }

}
