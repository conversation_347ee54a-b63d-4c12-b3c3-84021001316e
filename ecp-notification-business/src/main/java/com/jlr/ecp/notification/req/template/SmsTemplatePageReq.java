package com.jlr.ecp.notification.req.template;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 *  模板列表页请求的入参
 *
 * <AUTHOR>
 * */

@Data
@Schema(description = "模板列表页请求的入参")
public class SmsTemplatePageReq extends PageParam {

    @Schema(description = "业务线code")
    private List<String> businessCode;

    @Schema(description = "排序: 0-正序 1-倒叙")
    private Integer timeSortType;


    @Schema(description = "模板类型 1自动配置 2手动配置")
    private Integer templateType;


    @Schema(description = "模板类型排序, 0:升序 1:降序")
    private Integer templateTypeSort;
}
