package com.jlr.ecp.notification.dal.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.notification.dal.dataobject.task.auto.AutoTaskConditionDO;

import java.util.List;
import java.util.Map;

public interface AutoTaskConditionRepository extends IService<AutoTaskConditionDO> {

    /**
     * 根据条件类型查询任务条件
     *
     * @param type 条件类型，用于筛选特定类型的自动任务条件
     * @return 返回符合条件类型且未被删除的任务条件列表
     */
    List<AutoTaskConditionDO> queryConditionByType(Integer type);

    /**
     * 根据条件ID列表获取未删除的条件信息, key：conditionType
     *
     * @param conditionIdList 条件ID列表，用于指定需要查询的条件的ID
     * @return 返回一个自动任务条件对象列表，包含未被删除的条件信息
     */
    Map<Integer, AutoTaskConditionDO> getConditionTypeMapByIds(List<String> conditionIdList);

    /**
     * 根据条件ID列表获取条件信息列表
     *
     * @param conditionIdList 条件ID列表，用于查询条件信息
     * @return 返回一个AutoTaskConditionDO对象列表，包含符合条件ID列表的条件信息
     */
    List<AutoTaskConditionDO> getConditionByIdList(List<String> conditionIdList);
}
