package com.jlr.ecp.notification.service;


import com.jlr.ecp.notification.dto.shortlink.ShortLinkConfigDTO;
import com.jlr.ecp.notification.vo.shortlink.ShortLinkConfigDetailVO;
import com.jlr.ecp.notification.vo.shortlink.ShortLinkConfigModifyLogVO;

import java.util.List;

public interface ShortLinkConfigService {

    /**
     * 保存或更新短信短链配置
     *
     * @param shortLinkConfigDTO 短链配置数据传输对象，包含配置的相关信息
     * @return 操作是否成功的布尔值，插入或更新成功返回true，否则返回false
     */
    Boolean saveOrUpdateSmsShortLinkConfig(ShortLinkConfigDTO shortLinkConfigDTO);

    /**
     * 根据ID和品牌代码获取短信短链接配置
     *
     * @param brandCode 品牌代码
     * @return 返回配置的详细信息对象
     */
    ShortLinkConfigDetailVO getSmsShortConfig(String brandCode);

    /**
     * 根据配置ID查询短链接配置修改日志
     *
     * @param configId 短链接配置的唯一标识符
     * @return 一个包含短链接配置修改日志的列表，以ShortLinkConfigModifyLogVO的形式
     */
    List<ShortLinkConfigModifyLogVO> queryModifyLog(Long configId);
}
