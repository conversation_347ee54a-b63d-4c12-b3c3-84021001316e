package com.jlr.ecp.notification.dal.repository.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.notification.dal.dataobject.valet.OrderNotificationDetailDO;
import com.jlr.ecp.notification.dal.mysql.valet.OrderNotificationDetailDOMapper;
import com.jlr.ecp.notification.dal.repository.OrderNotificationDetailRepository;
import com.jlr.ecp.notification.enums.SmsSendResultEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
@Slf4j
public class OrderNotificationDetailRepositoryImpl extends ServiceImpl<OrderNotificationDetailDOMapper, OrderNotificationDetailDO>
        implements OrderNotificationDetailRepository {

    private static final String PHONE_PREFIX = "+86";

    @Override
    public boolean insert(OrderNotificationDetailDO orderNotificationDetailDO) {
        return save(orderNotificationDetailDO);
    }

    /**
     * 根据消息ID列表查询订单通知详情信息
     *
     * @param msgIdList 消息ID列表，用于查询对应的订单通知详情
     * @return 返回与消息ID列表匹配的未删除的订单通知详情列表
     */
    @Override
    public List<OrderNotificationDetailDO> selectByMsgIdList(List<String> msgIdList) {
        if (CollUtil.isEmpty(msgIdList)) {
            log.info("根据消息ID列表查询订单通知详情信息，msgId列表为空");
            return new ArrayList<>();
        }
        log.info("根据消息ID列表查询订单通知详情信息, msgIdList的数量:{}", msgIdList.size());
        LambdaQueryWrapper<OrderNotificationDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrderNotificationDetailDO::getMsgId, msgIdList)
                .eq(OrderNotificationDetailDO::getIsDeleted, false);
        return list(queryWrapper);
    }

    /**
     * 根据电话号码获取礼宾订单详情
     *
     * @param phone 电话号码，用于查询订单通知详情
     * @return 返回一个列表，包含匹配的订单通知详情对象
     */
    @Override
    public List<OrderNotificationDetailDO> getValetOrderDetailByPhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<OrderNotificationDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrderNotificationDetailDO::getSendPhone, Arrays.asList(phone, PHONE_PREFIX+phone))
                .ne(OrderNotificationDetailDO::getSendResult, SmsSendResultEnum.WAITED.getCode())
                .eq(OrderNotificationDetailDO::getIsDeleted, false);
        return list(queryWrapper);
    }

    /**
     * 根据车辆识别号（VIN）获取代驾订单详情列表
     *
     * @param carVin 车辆识别号（VIN），用于查询订单详情
     * @return 返回一个代驾订单详情列表，如果查询不到或参数无效，则返回空列表
     */
    @Override
    public List<OrderNotificationDetailDO> getValetOrderDetailByVin(String carVin) {
        if (StringUtils.isBlank(carVin)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<OrderNotificationDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderNotificationDetailDO::getCarVin, carVin)
                .ne(OrderNotificationDetailDO::getSendResult, SmsSendResultEnum.WAITED.getCode())
                .eq(OrderNotificationDetailDO::getIsDeleted, false);
        return list(queryWrapper);
    }
}
