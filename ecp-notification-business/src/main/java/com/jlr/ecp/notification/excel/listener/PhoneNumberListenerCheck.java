package com.jlr.ecp.notification.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.util.ListUtils;
import com.jlr.ecp.notification.constant.Constants;
import com.jlr.ecp.notification.excel.pojo.PhoneErrorDetail;
import com.jlr.ecp.notification.excel.pojo.PhoneNumber;
import com.jlr.ecp.notification.excel.utils.NotificationExcelUtil;
import com.jlr.ecp.notification.util.PhoneNumberValidatorUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.io.File;
import java.util.*;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Slf4j
public class PhoneNumberListenerCheck extends AnalysisEventListener<PhoneNumber> {

    private Boolean formatError = false;

    private Boolean checkResult = true;

    private File phoneErrorDetailFile = null;

    private static final int BATCH_COUNT = 500;

    private List<PhoneNumber> dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private List<List<PhoneErrorDetail>> finalDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private List<Map<Integer, String>> headers = new ArrayList<>();

    private List<PhoneNumber> allDataList = new ArrayList<>();

    private static final String PHONE_NUMBER_TITLE = "Phone Number";

    private Set<String> phoneSet = new HashSet<>();

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 当遇到表头行时，将表头数据添加到headers列表中
        headers.add(headMap);
    }


    /**
     * 调用给定的VinAndPhoneNumber对象和AnalysisContext对象执行某些操作。
     * 该方法会根据传入的车辆识别号码（VIN）和电话号码进行特定的分析处理。
     *
     * @param phoneNumber 包含车辆识别号码和电话号码的对象，用于分析处理。
     * @param analysisContext 分析上下文，提供额外的上下文信息以支持分析过程。
     */
    @Override
    public void invoke(PhoneNumber phoneNumber, AnalysisContext analysisContext) {
        try {
            dataList.add(phoneNumber);
            if (dataList.size() >= BATCH_COUNT) {
                allDataList.addAll(dataList);
                List<PhoneErrorDetail> data = processData(dataList);
                finalDataList.add(data);
                dataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            }
        } catch (Exception e) {
            log.error("解析电话号码异常：", e);
            finalDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    /**
     * 在所有分析完成后执行的操作。
     * 这个方法是分析上下文生命周期的一部分，可以在整个分析过程结束后执行一些额外的逻辑。
     *
     * @param analysisContext 分析上下文，提供了关于当前分析会话的上下文信息，例如分析的文件列表等。
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("手机号码处理最后剩余的数据");
        try {
            allDataList.addAll(dataList);
            List<PhoneErrorDetail> data = finalProcessData(dataList);
            finalDataList.add(data);
            uploadErrorDetail(finalDataList);
        } catch (Exception e) {
            log.error("手机号码处理最后剩余的数据异常：", e);
        }
        log.info("手机号码所有数据解析完成！");
    }



    /**
     * 处理数据的方法。
     * 这是一个抽象方法，需要在具体实现类中进行具体实现。
     * 它接受一个VinAndPhoneNumber对象的列表作为输入，处理这些数据，并返回处理后的结果。
     *
     * @param phoneNumbers 包含车辆识别号（VIN）和电话号码的列表。列表中的每个元素都包含一个VIN和一个对应的电话号码。
     * @return 返回处理后的数据。返回类型为泛型T，具体类型取决于实现类。
     */
    public List<PhoneErrorDetail> processData(List<PhoneNumber> phoneNumbers) {
        return getPhoneErrorDetailList(phoneNumbers);
    }

    /**
     * 对收集到的车辆识别码（VIN）和电话号码列表进行最终处理。
     * 这是一个抽象方法，需要在子类中具体实现处理逻辑。
     *
     * @param phoneNumbers 包含车辆识别码和电话号码的列表，列表中每一项都是一个VinAndPhoneNumber对象。
     *                           车辆识别码（VIN）是世界范围内通用的用于识别汽车的一组独特的字母和数字，
     *                           电话号码则是与车辆相关联的联系电话。
     * @return 返回处理后的数据，具体类型由泛型T决定，取决于具体实现。
     */
    public List<PhoneErrorDetail> finalProcessData(List<PhoneNumber> phoneNumbers) {
        return getPhoneErrorDetailList(phoneNumbers);
    }

    /**
     * 上传错误详情的方法。
     * 这是一个抽象方法，需要在具体实现类中进行具体实现。
     * 它接受一个包含处理后的数据的列表作为输入，并将这些数据上传到相应的错误详情表中。
     *
     * @param data 包含处理后的数据的列表，列表中的每个元素都包含一个处理后的数据。
     */
    public void uploadErrorDetail(List<List<PhoneErrorDetail>> data) {
        try {
            if (!checkResult) {
                phoneErrorDetailFile = NotificationExcelUtil.batchWritePhoneError(data);
            }
        } catch (Exception e) {
            log.error("写入手机号错误详情异常:", e);
        } finally {
            checkResult = true;
            dataList.clear();
            finalDataList.clear();
            headers.clear();
        }
    }



    /**
     * 获取VIN码和手机号错误详情列表。
     *
     * @param phoneNumbers V手机号的列表。可能会包含不合法或不完整的VIN码和手机号。
     * @return 返回一个包含所有输入项错误详情的列表。每个错误详情包括原始的VIN码、手机号以及对应的错误信息。
     */
    private List<PhoneErrorDetail> getPhoneErrorDetailList(List<PhoneNumber> phoneNumbers) {
        List<PhoneErrorDetail> phoneErrorDetails = new ArrayList<>();
        if (headers.isEmpty() || !PHONE_NUMBER_TITLE.equals(headers.get(0).get(0))) {
            formatError = true;
            return phoneErrorDetails;
        }
        for (PhoneNumber phoneNumber : phoneNumbers) {
            PhoneErrorDetail phoneErrorDetail = getPhoneErrorDetail(phoneNumber);
            phoneErrorDetails.add(phoneErrorDetail);
        }
        if (dataList.isEmpty() && finalDataList.isEmpty()) {
            checkResult = false;
            PhoneErrorDetail phoneErrorDetail = PhoneErrorDetail.builder()
                    .errorMessage(Constants.PHONE_MISS)
                    .build();
            phoneErrorDetails.add(phoneErrorDetail);
        }
        return phoneErrorDetails;
    }

    /**
     * 获取电话号码错误详情
     *
     * @param phoneNumber 电话号码对象，包含具体的电话号码信息
     * @return 返回电话号码的错误详情，包括错误消息和是否有效的标志
     */
    private PhoneErrorDetail getPhoneErrorDetail(PhoneNumber phoneNumber) {
        PhoneErrorDetail phoneErrorDetail = PhoneErrorDetail.builder()
                .phoneNumber(phoneNumber.getPhoneNumber())
                .build();
        if (StringUtils.isBlank(phoneErrorDetail.getPhoneNumber())) {
            checkResult = false;
            phoneErrorDetail.setErrorMessage(Constants.PHONE_MISS);
        } else if (!PhoneNumberValidatorUtil.isValidChinaMobileNumber(phoneNumber.getPhoneNumber())) {
            checkResult = false;
            phoneErrorDetail.setErrorMessage("手机号格式错误");
        } else if (phoneSet.contains(phoneNumber.getPhoneNumber())) {
            checkResult = false;
            phoneErrorDetail.setErrorMessage("手机号重复");
        }
        phoneSet.add(phoneNumber.getPhoneNumber());
        return phoneErrorDetail;
    }
}
