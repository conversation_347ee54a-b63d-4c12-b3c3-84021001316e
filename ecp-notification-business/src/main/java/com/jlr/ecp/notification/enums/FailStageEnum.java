package com.jlr.ecp.notification.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
@Getter
public enum FailStageEnum {
    ZERO("0","ECP到短信平台推送失败"),
    ONE("1","短信平台到运营商推送失败"),
    TWO("2","运营商到客户手机推送失败")
    ;
    private final String label;
    private final String name;

    public static String getMsgErrorStageDesc(String code) {
        for (FailStageEnum value : values()) {
            if (value.getLabel().equals(code)) {
                return value.getName();
            }
        }
        return StringUtils.EMPTY;
    }
}
