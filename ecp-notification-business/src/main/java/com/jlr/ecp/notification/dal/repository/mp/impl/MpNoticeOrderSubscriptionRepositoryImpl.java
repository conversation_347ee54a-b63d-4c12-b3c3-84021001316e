package com.jlr.ecp.notification.dal.repository.mp.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.notification.dal.dataobject.mp.MpNoticeOrderSubscriptionDO;
import com.jlr.ecp.notification.dal.mysql.mp.MpNoticeOrderSubscriptionMapper;
import com.jlr.ecp.notification.dal.repository.mp.MpNoticeOrderSubscriptionRepository;
import com.jlr.ecp.notification.enums.mp.MpMsgTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class MpNoticeOrderSubscriptionRepositoryImpl extends ServiceImpl<MpNoticeOrderSubscriptionMapper,
        MpNoticeOrderSubscriptionDO> implements MpNoticeOrderSubscriptionRepository {

    /**
     * 根据订单号和机构ID查询订单订阅信息
     *
     * @param jlrId 机构ID，标识查询的机构
     * @param orderNo 订单号，标识查询的特定订单
     * @return 返回查询到的订单订阅信息对象，如果没有找到匹配的订阅信息，则返回null
     */
    @Override
    public MpNoticeOrderSubscriptionDO queryOrderSubscribeByOrderNoAndJlrId(String jlrId, String orderNo) {
        log.info("根据订单号和机构ID查询订单订阅信息, jlrId:{}, orderNo:{}", jlrId, orderNo);
        LambdaQueryWrapper<MpNoticeOrderSubscriptionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(jlrId), MpNoticeOrderSubscriptionDO::getJlrId, jlrId);
        queryWrapper.eq(StringUtils.isNotBlank(orderNo), MpNoticeOrderSubscriptionDO::getOrderNo, orderNo);
        queryWrapper.eq(MpNoticeOrderSubscriptionDO::getIsDeleted, false);
        return getOne(queryWrapper);
    }

    /**
     * 根据订单号查询订单订阅信息
     *
     * @param orderNoList 订单号，用于查询订单订阅信息
     * @return 返回查询到的订单订阅信息对象，如果没有找到则返回null
     */
    @Override
    public List<MpNoticeOrderSubscriptionDO> queryOrderSubscribeByOrderNoList(List<String> orderNoList, Integer mpType) {
        log.info("根据订单号和机构ID查询订单订阅信息, orderNoList:{}", orderNoList);
        if (CollUtil.isEmpty(orderNoList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<MpNoticeOrderSubscriptionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MpNoticeOrderSubscriptionDO::getOrderNo, orderNoList);
        queryWrapper.eq(MpNoticeOrderSubscriptionDO::getIsDeleted, false);
        if(MpMsgTypeEnum.ORDER_CHANGE.getCode().equals(mpType)) {
            queryWrapper.ge(MpNoticeOrderSubscriptionDO::getOrderSuccessFlag, 0);
        } else if (MpMsgTypeEnum.ORDER_CANCEL.getCode().equals(mpType)) {
            queryWrapper.ge(MpNoticeOrderSubscriptionDO::getOrderCancelFlag, 0);
        }
        return list(queryWrapper);
    }

    /**
     * 根据订单号列表查询订单订阅信息映射
     *
     * @param orderNoList 订单号列表，用于查询订阅信息
     * @return 返回一个映射，键是订单号，值是对应的订阅信息对象MpNoticeOrderSubscriptionDO
     */
    @Override
    public Map<String, MpNoticeOrderSubscriptionDO> queryOrderSubscribeMapByList(List<String> orderNoList, Integer mpType) {
        List<MpNoticeOrderSubscriptionDO> orderSubscribeList = queryOrderSubscribeByOrderNoList(orderNoList, mpType);
        if (CollUtil.isEmpty(orderSubscribeList)) {
            return new HashMap<>();
        }
        return orderSubscribeList.stream()
                .collect(Collectors.toMap(MpNoticeOrderSubscriptionDO::getOrderNo, Function.identity(), (v1, v2) -> v1));
    }
}
