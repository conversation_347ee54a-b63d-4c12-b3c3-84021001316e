package com.jlr.ecp.notification.controller.admin;


import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.dto.shortlink.ShortLinkConfigDTO;
import com.jlr.ecp.notification.service.ShortLinkConfigService;
import com.jlr.ecp.notification.vo.shortlink.ShortLinkConfigDetailVO;
import com.jlr.ecp.notification.vo.shortlink.ShortLinkConfigModifyLogVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;

@Tag(name = "短链配置")
@RestController
@RequestMapping("/shortLink/config")
@Validated
public class ShortLinkConfigController {

    @Resource
    private ShortLinkConfigService shortConfigService;


    @PostMapping("/saveOrUpdate")
    @Operation(summary = "修改短链配置")
    @PreAuthorize("@ss.hasPermission('notification:shortlink:list')")
    public CommonResult<Boolean> saveOrUpdateShortLink(@Valid @RequestBody ShortLinkConfigDTO shortLinkConfigDTO) {
        return CommonResult.success(shortConfigService.saveOrUpdateSmsShortLinkConfig(shortLinkConfigDTO));
    }

    @GetMapping(value = "/getShortConfig")
    @Operation(summary = "获得短链配置")
    @PreAuthorize("@ss.hasPermission('notification:shortlink:list')")
    @Parameter(name = "id", description = "编号", required = false, example = "1024")
    @Parameter(name = "brandCode", description = "品牌标识", required = true, example = "LAN:路虎，JAG：捷豹")
    public CommonResult<ShortLinkConfigDetailVO> getShortConfig(@RequestParam(value = "brandCode") String brandCode) {
        return CommonResult.success(shortConfigService.getSmsShortConfig(brandCode));
    }

    @GetMapping(value = "/queryModifyLog")
    @Operation(summary = "获取短链编辑记录")
    @PreAuthorize("@ss.hasPermission('notification:shortlink:list')")
    @Parameter(name = "configId", description = "配置id", required = true, example = "3355")
    public CommonResult<List<ShortLinkConfigModifyLogVO>> queryModifyLog( @RequestParam(value = "configId") Long configId) {
        return CommonResult.success(shortConfigService.queryModifyLog(configId));
    }

}
