package com.jlr.ecp.notification.dal.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.notification.dal.dataobject.template.MessageTemplate;

import java.util.Collection;
import java.util.List;

public interface MessageTemplateRepository {

    /**
     * 手动添加短信模板
     * @param messageTemplate 包含手动模板信息的数据
     * @return 返回操作结果，成功返回true，失败返回false
     */
    Boolean addMessageTemplate(MessageTemplate messageTemplate);

    /**
     *  修改手动短信模板
     * @param messageTemplate 包含手动模板信息的数据
     * @return 返回操作结果，成功返回true，失败返回false
     */
    Boolean updateMessageManualTemplate(MessageTemplate messageTemplate);

    /**
     *  获取手动模板详情
     *
     * @param templateCode 模板code
     * @return MessageTemplate
     * */
    MessageTemplate getManualTemplateDetail(String templateCode);

    /**
     * 通过模板code删除模板（逻辑删除）
     *
     * @param templateCode 模板code
     * @return Boolean
     * */
    Boolean deleteTemplateByCode(String templateCode);

    /**
     *  按照模板Code查询模板数据
     * @param templateCode 模板code
     * @return MessageTemplate
     * */
    MessageTemplate getMessageTemplateByCode(String templateCode);

    /**
     * 根据模板名称获取消息模板列表。
     *
     * @param templateName 模板名称，不可为空或仅包含空白字符。
     * @param businessCode 业务线code
     * @return 如果找到匹配的且未被删除的消息模板，则返回其列表；如果名称为空或仅包含空白字符，则返回null。
     */
    List<MessageTemplate> getMessageTemplateByName(String templateName, String businessCode);


    /**
     * 根据模板名称获取消息模板列表。
     *
     * @param templateName 模板名称，不可为空或仅包含空白字符。
     * @param businessCode 业务线code
     * @param templateType 模板类型
     * @return 如果找到匹配的且未被删除的消息模板，则返回其列表；如果名称为空或仅包含空白字符，则返回null。
     */
    List<MessageTemplate> getTemplateByTypeName(String templateName, String businessCode, Integer templateType);

    /**
     * 自动模板分页
     *
     * @param pageReq 分页请求对象，包含分页查询所需的信息，如当前页码、每页记录数等
     * @param wrapper 查询包装器，用于指定查询条件
     * @return 返回一个分页对象，其中包含根据查询条件筛选出的记录
     */
    Page<MessageTemplate> selectAutoTemplatePage(Page<MessageTemplate> pageReq, LambdaQueryWrapper<MessageTemplate> wrapper);

    /**
     * 根据ID更新短信模板信息
     *
     * @param messageTemplate 包含更新信息的短信模板对象
     * @return 更新操作的结果，通常为受影响的行数
     */
    int updateById(MessageTemplate messageTemplate);

    /**
     *   按照模板code返回模板内容
     * @param templateCode  模板code
     * @return String
     * */
    String getTemplateContentByCode(String templateCode);

    /**
     * 根据模板代码列表查询消息模板列表
     *
     * @param templateCodeList 模板代码列表，用于查询消息模板
     * @return 返回一个消息模板列表，如果输入为空，则返回空列表
     */
    List<MessageTemplate> queryTemplateListByCodes(Collection<String> templateCodeList);

    /**
     * 检查指定的模板代码是否已存在于数据库中
     *
     * @param templateCode 需要检查的模板代码
     * @return 如果模板代码存在，则返回true；否则返回false
     */
    boolean existsTemplateCode(String templateCode);

    /**
     * 检查手动模板代码是否已存在于数据库中
     *
     * @param templateCode 需要检查的模板代码
     * @param templateType 模板类型
     * @return 如果模板代码存在，则返回true；否则返回false
     */
    boolean existsTemplateCodeByType(String templateCode, Integer templateType);

    /**
     * 根据模板类型获取模板名称列表
     *
     * @param templateType 模板类型，用于筛选具有特定类型的邮件模板
     * @param autoType     自动类型，用于筛选具有特定自动类型的邮件模板
     * @param businessCode 业务线code
     * @return 包含所有指定类型的模板的名称
     */
    List<MessageTemplate> getTemplateListByType(Integer templateType, Integer autoType, String businessCode);

    /**
     * 根据业务线获取模板代码列表
     *
     * @param businessCode 业务线代码
     * @return 模板代码列表
     */
    List<String> listTemplateCodesByBusinessCode(String businessCode);
}
