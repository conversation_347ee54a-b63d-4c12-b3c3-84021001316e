package com.jlr.ecp.notification.file.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.notification.file.dao.FileConfigDO;
import com.jlr.ecp.notification.file.vo.FileConfigCreateReqVO;
import com.jlr.ecp.notification.file.vo.FileConfigRespVO;
import com.jlr.ecp.notification.file.vo.FileConfigUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 文件配置 Convert
 *
 * <AUTHOR>
 */
@Mapper
@DS("ecp_system")
public interface FileConfigConvert {

    FileConfigConvert INSTANCE = Mappers.getMapper(FileConfigConvert.class);

    @Mapping(target = "config", ignore = true)
    FileConfigDO convert(FileConfigCreateReqVO bean);

    @Mapping(target = "config", ignore = true)
    FileConfigDO convert(FileConfigUpdateReqVO bean);

    FileConfigRespVO convert(FileConfigDO bean);

    List<FileConfigRespVO> convertList(List<FileConfigDO> list);

    PageResult<FileConfigRespVO> convertPage(PageResult<FileConfigDO> page);

}

