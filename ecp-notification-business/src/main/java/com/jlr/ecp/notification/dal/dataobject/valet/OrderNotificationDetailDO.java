package com.jlr.ecp.notification.dal.dataobject.valet;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_order_notification_detail")
public class OrderNotificationDetailDO extends BaseDO {

    @TableId(type = IdType.AUTO) // 主键自增
    private Long id;

    // 信息发送UUID
    @TableField(value = "msgid")
    private String msgId;

    // 发送内容
    @TableField(value = "send_message")
    private String sendMessage;

    // 发送手机号
    @TableField(value = "send_phone")
    private String sendPhone;

    // 车辆VIN
    @TableField(value = "car_vin")
    private String carVin;

    // 发送时间
    @TableField(value = "send_time")
    private LocalDateTime sendTime;

    // 发送结果
    @TableField(value = "send_result")
    private Integer sendResult;

    // 发送失败原因
    @TableField(value = "error_message")
    private String errorMessage;

    // 品牌code
    @TableField(value = "brand_code")
    private String brandCode;

    // 最终短信触达结果
    @TableField(value = "msg_result")
    private String msgResult;

    /**
     *  提交到短信代理商错误code
     * */
    @TableField(value = "submit_error_code")
    private String submitErrorCode;

    // 短信触达错误描述
    @TableField(value = "msg_error_desc")
    private String msgErrorDesc;

    // 短信触达运营商错误码
    @TableField(value = "wgcode")
    private String wgCode;

    // 短鏈URL
    @TableField(value = "short_link")
    private String shortLink;

    // 是否打开短链
    @TableField(value = "open_short_link")
    private Integer openShortLink;

    // 租户号
    @TableField(value = "tenant_id")
    private Integer tenantId;
}
