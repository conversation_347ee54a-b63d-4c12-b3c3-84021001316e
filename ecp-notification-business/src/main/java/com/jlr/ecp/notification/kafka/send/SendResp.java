package com.jlr.ecp.notification.kafka.send;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SendResp {
    /**
     *  发送结果
     * */
    private String result;

    /**
     * 结果描述
     * */
    private String desc;

    /**
     * 消息Id
     * */
    private String msgid;


    /**
     * 失败号码
     * */
    private String failPhones;

    /**
     *  序列
     * */
    private Integer sequence;
}

