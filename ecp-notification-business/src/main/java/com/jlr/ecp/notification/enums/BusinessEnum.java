package com.jlr.ecp.notification.enums;

import com.jlr.ecp.framework.common.core.IntArrayValuable;
import com.jlr.ecp.system.enums.social.SocialTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

public final class BusinessEnum {

    /**
     * 数据操作类型
     */
    @AllArgsConstructor
    @Getter
    public enum OperationType implements IntArrayValuable {
        DRAFT_ONLY(1, "保存为草稿"),
        SAVE_AND_SUBMIT(2, "保存并提交");

        private final Integer type;

        private final String desc;

        public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(OperationType::getType).toArray();

        @Override
        public int[] array() {
            return ARRAYS;
        }
    }

    /**
     *  发送类型 1：实时发送 2：定时发送
     */
    @AllArgsConstructor
    @Getter
    public enum TaskSendType implements IntArrayValuable {
        REAL_TIME(1, "实时发送"),
        TIMING(2, "定时发送");

        private final Integer type;

        private final String desc;

        public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(TaskSendType::getType).toArray();

        @Override
        public int[] array() {
            return ARRAYS;
        }
    }
}
