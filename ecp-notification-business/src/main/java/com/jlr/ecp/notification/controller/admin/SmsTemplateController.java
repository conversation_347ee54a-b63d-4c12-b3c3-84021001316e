package com.jlr.ecp.notification.controller.admin;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.datapermission.core.annotation.DataPermission;
import com.jlr.ecp.notification.dto.template.AutoTemplateDTO;
import com.jlr.ecp.notification.req.template.SmsTemplatePageReq;
import com.jlr.ecp.notification.req.template.SmsTemplateReq;
import com.jlr.ecp.notification.req.template.TemplateHistoryPageReq;
import com.jlr.ecp.notification.service.SmsTemplateService;
import com.jlr.ecp.notification.service.manual.ManualTemplateService;
import com.jlr.ecp.notification.vo.template.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import java.util.List;


/**
 * 模板编辑接口
 *
 * <AUTHOR>
 * */
@Tag(name = "notification - 自动模板管理")
@RestController
@RequestMapping("/sms/template")
@Slf4j
public class SmsTemplateController {
    @Autowired
    private SmsTemplateService smsTemplateService;

    @Resource
    private ManualTemplateService manualTemplateService;

    @GetMapping("/list")
    @Operation(summary = "自动模板列表页")
    @PreAuthorize("@ss.hasAnyPermissions('notification:template-automatic:list','notification:lretemplate-automatic:list', 'notification:bgtemplate-automatic:list')")
    public CommonResult<PageResult<SmsTemplateVo>> pageList(@SpringQueryMap SmsTemplatePageReq pageReq) {
        log.info("开始查询模板页面, 当businessCode为空查询全部, pageReq:{}", pageReq);
        PageResult<SmsTemplateVo> pageResult = smsTemplateService.getTemplateList(pageReq);
        return CommonResult.success(pageResult);
    }

    @GetMapping("/detail")
    @Operation(summary = "自动模板详情页")
    @PreAuthorize("@ss.hasAnyPermissions('notification:template-automatic:detail', 'notification:lretemplate-automatic:detail', 'notification:bgtemplate-automatic:detail')")
    @DataPermission(enable = false)
    public CommonResult<SmsTemplateDetailVo> modifyDetail(@RequestParam String templateCode) {
        log.info("开始模板编辑详情和历史操作, templateCode:{}", templateCode);
        return smsTemplateService.getSmsTemplateDetail(templateCode);
    }

    @GetMapping("/history")
    @Operation(summary = "自动模板编辑历史")
    @PreAuthorize("@ss.hasAnyPermissions('notification:template-automatic:detail', 'notification:template-manual:detail', 'notification:lretemplate-automatic:detail', 'notification:bgtemplate-automatic:detail')")
    @DataPermission(enable = false)
    public CommonResult<PageResult<SmsTemplateHistoryVo>> modifyHistory(@SpringQueryMap
                                                                            TemplateHistoryPageReq historyPageReq) {
        log.info("模板编辑历史入参, historyPageReq:{}", historyPageReq);
        PageResult<SmsTemplateHistoryVo> historyVo = smsTemplateService.getTemplateHistory(historyPageReq);
        return CommonResult.success(historyVo);
    }

    @GetMapping("/history/detail")
    @Operation(summary = "自动模版 编辑详情")
    @PreAuthorize("@ss.hasAnyPermissions('notification:template-automatic:detail', 'notification:lretemplate-automatic:detail', 'notification:bgtemplate-automatic:detail')")
    public CommonResult<SmsTemplateHistoryDetailVO> getModifyDetail(
            @RequestParam("id") @Schema(description = "日志ID") Long id) {
        log.info("获取自动模版编辑记录详情, id:{}", id);

        SmsTemplateHistoryDetailVO detail = manualTemplateService.getManualTemplateModifyDetail(id);
        return CommonResult.success(detail);
    }

    @PostMapping("/modify")
    @Operation(summary = "自动模板编辑")
    @PreAuthorize("@ss.hasAnyPermissions('notification:template-automatic:edit', 'notification:lretemplate-automatic:edit', 'notification:bgtemplate-automatic:edit')")
    @DataPermission(enable = false)
    public CommonResult<Boolean> modifySmsAutoTemplate(@RequestBody SmsTemplateReq smsTemplateReq) {
        log.info("开始模板编辑, smsTemplateReq:{}", smsTemplateReq);
        return smsTemplateService.smsTemplateModify(smsTemplateReq);
    }

    @GetMapping("/variable")
    @Operation(summary = "获取自动模板变量")
    @PreAuthorize("@ss.hasPermission('notification:template-automatic:create')")
    public CommonResult<List<AutoTemplateVariableVO>> getAutoTemplateVariableList(@RequestParam(value = "businessCode", required = false) String businessCode) {
        log.info("获取模板变量");
        return CommonResult.success(smsTemplateService.queryAutoTemplateVariableList(businessCode));
    }

    @PostMapping("/add")
    @Operation(summary = "添加自动模板")
    @PreAuthorize("@ss.hasPermission('notification:template-automatic:create')")
    public CommonResult<Boolean> addAutoTemplate(@RequestBody @Validated AutoTemplateDTO autoTemplateDTO) {
        log.info("添加自动模板, autoTemplateDTO:{}", autoTemplateDTO);
        return smsTemplateService.addSmsAutoTemplate(autoTemplateDTO);
    }

    @PostMapping("/delete")
    @Operation(summary = "自动模板删除")
    @PreAuthorize("@ss.hasAnyPermissions('notification:template-automatic:delete', 'notification:lretemplate-automatic:delete', 'notification:bgtemplate-automatic:delete')")
    public CommonResult<Boolean> deleteManualTemplate(@RequestParam(value = "templateCode", required = true) String templateCode) {
        log.info("自动模板删除, templateCode:{}", templateCode);
        return smsTemplateService.deleteAutoTemplate(templateCode);
    }


    @GetMapping("/getTemplate")
    @Operation(summary = "获取自动模板（包含代客下单模板）")
    @PreAuthorize("@ss.hasAnyPermissions('trade:order:create', 'notification:smstask-automatic:detail')")
    @PermitAll
    public CommonResult<List<SmsTemplateVo>> selectTemplateByType(@RequestParam(value = "templateType", required = true) Integer templateType,
                                                            @RequestParam(value = "autoType", required = false) Integer autoType,
                                                                  @RequestParam(value = "businessCode", required = false)String businessCode) {
        log.info("获取自动模板（包含代客下单模板）, templateType:{}, autoType:{}, businessCode:{}", templateType, autoType, businessCode);
        return smsTemplateService.queryTemplateByType(templateType, autoType, businessCode);
    }
}
