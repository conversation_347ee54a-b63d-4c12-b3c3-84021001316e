package com.jlr.ecp.notification.constant;

/**
 * 通用常量
 *
 * <AUTHOR>
 */
public class Constants {

    /***
     *  ========车型年款相关=======
     */
    public static final String SERIES_CREATE_SUCCESS_MESSAGE = "车型年款配置创建成功";
    public static final String SERIES_UPDATE_SUCCESS_MESSAGE = "车型年款配置编辑成功";
    public static final String SERIES_DELETE_SUCCESS_MESSAGE = "车型年款配置刪除成功";

    /***
     *  ========服务包相关=======
     */
    public static final String PACKAGE_CREATE_SUCCESS_MESSAGE = "服务包配置创建成功";
    public static final String PACKAGE_DELETE_SUCCESS_MESSAGE = "服务包配置刪除成功";
    public static final String PACKAGE_BATCH_UPLOAD_SUCCESS_MESSAGE = "批量上传服务包编号完成";


    /**
     * 业务线缓存key
     */
    public static final String BUSINESS_CACHE_KEY = "global:business:info";

    /**
     * redis中幂等校验前缀
     * */
    public static final String NOTIFICATION_IDEMPOTENT_KEY = "notification:idempotent:key:";

    /**
     *  服务到期通知保存的幂等校验key
     * */
    public static final String SAVE_SMS_IDEMPOTENT_KEY = "save:sms:idempotent:key:";

    /**
     *  自动任务服务到期通知条件
     * */
    public static final String AUTO_TASK_CONDITION_KEY = "auto:task:condition:key:";

    /***
     *  短信发送的每条详情记录的code的key
     * */
    public static final String DETAIL_INSTANCE_CODE_KEY = "detail:instance:code:key:";
    public static final String DRAFT = "草稿";

    /**
     * ========== policy相关 ==========
     */
    public static final String POLICY_CREATE_SUCCESS_MESSAGE = "添加信息配置成功";
    public static final String POLICY_UPDATE_SUCCESS_MESSAGE = "编辑信息配置成功";
    public static final String POLICY_DELETE_SUCCESS_MESSAGE = "删除信息配置成功";

    /**
     * ========== faq相关 ==========
     */
    public static final String FAQ_CREATE_SUCCESS_MESSAGE = "添加问答配置成功";
    public static final String FAQ_UPDATE_SUCCESS_MESSAGE = "编辑问答配置成功";
    public static final String FAQ_DELETE_SUCCESS_MESSAGE = "删除问答配置成功";

    /**
     * ========== faq type 相关 ==========
     */
    public static final String FAQ_TYPE_CREATE_SUCCESS_MESSAGE = "问题类别创建成功";
    public static final String FAQ_TYPE_EDIT_SUCCESS_MESSAGE = "问题类别编辑成功";
    public static final String FAQ_TYPE_DELETE_SUCCESS_MESSAGE = "问题类别刪除成功";


    /***
     *  ========销售单位相关=======
     */
    public static final String UNIT_CREATE_SUCCESS_MESSAGE = "商品销售单位创建成功";
    public static final String UNIT_UPDATE_SUCCESS_MESSAGE = "商品销售单位编辑成功";
    public static final String UNIT_DELETE_SUCCESS_MESSAGE = "商品销售单位刪除成功";



    /***
     *  ========商品类别相关=======
     */
    public static final String CATEGORY_CREATE_SUCCESS_MESSAGE = "商品类别创建成功";
    public static final String CATEGORY_UPDATE_SUCCESS_MESSAGE = "商品类别编辑成功";
    public static final String CATEGORY_DELETE_SUCCESS_MESSAGE = "商品类别刪除成功";


    /***
     *  ========商品属性相关=======
     */
    public static final String ATTRIBUTE_CREATE_SUCCESS_MESSAGE = "商品属性创建成功";
    public static final String ATTRIBUTE_UPDATE_SUCCESS_MESSAGE = "商品属性编辑成功";
    public static final String ATTRIBUTE_DELETE_SUCCESS_MESSAGE = "商品属性刪除成功";

    /***
     *  ========商品品牌相关=======
     */
    public static final String BRAND_CREATE_SUCCESS_MESSAGE = "商品品牌创建成功";
    public static final String BRAND_UPDATE_SUCCESS_MESSAGE = "商品品牌编辑成功";
    public static final String BRAND_DELETE_SUCCESS_MESSAGE = "商品品牌刪除成功";

    public static final String PHONE_MISS = "手机号缺失";

    public static final String PHONE_FORMAT = "手机号格式错误";

    public static final String VIN_MISS = "VIN缺失";


    /**
     *  redis key
     */
    public static class REDIS_KEY {
        /**
         * 车型编码映射缓存
         */
        public static final String SERIES_CACHE_KEY = "global:series:mapping";


    }
}
