package com.jlr.ecp.notification.service.impl;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.notification.dal.dataobject.history.NotificationHistory;
import com.jlr.ecp.notification.dal.dataobject.history.SendHistoryDetail;
import com.jlr.ecp.notification.dal.dataobject.task.NotificationTask;
import com.jlr.ecp.notification.dal.dataobject.task.auto.NotificationAutoTaskDO;
import com.jlr.ecp.notification.dal.dataobject.valet.OrderNotificationDetailDO;
import com.jlr.ecp.notification.dal.mysql.history.SendHistoryMapper;
import com.jlr.ecp.notification.dal.repository.*;
import com.jlr.ecp.notification.dto.NotificationLogSingleDTO;
import com.jlr.ecp.notification.enums.task.TaskSendTypeEnum;
import com.jlr.ecp.notification.enums.task.TaskTypeEnum;
import com.jlr.ecp.notification.req.history.SendLogPageReq;
import com.jlr.ecp.notification.vo.history.NotificationLogSingleVO;
import com.jlr.ecp.notification.vo.history.SendLogPageVo;
import com.jlr.ecp.subscription.api.icrvehicle.IcrVehicleApi;
import com.jlr.ecp.subscription.api.icrvehicle.vo.IcrVehicleListRespVO;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class SendHistoryServiceImplTest {

    @Mock
    private SendHistoryMapper mockHistoryMapper;
    @Mock
    private MessageTaskRepository mockManualTaskRepository;
    @Mock
    private SendHistoryDetailRepository mockDetailRepository;
    @Mock
    private IcrVehicleApi mockIcrVehicleApi;
    @Mock
    private NotificationHistoryRepository mockHistoryRepository;
    @Mock
    private NotificationAutoTaskRepository mockAutoTaskRepository;
    @Mock
    private OrderNotificationDetailRepository mockValetDetailRepository;
    @Mock
    private Snowflake mockSnowflake;

    @InjectMocks
    @Spy
    private SendHistoryServiceImpl sendHistoryServiceImplUnderTest;

    @Test
    public void testLoadSmsErrorCompare() throws Exception {
        // Setup
        // Run the test
        sendHistoryServiceImplUnderTest.loadSmsErrorCompare();

        // Verify the results
        verify(sendHistoryServiceImplUnderTest, times(1)).loadSmsErrorCompare();
    }

    @Test
    public void testSendHistoryPageList() {
        // Setup
        final SendLogPageReq sendLogPageReq = new SendLogPageReq();
        sendLogPageReq.setPageNo(1);
        sendLogPageReq.setPageSize(10);
        sendLogPageReq.setStartTime("2025-01-01");
        sendLogPageReq.setEndTime("2025-01-01");

        // Configure NotificationAutoTaskRepository.getAutoTaskBySendTypeAndName(...).
        final List<NotificationAutoTaskDO> autoTaskDOList = List.of(NotificationAutoTaskDO.builder()
                .taskCode("autoTaskCode")
                .taskName("autoTaskName")
                .build());
        when(mockAutoTaskRepository.getAutoTaskBySendTypeAndName(TaskSendTypeEnum.SCHEDULED.getCode(), null)).thenReturn(autoTaskDOList);

        // Configure MessageTaskRepository.getManualTaskByNameAndSendType(...).
        final List<NotificationTask> manualTasks = List.of(NotificationTask.builder()
                .taskCode("manualTaskCode")
                .taskName("manualTaskName")
                .taskType(2)
                .build());
        when(mockManualTaskRepository.getManualTaskByNameAndSendType(TaskTypeEnum.MANUAL.getType(), null, null)).thenReturn(manualTasks);

        final List<NotificationTask> oldAutoTasks = List.of(NotificationTask.builder()
                .taskCode("oldAutoTaskCode")
                .taskName("oldAutoTaskName")
                .taskType(1)
                .build());
        when(mockManualTaskRepository.getManualTaskByNameAndSendType(TaskTypeEnum.AUTO_TASK.getType(), TaskSendTypeEnum.SCHEDULED.getCode(), null)).thenReturn(oldAutoTasks);

        // Configure NotificationHistoryRepository.selectPageByTaskInstanceCode(...).
        Page<NotificationHistory> pageRes = new Page<>(1L, 10L, 3L, true);
        pageRes.setRecords(List.of(new NotificationHistory(), new NotificationHistory(), new NotificationHistory()));
        when(mockHistoryRepository.selectPageByTaskInstanceCode(anyList(), any(SendLogPageReq.class))).thenReturn(pageRes);

        // Run the test
        final CommonResult<PageResult<SendLogPageVo>> result = sendHistoryServiceImplUnderTest.sendHistoryPageList(
                sendLogPageReq);

        // Verify the results
        assertEquals(Optional.ofNullable(result.getData().getTotal()), Optional.of(3L));
    }

    @Test
    public void testQueryByInstanceCode() {
        // Setup
        // Configure SendHistoryMapper.selectOne(...).
        final NotificationHistory history = NotificationHistory.builder()
                .taskCode("taskCode")
                .taskInstanceCode("instanceCode")
                .taskSendTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .sendTotalCount(0)
                .sendFailCount(0)
                .reachUserSuccessCount(0)
                .reachUserFailCount(0)
                .openShortLinkCount(0)
                .build();
        when(mockHistoryMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(history);

        // Run the test
        final String result = sendHistoryServiceImplUnderTest.queryByInstanceCode("instanceCode");

        // Verify the results
        assertEquals("instanceCode", result);
    }

    @Test
    public void testQueryTaskNameList() {
        // Setup
        when(mockManualTaskRepository.queryTaskNameListFilterAutoEvent(1)).thenReturn(List.of("value"));
        when(mockAutoTaskRepository.querytAutoTaskNameList()).thenReturn(List.of("value"));

        // Run the test
        final List<String> result = sendHistoryServiceImplUnderTest.queryTaskNameList(1);

        // Verify the results
        assertEquals(List.of("value", "value"), result);
    }

    @Test
    public void testQueryTaskNameList_MessageTaskRepositoryReturnsNoItems() {
        // Setup
        when(mockManualTaskRepository.queryTaskNameListFilterAutoEvent(0)).thenReturn(Collections.emptyList());
        when(mockAutoTaskRepository.querytAutoTaskNameList()).thenReturn(List.of("value"));

        // Run the test
        final List<String> result = sendHistoryServiceImplUnderTest.queryTaskNameList(0);

        // Verify the results
        assertEquals(List.of(), result);
    }

    @Test
    public void testQueryTaskNameList_Null() {
        // Setup
        when(mockAutoTaskRepository.querytAutoTaskNameList()).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = sendHistoryServiceImplUnderTest.queryTaskNameList(null);

        // Verify the results
        assertEquals(List.of(), result);
    }

    @Test
    public void testQueryTaskNameList_manual() {
        // Setup
        when(mockAutoTaskRepository.querytAutoTaskNameList()).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = sendHistoryServiceImplUnderTest.queryTaskNameList(2);

        // Verify the results
        assertEquals(List.of(), result);
    }

    @Test
    public void testQuerySmsRecordListBySingle_VCS_phone() {
        // Setup
        final NotificationLogSingleDTO singleDTO = NotificationLogSingleDTO.builder()
                .phone("phone")
                .businessCode(BusinessIdEnum.VCS.getCode())
                .build();
        final CommonResult<List<NotificationLogSingleVO>> expectedResult = CommonResult.success(
                List.of(NotificationLogSingleVO.builder()
                        .taskInstanceCode("taskInstanceCode")
                        .taskName("taskName")
                        .sendType("sendType")
                        .sendContent("sendContent")
                        .sendTime("sendTime")
                        .phone("phone")
                        .carVin("carVin")
                        .brand("brand")
                        .configName("configName")
                        .sendStatus("sendStatus")
                        .failStage("failStage")
                        .failReasons("failReasons")
                        .openShortLink(0)
                        .build()));

        // Configure SendHistoryDetailRepository.getHistoryDetailByPhone(...).
        final List<SendHistoryDetail> details = List.of(SendHistoryDetail.builder()
                .taskInstanceCode("taskInstanceCode")
                .sendMessage("sendContent")
                .sendPhone("phone")
                .carVin("carVin")
                .sendResult(0)
                .submitErrorCode("failReasons")
                .msgResult("failStage")
                .wgCode("failReasons")
                .taskCode("taskCode")
                .openShortLink(0)
                .build());
        when(mockDetailRepository.getHistoryDetailByPhone("phone", BusinessIdEnum.VCS.getCode())).thenReturn(details);

        // Configure OrderNotificationDetailRepository.getValetOrderDetailByPhone(...).
        final List<OrderNotificationDetailDO> orderNotificationDetailDOS = List.of(OrderNotificationDetailDO.builder()
                .sendMessage("sendContent")
                .sendPhone("phone")
                .carVin("carVin")
                .sendTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .sendResult(0)
                .msgResult("failStage")
                .submitErrorCode("failReasons")
                .wgCode("failReasons")
                .openShortLink(0)
                .build());
        when(mockValetDetailRepository.getValetOrderDetailByPhone("phone")).thenReturn(orderNotificationDetailDOS);

        // Configure MessageTaskRepository.getTaskMapByCodes(...).
        final Map<String, NotificationTask> notificationTaskMap = Map.ofEntries(
                Map.entry("value", NotificationTask.builder()
                        .taskCode("taskCode")
                        .taskName("taskName")
                        .taskType(0)
                        .build()));
        when(mockManualTaskRepository.getTaskMapByCodes(List.of("taskCode"))).thenReturn(notificationTaskMap);

        // Configure NotificationAutoTaskRepository.getAutoTaskMapByCodes(...).
        final Map<String, NotificationAutoTaskDO> autoTaskDOMap = Map.ofEntries(
                Map.entry("value", NotificationAutoTaskDO.builder()
                        .taskCode("taskCode")
                        .taskName("taskName")
                        .build()));
        when(mockAutoTaskRepository.getAutoTaskMapByCodes(List.of("taskCode"))).thenReturn(autoTaskDOMap);

        // Configure IcrVehicleApi.vinInfo(...).
        final IcrVehicleListRespVO icrVehicleListRespVO = new IcrVehicleListRespVO();
        icrVehicleListRespVO.setCarVin("carVin");
        icrVehicleListRespVO.setSeriesCode("seriesCode");
        icrVehicleListRespVO.setSeriesName("seriesName");
        icrVehicleListRespVO.setBrandName("brand");
        icrVehicleListRespVO.setConfigName("configName");
        final CommonResult<List<IcrVehicleListRespVO>> listCommonResult = CommonResult.success(
                List.of(icrVehicleListRespVO));
        when(mockIcrVehicleApi.vinInfo(List.of("carVin"))).thenReturn(listCommonResult);

        // Configure NotificationHistoryRepository.queryHistoryByInstanceCode(...).
        final NotificationHistory history = NotificationHistory.builder()
                .taskCode("taskCode")
                .taskInstanceCode("instanceCode")
                .taskSendTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .sendTotalCount(0)
                .sendFailCount(0)
                .reachUserSuccessCount(0)
                .reachUserFailCount(0)
                .openShortLinkCount(0)
                .build();
        when(mockHistoryRepository.queryHistoryByInstanceCode("taskInstanceCode")).thenReturn(history);

        when(mockSnowflake.nextIdStr()).thenReturn("taskInstanceCode");

        // Run the test
        final CommonResult<List<NotificationLogSingleVO>> result = sendHistoryServiceImplUnderTest.querySmsRecordListBySingle(
                singleDTO);

        // Verify the results
        assertEquals(2, result.getData().size());
    }

    @Test
    public void testQuerySmsRecordListBySingle_LRE_phone() {
        // Setup
        final NotificationLogSingleDTO singleDTO = NotificationLogSingleDTO.builder()
                .phone("phone")
                .businessCode(BusinessIdEnum.LRE.getCode())
                .build();
        final CommonResult<List<NotificationLogSingleVO>> expectedResult = CommonResult.success(
                List.of(NotificationLogSingleVO.builder()
                        .taskInstanceCode("taskInstanceCode")
                        .taskName("taskName")
                        .sendType("sendType")
                        .sendContent("sendContent")
                        .sendTime("sendTime")
                        .phone("phone")
                        .carVin("carVin")
                        .brand("brand")
                        .configName("configName")
                        .sendStatus("sendStatus")
                        .failStage("failStage")
                        .failReasons("failReasons")
                        .openShortLink(0)
                        .build()));

        // Configure SendHistoryDetailRepository.getHistoryDetailByPhone(...).
        final List<SendHistoryDetail> details = List.of(SendHistoryDetail.builder()
                .taskInstanceCode("taskInstanceCode")
                .sendMessage("sendContent")
                .sendPhone("phone")
                .carVin("carVin")
                .sendResult(0)
                .submitErrorCode("failReasons")
                .msgResult("failStage")
                .wgCode("failReasons")
                .taskCode("taskCode")
                .openShortLink(0)
                .build());
        // Configure OrderNotificationDetailRepository.getValetOrderDetailByPhone(...).
        final List<OrderNotificationDetailDO> orderNotificationDetailDOS = List.of(OrderNotificationDetailDO.builder()
                .sendMessage("sendContent")
                .sendPhone("phone")
                .carVin("carVin")
                .sendTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .sendResult(0)
                .msgResult("failStage")
                .submitErrorCode("failReasons")
                .wgCode("failReasons")
                .openShortLink(0)
                .build());
        // Configure MessageTaskRepository.getTaskMapByCodes(...).
        final Map<String, NotificationTask> notificationTaskMap = Map.ofEntries(
                Map.entry("value", NotificationTask.builder()
                        .taskCode("taskCode")
                        .taskName("taskName")
                        .taskType(0)
                        .build()));
        // Configure NotificationAutoTaskRepository.getAutoTaskMapByCodes(...).
        final Map<String, NotificationAutoTaskDO> autoTaskDOMap = Map.ofEntries(
                Map.entry("value", NotificationAutoTaskDO.builder()
                        .taskCode("taskCode")
                        .taskName("taskName")
                        .build()));
        // Configure IcrVehicleApi.vinInfo(...).
        final IcrVehicleListRespVO icrVehicleListRespVO = new IcrVehicleListRespVO();
        icrVehicleListRespVO.setCarVin("carVin");
        icrVehicleListRespVO.setSeriesCode("seriesCode");
        icrVehicleListRespVO.setSeriesName("seriesName");
        icrVehicleListRespVO.setBrandName("brand");
        icrVehicleListRespVO.setConfigName("configName");
        final CommonResult<List<IcrVehicleListRespVO>> listCommonResult = CommonResult.success(
                List.of(icrVehicleListRespVO));

        // Configure NotificationHistoryRepository.queryHistoryByInstanceCode(...).
        final NotificationHistory history = NotificationHistory.builder()
                .taskCode("taskCode")
                .taskInstanceCode("instanceCode")
                .taskSendTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .sendTotalCount(0)
                .sendFailCount(0)
                .reachUserSuccessCount(0)
                .reachUserFailCount(0)
                .openShortLinkCount(0)
                .build();

        // Run the test
        final CommonResult<List<NotificationLogSingleVO>> result = sendHistoryServiceImplUnderTest.querySmsRecordListBySingle(
                singleDTO);

        // Verify the results
        assertEquals(0, result.getData().size());
    }

    @Test
    public void testQuerySmsRecordListBySingle_VCS_vin() {
        // Setup
        final NotificationLogSingleDTO singleDTO = NotificationLogSingleDTO.builder()
                .carVin("carVin")
                .businessCode(BusinessIdEnum.VCS.getCode())
                .build();
        final CommonResult<List<NotificationLogSingleVO>> expectedResult = CommonResult.success(
                List.of(NotificationLogSingleVO.builder()
                        .taskInstanceCode("taskInstanceCode")
                        .taskName("taskName")
                        .sendType("sendType")
                        .sendContent("sendContent")
                        .sendTime("sendTime")
                        .phone("phone")
                        .carVin("carVin")
                        .brand("brand")
                        .configName("configName")
                        .sendStatus("sendStatus")
                        .failStage("failStage")
                        .failReasons("failReasons")
                        .openShortLink(0)
                        .build()));

        // Configure SendHistoryDetailRepository.getHistoryDetailByPhone(...).
        final List<SendHistoryDetail> details = List.of(SendHistoryDetail.builder()
                .taskInstanceCode("taskInstanceCode")
                .sendMessage("sendContent")
                .sendPhone("phone")
                .carVin("carVin")
                .sendResult(0)
                .submitErrorCode("failReasons")
                .msgResult("failStage")
                .wgCode("failReasons")
                .taskCode("taskCode")
                .openShortLink(0)
                .build());
        when(mockDetailRepository.getHistoryDetailByVin("carVin", BusinessIdEnum.VCS.getCode())).thenReturn(details);

        // Configure OrderNotificationDetailRepository.getValetOrderDetailByPhone(...).
        final List<OrderNotificationDetailDO> orderNotificationDetailDOS = List.of(OrderNotificationDetailDO.builder()
                .sendMessage("sendContent")
                .sendPhone("phone")
                .carVin("carVin")
                .sendTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .sendResult(0)
                .msgResult("failStage")
                .submitErrorCode("failReasons")
                .wgCode("failReasons")
                .openShortLink(0)
                .build());
        when(mockValetDetailRepository.getValetOrderDetailByVin("carVin")).thenReturn(orderNotificationDetailDOS);

        // Configure MessageTaskRepository.getTaskMapByCodes(...).
        final Map<String, NotificationTask> notificationTaskMap = Map.ofEntries(
                Map.entry("value", NotificationTask.builder()
                        .taskCode("taskCode")
                        .taskName("taskName")
                        .taskType(0)
                        .build()));
        when(mockManualTaskRepository.getTaskMapByCodes(List.of("taskCode"))).thenReturn(notificationTaskMap);

        // Configure NotificationAutoTaskRepository.getAutoTaskMapByCodes(...).
        final Map<String, NotificationAutoTaskDO> autoTaskDOMap = Map.ofEntries(
                Map.entry("value", NotificationAutoTaskDO.builder()
                        .taskCode("taskCode")
                        .taskName("taskName")
                        .build()));
        when(mockAutoTaskRepository.getAutoTaskMapByCodes(List.of("taskCode"))).thenReturn(autoTaskDOMap);

        // Configure IcrVehicleApi.vinInfo(...).
        final IcrVehicleListRespVO icrVehicleListRespVO = new IcrVehicleListRespVO();
        icrVehicleListRespVO.setCarVin("carVin");
        icrVehicleListRespVO.setSeriesCode("seriesCode");
        icrVehicleListRespVO.setSeriesName("seriesName");
        icrVehicleListRespVO.setBrandName("brand");
        icrVehicleListRespVO.setConfigName("configName");
        final CommonResult<List<IcrVehicleListRespVO>> listCommonResult = CommonResult.success(
                List.of(icrVehicleListRespVO));
        when(mockIcrVehicleApi.vinInfo(List.of("carVin"))).thenReturn(listCommonResult);

        // Configure NotificationHistoryRepository.queryHistoryByInstanceCode(...).
        final NotificationHistory history = NotificationHistory.builder()
                .taskCode("taskCode")
                .taskInstanceCode("instanceCode")
                .taskSendTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .sendTotalCount(0)
                .sendFailCount(0)
                .reachUserSuccessCount(0)
                .reachUserFailCount(0)
                .openShortLinkCount(0)
                .build();
        when(mockHistoryRepository.queryHistoryByInstanceCode("taskInstanceCode")).thenReturn(history);

        when(mockSnowflake.nextIdStr()).thenReturn("taskInstanceCode");

        // Run the test
        final CommonResult<List<NotificationLogSingleVO>> result = sendHistoryServiceImplUnderTest.querySmsRecordListBySingle(
                singleDTO);

        // Verify the results
        assertEquals(2, result.getData().size());
    }

    @Test
    public void testQuerySmsRecordListBySingle_LRE_vin() {
        // Setup
        final NotificationLogSingleDTO singleDTO = NotificationLogSingleDTO.builder()
                .carVin("carVin")
                .businessCode(BusinessIdEnum.VCS.getCode())
                .build();
        final CommonResult<List<NotificationLogSingleVO>> expectedResult = CommonResult.success(
                List.of(NotificationLogSingleVO.builder()
                        .taskInstanceCode("taskInstanceCode")
                        .taskName("taskName")
                        .sendType("sendType")
                        .sendContent("sendContent")
                        .sendTime("sendTime")
                        .phone("phone")
                        .carVin("carVin")
                        .brand("brand")
                        .configName("configName")
                        .sendStatus("sendStatus")
                        .failStage("failStage")
                        .failReasons("failReasons")
                        .openShortLink(0)
                        .build()));

        // Configure SendHistoryDetailRepository.getHistoryDetailByPhone(...).
        final List<SendHistoryDetail> details = List.of(SendHistoryDetail.builder()
                .taskInstanceCode("taskInstanceCode")
                .sendMessage("sendContent")
                .sendPhone("phone")
                .carVin("carVin")
                .sendResult(0)
                .submitErrorCode("failReasons")
                .msgResult("failStage")
                .wgCode("failReasons")
                .taskCode("taskCode")
                .openShortLink(0)
                .build());
        when(mockDetailRepository.getHistoryDetailByVin("carVin", BusinessIdEnum.VCS.getCode())).thenReturn(details);

        // Configure OrderNotificationDetailRepository.getValetOrderDetailByPhone(...).
        final List<OrderNotificationDetailDO> orderNotificationDetailDOS = List.of(OrderNotificationDetailDO.builder()
                .sendMessage("sendContent")
                .sendPhone("phone")
                .carVin("carVin")
                .sendTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .sendResult(0)
                .msgResult("failStage")
                .submitErrorCode("failReasons")
                .wgCode("failReasons")
                .openShortLink(0)
                .build());
        when(mockValetDetailRepository.getValetOrderDetailByVin("carVin")).thenReturn(orderNotificationDetailDOS);

        // Configure MessageTaskRepository.getTaskMapByCodes(...).
        final Map<String, NotificationTask> notificationTaskMap = Map.ofEntries(
                Map.entry("value", NotificationTask.builder()
                        .taskCode("taskCode")
                        .taskName("taskName")
                        .taskType(0)
                        .build()));
        when(mockManualTaskRepository.getTaskMapByCodes(List.of("taskCode"))).thenReturn(notificationTaskMap);

        // Configure NotificationAutoTaskRepository.getAutoTaskMapByCodes(...).
        final Map<String, NotificationAutoTaskDO> autoTaskDOMap = Map.ofEntries(
                Map.entry("value", NotificationAutoTaskDO.builder()
                        .taskCode("taskCode")
                        .taskName("taskName")
                        .build()));
        when(mockAutoTaskRepository.getAutoTaskMapByCodes(List.of("taskCode"))).thenReturn(autoTaskDOMap);

        // Configure IcrVehicleApi.vinInfo(...).
        final IcrVehicleListRespVO icrVehicleListRespVO = new IcrVehicleListRespVO();
        icrVehicleListRespVO.setCarVin("carVin");
        icrVehicleListRespVO.setSeriesCode("seriesCode");
        icrVehicleListRespVO.setSeriesName("seriesName");
        icrVehicleListRespVO.setBrandName("brand");
        icrVehicleListRespVO.setConfigName("configName");
        final CommonResult<List<IcrVehicleListRespVO>> listCommonResult = CommonResult.success(
                List.of(icrVehicleListRespVO));
        when(mockIcrVehicleApi.vinInfo(List.of("carVin"))).thenReturn(listCommonResult);

        // Configure NotificationHistoryRepository.queryHistoryByInstanceCode(...).
        final NotificationHistory history = NotificationHistory.builder()
                .taskCode("taskCode")
                .taskInstanceCode("instanceCode")
                .taskSendTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .sendTotalCount(0)
                .sendFailCount(0)
                .reachUserSuccessCount(0)
                .reachUserFailCount(0)
                .openShortLinkCount(0)
                .build();
        when(mockHistoryRepository.queryHistoryByInstanceCode("taskInstanceCode")).thenReturn(history);

        when(mockSnowflake.nextIdStr()).thenReturn("taskInstanceCode");

        // Run the test
        final CommonResult<List<NotificationLogSingleVO>> result = sendHistoryServiceImplUnderTest.querySmsRecordListBySingle(
                singleDTO);

        // Verify the results
        assertEquals(2, result.getData().size());
    }


    @Test
    public void testGetTaskSendTime() {
        assertEquals("2020-01-01 00:00",
                sendHistoryServiceImplUnderTest.getTaskSendTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
    }

    @Test
    public void testGetTaskTypeDesc() {
        // Setup
        final Map<String, NotificationTask> oldTaskMap = Map.ofEntries(Map.entry("value", NotificationTask.builder()
                .taskCode("taskCode")
                .taskName("taskName")
                .taskType(0)
                .build()));
        final Map<String, NotificationAutoTaskDO> autoTaskDOMap = Map.ofEntries(
                Map.entry("value", NotificationAutoTaskDO.builder()
                        .taskCode("taskCode")
                        .taskName("taskName")
                        .build()));

        // Run the test
        final String result = sendHistoryServiceImplUnderTest.getTaskTypeDesc("taskCode", oldTaskMap, autoTaskDOMap);

        // Verify the results
        assertEquals("", result);
    }

    @Test
    public void testGetTaskTypeDesc_old() {
        // Setup
        final Map<String, NotificationTask> oldTaskMap = Map.ofEntries(Map.entry("taskCode", NotificationTask.builder()
                .taskCode("taskCode")
                .taskName("taskName")
                .taskType(1)
                .build()));
        final Map<String, NotificationAutoTaskDO> autoTaskDOMap = Map.ofEntries(
                Map.entry("value", NotificationAutoTaskDO.builder()
                        .taskCode("taskCode")
                        .taskName("taskName")
                        .build()));

        // Run the test
        final String result = sendHistoryServiceImplUnderTest.getTaskTypeDesc("taskCode", oldTaskMap, autoTaskDOMap);

        // Verify the results
        assertEquals("自动通知任务", result);
    }

    @Test
    public void testGetTaskTypeDesc_auto() {
        // Setup
        final Map<String, NotificationTask> oldTaskMap = Map.ofEntries(Map.entry("value", NotificationTask.builder()
                .taskCode("taskCode")
                .taskName("taskName")
                .taskType(1)
                .build()));
        final Map<String, NotificationAutoTaskDO> autoTaskDOMap = Map.ofEntries(
                Map.entry("taskCode", NotificationAutoTaskDO.builder()
                        .taskCode("taskCode")
                        .taskName("taskName")
                        .build()));

        // Run the test
        final String result = sendHistoryServiceImplUnderTest.getTaskTypeDesc("taskCode", oldTaskMap, autoTaskDOMap);

        // Verify the results
        assertEquals("自动通知任务", result);
    }
}
