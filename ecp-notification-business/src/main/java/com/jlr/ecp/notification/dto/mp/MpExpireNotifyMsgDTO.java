package com.jlr.ecp.notification.dto.mp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Data
@Schema(description = "小程序-到期通知消息DTO")
public class MpExpireNotifyMsgDTO extends MpBaseMessageDTO {
    @JsonProperty("parameters")
    private MpExpireParametersDTO parameters;
}
