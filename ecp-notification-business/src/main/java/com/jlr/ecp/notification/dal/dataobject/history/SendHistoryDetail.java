package com.jlr.ecp.notification.dal.dataobject.history;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 每条消息发送的详情
 *
 * <AUTHOR>
 * */
@TableName("t_notification_history_detail")
@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class SendHistoryDetail extends BaseDO {
    /**
     * 主键，自增
     * */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务线编码
     * */
    @TableField("business_code")
    private String businessCode;

    /**
     * 信息发送UUID
     * */
    @TableField(value = "msgid")
    private String msgId;

    /**
     * 实例code
     * */
    @TableField(value = "task_instance_code")
    private String taskInstanceCode;

    /**
     * 发送消息内容，不要超过900字符
     * */
    @TableField(value = "send_message")
    private String sendMessage;

    /**
     * 发送电话号码
     * */
    @TableField(value = "send_phone")
    private String sendPhone;

    /**
     *  车架号
     * */
    @TableField(value = "car_vin")
    private String carVin;

    /**
     *  发送时间
     * */
    @TableField(value = "send_time")
    private LocalDateTime sendTime;

    /**
     * 发送结果 0失败 1成功 2待发
     * */
    @TableField(value = "send_result")
    private Integer sendResult;

    /**
     *  提交到短信代理商错误code
     * */
    @TableField(value = "submit_error_code")
    private String submitErrorCode;

    /**
     * 提交短息到代理商错误原因
     * */
    @TableField(value = "error_message")
    private String errorMessage;


    /**
     *  最终短信触达结果: 0——成功；1——接口处理失败；2——运营商网关失败；
     * */
    @TableField(value = "msg_result")
    private String msgResult;

    /**
     *  短信触达错误描述, 当status 为1 时，以desc 的错误码为准
     * */
    @TableField(value = "msg_error_desc")
    private String msgErrorDesc;

    /**
     *  当status 为2 时，表示运营商网关返回的原始值
     * */
    @TableField(value = "wg_code")
    private String wgCode;


    /**
     * 发送任务code
     * */
    @TableField(value = "task_code")
    private String taskCode;

    /**
     * 发送任务类型 1：实时发送 2：定时发送
     * */
    @TableField(value = "task_send_type")
    private Integer taskSendType;

    /**
     * 品牌code
     * */
    @TableField(value = "brand_code")
    private String brandCode;

    /**
     *  是否打开短链，0否 1是
     * */
    @TableField(value = "open_short_link")
    private Integer openShortLink;

    /**
     *  租户号
     * */
    @TableField(value = "tenant_id")
    private Integer tenantId;

}
