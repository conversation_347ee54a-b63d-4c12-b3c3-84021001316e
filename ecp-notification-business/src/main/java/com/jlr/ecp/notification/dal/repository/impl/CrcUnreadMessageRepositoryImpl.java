package com.jlr.ecp.notification.dal.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.notification.dal.dataobject.crc.CrcMessagesDO;
import com.jlr.ecp.notification.dal.mysql.crc.CrcMessagesDOMapper;
import com.jlr.ecp.notification.dal.repository.CrcUnreadMessageRepository;
import com.jlr.ecp.notification.enums.crc.CrcMessageStatusEnum;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class CrcUnreadMessageRepositoryImpl extends ServiceImpl<CrcMessagesDOMapper, CrcMessagesDO>
        implements CrcUnreadMessageRepository {
    /**
     * 保存消息记录
     *
     * @param crcMessagesDO 消息记录对象，包含消息的相关信息
     * @return 返回保存操作是否成功true表示成功，false表示失败
     */
    @Override
    public boolean saveCrcMessagesDO(CrcMessagesDO crcMessagesDO) {
        return save(crcMessagesDO);
    }

    /**
     * 根据记录ID和消息状态查找CRC消息数量
     *
     * @param jilId 消息记录ID，用于定位特定的消息记录
     * @param status 消息的状态，用于过滤出具有特定状态的消息
     * @return 符合条件的消息数量
     * */
    @Override
    public Long findCrcMsgByJlrIdAndStatus(String jilId, Integer status, String businessCode) {
        LambdaQueryWrapper<CrcMessagesDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CrcMessagesDO::getJlrId, jilId)
                .eq(CrcMessagesDO::getMessageStatus, status)
                .eq(CrcMessagesDO::getIsDeleted, false)
                .eq(CrcMessagesDO::getBusinessCode, businessCode); // TODO 需要数据清洗处理 VCS 历史数据
        return count(queryWrapper);
    }

    /**
     * 根据记录ID更新所有消息的状态为已读
     *
     * @param jilId 记录ID，用于标识需要更新的消息集合
     * @return 返回更新操作是否成功
     */
    @Override
    public boolean updateCrcAllMsgStatusByJlrId(String jilId, String businessCode) {
        CrcMessagesDO crcMessagesDO = CrcMessagesDO.builder()
                .messageStatus(CrcMessageStatusEnum.READ.getCode())
                .build();
        crcMessagesDO.setUpdatedTime(LocalDateTime.now());
        LambdaUpdateWrapper<CrcMessagesDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CrcMessagesDO::getJlrId, jilId)
                .eq(CrcMessagesDO::getMessageStatus, CrcMessageStatusEnum.UNREAD.getCode())
                .eq(CrcMessagesDO::getIsDeleted, false)
                .eq(CrcMessagesDO::getBusinessCode, businessCode); // TODO 需要数据清洗处理 VCS 历史数据
        return update(crcMessagesDO, updateWrapper);
    }
}
