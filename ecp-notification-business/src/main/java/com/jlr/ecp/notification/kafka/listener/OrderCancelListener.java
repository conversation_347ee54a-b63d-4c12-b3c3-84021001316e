package com.jlr.ecp.notification.kafka.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import com.alibaba.fastjson.JSON;
import com.jlr.ecp.notification.constant.MpEventTypeConstants;
import com.jlr.ecp.notification.dal.dataobject.mp.MpNoticeOrderSubscriptionDO;
import com.jlr.ecp.notification.dal.dataobject.mp.MpTemplateNoticeRecordDO;
import com.jlr.ecp.notification.dal.repository.mp.MpNoticeOrderSubscriptionRepository;
import com.jlr.ecp.notification.dal.repository.mp.MpTemplateNoticeRecordRepository;
import com.jlr.ecp.notification.dto.mp.MpOrderChangeMsgDTO;
import com.jlr.ecp.notification.dto.mp.MpOrderParametersMsgDTO;
import com.jlr.ecp.notification.enums.BrandCodeEnum;
import com.jlr.ecp.notification.enums.mp.MpMsgTypeEnum;
import com.jlr.ecp.notification.kafka.message.OrderMessage;
import com.jlr.ecp.notification.kafka.send.template.SmsSendTemplate;
import com.jlr.ecp.notification.properties.MpMsgProperties;
import com.jlr.ecp.notification.properties.SnsProperties;
import com.jlr.ecp.notification.util.mp.SnsUtil;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import software.amazon.awssdk.services.sns.SnsClient;

import javax.annotation.Resource;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class OrderCancelListener {
    @Resource
    private SmsSendTemplate smsSendTemplate;

    @Resource
    private MpNoticeOrderSubscriptionRepository orderCancelRepository;

    @Resource
    private MpMsgProperties mpMsgProperties;

    @Resource
    private SnsProperties snsProperties;

    @Resource
    private SnsUtil snsUtil;

    @Resource
    private MpTemplateNoticeRecordRepository mpRecordRepository;

    @Resource
    private Snowflake snowflake;

    /**
     * 订单取消成功发送消息
     * @param records 消息内容
     * */
    @KafkaListener(topics = "order-cancel-topic", groupId = "order-cancel-group", batch = "true",
            properties = "max.poll.records:100")
    public void listen(List<ConsumerRecord<String, String >> records) {
        log.info("取消订单消息，records:{}", records);
        List<OrderMessage> orderMessageList = buildOrderMessageList(records);
        for (OrderMessage orderMessage : orderMessageList) {
            smsSendTemplate.sendSmsMsg(orderMessage);
        }
        //发送小程序模板消息-订单取消通知
        sendOrderCancelMsgToSns(orderMessageList);
    }

    /**
     * 向小程序发送订单取消消息
     *
     * @param orderMessageList 一个包含多个OrderMessage对象的列表，用于发送订单取消通知
     */
    public void sendOrderCancelMsgToSns(List<OrderMessage> orderMessageList) {
        log.info("向小程序发送订单取消消息, 数量：{}", orderMessageList.size());
        List<String> parentOrderCodeList = orderMessageList.stream()
                .map(OrderMessage::getParentOrderCode)
                .collect(Collectors.toList());
        Map<String, MpNoticeOrderSubscriptionDO> orderChangeMap = orderCancelRepository.queryOrderSubscribeMapByList(parentOrderCodeList,
                MpMsgTypeEnum.ORDER_CANCEL.getCode());
        if (CollUtil.isEmpty(orderChangeMap)) {
            log.info("根据订单号列表查询订单订阅信息Map为空, 不发送MP订单取消通知, parentOrderCodeList:{}", parentOrderCodeList);
            return ;
        }
        List<MpTemplateNoticeRecordDO> mpRecordDOList = new ArrayList<>();
        List<MpNoticeOrderSubscriptionDO> orderChangeList = new ArrayList<>();
        //发送小程序模板消息-订单状态变化通知
        for (OrderMessage orderMessage : orderMessageList) {
            MpNoticeOrderSubscriptionDO orderChangeDO = orderChangeMap.get(orderMessage.getParentOrderCode());
            MpOrderChangeMsgDTO orderChangeMsgDTO = buildMpOrderCancelMsgDTO(orderMessage, orderChangeDO);
            if (Objects.isNull(orderChangeMsgDTO)) {
                log.info("MpNoticeOrderSubscriptionDO为空, 不发送MP订单取消通知, orderMessage:{}", orderMessage);
                continue;
            }
            String messageGroupId = getOrderChangeEventType(orderMessage.getBrandCode());
            String messageId = sendMpMsgToSns(orderChangeMsgDTO, messageGroupId);

            // 发送到sns
            if (StringUtil.isEmpty(messageId)) {
                log.info("发送小程序模板消息-订单取消通知失败, orderChangeMsgDTO:{}", orderChangeMsgDTO);
            }
            mpRecordDOList.add(buildMpTemplateNoticeRecordDO(orderChangeMsgDTO, messageId));

            orderChangeDO.setOrderCancelFlag(calculateOrderCancelFlag(orderChangeDO, messageId));
            orderChangeDO.setUpdatedTime(LocalDateTime.now());
            orderChangeList.add(orderChangeDO);
        }
        // 保存发送记录
        mpRecordRepository.saveBatch(mpRecordDOList);
        //更新订阅记录
        orderCancelRepository.updateBatchById(orderChangeList);
    }

    /**
     * 计算订单状态变化标志位。
     *
     * @param orderChange 订单变更信息对象，包含订单成功标志位等信息
     * @param messageId 消息ID，用于判断是否需要返回订单成功标志位
     * @return 订单成功标志位，0 或 `orderChange` 中的 `orderSuccessFlag`
     */
    private Integer calculateOrderCancelFlag(MpNoticeOrderSubscriptionDO orderChange, String messageId) {
        if (Objects.isNull(orderChange) || Objects.isNull(orderChange.getOrderCancelFlag())) {
            return 0;
        }
        if (StringUtils.isBlank(messageId)) {
            return orderChange.getOrderCancelFlag();
        }
        return 0;
    }

    /**
     * 发送订单取消消息到SNS（Amazon Simple Notification Service）主题
     *
     * @param orderChangeMsgDTO 订单变更消息数据传输对象，包含需要发送到SNS的订单变更信息
     */
    public String sendMpMsgToSns(MpOrderChangeMsgDTO orderChangeMsgDTO, String messageGroupId) {
        log.info("发送订单取消消息到SNS, orderChangeMsgDTO:{}", orderChangeMsgDTO);
        try {
            SnsClient snsClient = snsUtil.getSnsClient();
            String message = JSON.toJSONString(orderChangeMsgDTO);
            return snsUtil.publishFIFOTopic(message, snsProperties.getTopicArn(), snsClient, messageGroupId, snowflake.nextIdStr());
        } catch (Exception e) {
            // 使用StringWriter和PrintWriter来获取堆栈跟踪的字符串表示
            StringWriter sw = new StringWriter();
            e.printStackTrace(new PrintWriter(sw));
            String exceptionAsString = sw.toString();
            // 打印异常信息和堆栈跟踪
            log.info("发送订单取消消息到SNS，异常: {}", e.getMessage());
            log.info("发送订单取消消息到SNS，异常堆栈跟踪: {}", exceptionAsString);
        }
        return "";
    }

    /**
     * 构建小程序模板消息记录实体对象
     *
     * @param orderChangeMsgDTO 订单变更消息数据传输对象，包含消息原始数据
     * @param messageId 消息ID，用于标识消息
     * @return MpTemplateNoticeRecordDO 构建完成的模板消息记录实体对象
     */
    private MpTemplateNoticeRecordDO buildMpTemplateNoticeRecordDO(MpOrderChangeMsgDTO orderChangeMsgDTO, String messageId) {
        MpTemplateNoticeRecordDO mpRecordDO = new MpTemplateNoticeRecordDO();
        mpRecordDO.setMessageId(messageId);
        mpRecordDO.setJlrId(orderChangeMsgDTO.getJlrId());
        mpRecordDO.setOpenId(orderChangeMsgDTO.getOpenId());
        mpRecordDO.setSnsTopic("ECP_VCS_TEMPLATE_MESSAGE");
        mpRecordDO.setEventType(orderChangeMsgDTO.getEventType());
        mpRecordDO.setOrderNo(orderChangeMsgDTO.getBusinessNo());
        mpRecordDO.setSendMessage(JSON.toJSONString(orderChangeMsgDTO));
        return mpRecordDO;
    }

    /**
     * 构建MpOrderChangeMsgDTO对象
     *
     * @param orderMessage 订单消息对象，包含激活相关信息
     * @param orderChange 订单变更通知对象，包含订单变更细节
     * @return 返回构建好的MpOrderChangeMsgDTO对象，如果输入参数为空则返回null
     */
    private MpOrderChangeMsgDTO buildMpOrderCancelMsgDTO(OrderMessage orderMessage,
                                                         MpNoticeOrderSubscriptionDO orderChange) {
        log.info("构建MpOrderChangeMsgDTO对象, orderMessage:{}, orderChange:{}", orderMessage, orderChange);
        if (Objects.isNull(orderMessage) || Objects.isNull(orderChange)) {
            return null;
        }
        MpOrderChangeMsgDTO orderChangeMsgDTO = new MpOrderChangeMsgDTO();
        orderChangeMsgDTO.setClientId("VCS_ECP");
        orderChangeMsgDTO.setJlrId(orderChange.getJlrId());
        String eventType = getOrderChangeEventType(orderMessage.getBrandCode());
        orderChangeMsgDTO.setEventType(eventType);
        orderChangeMsgDTO.setBusinessNo(orderMessage.getOrderNumber());
        orderChangeMsgDTO.setApp(mpMsgProperties.getApp());
        orderChangeMsgDTO.setOpenId(orderChange.getOpenId());
        MpOrderParametersMsgDTO orderParametersMsgDTO = MpOrderParametersMsgDTO.builder()
                .orderNo(orderMessage.getOrderNumber())
                .reminder("订单已取消，请留意退款信息。")
                .build();
        orderChangeMsgDTO.setParameters(orderParametersMsgDTO);
        return orderChangeMsgDTO;
    }

    /**
     * 根据品牌代码获取订单取消事件类型。
     *
     * @param brandCode 品牌代码，用于标识不同的品牌。
     * @return String 返回对应品牌的订单取消事件类型。
     */
    private String getOrderChangeEventType(Integer brandCode) {
        String eventType = "";
        if (BrandCodeEnum.LAND_ROVER.getCode().equals(brandCode)) {
            eventType = MpEventTypeConstants.VCS_LAN_ORDER_CANCEL_EVENT_TYPE;
        } else if (BrandCodeEnum.JAGUAR.getCode().equals(brandCode)) {
            eventType = MpEventTypeConstants.VCS_JAG_ORDER_CANCEL_EVENT_TYPE_DESC;
        }
        return eventType;
    }


    /**
     *  构建订单消息
     *  @param records 消息体
     *  @return  List<OrderMessage>
     * */
    public List<OrderMessage> buildOrderMessageList(List<ConsumerRecord<String, String >> records) {
        List<OrderMessage> orderMessageList = new ArrayList<>();
        if (CollectionUtils.isEmpty(records)) {
            return orderMessageList;
        }
        for (ConsumerRecord<String, String> record : records) {
            OrderMessage orderMessage = JSON.parseObject(record.value(), OrderMessage.class);
            orderMessageList.add(orderMessage);
        }
        return orderMessageList;
    }

}
