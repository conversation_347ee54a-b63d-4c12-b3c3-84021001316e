package com.jlr.ecp.notification.dal.dataobject.task;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

@TableName("t_task_modify_log")
@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class TaskModifyLog extends BaseDO {
    /**
     * 主键id, 自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务编码（原task_code）
     */
    @TableField(value = "task_code")
    private String taskCode;

    /**
     * 修改模块
     */
    @TableField(value = "modify_module")
    private String modifyModule;

    /**
     * 修改字段数量
     */
    @TableField(value = "modify_field_count")
    private Integer modifyFieldCount;

    /**
     * 修改前字段值（JSON格式）
     */
    @TableField(value = "modify_field_old_value")
    private String modifyFieldOldValue;

    /**
     * 修改后字段值（JSON格式）
     */
    @TableField(value = "modify_field_new_value")
    private String modifyFieldNewValue;

    /**
     * 修改时间（原modify_time）
     */
    @TableField(value = "operate_time")
    private LocalDateTime operateTime;

    /**
     * 修改人账号（原modify_user）
     */
    @TableField(value = "operate_user")
    private String operateUser;

    /**
     * 租户号
     */
    @TableField(value = "tenant_id")
    private Integer tenantId;

    // 以下字段继承自 BaseDO，无需重复定义：
    // private String createdBy;
    // private LocalDateTime createdTime;
    // private String updatedBy;
    // private LocalDateTime updatedTime;
    // private Integer isDeleted;
    // private Integer revision;
}
