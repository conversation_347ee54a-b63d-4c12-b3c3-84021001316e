package com.jlr.ecp.notification.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.notification.api.dto.ExpireNotifyConditionDTO;
import com.jlr.ecp.notification.constant.BusinessConstants;
import com.jlr.ecp.notification.constant.Constants;
import com.jlr.ecp.notification.dal.dataobject.task.TaskModifyLog;
import com.jlr.ecp.notification.dal.dataobject.task.auto.AutoTaskConditionDO;
import com.jlr.ecp.notification.dal.dataobject.task.auto.AutoTaskTriggerMapDO;
import com.jlr.ecp.notification.dal.dataobject.task.auto.NotificationAutoTaskDO;
import com.jlr.ecp.notification.dal.dataobject.template.MessageTemplate;
import com.jlr.ecp.notification.dal.repository.*;
import com.jlr.ecp.notification.enums.ErrorCodeConstants;
import com.jlr.ecp.notification.enums.task.*;
import com.jlr.ecp.notification.enums.template.AutoTemplateTimeType;
import com.jlr.ecp.notification.enums.template.SmsTemplateTypeEnum;
import com.jlr.ecp.notification.req.task.*;
import com.jlr.ecp.notification.req.task.auto.AutoTaskCreatReq;
import com.jlr.ecp.notification.req.task.auto.AutoTaskModifyTimeReq;
import com.jlr.ecp.notification.req.task.auto.TriggerActionFieldReq;
import com.jlr.ecp.notification.req.task.auto.TriggerActionReq;
import com.jlr.ecp.notification.service.SmsAutoTaskService;
import com.jlr.ecp.notification.util.OperatorUtil;
import com.jlr.ecp.notification.util.sms.TimeFormatUtil;
import com.jlr.ecp.notification.vo.task.SmsTaskPageListVo;
import com.jlr.ecp.notification.vo.task.TaskHistoryDetailVO;
import com.jlr.ecp.notification.vo.task.TaskHistoryPageListVo;
import com.jlr.ecp.notification.vo.task.TaskStatusVO;
import com.jlr.ecp.notification.vo.task.auto.*;
import com.jlr.ecp.product.api.spu.ProductSpuApi;
import com.jlr.ecp.product.api.spu.dto.SpuInfoDTO;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RMap;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.*;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static com.jlr.ecp.notification.enums.task.AutoNotificationTaskFieldEnum.EDIT_SCHEDULE;

@Service
@Slf4j
public class SmsAutoTaskServiceImpl implements SmsAutoTaskService {

    @Resource
    private MessageTemplateRepository templateRepository;

    @Resource
    private NotificationAutoTaskRepository autoTaskRepository;

    @Resource
    private MessageTaskRepository manualTaskRepository;

    @Resource
    private TaskModifyRepository taskModifyRepository;

    @Resource
    private AutoTaskConditionRepository conditionRepository;

    @Resource
    private AutoTaskTriggerMapRepository triggerMapRepository;

    @Resource
    private Snowflake snowflake;

    @Resource
    private ProductSpuApi productSpuApi;

    @Resource
    private Redisson redisson;

    @Resource
    private ApplicationContext applicationContext;


    private static final String DAY_ZERO = "00:00";


    private static final String START_TIME = " 00:00:00";

    private static final String END_TIME = ":00";

    private static final String BLANK = " ";


    /**
     *  返回自动通知任务的列表页面
     * @param taskPageListReq 任务列表页请求入参
     * @return  PageResult<SmsTaskPageListVo>
     * */
    @Override
    public PageResult<SmsTaskPageListVo> getTaskPageList(TaskPageListReq taskPageListReq) {
        Page<NotificationAutoTaskDO> taskPage = autoTaskRepository.queryAutoTaskPageList(taskPageListReq);
        if (CollUtil.isEmpty(taskPage.getRecords())) {
            log.info("返回自动通知任务的列表页面, 查询结果为空, taskPageListReq:{}", taskPageListReq);
            return new PageResult<>();
        }
        List<NotificationAutoTaskDO> taskList = taskPage.getRecords();
        List<SmsTaskPageListVo> taskPageListVos = buildSmsTaskPageList(taskList);
        return new PageResult<>(taskPageListVos, taskPage.getTotal());
    }

    /**
     * 获取自动任务状态视图对象列表。
     *
     * @return 包含任务状态码和描述信息的视图对象列表
     */
    @Override
    public List<TaskStatusVO> getAutoTaskStatusVO() {
        List<TaskStatusVO> resp = new ArrayList<>();
        for (TaskStatusEnum taskStatusEnum : TaskStatusEnum.values()) {
            if (TaskStatusEnum.FINISH_SEND.equals(taskStatusEnum)) {
                continue;
            }
            TaskStatusVO taskStatusVO = TaskStatusVO.builder()
                    .status(taskStatusEnum.getStatus())
                    .desc(taskStatusEnum.getDesc())
                    .build();
            resp.add(taskStatusVO);
        }
        return resp;
    }

    /**
     * 自动任务详情页查询
     * @param taskDetailReq 任务详情页入参
     * @return CommonResult<TaskDetailVo>
     */
    @Override
    public CommonResult<AutoTaskDetailVO> getAutoTaskDetail(TaskDetailReq taskDetailReq) {
        AutoTaskDetailVO taskDetailVo = new AutoTaskDetailVO();
        NotificationAutoTaskDO autoTask = autoTaskRepository.queryAutoTaskByCode(taskDetailReq.getTaskCode());
        if (Objects.isNull(autoTask)) {
            log.info("自动任务任务详情页查询, 查询任务为空, taskDetailReq:{}", taskDetailReq);
            return CommonResult.success(taskDetailVo);
        }
        MessageTemplate messageTemplate = templateRepository.getMessageTemplateByCode(autoTask.getMessageTemplateCode());
        taskDetailVo.setTaskCode(autoTask.getTaskCode());
        taskDetailVo.setTaskName(autoTask.getTaskName());
        taskDetailVo.setTriggerActionString(autoTask.getTriggerAction());
        taskDetailVo.setTriggerAction(getAutoTaskDetailTriggerAction(taskDetailReq.getTaskCode()));
        taskDetailVo.setRangeBeginDate(TimeFormatUtil.timeToStringByFormat(autoTask.getRangeBeginDate(), TimeFormatUtil.formatter_7));
        taskDetailVo.setRangeEndDate(TimeFormatUtil.timeToStringByFormat(autoTask.getRangeEndDate(), TimeFormatUtil.formatter_7));
        taskDetailVo.setDailySendTime(getAutoTaskSendTime(autoTask));
        taskDetailVo.setTaskTimeType(autoTask.getTaskTimeType());
        taskDetailVo.setNotifySpuCode(autoTask.getNotifySpuCode());
        taskDetailVo.setNotifySpuName(querySpuNameByCode(autoTask.getNotifySpuCode()));
        taskDetailVo.setStatus(autoTask.getStatus());
        if (Objects.nonNull(messageTemplate)) {
            taskDetailVo.setMessageTemplateCode(messageTemplate.getTemplateCode());
            taskDetailVo.setTemplateName(messageTemplate.getTemplateName());
            taskDetailVo.setTemplateType(getTaskTemplateType(messageTemplate.getTemplateType()));
            taskDetailVo.setMessageTemplateContent(messageTemplate.getTemplateContent());
        }
        return CommonResult.success(taskDetailVo);
    }

    /**
     * 获取自动任务的发送时间描述
     * @param autoTask 自动任务对象
     * @return 发送时间描述字符串
     * */
    private String getAutoTaskSendTime(NotificationAutoTaskDO autoTask) {
        if (Objects.isNull(autoTask)) {
            return "";
        }
        if (TaskSendTypeEnum.REALTIME.getCode().equals(autoTask.getTaskSendType())) {
            return "实时";
        }
        return autoTask.getDailySendTime();
    }

    /**
     * 根据任务代码获取自动任务详情中的触发动作
     *
     * @param taskCode 任务代码，用于唯一标识一个自动任务
     * @return 返回一个ConditionAttributeVO对象列表
     */
    private List<ConditionAttributeVO> getAutoTaskDetailTriggerAction(String taskCode) {
        List<AutoTaskTriggerMapDO> taskTriggerMapDOList = triggerMapRepository.getAutoTriggerByTaskCode(taskCode);
        if (CollUtil.isEmpty(taskTriggerMapDOList)) {
            return null;
        }
        List<ConditionAttributeVO> resp = new ArrayList<>();
        for (AutoTaskTriggerMapDO autoTaskTriggerMapDO : taskTriggerMapDOList) {
            resp.add(ConditionAttributeVO.builder()
                    .conditionId(autoTaskTriggerMapDO.getConditionId())
                    .conditionValue(autoTaskTriggerMapDO.getConditionValue())
                    .build());
        }
        return resp;
    }

    /**
     * 根据SPU代码查询SPU名称
     *
     * @param spuCode SPU的唯一代码
     * @return SPU的名称，如果找不到则返回空字符串
     */
    public String querySpuNameByCode(String spuCode) {
        try {
            CommonResult<SpuInfoDTO> resp = productSpuApi.getSpuInfoByCode(spuCode);
            log.info("根据spuCode查询spu信息, spuCode:{}, resp:{}", spuCode, resp);
            if (Objects.isNull(resp) || Objects.isNull(resp.getData())) {
                return "";
            }
            SpuInfoDTO spuInfoDTO = resp.getData();
            return spuInfoDTO.getProductName();
        } catch (Exception e) {
            log.info("根据SPU代码查询SPU名称异常：", e);
        }
        return "";
    }


    /**
     * 根据任务类型获取任务名称列表
     *
     * @param taskType 任务类型，如果为null，则获取所有类型的任务名称列表；
     *                 如果是特定的任务类型，则获取该类型的任务名称列表
     * @return 任务名称列表
     */
    @Override
    public List<String> getTaskNameList(Integer taskType) {
        List<String> resp = new ArrayList<>();
        if (Objects.isNull(taskType)) {
            resp = getAllTaskTypeNameList();
        } else if (TaskTypeEnum.AUTO_TASK.getType().equals(taskType)) {
            resp = getAutoTaskNameList();
        } else if (TaskTypeEnum.MANUAL.getType().equals(taskType)) {
            resp = getManualTaskNameList();
        }
        return resp;
    }

    /**
     * 获取所有任务类型名称列表
     *
     * @return 所有任务类型名称的列表
     */
    public List<String> getAllTaskTypeNameList() {
        List<String> resp = new ArrayList<>();
        resp.addAll(getAutoTaskNameList());
        resp.addAll(getManualTaskNameList());
        return resp;
    }

    /**
     * 获取自动任务名称列表
     *
     * @return List<String> 自动任务名称列表
     */
    public List<String> getAutoTaskNameList() {
        return autoTaskRepository.querytAutoTaskNameList();
    }

    /**
     * 获取手动任务名称列表
     *
     * @return 手动任务名称列表如果数据库中没有手动任务，则返回空列表
     */
    public List<String> getManualTaskNameList() {
        return manualTaskRepository.queryTaskNameListFilterAutoEvent(TaskTypeEnum.MANUAL.getType());
    }

    /**
     *  任务编辑历史分页查询
     * @param taskHistoryReq 编辑任务请求入参
     * @return PageResult<TaskHistoryPageListVo>
     * */
    @Override
    public PageResult<TaskHistoryPageListVo> getTaskHistoryPageList(TaskHistoryReq taskHistoryReq) {
        Page<TaskModifyLog> historyPage = taskModifyRepository.queryTaskHistoryPageList(taskHistoryReq);
        List<TaskModifyLog> taskHistoryList = historyPage.getRecords();
        List<TaskHistoryPageListVo> historyPageListVos = buildTaskHistoryPageList(taskHistoryList);
        return new PageResult<>(historyPageListVos, historyPage.getTotal());
    }

    /**
     * 修改发送任务的定时发送时间
     * @param taskModifyReq 发送任务入参
     * @return CommonResult<Boolean>
     * */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult<Boolean> modifySendTime(AutoTaskModifyTimeReq taskModifyReq) {
        CommonResult<Boolean> checkResp = checkModifySendTimeArg(taskModifyReq);
        if (!checkResp.isSuccess()) {
            return checkResp;
        }
        NotificationAutoTaskDO autoTaskDO = autoTaskRepository.queryAutoTaskByCode(taskModifyReq.getTaskCode());
        if (Objects.isNull(autoTaskDO)) {
            return CommonResult.error(ErrorCodeConstants.TASK_NO_EXIST);
        }

        //获取旧任务数据
        NotificationAutoTaskDO oldTaskDO = new NotificationAutoTaskDO();
        BeanUtils.copyProperties(autoTaskDO, oldTaskDO);
        log.info("修改任务定时发送时间, oldTaskDO:{}", JSON.toJSONString(oldTaskDO));

        if (!TaskSendTypeEnum.SCHEDULED.getCode().equals(autoTaskDO.getTaskSendType())) {
            return CommonResult.error(ErrorCodeConstants.TASK_NOT_TIMED);
        }
        if (TaskStatusEnum.START.getStatus().equals(autoTaskDO.getStatus())
                || TaskStatusEnum.FINISH_SEND.getStatus().equals(autoTaskDO.getStatus())) {
            return CommonResult.error(ErrorCodeConstants.TASK_STATUS_NOT_STOP);
        }
        updateTaskSendTime(autoTaskDO, taskModifyReq);
        // 记录日志
        addTaskModifyTimeLog(oldTaskDO, taskModifyReq);

        return CommonResult.success(true);
    }

    /**
     * 根据模板类型获取模板名称列表
     * @param businessCode 业务线编码
     * @return 返回一个字符串列表，包含所有指定类型的模板的名称
     */
    @Override
    public  List<AutoTemplateNameListVO> getAutoTemplateNameList(String businessCode) {
        List<MessageTemplate> templateList = templateRepository.getTemplateListByType(SmsTemplateTypeEnum.AUTO.getCode(),
                AutoTemplateTimeType.AUTO_CONFIGURATION.getCode(), businessCode);
        return builderAutoTemplateNameListVOList(templateList);
    }

    /**
     * 根据消息模板列表构建自动模板名称列表VO
     *
     * @param templateList 消息模板列表，用于构建VO对象列表
     * @return 返回一个自动模板名称列表VO对象的列表
     */
    public List<AutoTemplateNameListVO> builderAutoTemplateNameListVOList(List<MessageTemplate> templateList) {
        List<AutoTemplateNameListVO> resp = new ArrayList<>();
        if (CollUtil.isEmpty(templateList)) {
            return resp;
        }
        for (MessageTemplate template : templateList) {
            resp.add(builderAutoTemplateNameListVO(template));
        }
        return resp;
    }

    /**
     * 构建自动模板名称列表视图对象
     *
     * @param template 消息模板对象，包含模板的详细信息如果传入的模板为null，则方法返回null
     * @return 返回构建的AutoTemplateNameListVO对象，如果输入为null，则返回null
     */
    public AutoTemplateNameListVO builderAutoTemplateNameListVO(MessageTemplate template) {
        if (Objects.isNull(template)) {
            return null;
        }
        return AutoTemplateNameListVO.builder()
                .templateName(template.getTemplateName())
                .templateCode(template.getTemplateCode())
                .build();
    }

    /**
     * 根据模板代码获取自动模板内容
     *
     * @param templateCode 模板代码，用于唯一标识一个模板
     * @return 返回与模板代码对应的模板内容
     */
    @Override
    public String getAutoTemplateContent(String templateCode) {
        return templateRepository.getTemplateContentByCode(templateCode);
    }

    /**
     * 编辑发送任务启用、禁用
     * @param modifyStatusReq 任务状态编辑
     * @return CommonResult<Boolean>
     * */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult<Boolean> modifyTaskStatus(TaskModifyStatusReq modifyStatusReq) {
        log.info("编辑发送任务启用、禁用, modifyStatusReq:{}", modifyStatusReq);
        CommonResult<Boolean> taskCheck = checkTaskArg(modifyStatusReq);
        if (!taskCheck.isSuccess()) {
            return taskCheck;
        }
        NotificationAutoTaskDO autoTaskDO = autoTaskRepository.queryAutoTaskByCode(modifyStatusReq.getTaskCode());
        if (Objects.isNull(autoTaskDO)) {
            return CommonResult.error(ErrorCodeConstants.TASK_NO_EXIST);
        }
        // 保存旧状态用于日志记录
        NotificationAutoTaskDO oldTaskDO = new NotificationAutoTaskDO();
        BeanUtils.copyProperties(autoTaskDO, oldTaskDO);

        if (TaskStatusEnum.START.getStatus().equals(modifyStatusReq.getModifyStatus())
                && Objects.nonNull(autoTaskDO.getRangeEndDate())
                && autoTaskDO.getRangeEndDate().isBefore(LocalDateTime.now())) {
            log.info("编辑发送任务启用, modifyStatusReq:{}", modifyStatusReq);
            return CommonResult.error(ErrorCodeConstants.AUTO_TASK_EXPIRE);
        }
        updateTaskStatus(autoTaskDO, modifyStatusReq);

        // 记录状态修改日志
        addTaskModifyStatusLog(oldTaskDO, modifyStatusReq);

        updateAutoTaskConditionCache(autoTaskDO);
        return CommonResult.success(true);
    }


    /**
     * 更新自动任务条件缓存
     * 当自动任务的状态或每日发送时间发生变化时，调用此方法更新缓存中的相应信息
     *
     * @param autoTaskDO 自动任务的数据对象，包含任务代码、状态和每日发送时间等信息
     */
    public void updateAutoTaskConditionCache(NotificationAutoTaskDO autoTaskDO) {
        String conditionKey = getAutoTaskConditionRMapKey();
        log.info("更新自动任务条件缓存，conditionKey:{}, autoTaskDO:{}", conditionKey, autoTaskDO);
        RMap<String, Set<ExpireNotifyConditionDTO>> rMapCache = redisson.getMap(conditionKey);
        // 更新数据逻辑保持不变
        List<ExpireNotifyConditionDTO> conditionDTOList = new ArrayList<>();
        for (Map.Entry<String, Set<ExpireNotifyConditionDTO>> entry : rMapCache.entrySet()) {
            for (ExpireNotifyConditionDTO conditionDTO : entry.getValue()) {
                if (conditionDTO.getTaskCode().equals(autoTaskDO.getTaskCode())) {
                    conditionDTO.setDailySendTime(autoTaskDO.getDailySendTime());
                    conditionDTO.setStatus(autoTaskDO.getStatus());
                }
                conditionDTOList.add(conditionDTO);
            }
        }
        Map<String, Set<ExpireNotifyConditionDTO>> map = new HashMap<>();
        for (ExpireNotifyConditionDTO conditionDTO : conditionDTOList) {
            String key = getAutoConditionKey(conditionDTO.getDailySendTime());
            Set<ExpireNotifyConditionDTO> set = map.computeIfAbsent(key, k -> new HashSet<>());
            set.add(conditionDTO);
        }
        // 清空rMap集合并重新放入更新后的数据
        rMapCache.clear();  // 使用clear()方法清空
        rMapCache.putAll(map);
        // 设置rMapCache过期时间到今天最大时间
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime endOfDay = now.toLocalDate().atTime(LocalTime.MAX); // 当天的最大时间
        Duration ttlDuration = Duration.between(now, endOfDay);
        long ttlMillis = ttlDuration.toMillis();
        // 设置过期时间为从现在到今天结束的时间
        rMapCache.expire(Duration.ofMillis(ttlMillis));
        log.info("完成自动任务条件缓存更新，rMapCache将在今天结束时失效,ttlMillis:{}ms", ttlMillis);
        printRMapLog(rMapCache);
    }

    private void printRMapLog(RMap<String, Set<ExpireNotifyConditionDTO>> conditionRMap) {
        log.info("根据条件获取自动任务缓存, conditionRMap:");
        for (Map.Entry<String, Set<ExpireNotifyConditionDTO>> entry : conditionRMap.entrySet()) {
            log.info("Key: {}, Value: {}", entry.getKey(), entry.getValue());
        }
    }


    /**
     * 根据每日发送时间生成自动条件键
     *
     * @param dailySendTime 每日发送时间，格式为字符串
     * @return 返回组合后的自动条件键字符串
     */
    private String getAutoConditionKey(String dailySendTime) {
        return LocalDate.now() + BLANK + dailySendTime;
    }

    /**
     * 生成自动任务条件的Redis映射键
     *
     * @return String 自动任务条件的Redis映射键
     */
    private String getAutoTaskConditionRMapKey() {
        return Constants.AUTO_TASK_CONDITION_KEY + LocalDate.now();
    }

    /**
     * 查询自动任务条件信息
     *
     * @return AutoTaskConditionVO 包含各种自动任务条件信息的视图对象
     */
    @Override
    public AutoTaskConditionVO queryAutoTaskCondition() {
        List<AutoTaskConditionDO> brandList = conditionRepository.queryConditionByType(AutoTaskConditionTypeEnum.BRAND.getType());
        List<AutoTaskConditionDO> vehicleOriginList = conditionRepository.queryConditionByType(AutoTaskConditionTypeEnum.VEHICLE_ORIGIN.getType());
        List<AutoTaskConditionDO> expirationServiceList = conditionRepository.queryConditionByType(AutoTaskConditionTypeEnum.EXPIRATION_SERVICE.getType());
        List<AutoTaskConditionDO> realNameStatusList = conditionRepository.queryConditionByType(AutoTaskConditionTypeEnum.REAL_NAME_STATUS.getType());
        List<AutoTaskConditionDO> expirationIntervalList = conditionRepository.queryConditionByType(AutoTaskConditionTypeEnum.EXPIRATION_INTERVAL.getType());
        List<TaskConditionAttributeVO> attributeVOList = new ArrayList<>();
        TaskConditionAttributeVO brandVO = buildTaskConditionAttributeVOList(brandList, false);
        brandVO.setRequestName("brandReq");
        TaskConditionAttributeVO vehicleOriginVO = buildTaskConditionAttributeVOList(vehicleOriginList, false);
        vehicleOriginVO.setRequestName("vehicleOriginReq");
        TaskConditionAttributeVO expirationServiceVO = buildTaskConditionAttributeVOList(expirationServiceList, false);
        expirationServiceVO.setRequestName("expirationServiceReq");
        TaskConditionAttributeVO realNameStatusVO = buildTaskConditionAttributeVOList(realNameStatusList, false);
        realNameStatusVO.setRequestName("realNameStatusReq");
        TaskConditionAttributeVO expirationIntervalVO = buildTaskConditionAttributeVOList(expirationIntervalList, true);
        expirationIntervalVO.setRequestName("expirationIntervalReq");
        attributeVOList.add(brandVO);
        attributeVOList.add(vehicleOriginVO);
        attributeVOList.add(expirationServiceVO);
        attributeVOList.add(realNameStatusVO);
        attributeVOList.add(expirationIntervalVO);
        return AutoTaskConditionVO.builder()
                .attributeVOList(attributeVOList)
                .build();
    }

    /**
     * 添加自动任务
     *
     * @param autoTaskCreatReq 自动任务创建请求对象，包含创建任务所需的信息
     * @return 返回一个CommonResult对象，包含操作是否成功的布尔值
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> addAutoTask(AutoTaskCreatReq autoTaskCreatReq) {
        String taskCode = snowflake.nextIdStr();
        List<AutoTaskTriggerMapDO> taskTriggerMapDOList = buildAutoTaskTriggerMapDOList(autoTaskCreatReq, taskCode);
        boolean triggerResp = triggerMapRepository.batchInsertAutoTaskTrigger(taskTriggerMapDOList);
        NotificationAutoTaskDO autoTaskDO = buildAutoTaskDO(autoTaskCreatReq, taskCode);
        boolean taskResp = autoTaskRepository.insertAutoTask(autoTaskDO);
        return CommonResult.success(triggerResp && taskResp);
    }

    @Override
    public TaskHistoryDetailVO getAutoTaskModifyDetail(Long logId) {
        TaskModifyLog logDO = taskModifyRepository.getById(logId);
        if (logDO == null) {
            log.info("自动任务修改记录不存在, logId:{}", logId);
            return null;
        }

        TaskHistoryDetailVO vo = new TaskHistoryDetailVO();
        BeanUtils.copyProperties(logDO, vo);

        // 如果需要解析JSON字段，可以取消注释以下代码
        /*
        JSONObject oldJson = JSONUtil.parseObj(logDO.getModifyFieldOldValue());
        JSONObject newJson = JSONUtil.parseObj(logDO.getModifyFieldNewValue());
        List<TaskHistoryDetailVO.FieldChange> changes = new ArrayList<>();
        oldJson.forEach((key, oldValue) -> {
            String newValue = newJson.getStr(key);
            changes.add(new TaskHistoryDetailVO.FieldChange(key, oldValue.toString(), newValue));
        });
        vo.setChanges(changes);
        */
        return vo;
    }


    /**
     * 批量开启自动任务
     *
     * @param batchStatusReq 包含多个任务状态修改请求参数的列表
     * @return CommonResult<String> 操作结果信息，包含成功或失败的提示
     */
    @Override
    public CommonResult<String> batchStartTaskStatus(BatchTaskModifyStatusReq batchStatusReq) {
        List<TaskModifyStatusReq> modifyStatusReqList = batchStatusReq.getTaskModifyStatusReqList();
        if (CollUtil.isEmpty(modifyStatusReqList)) {
            return CommonResult.success("批量开启自动任务, 要开启的任务数量为空");
        }
        log.info("批量开启自动任务, modifyStatusReqList数量:{}", modifyStatusReqList.size());
        int success = 0;
        int fail = 0;
        SmsAutoTaskServiceImpl smsAutoTaskService = applicationContext.getBean(getClass());
        for (TaskModifyStatusReq taskModifyStatusReq : modifyStatusReqList) {
            taskModifyStatusReq.setModifyStatus(TaskStatusEnum.START.getStatus());
            CommonResult<Boolean> resp = smsAutoTaskService.modifyTaskStatus(taskModifyStatusReq);
            if (resp.isSuccess() || Boolean.TRUE.equals(resp.getData())) {
                success++;
            } else {
                fail++;
            }
        }
        String resp = success + "条启用成功";
        String errorMsg = fail + "条启用失败";
        if (success == modifyStatusReqList.size()) {
            return CommonResult.success(resp);
        } else if (fail == modifyStatusReqList.size()){
            return CommonResult.error(168987312, errorMsg);
        } else {
            resp += "，" + errorMsg;
        }
        return CommonResult.error(168987312, resp);
    }

    /**
     * 批量停用任务状态处理。
     *
     * @param batchStatusReq 任务状态修改请求列表，包含需要停止的任务ID及对应的状态变更信息
     * @return CommonResult<String> 操作结果封装对象
     */
    @Override
    public CommonResult<String> batchStopTaskStatus(BatchTaskModifyStatusReq batchStatusReq) {
        List<TaskModifyStatusReq> modifyStatusReqList = batchStatusReq.getTaskModifyStatusReqList();
        if (CollUtil.isEmpty(modifyStatusReqList)) {
            return CommonResult.success("批量停止任务状态处理, 要停止的任务数量为空");
        }
        log.info("批量停止任务状态处理, modifyStatusReqList数量:{}", modifyStatusReqList.size());
        int success = 0;
        int fail = 0;
        SmsAutoTaskServiceImpl smsAutoTaskService = applicationContext.getBean(getClass());
        for (TaskModifyStatusReq taskModifyStatusReq : modifyStatusReqList) {
            taskModifyStatusReq.setModifyStatus(TaskStatusEnum.STOP.getStatus());
            CommonResult<Boolean> resp = smsAutoTaskService.modifyTaskStatus(taskModifyStatusReq);
            if (resp.isSuccess() || Boolean.TRUE.equals(resp.getData())) {
                success++;
            } else {
                fail++;
            }
        }
        String resp = success + "条停用成功";
        String errorMsg = fail + "条停用失败";
        if (success == modifyStatusReqList.size()) {
            return CommonResult.success(resp);
        } else if (fail == modifyStatusReqList.size()) {
            return CommonResult.error(168987389, errorMsg);
        } else {
            resp += "，" + fail + "条停用失败";
        }
        return CommonResult.error(168987389, resp);
    }


    /**
     * 构建自动任务对象
     *
     * @param autoTaskCreatReq 自动任务创建请求对象，包含用户提交的任务信息
     * @param taskCode 任务代码，用于唯一标识一个任务
     * @return 返回构建好的NotificationAutoTaskDO对象
     */
    public NotificationAutoTaskDO buildAutoTaskDO(AutoTaskCreatReq autoTaskCreatReq, String taskCode) {
        NotificationAutoTaskDO autoTaskDO = NotificationAutoTaskDO.builder()
                .businessCode(BusinessIdEnum.VCS.getCode())
                .taskCode(taskCode)
                .taskName(autoTaskCreatReq.getTaskName())
                .triggerAction(autoTaskCreatReq.getTriggerActionString())
                .taskTimeType(autoTaskCreatReq.getTaskTimeType())
                .taskSendType(TaskSendTypeEnum.SCHEDULED.getCode())
                .messageTemplateCode(autoTaskCreatReq.getMessageTemplateCode())
                .dailySendTime(autoTaskCreatReq.getDailySendTime())
                .notifySpuCode(autoTaskCreatReq.getNotifySpuCode())
                .status(TaskStatusEnum.TO_BE_ACTIVATED.getStatus())
                .build();
        if (AutoTaskTimeTypeEnum.RANGE.getCode().equals(autoTaskCreatReq.getTaskTimeType())) {
            autoTaskDO.setRangeBeginDate(TimeFormatUtil.stringToTimeByFormat(
                    autoTaskCreatReq.getRangeBeginDate() + START_TIME, TimeFormatUtil.formatter_6));
            autoTaskDO.setRangeEndDate(TimeFormatUtil.stringToTimeByFormat(
                    autoTaskCreatReq.getRangeEndDate() + BLANK + autoTaskCreatReq.getDailySendTime() + END_TIME,
                    TimeFormatUtil.formatter_6));
        }
        return autoTaskDO;
    }

    /**
     * 根据自动任务创建请求和任务代码构建自动任务触发映射对象列表
     *
     * @param autoTaskCreatReq 自动任务创建请求对象，包含触发动作等信息
     * @param taskCode 任务代码，用于关联任务和触发条件
     * @return 返回一个自动任务触发映射对象列表，每个对象代表一个特定触发条件与任务的映射关系
     */
    public List<AutoTaskTriggerMapDO> buildAutoTaskTriggerMapDOList(AutoTaskCreatReq autoTaskCreatReq, String taskCode) {
        List<AutoTaskTriggerMapDO> taskTriggerMapDOList = new ArrayList<>();
        TriggerActionReq triggerAction = autoTaskCreatReq.getTriggerAction();
        AutoTaskTriggerMapDO brandTriggerMapDO = builderAutoTaskTriggerMapDO(taskCode, triggerAction.getBrandReq());
        AutoTaskTriggerMapDO vehicleOriginTriggerMapDO = builderAutoTaskTriggerMapDO(taskCode, triggerAction.getVehicleOriginReq());
        AutoTaskTriggerMapDO expirationServiceTriggerMapDO = builderAutoTaskTriggerMapDO(taskCode, triggerAction.getExpirationServiceReq());
        if (Objects.nonNull(triggerAction.getRealNameStatusReq())) {
            AutoTaskTriggerMapDO realNameStatusTriggerMapDO = builderAutoTaskTriggerMapDO(taskCode, triggerAction.getRealNameStatusReq());
            taskTriggerMapDOList.add(realNameStatusTriggerMapDO);
        }
        AutoTaskTriggerMapDO expirationIntervalTriggerMapDO = builderAutoTaskTriggerMapDO(taskCode, triggerAction.getExpirationIntervalReq());
        taskTriggerMapDOList.add(brandTriggerMapDO);
        taskTriggerMapDOList.add(vehicleOriginTriggerMapDO);
        taskTriggerMapDOList.add(expirationServiceTriggerMapDO);
        taskTriggerMapDOList.add(expirationIntervalTriggerMapDO);
        return taskTriggerMapDOList;
    }

    /**
     * 构建自动任务触发映射对象
     *
     * @param taskCode 任务代码，标识一个特定的自动任务
     * @param triggerFieldReq 触发动作字段请求对象，包含触发条件的详细信息
     * @return 返回构建好的自动任务触发映射对象
     */
    public AutoTaskTriggerMapDO builderAutoTaskTriggerMapDO(String taskCode, TriggerActionFieldReq triggerFieldReq) {
        return AutoTaskTriggerMapDO.builder()
                .taskCode(taskCode)
                .conditionId(triggerFieldReq.getConditionId())
                .conditionValue(triggerFieldReq.getConditionValue())
                .build();
    }

    /**
     * 根据自动任务条件列表构建任务条件属性视图列表
     *
     * @param conditionDOList 自动任务条件数据对象列表，代表从数据库中获取的原始数据
     * @param hasAttribute 是否有属性值
     * @return 返回一个任务条件属性视图对象
     */
    public TaskConditionAttributeVO buildTaskConditionAttributeVOList(List<AutoTaskConditionDO> conditionDOList,
                                                                      Boolean hasAttribute) {
        TaskConditionAttributeVO attributeVO = TaskConditionAttributeVO.builder().build();
        if (CollUtil.isEmpty(conditionDOList)) {
            return attributeVO;
        }
        attributeVO.setButtonName(AutoTaskConditionTypeEnum.getDescByType(conditionDOList.get(0).getConditionType()));
        attributeVO.setButtonDesc(AutoTaskConditionTypeEnum.getNoteByType(conditionDOList.get(0).getConditionType()));
        attributeVO.setHasAttribute(hasAttribute);
        attributeVO.setConditionAttributeVOList(buildConditionAttributeVOList(conditionDOList));
        return attributeVO;
    }

    /**
     * 根据自动任务条件数据对象列表构建条件属性值对象列表
     *
     * @param conditionDOList 自动任务条件数据对象列表，代表从数据库中获取的原始条件信息
     * @return 返回一个条件属性值对象列表，如果输入为null，则返回null
     */
    public List<ConditionAttributeVO> buildConditionAttributeVOList(List<AutoTaskConditionDO> conditionDOList) {
        if (Objects.isNull(conditionDOList)) {
            return null;
        }
        List<ConditionAttributeVO> resp = new ArrayList<>();
        for (AutoTaskConditionDO conditionDO : conditionDOList) {
            resp.add(ConditionAttributeVO.builder()
                    .conditionId(conditionDO.getConditionId())
                    .conditionCode(conditionDO.getConditionCode())
                    .conditionName(conditionDO.getConditionName())
                    .build());
        }
        return resp;
    }

    /**
     * 更新任务状态
     *
     * @param autoTaskDO 任务自动处理数据对象，不能为空
     * @param modifyStatusReq 修改任务状态的请求对象，不能为空
     */
    public void updateTaskStatus(NotificationAutoTaskDO autoTaskDO, TaskModifyStatusReq modifyStatusReq) {
        if (Objects.isNull(autoTaskDO) || Objects.isNull(modifyStatusReq)) {
            log.info("发送任务或修改任务入参为空");
            return ;
        }
        autoTaskDO.setStatus(modifyStatusReq.getModifyStatus());
        LocalDateTime now = LocalDateTime.now();
        //当修改状态为禁用时, 修改禁用时间为当前
        if (TaskStatusEnum.STOP.getStatus().equals(modifyStatusReq.getModifyStatus())) {
            autoTaskDO.setDeactivateTime(now);
        }
        //当修改状态为启用时，修改启用时间为当前,停用时间清空
        if (TaskStatusEnum.START.getStatus().equals(modifyStatusReq.getModifyStatus())) {
            autoTaskDO.setActivateTime(now);
            autoTaskDO.setDeactivateTime(null);
            if (BusinessConstants.TASK_SEND_TYPE.REAL_TIME.equals(autoTaskDO.getTaskSendType())){
                // 启用时，给发送时间赋值
                autoTaskDO.setActivateTime(now);
            }
        }
        autoTaskDO.setUpdatedBy(OperatorUtil.getOperator());
        autoTaskDO.setUpdatedTime(LocalDateTime.now());
        autoTaskRepository.updateAutoTaskById(autoTaskDO);
    }

    // 改造后的状态日志记录方法
    private void addTaskModifyStatusLog(NotificationAutoTaskDO oldTask,
                                        TaskModifyStatusReq newReq) {
        log.info("记录状态修改日志, oldTask:{}, newReq:{}", JSON.toJSONString(oldTask), JSON.toJSONString(newReq));

        Map<String, Object> oldValues = new HashMap<>();
        Map<String, Object> newValues = new HashMap<>();
        AtomicInteger modifyCount = new AtomicInteger(0);

        // 比较状态变化
        Integer oldStatus = oldTask.getStatus();
        Integer newStatus = newReq.getModifyStatus();

        if (!Objects.equals(oldStatus, newStatus)) {
            modifyCount.incrementAndGet();
            String fieldName = AutoNotificationTaskFieldEnum.EDIT_STATUS.getFieldName();

            oldValues.put(fieldName, TaskStatusEnum.getTaskStatusEnumByCode(oldStatus).getDesc());
            newValues.put(fieldName, TaskStatusEnum.getTaskStatusEnumByCode(newStatus).getDesc());
        }

        // 插入日志（仅当有修改时）
        if (modifyCount.get() > 0) {
            TaskModifyLog log = buildModifyLog(
                    newReq.getTaskCode(),
                    AutoNotificationTaskFieldEnum.EDIT_STATUS.getModuleName(),
                    modifyCount.get(),
                    oldValues,
                    newValues
            );
            taskModifyRepository.insertTaskModifyLog(log);
        }
    }


    /**
     * 校验编辑任务状态的参数
     * @param statusReq 任务状态入参
     * @return CommonResult<Boolean>
     * */
    public CommonResult<Boolean> checkTaskArg(TaskModifyStatusReq statusReq) {
        if (Objects.isNull(statusReq)) {
            return CommonResult.error(ErrorCodeConstants.TASK_REQ_NULL);
        }
        if (StringUtils.isBlank(statusReq.getTaskCode())) {
            return CommonResult.error(ErrorCodeConstants.TASK_CODE_NULL);
        }
        if (Objects.isNull(statusReq.getModifyStatus())) {
            return CommonResult.error(ErrorCodeConstants.TASK_STATUS_NULL);
        }
        if (!TaskStatusEnum.STOP.getStatus().equals(statusReq.getModifyStatus()) &&
                !TaskStatusEnum.START.getStatus().equals(statusReq.getModifyStatus())) {
            return CommonResult.error(ErrorCodeConstants.TASK_STATUS_ERROR);
        }
        return CommonResult.success(true);
    }


    /**
     * 更新任务定时发送时间
     * @param autoTask 通知任务数据
     * @param taskModifyReq 任务编辑入参
     * */
    public void updateTaskSendTime(NotificationAutoTaskDO autoTask, AutoTaskModifyTimeReq taskModifyReq) {
        if (Objects.isNull(autoTask) || Objects.isNull(taskModifyReq)) {
            log.info("更新任务定时发送时间, 任务或编辑入参为空");
            return ;
        }
        if (!TaskSendTypeEnum.SCHEDULED.getCode().equals(autoTask.getTaskSendType())) {
            log.info("更新自动任务定时发送时间, 任务类型不是定时任务, autoTask:{}", autoTask);
            return ;
        }
        autoTask.setDailySendTime(taskModifyReq.getDailySendTime());
        autoTask.setTaskTimeType(taskModifyReq.getTaskTimeType());
        //定时任务，编辑时间后，任务的状态是待启用
        autoTask.setStatus(TaskStatusEnum.TO_BE_ACTIVATED.getStatus());
        if (AutoTaskTimeTypeEnum.RANGE.getCode().equals(taskModifyReq.getTaskTimeType())) {
            autoTask.setRangeBeginDate(TimeFormatUtil.stringToTimeByFormat(taskModifyReq.getRangeBeginDate()+START_TIME,
                    TimeFormatUtil.formatter_6));
            autoTask.setRangeEndDate(TimeFormatUtil.stringToTimeByFormat(
                    taskModifyReq.getRangeEndDate() + BLANK + taskModifyReq.getDailySendTime() + END_TIME,
                    TimeFormatUtil.formatter_6));
        }
        autoTask.setUpdatedTime(LocalDateTime.now());
        autoTask.setUpdatedBy(OperatorUtil.getOperator());
        autoTaskRepository.updateAutoTaskById(autoTask);
        if (AutoTaskTimeTypeEnum.CYCLIC.getCode().equals(taskModifyReq.getTaskTimeType())) {
            autoTaskRepository.updateBeginEndDateNull(autoTask.getTaskCode());
        }
    }

    // 改造后的日志记录方法
    private void addTaskModifyTimeLog(NotificationAutoTaskDO oldTask, AutoTaskModifyTimeReq newReq) {
        log.info("任务定时发送时间修改, oldTask:{}, newReq:{}", JSON.toJSONString(oldTask), JSON.toJSONString(newReq));

        Map<String, Object> oldValues = new HashMap<>();
        Map<String, Object> newValues = new HashMap<>();
        AtomicInteger modifyCount = new AtomicInteger(0);

        // 比较发送时间配置（包含类型+时间）
        String oldTimeDesc = buildTimeDescription(oldTask);
        String newTimeDesc = buildTimeDescription(newReq);
        log.info("任务定时发送时间修改, oldTimeDesc:{}, newTimeDesc:{}", oldTimeDesc, newTimeDesc);

        if (!Objects.equals(oldTimeDesc, newTimeDesc)) {
            modifyCount.incrementAndGet();
            String fieldName = EDIT_SCHEDULE.getFieldName();
            oldValues.put(fieldName, oldTimeDesc);
            newValues.put(fieldName, newTimeDesc);
        }

        // 单独比较每日发送时间（当类型为永久开启时）
//        if (newReq.getTaskTimeType() == 1) {
//            if (!Objects.equals(oldTask.getDailySendTime(), newReq.getDailySendTime())) {
//                modifyCount.incrementAndGet();
//                oldValues.put("每日定时时间", oldTask.getDailySendTime());
//                newValues.put("每日定时时间", newReq.getDailySendTime());
//            }
//        }

        // 插入日志（仅当有修改时）
        if (modifyCount.get() > 0) {
            TaskModifyLog log = buildModifyLog(
                    newReq.getTaskCode(),
                    EDIT_SCHEDULE.getModuleName(),
                    modifyCount.get(),
                    oldValues,
                    newValues
            );
            taskModifyRepository.insertTaskModifyLog(log);
        }
    }

    // 时间描述生成方法 也可用反射合并两个buildTimeDescription方法
    private String buildTimeDescription(NotificationAutoTaskDO task) {
        if (task.getTaskTimeType() == 1) {
            return String.format("永久开启每日定时发送时间%s", task.getDailySendTime());
        } else {
            return String.format("限时开启%s至%s",
                    task.getRangeBeginDate(),
                    task.getRangeEndDate()
            );
        }
    }

    private String buildTimeDescription(AutoTaskModifyTimeReq req) {
        if (req.getTaskTimeType() == 1) {
            return String.format("永久开启每日定时发送时间%s", req.getDailySendTime());
        } else {
            return String.format("限时开启%s至%s",
                    req.getRangeBeginDate(),
                    req.getRangeEndDate()
            );
        }
    }

    private TaskModifyLog buildModifyLog(String taskCode, String module,
                                         int fieldCount,
                                         Map<String, Object> oldVals,
                                         Map<String, Object> newVals) {
        return TaskModifyLog.builder()
                .taskCode(taskCode)
                .modifyModule(module)
                .modifyFieldCount(fieldCount)
                .modifyFieldOldValue(JSON.toJSONString(oldVals))
                .modifyFieldNewValue(JSON.toJSONString(newVals))
                .operateTime(LocalDateTime.now())
                .operateUser(OperatorUtil.getOperator())
               .build();
    }

    /**
     * 任务编辑类型
     * @param modifyType 修改类型
     * @return String
     * */
    public String getTaskModifyContent(Integer modifyType) {
        TaskModifyTypeEnum modifyTypeEnum = TaskModifyTypeEnum.getTaskModifyTypeEnumByType(modifyType);
        if (Objects.isNull(modifyTypeEnum)) {
            log.info("按照任务编辑类型返回任务修改枚举为空, taskModifyType:{}", modifyType);
            return "";
        }
        return modifyTypeEnum.getDesc();
    }


    /**
     *  校验修改发送任务时间的参数
     * @param  taskModifyTimeReq 发送任务入参
     * @return CommonResult<Boolean>
     * */
    public CommonResult<Boolean> checkModifySendTimeArg(AutoTaskModifyTimeReq taskModifyTimeReq) {
        if (Objects.isNull(taskModifyTimeReq)) {
            return CommonResult.error(ErrorCodeConstants.TASK_REQ_NULL);
        }
        if (StringUtils.isBlank(taskModifyTimeReq.getTaskCode())) {
            return CommonResult.error(ErrorCodeConstants.TASK_CODE_NULL);
        }
        if (StringUtils.isBlank(taskModifyTimeReq.getDailySendTime())) {
            return CommonResult.error(ErrorCodeConstants.TASK_SEND_TIME_NULL);
        }
        if (DAY_ZERO.equals(taskModifyTimeReq.getDailySendTime())) {
            return CommonResult.error(ErrorCodeConstants.TASK_SEND_TIME_ERROR);
        }
        if (!taskModifyTimeReq.getDailySendTime().contains(":")) {
            return CommonResult.error(ErrorCodeConstants.TASK_SEND_TIME_FORMAT_ERROR);
        }
        String[] sendTimes = taskModifyTimeReq.getDailySendTime().split(":");
        if (sendTimes.length != 2) {
            return CommonResult.error(ErrorCodeConstants.TASK_SEND_TIME_FORMAT_ERROR);
        }
        return CommonResult.success(true);
    }

    /**
     *  批量构建发送任务的分页列表页
     * @param taskHistoryList 发送任务列表页
     * @return  List<TaskHistoryPageListVo>
     * */
    public List<TaskHistoryPageListVo> buildTaskHistoryPageList(List<TaskModifyLog> taskHistoryList) {
        List<TaskHistoryPageListVo> pageListVos = new ArrayList<>();
        if (CollectionUtils.isEmpty(taskHistoryList)) {
            return pageListVos;
        }
        for (TaskModifyLog pageListVo : taskHistoryList) {
            pageListVos.add(buildTaskHistoryPageVo(pageListVo));
        }
        return pageListVos;
    }

    /**
     * 构建任务编辑历史记录VO
     *
     * @param taskHistory 发送任务编辑历史
     */
    public TaskHistoryPageListVo buildTaskHistoryPageVo(TaskModifyLog taskHistory) {
        if (Objects.isNull(taskHistory)) {
            return TaskHistoryPageListVo.builder().build();
        }
        TaskHistoryPageListVo vo = new TaskHistoryPageListVo();
        BeanUtils.copyProperties(taskHistory, vo);

        // 获取所有需要显示详情的字段
        Set<String> allDetailFields = AutoNotificationTaskFieldEnum.getAllDetailFields();

        String oldValue = taskHistory.getModifyFieldOldValue();
        if (StrUtil.isNotBlank(oldValue)) {

            Set<String> oldSet = JSON.parseObject(oldValue, Feature.OrderedField).keySet();
            vo.setModifyField(String.join("、", oldSet));

            // 判断是否包含任意一个需要显示详情的字段
            boolean modifyDetail = oldSet.stream().anyMatch(allDetailFields::contains);
            vo.setModifyDetail(modifyDetail);
        }

        return vo;
    }

    /**
     * 任务模板类型
     * @param typeCode 模板类型code
     * @return String
     * */
    public String getTaskTemplateType(Integer typeCode) {
        SmsTemplateTypeEnum typeEnum = SmsTemplateTypeEnum.getTemplateByCode(typeCode);
        if (Objects.isNull(typeEnum)) {
            log.error("短信模板类型不存在，smsTemplateTypeCode:{}", typeCode);
            return "";
        }
        return typeEnum.getDesc();
    }

    /**
     * 批量构建短信通知列表对象
     * @param taskList 通知任务列表
     * @return List<SmsTaskPageListVo>
     * */
    public List<SmsTaskPageListVo> buildSmsTaskPageList(List<NotificationAutoTaskDO> taskList) {
        List<SmsTaskPageListVo> smsTaskPageListVos = new ArrayList<>();
        if (CollectionUtils.isEmpty(taskList)) {
            return smsTaskPageListVos;
        }
        for (NotificationAutoTaskDO task : taskList) {
            smsTaskPageListVos.add(buildSmsTaskPage(task));
        }
        return smsTaskPageListVos;
    }

    /**
     *  构建短信通知分页任务列表
     * @param  autoTaskDO  任务列表页
     * @return SmsTaskPageListVo
     * */
    public SmsTaskPageListVo buildSmsTaskPage(NotificationAutoTaskDO autoTaskDO) {
        if (Objects.isNull(autoTaskDO)) {
            return SmsTaskPageListVo.builder().build();
        }
        return SmsTaskPageListVo.builder()
                .taskCode(autoTaskDO.getTaskCode())
                .taskName(autoTaskDO.getTaskName())
                .triggerAction(autoTaskDO.getTriggerAction())
                .taskSendTimeType(getTaskSendTimeType(autoTaskDO.getTaskSendType()))
                .rangeBeginDate(TimeFormatUtil.timeToStringByFormat(autoTaskDO.getRangeBeginDate(), TimeFormatUtil.formatter_7))
                .rangeEndDate(TimeFormatUtil.timeToStringByFormat(autoTaskDO.getRangeEndDate(), TimeFormatUtil.formatter_7))
                .dailySendTime(autoTaskDO.getDailySendTime())
                .messageTemplateContent(templateRepository.getTemplateContentByCode(autoTaskDO.getMessageTemplateCode()))
                .status(autoTaskDO.getStatus())
                .activateTime(TimeFormatUtil.localDateToString(autoTaskDO.getActivateTime()))
                .deactivateTime(getTaskDeactivateTime(autoTaskDO.getStatus(),
                        autoTaskDO.getDeactivateTime()))
                .expirationFlag(getExpirationFlag(autoTaskDO))
                .build();
    }

    /**
     * 获取通知自动任务的过期标志
     *
     * @param autoTaskDO 自动任务的实体对象，包含任务的各种信息
     * @return 如果任务已经过期，则返回true；否则返回false
     */
    public Boolean getExpirationFlag(NotificationAutoTaskDO autoTaskDO) {
        if (Objects.isNull(autoTaskDO) || Objects.isNull(autoTaskDO.getRangeEndDate())
                || TaskSendTypeEnum.REALTIME.getCode().equals(autoTaskDO.getTaskSendType())
                || AutoTaskTimeTypeEnum.CYCLIC.getCode().equals(autoTaskDO.getTaskTimeType())) {
            return false;
        }
        return autoTaskDO.getRangeEndDate().isBefore(LocalDateTime.now());
    }

    /**
     *  返回任务的停用时间
     * @param taskStatus 任务状态
     * @param deactivateTime 停止时间
     * @return String
     * */
    public String getTaskDeactivateTime(Integer taskStatus, LocalDateTime deactivateTime) {
        if (Objects.isNull(taskStatus) || Objects.isNull(deactivateTime)) {
            log.info("发送任务状态或任务停用时间为空");
            return null;
        }
        if (TaskStatusEnum.START.getStatus().equals(taskStatus)) {
            log.info("当前发送任务为启用状态，无需停用时间");
            return null;
        }
        return TimeFormatUtil.localDateToString(deactivateTime);
    }

    /**
     *  自动任务发送时间类型
     *  @param sendType 发送时间类型
     *  @return String
     * */
    public String getTaskSendTimeType(Integer sendType) {
        TaskSendTypeEnum taskSendTypeEnum = TaskSendTypeEnum.getTaskEnumByCode(sendType);
        if (Objects.isNull(taskSendTypeEnum)) {
            return "";
        }
        return taskSendTypeEnum.getDesc();
    }

}
