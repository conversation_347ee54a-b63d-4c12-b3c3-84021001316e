package com.jlr.ecp.notification.vo.crc;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@Schema(description = "CRC保存未读消息VO")
public class SaveCrcUnreadMsgVO implements Serializable {
    /**
     *  用户全局唯一JLRID
     * */
    @Schema(description = "用户全局唯一JLRID")
    private String jlrid;

    /**
     *  唯一ID, UUID或雪花算法ID
     * */
    @Schema(description = "唯一ID, UUID或雪花算法ID")
    private String messageId;

    /**
     *  消息保存状态，success代表成功，failed代表保存失败
     * */
    @Schema(description = "消息保存状态")
    private String status;

    /**
     *  业务线编码
     * */
    @Schema(description = "业务线编码")
    private String businessCode;
}
