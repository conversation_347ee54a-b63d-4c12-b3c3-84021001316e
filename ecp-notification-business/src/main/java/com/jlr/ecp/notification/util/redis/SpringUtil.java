package com.jlr.ecp.notification.util.redis;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

@Component
public class SpringUtil implements ApplicationContextAware, ApplicationListener<ContextClosedEvent> {

	private static ThreadLocal<ApplicationContext> contextHolder = new ThreadLocal<>();

	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		contextHolder.set(applicationContext);
	}

	public static <T> T getBean(Class<T> clazz) {
		ApplicationContext context = contextHolder.get();
		if (context == null || clazz == null) {
			return null;
		}
		return context.getBean(clazz);
	}

	public static <T> T getBean(String beanId) {
		ApplicationContext context = contextHolder.get();
		if (context == null || beanId == null) {
			return null;
		}
		return (T) context.getBean(beanId);
	}

	public static ApplicationContext getContext() {
		return contextHolder.get();
	}

	@Override
	public void onApplicationEvent(ContextClosedEvent event) {
		contextHolder.remove();
	}
}