package com.jlr.ecp.notification.kafka.listener;

import com.alibaba.fastjson.JSON;
import com.jlr.ecp.notification.kafka.message.BaseBrandedGoodsMessage;
import com.jlr.ecp.notification.kafka.send.template.SmsSendTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 通知消息监听器基类
 * @param <T> 消息类型
 */
@Slf4j
public abstract class BaseNotificationListener<T extends BaseBrandedGoodsMessage> {
    
    @Resource
    protected SmsSendTemplate smsSendTemplate;

    /**
     * 批量处理消息
     * @param records Kafka消息记录
     * @param messageClass 消息类型
     */
    protected void processMessages(List<ConsumerRecord<String, String>> records, Class<T> messageClass) {
        log.info("接收通知消息，records:{}", records);
        List<T> messageList = buildMessageList(records, messageClass);
        for (T message : messageList) {
            try {
                smsSendTemplate.sendBgOrLreSmsMsg(message);
            } catch (Exception e) {
                log.error("发送短信异常，message:{}", message, e);
                handleException(message, e);
            }
        }
    }

    /**
     * 构建消息列表
     * @param records Kafka消息记录
     * @param messageClass 消息类型 
     * @return 消息对象列表
     */
    protected List<T> buildMessageList(List<ConsumerRecord<String, String>> records, Class<T> messageClass) {
        List<T> messageList = new ArrayList<>();
        if (CollectionUtils.isEmpty(records)) {
            return messageList;
        }
        for (ConsumerRecord<String, String> record : records) {
            T message = JSON.parseObject(record.value(), messageClass);
            messageList.add(message);
        }
        return messageList;
    }

    /**
     * 异常处理，子类可覆盖实现特殊处理
     * @param message 消息对象
     * @param e 异常
     */
    protected void handleException(T message, Exception e) {
        // 默认实现
    }
} 