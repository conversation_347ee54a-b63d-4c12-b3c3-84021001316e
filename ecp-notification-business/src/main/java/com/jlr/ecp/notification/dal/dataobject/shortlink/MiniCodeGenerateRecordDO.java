package com.jlr.ecp.notification.dal.dataobject.shortlink;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_minicode_generate_records")
public class MiniCodeGenerateRecordDO extends BaseDO {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("record_code")
    private String recordCode;

    @TableField("route_path_url")
    private String routePathUrl;

    @TableField("config_id")
    private Long configId;

    @TableField("result")
    private Integer result;

    @TableField("error_code")
    private String errorCode;

    @TableField("error_message")
    private String errorMessage;

    @TableField("tenant_id")
    private Integer tenantId;
}
