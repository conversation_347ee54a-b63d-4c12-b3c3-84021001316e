apiVersion: v1
kind: Pod
spec:
  securityContext:
    runAsUser: 0
  imagePullSecrets:
    - name: registry.tools.adidas.com.cn
  containers:
  - name: jnlp
    image: registry.tools.adidas.com.cn/pea-cicd/acid-slave:2.0-common
    workingDir: "/home/<USER>/work"
    env:
      - name: DOCKER_HOST
        value: "tcp://localhost:2375"
    volumeMounts:
      - name: dockersock
        mountPath: /var/lib/docker
  - name: docker
    image: registry.tools.adidas.com.cn/pea-cicd/docker:18.09-dind
    args:
      - '--mtu=1430'
    workingDir: "/home/<USER>/work"
    ports:
      - name: docker
        containerPort: 2375
        protocol: TCP
    volumeMounts:
      - name: dockersock
        mountPath: /var/lib/docker
    securityContext:
      privileged: true
    tty: true
  - name: kubectl
    image: registry.tools.adidas.com.cn/pea-cicd/kubectl:1.22.3
    command:
      - cat
    tty: true
  - name: kustomize
    image: registry.tools.adidas.com.cn/pea-cicd/kustomize:3.8
    command:
      - cat
    tty: true
  - name: maven
    image: registry.tools.adidas.com.cn/pea-cicd/maven:3.8.4-openjdk-11-slim
    command:
      - cat
    tty: true
    volumeMounts:
    - name: agentcache
      mountPath: "/root/.m2"
      subPath: "maven"
    - name: agentcache
      mountPath: "/root/.sonar"
      subPath: "sonar"
  - name: hadolint
    image: registry.tools.adidas.com.cn/pea-cicd/hadolint:latest-debian
    imagePullPolicy: IfNotPresent
    command:
      - cat
    tty: true
  volumes:
  - name: dockersock
    emptyDir: {}
  - name: agentcache
    persistentVolumeClaim:
      claimName: slave-cache-storage