package com.jlr.ecp.notification.controller.app;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.dto.mp.MpTemplateMsgSubscriptionDTO;
import com.jlr.ecp.notification.service.mp.MpTemplateMsgAppService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;

@RestController
@RequestMapping("/v1/mp/message")
@Tag(name = "小程序模板消息通知")
@Validated
public class MpTemplateMsgAppController {

    @Resource
    private MpTemplateMsgAppService mpTemplateMsgService;

    @PermitAll
    @Operation(summary = "小程序模板消息订阅")
    @PostMapping("/subscription")
    public CommonResult<String> subscribe(@RequestBody @Validated MpTemplateMsgSubscriptionDTO mpTemplateMsgSubscriptionDTO) {
        return CommonResult.success(mpTemplateMsgService.saveMpTemplateMsgSubscribe(mpTemplateMsgSubscriptionDTO));
    }

}
