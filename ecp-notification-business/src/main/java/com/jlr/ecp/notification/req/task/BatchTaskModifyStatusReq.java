package com.jlr.ecp.notification.req.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "批量修改任务状态请求")
@Data
public class BatchTaskModifyStatusReq {
    @Schema(description = "任务状态列表")
    private List<TaskModifyStatusReq> taskModifyStatusReqList;

    @Schema(description = "状态：0-禁用、1-启用")
    @NotNull(message = "状态不能为空")
    private Integer batchOperationStatus;
}
