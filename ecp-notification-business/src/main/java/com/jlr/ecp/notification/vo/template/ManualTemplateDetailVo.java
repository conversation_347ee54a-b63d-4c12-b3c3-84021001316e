package com.jlr.ecp.notification.vo.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Schema(description = "手动模板详情Vo")
@Builder
public class ManualTemplateDetailVo {
    @Schema(description = "模板code")
    private String templateCode;

    @Schema(description = "业务线code")
    private String businessCode;

    @Schema(description = "模板名称")
    private String templateName;

    @Schema(description = "模板类型, 1-自动配置, 2-手动配置")
    private String templateType;

    @Schema(description = "模板内容")
    private String templateContent;

    @Schema(description = "场景说明")
    private String templateRemarks;
}
