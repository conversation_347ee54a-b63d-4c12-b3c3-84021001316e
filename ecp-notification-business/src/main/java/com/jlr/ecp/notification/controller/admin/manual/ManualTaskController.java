package com.jlr.ecp.notification.controller.admin.manual;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.notification.constant.BusinessConstants;
import com.jlr.ecp.notification.req.task.*;
import com.jlr.ecp.notification.service.manual.SmsManualTaskService;
import com.jlr.ecp.notification.vo.task.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Tag(name = "手动任务管理")
@RestController
@RequestMapping("/manual/task")
@Slf4j
public class ManualTaskController {
    @Autowired
    private SmsManualTaskService taskService;

    @PostMapping("/list")
    @Operation(summary = "手动通知任务列表")
    @PreAuthorize("@ss.hasPermission('notification:smstask-manual:list')")
    public CommonResult<PageResult<ManualTaskPageListVo>> taskPageList(@RequestBody TaskPageListReq taskPageListReq) {
        taskPageListReq.setTaskType(BusinessConstants.TASK_TYPE.MANUAL);
        return CommonResult.success(taskService.getManualTaskPageList(taskPageListReq));
    }

    @GetMapping("/statusDesc")
    @Operation(summary = "手动通知任务列表-状态描述")
    @PreAuthorize("@ss.hasPermission('notification:smstask-manual:list')")
    public CommonResult<List<TaskStatusVO>> taskPageList() {
        return CommonResult.success(taskService.getManualTaskStatusVO());
    }

    @PutMapping("/add")
    @Operation(summary = "新增手动通知任务")
    @PreAuthorize("@ss.hasPermission('notification:smstask-manual:create')")
    public CommonResult<Boolean> addManualTask(@Validated @RequestBody TaskCreatReq taskCreatReq) {
        return taskService.addManualTask(taskCreatReq);
    }

    @GetMapping("/detail")
    @Operation(summary = "手动任务详情页")
    @PreAuthorize("@ss.hasPermission('notification:smstask-manual:detail')")
    public CommonResult<ManualTaskDetailVo> taskDetail(@Validated @SpringQueryMap TaskDetailReq taskDetailReq) {
        log.info("开始短信通知任务详情查询, taskDetailReq:{}", taskDetailReq);
        return taskService.getManualTaskDetail(taskDetailReq);
    }

    @GetMapping("/history")
    @Operation(summary = "手动任务编辑记录历史")
    @PreAuthorize("@ss.hasPermission('notification:smstask-manual:detail')")
    public CommonResult<PageResult<TaskHistoryPageListVo>> taskHistoryPageList(@SpringQueryMap
                                                                               TaskHistoryReq taskHistoryReq) {
        log.info("开始短信编辑历史记录, taskHistoryReq:{}", taskHistoryReq);
        PageResult<TaskHistoryPageListVo> pageResult = taskService.getTaskHistoryPageList(taskHistoryReq);
        return CommonResult.success(pageResult);
    }

    /**
     * 新增的手动任务编辑记录详情接口
     */
    @GetMapping("/history/detail")
    @Operation(summary = "手动任务编辑记录详情")
    @PreAuthorize("@ss.hasPermission('notification:smstask-manual:detail')")
    public CommonResult<ManualTaskHistoryDetailVO> getModifyDetail(
            @RequestParam("id") @Schema(description = "日志ID") Long id) {
        log.info("获取手动任务编辑记录详情, id:{}", id);
        ManualTaskHistoryDetailVO detail = taskService.getManualTaskModifyDetail(id);
        return CommonResult.success(detail);
    }

    @PostMapping("/modify/update")
    @Operation(summary = "手动任务编辑")
    @PreAuthorize("@ss.hasPermission('notification:smstask-manual:edit')")
    public CommonResult<Boolean> modifyTask(@Validated @RequestBody TaskModifyReq taskModifyReq) {
      return taskService.updateManualTask(taskModifyReq);
    }

    @PostMapping("/modify/status/start")
    @Operation(summary = "手动任务启用")
    @PreAuthorize("@ss.hasPermission('notification:smstask-manual:start-using')")
    public CommonResult<Boolean> modifyTaskStatusStart(@RequestBody TaskModifyStatusReq taskModifyStatusReq) {
        log.info("启用入参, taskModifyStatusReq:{}", taskModifyStatusReq);
        taskModifyStatusReq.setModifyStatus(BusinessConstants.WHETHER.YES);
        return taskService.openManualTask(taskModifyStatusReq);
    }

    @PostMapping("/modify/status/stop")
    @Operation(summary = "手动任务禁用")
    @PreAuthorize("@ss.hasPermission('notification:smstask-manual:stop-using')")
    public CommonResult<Boolean> modifyTaskStatusStop(@RequestBody TaskModifyStatusReq taskModifyStatusReq) {
        log.info("禁用入参, taskModifyStatusReq:{}", taskModifyStatusReq);
        taskModifyStatusReq.setModifyStatus(BusinessConstants.WHETHER.NOT);
        return taskService.modifyManualTaskStatus(taskModifyStatusReq);
    }

    @DeleteMapping("/file/delete")
    @Operation(summary = "手动任务删除上传文件")
    @PreAuthorize("@ss.hasPermission('notification:smstask-manual:edit')")
    public CommonResult<Boolean> deleteFile(@RequestParam String taskCode) {
        log.info("手动任务删除上传文件, taskCode:{}", taskCode);
        return taskService.deleteFile(taskCode);
    }

    @PostMapping("/batch/status/start")
    @Operation(summary = "手动任务批量启用")
    @PreAuthorize("@ss.hasPermission('notification:smstask-manual:start-using')")
    public CommonResult<String> taskStatusBatchStart(@RequestBody BatchTaskModifyStatusReq batchStatusReq) {
        return taskService.batchStartManualTask(batchStatusReq);
    }

    @PostMapping("/batch/status/stop")
    @Operation(summary = "手动任务批量禁用")
    @PreAuthorize("@ss.hasPermission('notification:smstask-manual:stop-using')")
    public CommonResult<String> taskStatusBatchStop(@RequestBody BatchTaskModifyStatusReq batchStatusReq) {
        return taskService.batchStopManualTask(batchStatusReq);
    }
}
